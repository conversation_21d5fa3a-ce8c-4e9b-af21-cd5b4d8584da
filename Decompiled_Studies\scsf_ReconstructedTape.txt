
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_ReconstructedTape
               (undefined1 param_1 [16],undefined8 param_2,longlong param_3,undefined8 param_4,
               undefined8 param_5,undefined8 param_6,undefined8 param_7,undefined8 param_8,
               undefined8 param_9,undefined8 param_10)

{
  float fVar1;
  bool bVar2;
  int *piVar3;
  HANDLE pvVar4;
  char *pcVar5;
  undefined8 uVar6;
  ulonglong uVar7;
  float *pfVar8;
  undefined4 *puVar9;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  undefined8 extraout_x1_10;
  undefined8 extraout_x1_11;
  undefined8 extraout_x1_12;
  undefined8 extraout_x1_13;
  longlong lVar10;
  longlong lVar11;
  float *pfVar12;
  int iVar13;
  uint uVar14;
  uint uVar15;
  int iVar16;
  longlong *plVar17;
  char *lpMem;
  longlong *plVar18;
  longlong *plVar19;
  int iVar20;
  char *pcVar21;
  undefined1 extraout_q0 [16];
  undefined1 auVar22 [16];
  undefined1 extraout_q0_00 [16];
  undefined1 extraout_q0_01 [16];
  undefined4 uVar23;
  float fVar24;
  float fVar25;
  float fVar26;
  float fVar27;
  float extraout_s18;
  longlong local_168;
  longlong local_160;
  longlong *local_130;
  longlong *local_128;
  longlong *local_118;
  longlong *local_100;
  longlong *local_f8;
  longlong *local_f0;
  longlong *local_e8;
  longlong *local_e0;
  longlong *local_d8;
  char *local_b0;
  undefined8 uStack_a8;
  undefined1 *local_a0;
  
                    /* 0x88bd0  22  scsf_ReconstructedTape */
  piVar3 = (int *)(**(code **)(param_3 + 0x18b8))(0);
  lVar10 = *(longlong *)(param_3 + 0x500);
  uVar6 = extraout_x1;
  auVar22 = extraout_q0;
  if (lVar10 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar6 = extraout_x1_00;
    }
    lVar10 = *(longlong *)(param_3 + 0x500);
    if (lVar10 != 0) goto LAB_180088c58;
    plVar19 = (longlong *)(param_3 + 0x528);
LAB_180088c84:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar6 = extraout_x1_01;
    }
    lVar10 = *(longlong *)(param_3 + 0x500);
    if (lVar10 != 0) goto LAB_180088cb0;
    local_118 = (longlong *)(param_3 + 0x528);
LAB_180088ce4:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar6 = extraout_x1_02;
    }
    lVar10 = *(longlong *)(param_3 + 0x500);
    if (lVar10 != 0) goto LAB_180088d10;
    local_168 = param_3 + 0x528;
LAB_180088d44:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar6 = extraout_x1_03;
    }
    lVar10 = *(longlong *)(param_3 + 0x500);
    if (lVar10 != 0) goto LAB_180088d70;
    local_160 = param_3 + 0x528;
LAB_180088da4:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar6 = extraout_x1_04;
    }
    lVar10 = *(longlong *)(param_3 + 0x500);
    if (lVar10 != 0) goto LAB_180088dc4;
    lVar10 = param_3 + 0x528;
  }
  else {
LAB_180088c58:
    iVar20 = *(int *)(param_3 + 0x520);
    if (iVar20 == 0) {
      plVar19 = (longlong *)(param_3 + 0x528);
    }
    else {
      iVar13 = 0;
      if (iVar20 < 1) {
        iVar13 = iVar20 + -1;
      }
      plVar19 = (longlong *)(lVar10 + (longlong)iVar13 * 0x170);
    }
    if (lVar10 == 0) goto LAB_180088c84;
LAB_180088cb0:
    iVar20 = *(int *)(param_3 + 0x520);
    if (iVar20 == 0) {
      local_118 = (longlong *)(param_3 + 0x528);
    }
    else {
      iVar13 = 1;
      if (iVar20 < 2) {
        iVar13 = iVar20 + -1;
      }
      local_118 = (longlong *)(lVar10 + (longlong)iVar13 * 0x170);
    }
    if (lVar10 == 0) goto LAB_180088ce4;
LAB_180088d10:
    iVar20 = *(int *)(param_3 + 0x520);
    if (iVar20 == 0) {
      local_168 = param_3 + 0x528;
    }
    else {
      iVar13 = 2;
      if (iVar20 < 3) {
        iVar13 = iVar20 + -1;
      }
      local_168 = lVar10 + (longlong)iVar13 * 0x170;
    }
    if (lVar10 == 0) goto LAB_180088d44;
LAB_180088d70:
    iVar20 = *(int *)(param_3 + 0x520);
    if (iVar20 == 0) {
      local_160 = param_3 + 0x528;
    }
    else {
      iVar13 = 3;
      if (iVar20 < 4) {
        iVar13 = iVar20 + -1;
      }
      local_160 = lVar10 + (longlong)iVar13 * 0x170;
    }
    if (lVar10 == 0) goto LAB_180088da4;
LAB_180088dc4:
    iVar20 = *(int *)(param_3 + 0x520);
    if (iVar20 == 0) {
      lVar10 = param_3 + 0x528;
    }
    else {
      iVar13 = 4;
      if (iVar20 < 5) {
        iVar13 = iVar20 + -1;
      }
      lVar10 = lVar10 + (longlong)iVar13 * 0x170;
    }
  }
  lVar11 = *(longlong *)(param_3 + 0x210);
  if (lVar11 == 0) {
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar6 = extraout_x1_05;
    }
    lVar11 = *(longlong *)(param_3 + 0x210);
    if (lVar11 != 0) goto LAB_180088e28;
    local_100 = (longlong *)(param_3 + 0x238);
LAB_180088e5c:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar6 = extraout_x1_06;
    }
    lVar11 = *(longlong *)(param_3 + 0x210);
    if (lVar11 != 0) goto LAB_180088e8c;
    local_f8 = (longlong *)(param_3 + 0x238);
LAB_180088ec4:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar6 = extraout_x1_07;
    }
    lVar11 = *(longlong *)(param_3 + 0x210);
    if (lVar11 != 0) goto LAB_180088ef4;
    local_f0 = (longlong *)(param_3 + 0x238);
LAB_180088f2c:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar6 = extraout_x1_08;
    }
    lVar11 = *(longlong *)(param_3 + 0x210);
    if (lVar11 != 0) goto LAB_180088f5c;
    local_e8 = (longlong *)(param_3 + 0x238);
LAB_180088f98:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar6 = extraout_x1_09;
    }
    lVar11 = *(longlong *)(param_3 + 0x210);
    if (lVar11 != 0) goto LAB_180088fc8;
    local_e0 = (longlong *)(param_3 + 0x238);
LAB_180089000:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar6 = extraout_x1_10;
    }
    lVar11 = *(longlong *)(param_3 + 0x210);
    if (lVar11 != 0) goto LAB_180089030;
    local_d8 = (longlong *)(param_3 + 0x238);
LAB_180089068:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar6 = extraout_x1_11;
    }
    lVar11 = *(longlong *)(param_3 + 0x210);
    if (lVar11 != 0) goto LAB_180089098;
    local_128 = (longlong *)(param_3 + 0x238);
LAB_1800890d0:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar6 = extraout_x1_12;
    }
    lVar11 = *(longlong *)(param_3 + 0x210);
    if (lVar11 != 0) goto LAB_180089100;
    local_130 = (longlong *)(param_3 + 0x238);
LAB_180089138:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar6 = extraout_x1_13;
    }
    lVar11 = *(longlong *)(param_3 + 0x210);
    if (lVar11 != 0) goto LAB_180089158;
    plVar17 = (longlong *)(param_3 + 0x238);
  }
  else {
LAB_180088e28:
    iVar20 = *(int *)(param_3 + 0x230);
    if (iVar20 == 0) {
      local_100 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar13 = 0;
      if (iVar20 < 1) {
        iVar13 = iVar20 + -1;
      }
      local_100 = (longlong *)(lVar11 + (longlong)iVar13 * 0x98);
    }
    if (lVar11 == 0) goto LAB_180088e5c;
LAB_180088e8c:
    iVar20 = *(int *)(param_3 + 0x230);
    if (iVar20 == 0) {
      local_f8 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar13 = 1;
      if (iVar20 < 2) {
        iVar13 = iVar20 + -1;
      }
      local_f8 = (longlong *)(lVar11 + (longlong)iVar13 * 0x98);
    }
    if (lVar11 == 0) goto LAB_180088ec4;
LAB_180088ef4:
    iVar20 = *(int *)(param_3 + 0x230);
    if (iVar20 == 0) {
      local_f0 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar13 = 2;
      if (iVar20 < 3) {
        iVar13 = iVar20 + -1;
      }
      local_f0 = (longlong *)(lVar11 + (longlong)iVar13 * 0x98);
    }
    if (lVar11 == 0) goto LAB_180088f2c;
LAB_180088f5c:
    iVar20 = *(int *)(param_3 + 0x230);
    if (iVar20 == 0) {
      local_e8 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar13 = 3;
      if (iVar20 < 4) {
        iVar13 = iVar20 + -1;
      }
      local_e8 = (longlong *)(lVar11 + (longlong)iVar13 * 0x98);
    }
    if (lVar11 == 0) goto LAB_180088f98;
LAB_180088fc8:
    iVar20 = *(int *)(param_3 + 0x230);
    if (iVar20 == 0) {
      local_e0 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar13 = 4;
      if (iVar20 < 5) {
        iVar13 = iVar20 + -1;
      }
      local_e0 = (longlong *)(lVar11 + (longlong)iVar13 * 0x98);
    }
    if (lVar11 == 0) goto LAB_180089000;
LAB_180089030:
    iVar20 = *(int *)(param_3 + 0x230);
    if (iVar20 == 0) {
      local_d8 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar13 = 5;
      if (iVar20 < 6) {
        iVar13 = iVar20 + -1;
      }
      local_d8 = (longlong *)(lVar11 + (longlong)iVar13 * 0x98);
    }
    if (lVar11 == 0) goto LAB_180089068;
LAB_180089098:
    iVar20 = *(int *)(param_3 + 0x230);
    if (iVar20 == 0) {
      local_128 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar13 = 6;
      if (iVar20 < 7) {
        iVar13 = iVar20 + -1;
      }
      local_128 = (longlong *)(lVar11 + (longlong)iVar13 * 0x98);
    }
    if (lVar11 == 0) goto LAB_1800890d0;
LAB_180089100:
    iVar20 = *(int *)(param_3 + 0x230);
    if (iVar20 == 0) {
      local_130 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar13 = 7;
      if (iVar20 < 8) {
        iVar13 = iVar20 + -1;
      }
      local_130 = (longlong *)(lVar11 + (longlong)iVar13 * 0x98);
    }
    if (lVar11 == 0) goto LAB_180089138;
LAB_180089158:
    iVar20 = *(int *)(param_3 + 0x230);
    if (iVar20 == 0) {
      plVar17 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar13 = 8;
      if (iVar20 < 9) {
        iVar13 = iVar20 + -1;
      }
      plVar17 = (longlong *)(lVar11 + (longlong)iVar13 * 0x98);
    }
  }
  if (*(int *)(param_3 + 0xac) != 0) {
    FUN_1800079f8((longlong *)(param_3 + 0x118),0x1800db668,0x12);
    lpMem = "";
    *(undefined4 *)(param_3 + 0x124) = 1;
    if ((*(char **)(param_3 + 0x338) != (char *)0x0) && (**(char **)(param_3 + 0x338) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0x338),0x1800d4ecd,0);
      *(undefined4 *)(param_3 + 0x344) = 1;
    }
    local_b0 = "";
    uStack_a8 = 0;
    local_a0 = &DAT_1800d4ecd;
    pvVar4 = GetProcessHeap();
    pcVar5 = (char *)HeapAlloc(pvVar4,0,0x15);
    if (pcVar5 == (char *)0x0) {
      local_b0 = "";
      pcVar21 = lpMem;
    }
    else {
      param_6 = 0x14;
      pcVar5[8] = '\0';
      pcVar5[9] = '\0';
      pcVar5[10] = '\0';
      pcVar5[0xb] = '\0';
      pcVar5[0xc] = '\0';
      pcVar5[0xd] = '\0';
      pcVar5[0xe] = '\0';
      pcVar5[0xf] = '\0';
      pcVar5[0] = '\0';
      pcVar5[1] = '\0';
      pcVar5[2] = '\0';
      pcVar5[3] = '\0';
      pcVar5[4] = '\0';
      pcVar5[5] = '\0';
      pcVar5[6] = '\0';
      pcVar5[7] = '\0';
      pcVar5[0x10] = '\0';
      pcVar5[0x11] = '\0';
      pcVar5[0x12] = '\0';
      pcVar5[0x13] = '\0';
      pcVar5[0x14] = '\0';
      local_b0 = pcVar5;
      FUN_180099d78(pcVar5,0x15,0x1800d6c40,0x14);
      uStack_a8 = 0x100000001;
      pcVar21 = pcVar5;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_b0);
    if ((pcVar5 != (char *)0x0) && (pcVar21 != (char *)0x0)) {
      pvVar4 = GetProcessHeap();
      HeapFree(pvVar4,0,pcVar21);
    }
    local_b0 = "";
    uStack_a8 = 0;
    local_a0 = &DAT_1800d4ecd;
    pvVar4 = GetProcessHeap();
    pcVar5 = (char *)HeapAlloc(pvVar4,0,0x8d);
    if (pcVar5 == (char *)0x0) {
      local_b0 = "";
      pcVar21 = lpMem;
    }
    else {
      param_6 = 0x8c;
      pcVar5[8] = '\0';
      pcVar5[9] = '\0';
      pcVar5[10] = '\0';
      pcVar5[0xb] = '\0';
      pcVar5[0xc] = '\0';
      pcVar5[0xd] = '\0';
      pcVar5[0xe] = '\0';
      pcVar5[0xf] = '\0';
      pcVar5[0] = '\0';
      pcVar5[1] = '\0';
      pcVar5[2] = '\0';
      pcVar5[3] = '\0';
      pcVar5[4] = '\0';
      pcVar5[5] = '\0';
      pcVar5[6] = '\0';
      pcVar5[7] = '\0';
      pcVar5[0x18] = '\0';
      pcVar5[0x19] = '\0';
      pcVar5[0x1a] = '\0';
      pcVar5[0x1b] = '\0';
      pcVar5[0x1c] = '\0';
      pcVar5[0x1d] = '\0';
      pcVar5[0x1e] = '\0';
      pcVar5[0x1f] = '\0';
      pcVar5[0x10] = '\0';
      pcVar5[0x11] = '\0';
      pcVar5[0x12] = '\0';
      pcVar5[0x13] = '\0';
      pcVar5[0x14] = '\0';
      pcVar5[0x15] = '\0';
      pcVar5[0x16] = '\0';
      pcVar5[0x17] = '\0';
      pcVar5[0x28] = '\0';
      pcVar5[0x29] = '\0';
      pcVar5[0x2a] = '\0';
      pcVar5[0x2b] = '\0';
      pcVar5[0x2c] = '\0';
      pcVar5[0x2d] = '\0';
      pcVar5[0x2e] = '\0';
      pcVar5[0x2f] = '\0';
      pcVar5[0x20] = '\0';
      pcVar5[0x21] = '\0';
      pcVar5[0x22] = '\0';
      pcVar5[0x23] = '\0';
      pcVar5[0x24] = '\0';
      pcVar5[0x25] = '\0';
      pcVar5[0x26] = '\0';
      pcVar5[0x27] = '\0';
      pcVar5[0x38] = '\0';
      pcVar5[0x39] = '\0';
      pcVar5[0x3a] = '\0';
      pcVar5[0x3b] = '\0';
      pcVar5[0x3c] = '\0';
      pcVar5[0x3d] = '\0';
      pcVar5[0x3e] = '\0';
      pcVar5[0x3f] = '\0';
      pcVar5[0x30] = '\0';
      pcVar5[0x31] = '\0';
      pcVar5[0x32] = '\0';
      pcVar5[0x33] = '\0';
      pcVar5[0x34] = '\0';
      pcVar5[0x35] = '\0';
      pcVar5[0x36] = '\0';
      pcVar5[0x37] = '\0';
      pcVar5[0x48] = '\0';
      pcVar5[0x49] = '\0';
      pcVar5[0x4a] = '\0';
      pcVar5[0x4b] = '\0';
      pcVar5[0x4c] = '\0';
      pcVar5[0x4d] = '\0';
      pcVar5[0x4e] = '\0';
      pcVar5[0x4f] = '\0';
      pcVar5[0x40] = '\0';
      pcVar5[0x41] = '\0';
      pcVar5[0x42] = '\0';
      pcVar5[0x43] = '\0';
      pcVar5[0x44] = '\0';
      pcVar5[0x45] = '\0';
      pcVar5[0x46] = '\0';
      pcVar5[0x47] = '\0';
      pcVar5[0x58] = '\0';
      pcVar5[0x59] = '\0';
      pcVar5[0x5a] = '\0';
      pcVar5[0x5b] = '\0';
      pcVar5[0x5c] = '\0';
      pcVar5[0x5d] = '\0';
      pcVar5[0x5e] = '\0';
      pcVar5[0x5f] = '\0';
      pcVar5[0x50] = '\0';
      pcVar5[0x51] = '\0';
      pcVar5[0x52] = '\0';
      pcVar5[0x53] = '\0';
      pcVar5[0x54] = '\0';
      pcVar5[0x55] = '\0';
      pcVar5[0x56] = '\0';
      pcVar5[0x57] = '\0';
      pcVar5[0x68] = '\0';
      pcVar5[0x69] = '\0';
      pcVar5[0x6a] = '\0';
      pcVar5[0x6b] = '\0';
      pcVar5[0x6c] = '\0';
      pcVar5[0x6d] = '\0';
      pcVar5[0x6e] = '\0';
      pcVar5[0x6f] = '\0';
      pcVar5[0x60] = '\0';
      pcVar5[0x61] = '\0';
      pcVar5[0x62] = '\0';
      pcVar5[99] = '\0';
      pcVar5[100] = '\0';
      pcVar5[0x65] = '\0';
      pcVar5[0x66] = '\0';
      pcVar5[0x67] = '\0';
      pcVar5[0x78] = '\0';
      pcVar5[0x79] = '\0';
      pcVar5[0x7a] = '\0';
      pcVar5[0x7b] = '\0';
      pcVar5[0x7c] = '\0';
      pcVar5[0x7d] = '\0';
      pcVar5[0x7e] = '\0';
      pcVar5[0x7f] = '\0';
      pcVar5[0x70] = '\0';
      pcVar5[0x71] = '\0';
      pcVar5[0x72] = '\0';
      pcVar5[0x73] = '\0';
      pcVar5[0x74] = '\0';
      pcVar5[0x75] = '\0';
      pcVar5[0x76] = '\0';
      pcVar5[0x77] = '\0';
      pcVar5[0x80] = '\0';
      pcVar5[0x81] = '\0';
      pcVar5[0x82] = '\0';
      pcVar5[0x83] = '\0';
      pcVar5[0x84] = '\0';
      pcVar5[0x85] = '\0';
      pcVar5[0x86] = '\0';
      pcVar5[0x87] = '\0';
      pcVar5[0x88] = '\0';
      pcVar5[0x89] = '\0';
      pcVar5[0x8a] = '\0';
      pcVar5[0x8b] = '\0';
      pcVar5[0x8c] = '\0';
      local_b0 = pcVar5;
      FUN_180099d78(pcVar5,0x8d,0x1800d6bb0,0x8c);
      uStack_a8 = 0x100000001;
      pcVar21 = pcVar5;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_b0);
    auVar22 = extraout_q0_00;
    if ((pcVar5 != (char *)0x0) && (pcVar21 != (char *)0x0)) {
      pvVar4 = GetProcessHeap();
      HeapFree(pvVar4,0,pcVar21);
      local_b0 = (char *)0x0;
      uStack_a8 = 0;
      auVar22 = extraout_q0_01;
    }
    pcVar5 = *(char **)(param_3 + 0x118);
    if (*(char **)(param_3 + 0x118) == (char *)0x0) {
      pcVar5 = lpMem;
    }
    FUN_180026368(auVar22,param_2,(undefined8 *)(param_3 + 0x338),0x1800d6c60,pcVar5,param_6,param_7
                  ,param_8,param_9,param_10);
    local_b0 = "";
    uStack_a8 = 0;
    local_a0 = &DAT_1800d4ecd;
    pvVar4 = GetProcessHeap();
    pcVar5 = (char *)HeapAlloc(pvVar4,0,7);
    if (pcVar5 == (char *)0x0) {
      local_b0 = "";
    }
    else {
      pcVar5[0] = '\0';
      pcVar5[1] = '\0';
      pcVar5[2] = '\0';
      pcVar5[3] = '\0';
      pcVar5[4] = '\0';
      pcVar5[5] = '\0';
      pcVar5[6] = '\0';
      local_b0 = pcVar5;
      FUN_180099d78(pcVar5,7,0x1800d6c58,6);
      uStack_a8 = 0x100000001;
      lpMem = pcVar5;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_b0);
    if ((pcVar5 != (char *)0x0) && (lpMem != (char *)0x0)) {
      pvVar4 = GetProcessHeap();
      HeapFree(pvVar4,0,lpMem);
      local_b0 = (char *)0x0;
      uStack_a8 = 0;
    }
    *(undefined4 *)(param_3 + 0x10) = 0;
    *(undefined4 *)(param_3 + 0x330) = 2;
    *(undefined4 *)(param_3 + 0x37c) = 1;
    *(undefined4 *)(param_3 + 0x38c) = 1;
    FUN_1800079f8(plVar19,0x1800db634,5);
    *(undefined4 *)((longlong)plVar19 + 0xc) = 1;
    *(undefined2 *)((longlong)plVar19 + 0x24) = 5;
    *(undefined4 *)(plVar19 + 3) = 0x808000;
    *(undefined2 *)(plVar19 + 5) = 1;
    *(undefined4 *)(plVar19 + 0x1b) = 1;
    FUN_1800079f8(local_118,0x1800db668,0x12);
    *(undefined4 *)((longlong)local_118 + 0xc) = 1;
    *(undefined2 *)((longlong)local_118 + 0x24) = 0x49;
    local_118[3] = 0x1621f000f0a60d;
    *(undefined4 *)(local_118 + 4) = 1;
    *(undefined2 *)(local_118 + 5) = 1;
    *(undefined4 *)(local_118 + 0x1b) = 1;
    FUN_1800079f8(local_100,0x1800db640,8);
    *(undefined4 *)((longlong)local_100 + 0xc) = 1;
    *(undefined4 *)((longlong)local_100 + 0x1c) = 0x32;
    *(undefined1 *)(local_100 + 3) = 0xb;
    FUN_1800079f8(local_f8,0x1800db650,0x10);
    *(undefined4 *)((longlong)local_f8 + 0xc) = 1;
    *(undefined1 *)(local_f8 + 3) = 0xb;
    *(undefined4 *)((longlong)local_f8 + 0x1c) = 0xb;
    FUN_1800079f8(local_f0,0x1800db6a0,0x10);
    *(undefined4 *)((longlong)local_f0 + 0xc) = 1;
    *(undefined1 *)(local_f0 + 3) = 0xb;
    *(undefined4 *)((longlong)local_f0 + 0x1c) = 1;
    FUN_1800079f8(local_e8,0x1800db6b8,0xe);
    *(undefined4 *)((longlong)local_e8 + 0xc) = 1;
    *(undefined4 *)((longlong)local_e8 + 0x1c) = 0x60b60;
    *(undefined1 *)(local_e8 + 3) = 0xe;
    FUN_1800079f8(local_e0,0x1800db680,0xe);
    *(undefined4 *)((longlong)local_e0 + 0xc) = 1;
    *(undefined1 *)(local_e0 + 3) = 0xe;
    *(undefined4 *)((longlong)local_e0 + 0x1c) = 0xa1196;
    FUN_1800079f8(local_d8,0x1800db690,0xe);
    *(undefined4 *)((longlong)local_d8 + 0xc) = 1;
    *(undefined1 *)(local_d8 + 3) = 0xe;
    *(undefined4 *)((longlong)local_d8 + 0x1c) = 0x1621f0;
    FUN_1800079f8(local_128,0x1800db6d8,0xe);
    *(undefined4 *)((longlong)local_128 + 0xc) = 1;
    *(undefined1 *)(local_128 + 3) = 0xe;
    *(undefined4 *)((longlong)local_128 + 0x1c) = 0xf0a60d;
    FUN_1800079f8(local_130,0x1800db6e8,0xe);
    *(undefined4 *)((longlong)local_130 + 0xc) = 1;
    *(undefined4 *)((longlong)local_130 + 0x1c) = 0xb2790a;
    *(undefined1 *)(local_130 + 3) = 0xe;
    FUN_1800079f8(plVar17,0x1800db6c8,0xe);
    *(undefined4 *)((longlong)plVar17 + 0xc) = 1;
    *(undefined4 *)((longlong)plVar17 + 0x1c) = 0x701c05;
    *(undefined1 *)(plVar17 + 3) = 0xe;
    return;
  }
  if (*(int *)(param_3 + 900) == 0) {
    uVar6 = FUN_1800254e8(auVar22,param_2,param_3,uVar6,param_5,param_6,param_7,param_8,param_9,
                          param_10);
    *piVar3 = (int)uVar6;
  }
  if (*piVar3 != 0) {
    return;
  }
  uVar14 = *(uint *)(param_3 + 900);
  lVar11 = *(longlong *)(param_3 + 0xab0);
  uVar15 = uVar14;
  if (lVar11 == 0) {
    if (*(code **)(param_3 + 0xac0) != (code *)0x0) {
      (**(code **)(param_3 + 0xac0))(*(undefined4 *)(param_3 + 0xac8));
      uVar15 = *(uint *)(param_3 + 900);
    }
    lVar11 = *(longlong *)(param_3 + 0xab0);
    if (lVar11 != 0) goto LAB_180089654;
    puVar9 = (undefined4 *)(param_3 + 0xad4);
  }
  else {
LAB_180089654:
    iVar20 = *(int *)(param_3 + 0xad0);
    if (iVar20 == 0) {
      puVar9 = (undefined4 *)(param_3 + 0xad4);
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar14) {
        uVar14 = iVar20 - 1;
      }
      puVar9 = (undefined4 *)(lVar11 + (longlong)(int)uVar14 * 4);
    }
  }
  uVar23 = *puVar9;
  lVar11 = plVar19[6];
  if (lVar11 == 0) {
    if ((code *)plVar19[8] != (code *)0x0) {
      (*(code *)plVar19[8])((int)plVar19[9]);
    }
    lVar11 = plVar19[6];
    if (lVar11 != 0) goto LAB_1800896a4;
    puVar9 = (undefined4 *)((longlong)plVar19 + 0x54);
  }
  else {
LAB_1800896a4:
    iVar20 = (int)plVar19[10];
    if (iVar20 == 0) {
      puVar9 = (undefined4 *)((longlong)plVar19 + 0x54);
    }
    else {
      uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar15) {
        uVar15 = iVar20 - 1;
      }
      puVar9 = (undefined4 *)(lVar11 + (longlong)(int)uVar15 * 4);
    }
  }
  *puVar9 = uVar23;
  uVar14 = *(uint *)(param_3 + 900);
  lVar11 = *(longlong *)(param_3 + 0xbf0);
  uVar15 = uVar14;
  if (lVar11 == 0) {
    if (*(code **)(param_3 + 0xc00) != (code *)0x0) {
      (**(code **)(param_3 + 0xc00))(*(undefined4 *)(param_3 + 0xc08));
      uVar15 = *(uint *)(param_3 + 900);
    }
    lVar11 = *(longlong *)(param_3 + 0xbf0);
    if (lVar11 != 0) goto LAB_180089704;
    pfVar8 = (float *)(param_3 + 0xc14);
  }
  else {
LAB_180089704:
    iVar20 = *(int *)(param_3 + 0xc10);
    if (iVar20 == 0) {
      pfVar8 = (float *)(param_3 + 0xc14);
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar14) {
        uVar14 = iVar20 - 1;
      }
      pfVar8 = (float *)(lVar11 + (longlong)(int)uVar14 * 4);
    }
  }
  fVar26 = *pfVar8;
  lVar11 = *(longlong *)(param_3 + 0xbc8);
  uVar14 = uVar15;
  if (lVar11 == 0) {
    if (*(code **)(param_3 + 0xbd8) != (code *)0x0) {
      (**(code **)(param_3 + 0xbd8))(*(undefined4 *)(param_3 + 0xbe0));
      uVar14 = *(uint *)(param_3 + 900);
    }
    lVar11 = *(longlong *)(param_3 + 0xbc8);
    if (lVar11 != 0) goto LAB_180089760;
    pfVar8 = (float *)(param_3 + 0xbec);
  }
  else {
LAB_180089760:
    iVar20 = *(int *)(param_3 + 0xbe8);
    if (iVar20 == 0) {
      pfVar8 = (float *)(param_3 + 0xbec);
    }
    else {
      uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar15) {
        uVar15 = iVar20 - 1;
      }
      pfVar8 = (float *)(lVar11 + (longlong)(int)uVar15 * 4);
    }
  }
  fVar25 = *pfVar8;
  lVar11 = local_118[0x10];
  fVar24 = fVar25 + fVar26;
  if (lVar11 == 0) {
    if ((code *)local_118[0x12] != (code *)0x0) {
      (*(code *)local_118[0x12])((int)local_118[0x13]);
      uVar14 = *(uint *)(param_3 + 900);
    }
    lVar11 = local_118[0x10];
    if (lVar11 != 0) goto LAB_1800897c0;
    plVar19 = local_118 + 0x15;
  }
  else {
LAB_1800897c0:
    iVar20 = (int)local_118[0x14];
    if (iVar20 == 0) {
      plVar19 = local_118 + 0x15;
    }
    else {
      iVar13 = 1;
      if (iVar20 < 2) {
        iVar13 = iVar20 + -1;
      }
      plVar19 = (longlong *)(lVar11 + (longlong)iVar13 * 0x28);
    }
  }
  (**(code **)(param_3 + 0x3a8))(param_3 + 0xad8,plVar19,uVar14,0x32);
  lVar11 = local_118[0x10];
  if (lVar11 == 0) {
    if ((code *)local_118[0x12] != (code *)0x0) {
      (*(code *)local_118[0x12])((int)local_118[0x13]);
    }
    lVar11 = local_118[0x10];
    if (lVar11 != 0) goto LAB_18008981c;
    plVar19 = local_118 + 0x15;
  }
  else {
LAB_18008981c:
    iVar20 = (int)local_118[0x14];
    if (iVar20 == 0) {
      plVar19 = local_118 + 0x15;
    }
    else {
      iVar13 = 2;
      if (iVar20 < 3) {
        iVar13 = iVar20 + -1;
      }
      plVar19 = (longlong *)(lVar11 + (longlong)iVar13 * 0x28);
    }
  }
  (**(code **)(param_3 + 0x3c0))(param_3 + 0xad8,plVar19,*(undefined4 *)(param_3 + 900),0x32);
  lVar11 = local_118[0x10];
  if (lVar11 == 0) {
    if ((code *)local_118[0x12] != (code *)0x0) {
      (*(code *)local_118[0x12])((int)local_118[0x13]);
    }
    lVar11 = local_118[0x10];
    if (lVar11 != 0) goto LAB_18008988c;
    uVar15 = *(uint *)(param_3 + 900);
    plVar19 = local_118 + 0x15;
LAB_1800898c4:
    uVar14 = uVar15;
    if ((code *)local_118[0x12] != (code *)0x0) {
      (*(code *)local_118[0x12])((int)local_118[0x13]);
      uVar14 = *(uint *)(param_3 + 900);
    }
    lVar11 = local_118[0x10];
    if (lVar11 != 0) goto LAB_1800898ec;
    plVar18 = local_118 + 0x15;
  }
  else {
LAB_18008988c:
    iVar20 = (int)local_118[0x14];
    if (iVar20 == 0) {
      plVar19 = local_118 + 0x15;
    }
    else {
      iVar13 = 1;
      if (iVar20 < 2) {
        iVar13 = iVar20 + -1;
      }
      plVar19 = (longlong *)(lVar11 + (longlong)iVar13 * 0x28);
    }
    uVar15 = *(uint *)(param_3 + 900);
    uVar14 = uVar15;
    if (lVar11 == 0) goto LAB_1800898c4;
LAB_1800898ec:
    iVar20 = (int)local_118[0x14];
    if (iVar20 == 0) {
      plVar18 = local_118 + 0x15;
    }
    else {
      iVar13 = 2;
      if (iVar20 < 3) {
        iVar13 = iVar20 + -1;
      }
      plVar18 = (longlong *)(lVar11 + (longlong)iVar13 * 0x28);
    }
  }
  lVar11 = *plVar18;
  if (lVar11 == 0) {
    if ((code *)plVar18[2] != (code *)0x0) {
      (*(code *)plVar18[2])((int)plVar18[3]);
    }
    lVar11 = *plVar18;
    if (lVar11 != 0) goto LAB_180089938;
    pfVar8 = (float *)((longlong)plVar18 + 0x24);
  }
  else {
LAB_180089938:
    iVar20 = (int)plVar18[4];
    if (iVar20 == 0) {
      pfVar8 = (float *)((longlong)plVar18 + 0x24);
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar14) {
        uVar14 = iVar20 - 1;
      }
      pfVar8 = (float *)(lVar11 + (longlong)(int)uVar14 * 4);
    }
  }
  lVar11 = *plVar19;
  if (lVar11 == 0) {
    if ((code *)plVar19[2] != (code *)0x0) {
      (*(code *)plVar19[2])((int)plVar19[3]);
    }
    lVar11 = *plVar19;
    if (lVar11 != 0) goto LAB_180089984;
    pfVar12 = (float *)((longlong)plVar19 + 0x24);
  }
  else {
LAB_180089984:
    iVar20 = (int)plVar19[4];
    if (iVar20 == 0) {
      pfVar12 = (float *)((longlong)plVar19 + 0x24);
    }
    else {
      uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar15) {
        uVar15 = iVar20 - 1;
      }
      pfVar12 = (float *)(lVar11 + (longlong)(int)uVar15 * 4);
    }
  }
  fVar27 = *pfVar12;
  fVar1 = *pfVar8;
  lVar11 = local_118[0x10];
  if (lVar11 == 0) {
    if ((code *)local_118[0x12] != (code *)0x0) {
      (*(code *)local_118[0x12])((int)local_118[0x13]);
    }
    lVar11 = local_118[0x10];
    if (lVar11 != 0) goto LAB_1800899dc;
    plVar19 = local_118 + 0x15;
  }
  else {
LAB_1800899dc:
    iVar20 = (int)local_118[0x14];
    if (iVar20 == 0) {
      plVar19 = local_118 + 0x15;
    }
    else {
      iVar13 = 3;
      if (iVar20 < 4) {
        iVar13 = iVar20 + -1;
      }
      plVar19 = (longlong *)(lVar11 + (longlong)iVar13 * 0x28);
    }
  }
  uVar14 = *(uint *)(param_3 + 900);
  lVar11 = *plVar19;
  if (lVar11 == 0) {
    if ((code *)plVar19[2] != (code *)0x0) {
      (*(code *)plVar19[2])((int)plVar19[3]);
    }
    lVar11 = *plVar19;
    if (lVar11 != 0) goto LAB_180089a2c;
    pfVar8 = (float *)((longlong)plVar19 + 0x24);
  }
  else {
LAB_180089a2c:
    iVar20 = (int)plVar19[4];
    if (iVar20 == 0) {
      pfVar8 = (float *)((longlong)plVar19 + 0x24);
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar14) {
        uVar14 = iVar20 - 1;
      }
      pfVar8 = (float *)(lVar11 + (longlong)(int)uVar14 * 4);
    }
  }
  *pfVar8 = fVar27 + fVar1;
  lVar11 = local_118[0x10];
  if (lVar11 == 0) {
    if ((code *)local_118[0x12] != (code *)0x0) {
      (*(code *)local_118[0x12])((int)local_118[0x13]);
    }
    lVar11 = local_118[0x10];
    if (lVar11 != 0) goto LAB_180089a8c;
    uVar15 = *(uint *)(param_3 + 900);
    plVar19 = local_118 + 0x15;
LAB_180089ac4:
    uVar14 = uVar15;
    if ((code *)local_118[0x12] != (code *)0x0) {
      (*(code *)local_118[0x12])((int)local_118[0x13]);
      uVar14 = *(uint *)(param_3 + 900);
    }
    lVar11 = local_118[0x10];
    if (lVar11 != 0) goto LAB_180089aec;
    plVar18 = local_118 + 0x15;
  }
  else {
LAB_180089a8c:
    iVar20 = (int)local_118[0x14];
    if (iVar20 == 0) {
      plVar19 = local_118 + 0x15;
    }
    else {
      iVar13 = 1;
      if (iVar20 < 2) {
        iVar13 = iVar20 + -1;
      }
      plVar19 = (longlong *)(lVar11 + (longlong)iVar13 * 0x28);
    }
    uVar15 = *(uint *)(param_3 + 900);
    uVar14 = uVar15;
    if (lVar11 == 0) goto LAB_180089ac4;
LAB_180089aec:
    iVar20 = (int)local_118[0x14];
    if (iVar20 == 0) {
      plVar18 = local_118 + 0x15;
    }
    else {
      iVar13 = 2;
      if (iVar20 < 3) {
        iVar13 = iVar20 + -1;
      }
      plVar18 = (longlong *)(lVar11 + (longlong)iVar13 * 0x28);
    }
  }
  lVar11 = *plVar18;
  if (lVar11 == 0) {
    if ((code *)plVar18[2] != (code *)0x0) {
      (*(code *)plVar18[2])((int)plVar18[3]);
    }
    lVar11 = *plVar18;
    if (lVar11 != 0) goto LAB_180089b38;
    pfVar8 = (float *)((longlong)plVar18 + 0x24);
  }
  else {
LAB_180089b38:
    iVar20 = (int)plVar18[4];
    if (iVar20 == 0) {
      pfVar8 = (float *)((longlong)plVar18 + 0x24);
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar14) {
        uVar14 = iVar20 - 1;
      }
      pfVar8 = (float *)(lVar11 + (longlong)(int)uVar14 * 4);
    }
  }
  lVar11 = *plVar19;
  if (lVar11 == 0) {
    if ((code *)plVar19[2] != (code *)0x0) {
      (*(code *)plVar19[2])((int)plVar19[3]);
    }
    lVar11 = *plVar19;
    if (lVar11 != 0) goto LAB_180089b84;
    pfVar12 = (float *)((longlong)plVar19 + 0x24);
  }
  else {
LAB_180089b84:
    iVar20 = (int)plVar19[4];
    if (iVar20 == 0) {
      pfVar12 = (float *)((longlong)plVar19 + 0x24);
    }
    else {
      uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar15) {
        uVar15 = iVar20 - 1;
      }
      pfVar12 = (float *)(lVar11 + (longlong)(int)uVar15 * 4);
    }
  }
  fVar27 = *pfVar8;
  fVar1 = *pfVar12;
  lVar11 = local_118[0x10];
  if (lVar11 == 0) {
    if ((code *)local_118[0x12] != (code *)0x0) {
      (*(code *)local_118[0x12])((int)local_118[0x13]);
    }
    lVar11 = local_118[0x10];
    if (lVar11 != 0) goto LAB_180089be4;
    plVar19 = local_118 + 0x15;
  }
  else {
LAB_180089be4:
    iVar20 = (int)local_118[0x14];
    if (iVar20 == 0) {
      plVar19 = local_118 + 0x15;
    }
    else {
      iVar13 = 4;
      if (iVar20 < 5) {
        iVar13 = iVar20 + -1;
      }
      plVar19 = (longlong *)(lVar11 + (longlong)iVar13 * 0x28);
    }
  }
  uVar14 = *(uint *)(param_3 + 900);
  lVar11 = *plVar19;
  if (lVar11 == 0) {
    if ((code *)plVar19[2] != (code *)0x0) {
      (*(code *)plVar19[2])((int)plVar19[3]);
    }
    lVar11 = *plVar19;
    if (lVar11 != 0) goto LAB_180089c34;
    pfVar8 = (float *)((longlong)plVar19 + 0x24);
  }
  else {
LAB_180089c34:
    iVar20 = (int)plVar19[4];
    if (iVar20 == 0) {
      pfVar8 = (float *)((longlong)plVar19 + 0x24);
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar14) {
        uVar14 = iVar20 - 1;
      }
      pfVar8 = (float *)(lVar11 + (longlong)(int)uVar14 * 4);
    }
  }
  *pfVar8 = fVar27 * 3.0 + fVar1;
  lVar11 = local_118[0x10];
  if (lVar11 == 0) {
    if ((code *)local_118[0x12] != (code *)0x0) {
      (*(code *)local_118[0x12])((int)local_118[0x13]);
    }
    lVar11 = local_118[0x10];
    if (lVar11 != 0) goto LAB_180089c94;
    uVar15 = *(uint *)(param_3 + 900);
    plVar19 = local_118 + 0x15;
LAB_180089ccc:
    uVar14 = uVar15;
    if ((code *)local_118[0x12] != (code *)0x0) {
      (*(code *)local_118[0x12])((int)local_118[0x13]);
      uVar14 = *(uint *)(param_3 + 900);
    }
    lVar11 = local_118[0x10];
    if (lVar11 != 0) goto LAB_180089cf4;
    plVar18 = local_118 + 0x15;
  }
  else {
LAB_180089c94:
    iVar20 = (int)local_118[0x14];
    if (iVar20 == 0) {
      plVar19 = local_118 + 0x15;
    }
    else {
      iVar13 = 1;
      if (iVar20 < 2) {
        iVar13 = iVar20 + -1;
      }
      plVar19 = (longlong *)(lVar11 + (longlong)iVar13 * 0x28);
    }
    uVar15 = *(uint *)(param_3 + 900);
    uVar14 = uVar15;
    if (lVar11 == 0) goto LAB_180089ccc;
LAB_180089cf4:
    iVar20 = (int)local_118[0x14];
    if (iVar20 == 0) {
      plVar18 = local_118 + 0x15;
    }
    else {
      iVar13 = 2;
      if (iVar20 < 3) {
        iVar13 = iVar20 + -1;
      }
      plVar18 = (longlong *)(lVar11 + (longlong)iVar13 * 0x28);
    }
  }
  lVar11 = *plVar18;
  if (lVar11 == 0) {
    if ((code *)plVar18[2] != (code *)0x0) {
      (*(code *)plVar18[2])((int)plVar18[3]);
    }
    lVar11 = *plVar18;
    if (lVar11 != 0) goto LAB_180089d40;
    pfVar8 = (float *)((longlong)plVar18 + 0x24);
  }
  else {
LAB_180089d40:
    iVar20 = (int)plVar18[4];
    if (iVar20 == 0) {
      pfVar8 = (float *)((longlong)plVar18 + 0x24);
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar14) {
        uVar14 = iVar20 - 1;
      }
      pfVar8 = (float *)(lVar11 + (longlong)(int)uVar14 * 4);
    }
  }
  lVar11 = *plVar19;
  if (lVar11 == 0) {
    if ((code *)plVar19[2] != (code *)0x0) {
      (*(code *)plVar19[2])((int)plVar19[3]);
    }
    lVar11 = *plVar19;
    if (lVar11 != 0) goto LAB_180089d8c;
    pfVar12 = (float *)((longlong)plVar19 + 0x24);
  }
  else {
LAB_180089d8c:
    iVar20 = (int)plVar19[4];
    if (iVar20 == 0) {
      pfVar12 = (float *)((longlong)plVar19 + 0x24);
    }
    else {
      uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar15) {
        uVar15 = iVar20 - 1;
      }
      pfVar12 = (float *)(lVar11 + (longlong)(int)uVar15 * 4);
    }
  }
  fVar27 = *pfVar8;
  fVar1 = *pfVar12;
  lVar11 = local_118[0x10];
  if (lVar11 == 0) {
    if ((code *)local_118[0x12] != (code *)0x0) {
      (*(code *)local_118[0x12])((int)local_118[0x13]);
    }
    lVar11 = local_118[0x10];
    if (lVar11 != 0) goto LAB_180089dec;
    plVar19 = local_118 + 0x15;
  }
  else {
LAB_180089dec:
    iVar20 = (int)local_118[0x14];
    if (iVar20 == 0) {
      plVar19 = local_118 + 0x15;
    }
    else {
      iVar13 = 5;
      if (iVar20 < 6) {
        iVar13 = iVar20 + -1;
      }
      plVar19 = (longlong *)(lVar11 + (longlong)iVar13 * 0x28);
    }
  }
  uVar14 = *(uint *)(param_3 + 900);
  lVar11 = *plVar19;
  if (lVar11 == 0) {
    if ((code *)plVar19[2] != (code *)0x0) {
      (*(code *)plVar19[2])((int)plVar19[3]);
    }
    lVar11 = *plVar19;
    if (lVar11 != 0) goto LAB_180089e3c;
    pfVar8 = (float *)((longlong)plVar19 + 0x24);
  }
  else {
LAB_180089e3c:
    iVar20 = (int)plVar19[4];
    if (iVar20 == 0) {
      pfVar8 = (float *)((longlong)plVar19 + 0x24);
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar14) {
        uVar14 = iVar20 - 1;
      }
      pfVar8 = (float *)(lVar11 + (longlong)(int)uVar14 * 4);
    }
  }
  *pfVar8 = fVar27 * 5.0 + fVar1;
  lVar11 = local_118[0x10];
  if (lVar11 == 0) {
    if ((code *)local_118[0x12] != (code *)0x0) {
      (*(code *)local_118[0x12])((int)local_118[0x13]);
    }
    lVar11 = local_118[0x10];
    if (lVar11 != 0) goto LAB_180089e8c;
    plVar19 = local_118 + 0x15;
  }
  else {
LAB_180089e8c:
    iVar20 = (int)local_118[0x14];
    if (iVar20 == 0) {
      plVar19 = local_118 + 0x15;
    }
    else {
      iVar13 = 3;
      if (iVar20 < 4) {
        iVar13 = iVar20 + -1;
      }
      plVar19 = (longlong *)(lVar11 + (longlong)iVar13 * 0x28);
    }
  }
  uVar14 = *(uint *)(param_3 + 900);
  lVar11 = *plVar19;
  uVar15 = uVar14;
  if (lVar11 == 0) {
    if ((code *)plVar19[2] != (code *)0x0) {
      (*(code *)plVar19[2])((int)plVar19[3]);
      uVar15 = *(uint *)(param_3 + 900);
    }
    lVar11 = *plVar19;
    if (lVar11 != 0) goto LAB_180089ef0;
    pfVar8 = (float *)((longlong)plVar19 + 0x24);
  }
  else {
LAB_180089ef0:
    iVar20 = (int)plVar19[4];
    if (iVar20 == 0) {
      pfVar8 = (float *)((longlong)plVar19 + 0x24);
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar14) {
        uVar14 = iVar20 - 1;
      }
      pfVar8 = (float *)(lVar11 + (longlong)(int)uVar14 * 4);
    }
  }
  uVar14 = uVar15;
  if (*pfVar8 <= fVar24) {
    lVar11 = local_118[0x10];
    if (lVar11 == 0) {
      if ((code *)local_118[0x12] != (code *)0x0) {
        (*(code *)local_118[0x12])((int)local_118[0x13]);
        uVar15 = *(uint *)(param_3 + 900);
      }
      lVar11 = local_118[0x10];
      if (lVar11 != 0) goto LAB_180089f54;
      plVar19 = local_118 + 0x15;
    }
    else {
LAB_180089f54:
      iVar20 = (int)local_118[0x14];
      if (iVar20 == 0) {
        plVar19 = local_118 + 0x15;
      }
      else {
        iVar13 = 3;
        if (iVar20 < 4) {
          iVar13 = iVar20 + -1;
        }
        plVar19 = (longlong *)(lVar11 + (longlong)iVar13 * 0x28);
      }
    }
    lVar11 = *plVar19;
    uVar14 = uVar15;
    if (lVar11 == 0) {
      if ((code *)plVar19[2] != (code *)0x0) {
        (*(code *)plVar19[2])((int)plVar19[3]);
        uVar14 = *(uint *)(param_3 + 900);
      }
      lVar11 = *plVar19;
      if (lVar11 != 0) goto LAB_180089fac;
      pfVar8 = (float *)((longlong)plVar19 + 0x24);
    }
    else {
LAB_180089fac:
      iVar20 = (int)plVar19[4];
      if (iVar20 == 0) {
        pfVar8 = (float *)((longlong)plVar19 + 0x24);
      }
      else {
        uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
        if (iVar20 <= (int)uVar15) {
          uVar15 = iVar20 - 1;
        }
        pfVar8 = (float *)(lVar11 + (longlong)(int)uVar15 * 4);
      }
    }
    fVar24 = *pfVar8;
  }
  lVar11 = *(longlong *)(local_168 + 0x30);
  if (lVar11 == 0) {
    if (*(code **)(local_168 + 0x40) != (code *)0x0) {
      (**(code **)(local_168 + 0x40))(*(undefined4 *)(local_168 + 0x48));
    }
    lVar11 = *(longlong *)(local_168 + 0x30);
    if (lVar11 != 0) goto LAB_18008a004;
    pfVar8 = (float *)(local_168 + 0x54);
  }
  else {
LAB_18008a004:
    iVar20 = *(int *)(local_168 + 0x50);
    if (iVar20 == 0) {
      pfVar8 = (float *)(local_168 + 0x54);
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar14) {
        uVar14 = iVar20 - 1;
      }
      pfVar8 = (float *)(lVar11 + (longlong)(int)uVar14 * 4);
    }
  }
  *pfVar8 = fVar24;
  lVar11 = local_118[0xb];
  uVar14 = *(uint *)(param_3 + 900);
  if (fVar26 < fVar25) {
    uVar23 = *(undefined4 *)((longlong)local_118 + 0x1c);
  }
  else {
    uVar23 = (undefined4)local_118[3];
  }
  if (lVar11 == 0) {
    if ((code *)local_118[0xd] != (code *)0x0) {
      (*(code *)local_118[0xd])((int)local_118[0xe]);
    }
    lVar11 = local_118[0xb];
    if (lVar11 != 0) goto LAB_18008a078;
    puVar9 = (undefined4 *)((longlong)local_118 + 0x7c);
  }
  else {
LAB_18008a078:
    iVar20 = (int)local_118[0xf];
    if (iVar20 == 0) {
      puVar9 = (undefined4 *)((longlong)local_118 + 0x7c);
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar14) {
        uVar14 = iVar20 - 1;
      }
      puVar9 = (undefined4 *)(lVar11 + (longlong)(int)uVar14 * 4);
    }
  }
  *puVar9 = uVar23;
  uVar7 = FUN_180026708((longlong)local_100);
  (**(code **)(param_3 + 0x3d0))
            (local_168 + 0x30,local_160 + 0x30,*(undefined4 *)(param_3 + 900),uVar7 & 0xffffffff);
  uVar7 = FUN_180026708((longlong)local_100);
  (**(code **)(param_3 + 0x3d8))
            (local_168 + 0x30,lVar10 + 0x30,*(undefined4 *)(param_3 + 900),uVar7 & 0xffffffff);
  FUN_180026708((longlong)local_f0);
  FUN_180026708((longlong)local_f8);
  if ((*(longlong *)(local_168 + 0x30) == 0) && (*(code **)(local_168 + 0x40) != (code *)0x0)) {
    (**(code **)(local_168 + 0x40))(*(undefined4 *)(local_168 + 0x48));
  }
  if ((*(longlong *)(local_160 + 0x30) == 0) && (*(code **)(local_160 + 0x40) != (code *)0x0)) {
    (**(code **)(local_160 + 0x40))(*(undefined4 *)(local_160 + 0x48));
  }
  uVar7 = FUN_180026708((longlong)local_f0);
  fVar24 = (float)(int)uVar7 + extraout_s18;
  iVar20 = (int)fVar24;
  bVar2 = 0.0 <= fVar24;
  if (fVar24 <= 0.0) {
LAB_18008a210:
    if ((!bVar2) && (fVar24 - (float)iVar20 <= -0.5)) {
      iVar20 = iVar20 + -1;
    }
  }
  else {
    if (fVar24 - (float)iVar20 < 0.5) {
      bVar2 = true;
      if (!NAN(fVar24)) {
        bVar2 = 0.0 <= fVar24;
      }
      goto LAB_18008a210;
    }
    iVar20 = iVar20 + 1;
  }
  local_a0 = &DAT_1800d4ecd;
  local_b0 = "";
  uStack_a8 = 0;
  lVar10 = local_118[0x10];
  if (lVar10 == 0) {
    if ((code *)local_118[0x12] != (code *)0x0) {
      (*(code *)local_118[0x12])((int)local_118[0x13]);
    }
    lVar10 = local_118[0x10];
    if (lVar10 != 0) goto LAB_18008a26c;
    plVar19 = local_118 + 0x15;
  }
  else {
LAB_18008a26c:
    iVar13 = (int)local_118[0x14];
    if (iVar13 == 0) {
      plVar19 = local_118 + 0x15;
    }
    else {
      iVar16 = 5;
      if (iVar13 < 6) {
        iVar16 = iVar13 + -1;
      }
      plVar19 = (longlong *)(lVar10 + (longlong)iVar16 * 0x28);
    }
  }
  uVar14 = *(uint *)(param_3 + 900);
  lVar10 = *plVar19;
  uVar15 = uVar14;
  if (lVar10 == 0) {
    if ((code *)plVar19[2] != (code *)0x0) {
      (*(code *)plVar19[2])((int)plVar19[3]);
      uVar15 = *(uint *)(param_3 + 900);
    }
    lVar10 = *plVar19;
    if (lVar10 != 0) goto LAB_18008a2c8;
    pfVar8 = (float *)((longlong)plVar19 + 0x24);
  }
  else {
LAB_18008a2c8:
    iVar13 = (int)plVar19[4];
    if (iVar13 == 0) {
      pfVar8 = (float *)((longlong)plVar19 + 0x24);
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar13 <= (int)uVar14) {
        uVar14 = iVar13 - 1;
      }
      pfVar8 = (float *)(lVar10 + (longlong)(int)uVar14 * 4);
    }
  }
  lVar10 = *(longlong *)(param_3 + 0xad8);
  if (lVar10 == 0) {
    if (*(code **)(param_3 + 0xae8) != (code *)0x0) {
      (**(code **)(param_3 + 0xae8))(*(undefined4 *)(param_3 + 0xaf0));
    }
    lVar10 = *(longlong *)(param_3 + 0xad8);
    if (lVar10 != 0) goto LAB_18008a314;
    pfVar12 = (float *)(param_3 + 0xafc);
  }
  else {
LAB_18008a314:
    iVar13 = *(int *)(param_3 + 0xaf8);
    if (iVar13 == 0) {
      pfVar12 = (float *)(param_3 + 0xafc);
    }
    else {
      uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
      if (iVar13 <= (int)uVar15) {
        uVar15 = iVar13 - 1;
      }
      pfVar12 = (float *)(lVar10 + (longlong)(int)uVar15 * 4);
    }
  }
  if (*pfVar12 <= *pfVar8) {
    lVar10 = local_118[0x10];
    if (lVar10 == 0) {
      if ((code *)local_118[0x12] != (code *)0x0) {
        (*(code *)local_118[0x12])((int)local_118[0x13]);
      }
      lVar10 = local_118[0x10];
      if (lVar10 != 0) goto LAB_18008a3ec;
      plVar19 = local_118 + 0x15;
    }
    else {
LAB_18008a3ec:
      iVar13 = (int)local_118[0x14];
      if (iVar13 == 0) {
        plVar19 = local_118 + 0x15;
      }
      else {
        iVar16 = 4;
        if (iVar13 < 5) {
          iVar16 = iVar13 + -1;
        }
        plVar19 = (longlong *)(lVar10 + (longlong)iVar16 * 0x28);
      }
    }
    uVar14 = *(uint *)(param_3 + 900);
    lVar10 = *plVar19;
    uVar15 = uVar14;
    if (lVar10 == 0) {
      if ((code *)plVar19[2] != (code *)0x0) {
        (*(code *)plVar19[2])((int)plVar19[3]);
        uVar15 = *(uint *)(param_3 + 900);
      }
      lVar10 = *plVar19;
      if (lVar10 != 0) goto LAB_18008a448;
      pfVar8 = (float *)((longlong)plVar19 + 0x24);
    }
    else {
LAB_18008a448:
      iVar13 = (int)plVar19[4];
      if (iVar13 == 0) {
        pfVar8 = (float *)((longlong)plVar19 + 0x24);
      }
      else {
        uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
        if (iVar13 <= (int)uVar14) {
          uVar14 = iVar13 - 1;
        }
        pfVar8 = (float *)(lVar10 + (longlong)(int)uVar14 * 4);
      }
    }
    lVar10 = *(longlong *)(param_3 + 0xad8);
    if (lVar10 == 0) {
      if (*(code **)(param_3 + 0xae8) != (code *)0x0) {
        (**(code **)(param_3 + 0xae8))(*(undefined4 *)(param_3 + 0xaf0));
      }
      lVar10 = *(longlong *)(param_3 + 0xad8);
      if (lVar10 != 0) goto LAB_18008a494;
      pfVar12 = (float *)(param_3 + 0xafc);
    }
    else {
LAB_18008a494:
      iVar13 = *(int *)(param_3 + 0xaf8);
      if (iVar13 == 0) {
        pfVar12 = (float *)(param_3 + 0xafc);
      }
      else {
        uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
        if (iVar13 <= (int)uVar15) {
          uVar15 = iVar13 - 1;
        }
        pfVar12 = (float *)(lVar10 + (longlong)(int)uVar15 * 4);
      }
    }
    if (*pfVar12 <= *pfVar8) {
      lVar10 = local_118[0x10];
      if (lVar10 == 0) {
        if ((code *)local_118[0x12] != (code *)0x0) {
          (*(code *)local_118[0x12])((int)local_118[0x13]);
        }
        lVar10 = local_118[0x10];
        if (lVar10 != 0) goto LAB_18008a4fc;
        plVar19 = local_118 + 0x15;
      }
      else {
LAB_18008a4fc:
        iVar13 = (int)local_118[0x14];
        if (iVar13 == 0) {
          plVar19 = local_118 + 0x15;
        }
        else {
          iVar16 = 3;
          if (iVar13 < 4) {
            iVar16 = iVar13 + -1;
          }
          plVar19 = (longlong *)(lVar10 + (longlong)iVar16 * 0x28);
        }
      }
      iVar13 = *(int *)(param_3 + 900);
      pfVar8 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),iVar13);
      fVar24 = *pfVar8;
      pfVar8 = (float *)FUN_180005d08(plVar19,iVar13);
      if (fVar24 <= *pfVar8) goto LAB_18008a574;
      iVar20 = iVar20 << 1;
      local_130 = local_128;
      local_e0 = local_d8;
    }
    else {
      iVar20 = iVar20 * 3;
    }
    if (fVar26 < fVar25) {
      local_130 = local_e0;
    }
    uVar23 = FUN_180026888((longlong)local_130);
    puVar9 = (undefined4 *)FUN_180005d08(local_118 + 0xb,*(int *)(param_3 + 900));
    *puVar9 = uVar23;
  }
  else {
    iVar20 = iVar20 << 2;
    if (fVar26 < fVar25) {
      plVar17 = local_e8;
    }
    uVar23 = FUN_180026888((longlong)plVar17);
    uVar14 = *(uint *)(param_3 + 900);
    lVar10 = local_118[0xb];
    if (lVar10 == 0) {
      if ((code *)local_118[0xd] != (code *)0x0) {
        (*(code *)local_118[0xd])((int)local_118[0xe]);
      }
      lVar10 = local_118[0xb];
      if (lVar10 == 0) {
        *(undefined4 *)((longlong)local_118 + 0x7c) = uVar23;
        goto LAB_18008a574;
      }
    }
    iVar13 = (int)local_118[0xf];
    if (iVar13 == 0) {
      *(undefined4 *)((longlong)local_118 + 0x7c) = uVar23;
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar13 <= (int)uVar14) {
        uVar14 = iVar13 - 1;
      }
      *(undefined4 *)(lVar10 + (longlong)(int)uVar14 * 4) = uVar23;
    }
  }
LAB_18008a574:
  lVar10 = local_118[0x10];
  if (lVar10 == 0) {
    if ((code *)local_118[0x12] != (code *)0x0) {
      (*(code *)local_118[0x12])((int)local_118[0x13]);
    }
    lVar10 = local_118[0x10];
    if (lVar10 != 0) goto LAB_18008a59c;
    plVar19 = local_118 + 0x15;
  }
  else {
LAB_18008a59c:
    iVar13 = (int)local_118[0x14];
    if (iVar13 == 0) {
      plVar19 = local_118 + 0x15;
    }
    else {
      iVar16 = 0;
      if (iVar13 < 1) {
        iVar16 = iVar13 + -1;
      }
      plVar19 = (longlong *)(lVar10 + (longlong)iVar16 * 0x28);
    }
  }
  uVar14 = *(uint *)(param_3 + 900);
  lVar10 = *plVar19;
  if (lVar10 == 0) {
    if ((code *)plVar19[2] != (code *)0x0) {
      (*(code *)plVar19[2])((int)plVar19[3]);
    }
    lVar10 = *plVar19;
    if (lVar10 != 0) goto LAB_18008a5e8;
    pfVar8 = (float *)((longlong)plVar19 + 0x24);
  }
  else {
LAB_18008a5e8:
    iVar13 = (int)plVar19[4];
    if (iVar13 == 0) {
      pfVar8 = (float *)((longlong)plVar19 + 0x24);
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar13 <= (int)uVar14) {
        uVar14 = iVar13 - 1;
      }
      pfVar8 = (float *)(lVar10 + (longlong)(int)uVar14 * 4);
    }
  }
  *pfVar8 = (float)iVar20;
  uVar14 = *(uint *)(param_3 + 900);
  lVar10 = *(longlong *)(param_3 + 0xab0);
  uVar15 = uVar14;
  if (lVar10 == 0) {
    if (*(code **)(param_3 + 0xac0) != (code *)0x0) {
      (**(code **)(param_3 + 0xac0))(*(undefined4 *)(param_3 + 0xac8));
      uVar15 = *(uint *)(param_3 + 900);
    }
    lVar10 = *(longlong *)(param_3 + 0xab0);
    if (lVar10 != 0) goto LAB_18008a64c;
    puVar9 = (undefined4 *)(param_3 + 0xad4);
  }
  else {
LAB_18008a64c:
    iVar20 = *(int *)(param_3 + 0xad0);
    if (iVar20 == 0) {
      puVar9 = (undefined4 *)(param_3 + 0xad4);
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar14) {
        uVar14 = iVar20 - 1;
      }
      puVar9 = (undefined4 *)(lVar10 + (longlong)(int)uVar14 * 4);
    }
  }
  uVar23 = *puVar9;
  lVar10 = local_118[6];
  if (lVar10 == 0) {
    if ((code *)local_118[8] != (code *)0x0) {
      (*(code *)local_118[8])((int)local_118[9]);
    }
    lVar10 = local_118[6];
    if (lVar10 != 0) goto LAB_18008a69c;
  }
  else {
LAB_18008a69c:
    iVar20 = (int)local_118[10];
    if (iVar20 != 0) {
      uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar15) {
        uVar15 = iVar20 - 1;
      }
      puVar9 = (undefined4 *)(lVar10 + (longlong)(int)uVar15 * 4);
      goto LAB_18008a6b8;
    }
  }
  puVar9 = (undefined4 *)((longlong)local_118 + 0x54);
LAB_18008a6b8:
  *puVar9 = uVar23;
  return;
}

