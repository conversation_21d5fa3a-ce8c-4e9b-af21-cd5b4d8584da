
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_GetPivotText(undefined1 param_1 [16],undefined1 param_2 [16],int *param_3,
                      undefined8 param_4,undefined8 param_5,undefined8 param_6,undefined8 param_7,
                      undefined8 param_8,undefined8 param_9,undefined8 param_10)

{
  longlong *******ppppppplVar1;
  longlong *******ppppppplVar2;
  int iVar3;
  byte bVar4;
  char cVar5;
  longlong ******lpMem;
  bool bVar6;
  uint uVar7;
  int iVar8;
  HANDLE pvVar9;
  longlong *******ppppppplVar10;
  undefined8 *puVar11;
  undefined8 uVar12;
  longlong *plVar13;
  ulonglong uVar14;
  ulonglong uVar15;
  ulonglong uVar16;
  char *pcVar17;
  ulonglong uVar18;
  char **ppcVar19;
  longlong *******ppppppplVar20;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  ulonglong uVar21;
  longlong *******ppppppplVar22;
  longlong *******ppppppplVar23;
  longlong *******ppppppplVar24;
  longlong *******ppppppplVar25;
  longlong *******ppppppplVar26;
  longlong lVar27;
  char *pcVar28;
  int iVar29;
  uint extraout_w11;
  longlong extraout_x11;
  longlong extraout_x11_00;
  uint extraout_w12;
  ulonglong extraout_x12;
  ulonglong extraout_x12_00;
  float *pfVar30;
  longlong extraout_x13;
  longlong extraout_x13_00;
  longlong extraout_x13_01;
  longlong extraout_x13_02;
  longlong extraout_x13_03;
  longlong *plVar31;
  longlong *******ppppppplVar32;
  longlong *plVar33;
  longlong *******lpMem_00;
  uint uVar34;
  longlong *******lpMem_01;
  int iVar35;
  int *piVar36;
  undefined1 extraout_q0 [16];
  undefined1 auVar37 [16];
  undefined1 extraout_q0_00 [16];
  undefined1 extraout_q0_01 [16];
  undefined1 auVar38 [16];
  undefined1 auVar39 [16];
  undefined1 auVar40 [16];
  undefined1 auVar41 [16];
  undefined1 auVar42 [16];
  undefined1 auVar43 [16];
  undefined1 auVar44 [16];
  undefined1 auVar45 [16];
  undefined1 auVar46 [16];
  undefined1 auVar47 [16];
  undefined1 extraout_q0_02 [16];
  undefined4 uVar48;
  undefined4 uVar50;
  double dVar49;
  float fVar51;
  float fVar52;
  float fVar53;
  float fVar54;
  float fVar55;
  float fVar56;
  float fVar57;
  float fVar58;
  float extraout_s17;
  float extraout_s17_00;
  double dVar59;
  longlong ******local_740;
  longlong ******local_738;
  longlong ******local_730;
  undefined8 local_728;
  longlong *****local_720;
  longlong ******local_718;
  longlong ******local_710;
  longlong ******local_708;
  longlong ******local_700;
  longlong ******local_6f8;
  longlong *local_6f0;
  longlong *local_6e8;
  longlong ******local_6e0;
  longlong ******local_6d8;
  longlong ******local_6d0;
  longlong ******local_6c8;
  longlong ******local_6c0;
  longlong ******local_6b8;
  longlong *****local_6b0;
  undefined8 local_6a8;
  ulonglong local_698;
  LPVOID local_690;
  undefined8 local_688;
  LPVOID local_678;
  undefined8 local_670;
  LPVOID local_660;
  undefined8 local_658;
  LPVOID local_648;
  undefined8 local_640;
  LPVOID local_630;
  undefined8 local_628;
  LPVOID local_618;
  undefined8 local_610;
  undefined8 local_600;
  longlong ******local_5f0;
  undefined8 uStack_5e8;
  undefined1 *local_5e0;
  undefined **local_5d0 [2];
  longlong ******local_5c0;
  undefined8 uStack_5b8;
  undefined1 *local_5b0;
  longlong ******local_5a0;
  undefined8 uStack_598;
  undefined1 *local_590;
  longlong ******local_580;
  undefined8 uStack_578;
  undefined1 *local_570;
  undefined1 *local_560;
  undefined8 uStack_558;
  undefined1 *local_550;
  undefined1 *local_540;
  undefined8 uStack_538;
  undefined1 *local_530;
  longlong local_520 [18];
  char acStack_490 [4];
  int local_48c;
  int local_484;
  float local_470;
  int local_468;
  undefined4 local_464;
  undefined4 local_458;
  longlong local_3d0;
  longlong ******local_3c0;
  undefined8 local_3b8;
  undefined4 local_3a8;
  undefined4 local_3a0;
  undefined4 local_398;
  char *local_390;
  undefined8 local_388;
  char *local_380;
  undefined4 local_378;
  undefined4 local_36c;
  undefined4 local_148;
  LPVOID local_f8;
  undefined8 local_f0;
  
  uVar50 = param_2._4_4_;
                    /* 0x40718  9  scsf_GetPivotText */
  uVar48 = param_2._0_4_;
  lpMem_01 = (longlong *******)&DAT_1800d4ecd;
  local_600 = 0xfffffffffffffffe;
  local_550 = &DAT_1800d4ecd;
  local_560 = &DAT_1800d4ecd;
  uStack_558 = 0;
  local_700 = (longlong ******)(**(code **)(param_3 + 0x62e))(0);
  lVar27 = *(longlong *)(param_3 + 0x84);
  uVar12 = extraout_x1;
  auVar37 = extraout_q0;
  if (lVar27 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar37 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_00;
    }
    lVar27 = *(longlong *)(param_3 + 0x84);
    if (lVar27 != 0) goto LAB_1800407c0;
    local_6e8 = (longlong *)(param_3 + 0x8e);
LAB_1800407f0:
    local_6f0 = local_6e8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar37 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_01;
    }
    lVar27 = *(longlong *)(param_3 + 0x84);
    if (lVar27 != 0) goto LAB_180040820;
    local_6e0 = (longlong ******)(param_3 + 0x8e);
LAB_180040858:
    local_6f8 = local_6e0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar37 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_02;
    }
    lVar27 = *(longlong *)(param_3 + 0x84);
    if (lVar27 != 0) goto LAB_180040888;
    local_6d8 = (longlong ******)(param_3 + 0x8e);
LAB_1800408c0:
    local_740 = local_6d8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar37 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_03;
    }
    lVar27 = *(longlong *)(param_3 + 0x84);
    if (lVar27 != 0) goto LAB_1800408f0;
    local_6d0 = (longlong ******)(param_3 + 0x8e);
LAB_18004092c:
    local_738 = local_6d0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar37 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_04;
    }
    lVar27 = *(longlong *)(param_3 + 0x84);
    if (lVar27 != 0) goto LAB_18004095c;
    local_6c8 = (longlong ******)(param_3 + 0x8e);
LAB_180040994:
    local_718 = local_6c8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar37 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_05;
    }
    lVar27 = *(longlong *)(param_3 + 0x84);
    if (lVar27 != 0) goto LAB_1800409c4;
    local_6c0 = (longlong ******)(param_3 + 0x8e);
LAB_1800409fc:
    local_710 = local_6c0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar37 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_06;
    }
    lVar27 = *(longlong *)(param_3 + 0x84);
    if (lVar27 != 0) goto LAB_180040a2c;
    local_6b8 = (longlong ******)(param_3 + 0x8e);
LAB_180040a64:
    local_708 = local_6b8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar37 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_07;
    }
    lVar27 = *(longlong *)(param_3 + 0x84);
    if (lVar27 != 0) goto LAB_180040a8c;
    plVar33 = (longlong *)(param_3 + 0x8e);
LAB_180040abc:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar37 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_08;
    }
    lVar27 = *(longlong *)(param_3 + 0x84);
    if (lVar27 != 0) goto LAB_180040ae4;
    plVar31 = (longlong *)(param_3 + 0x8e);
LAB_180040b14:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar37 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_09;
    }
    lVar27 = *(longlong *)(param_3 + 0x84);
    if (lVar27 != 0) goto LAB_180040b34;
    piVar36 = param_3 + 0x8e;
  }
  else {
LAB_1800407c0:
    iVar35 = param_3[0x8c];
    if (iVar35 == 0) {
      local_6f0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar29 = 0;
      if (iVar35 < 1) {
        iVar29 = iVar35 + -1;
      }
      local_6f0 = (longlong *)(lVar27 + (longlong)iVar29 * 0x98);
    }
    local_6e8 = local_6f0;
    if (lVar27 == 0) goto LAB_1800407f0;
LAB_180040820:
    iVar35 = param_3[0x8c];
    if (iVar35 == 0) {
      local_6f8 = (longlong ******)(param_3 + 0x8e);
    }
    else {
      iVar29 = 1;
      if (iVar35 < 2) {
        iVar29 = iVar35 + -1;
      }
      local_6f8 = (longlong ******)(lVar27 + (longlong)iVar29 * 0x98);
    }
    local_6e0 = local_6f8;
    if (lVar27 == 0) goto LAB_180040858;
LAB_180040888:
    iVar35 = param_3[0x8c];
    if (iVar35 == 0) {
      local_740 = (longlong ******)(param_3 + 0x8e);
    }
    else {
      iVar29 = 2;
      if (iVar35 < 3) {
        iVar29 = iVar35 + -1;
      }
      local_740 = (longlong ******)(lVar27 + (longlong)iVar29 * 0x98);
    }
    local_6d8 = local_740;
    if (lVar27 == 0) goto LAB_1800408c0;
LAB_1800408f0:
    iVar35 = param_3[0x8c];
    if (iVar35 == 0) {
      local_738 = (longlong ******)(param_3 + 0x8e);
    }
    else {
      iVar29 = 3;
      if (iVar35 < 4) {
        iVar29 = iVar35 + -1;
      }
      local_738 = (longlong ******)(lVar27 + (longlong)iVar29 * 0x98);
    }
    local_6d0 = local_738;
    if (lVar27 == 0) goto LAB_18004092c;
LAB_18004095c:
    iVar35 = param_3[0x8c];
    if (iVar35 == 0) {
      local_718 = (longlong ******)(param_3 + 0x8e);
    }
    else {
      iVar29 = 4;
      if (iVar35 < 5) {
        iVar29 = iVar35 + -1;
      }
      local_718 = (longlong ******)(lVar27 + (longlong)iVar29 * 0x98);
    }
    local_6c8 = local_718;
    if (lVar27 == 0) goto LAB_180040994;
LAB_1800409c4:
    iVar35 = param_3[0x8c];
    if (iVar35 == 0) {
      local_710 = (longlong ******)(param_3 + 0x8e);
    }
    else {
      iVar29 = 5;
      if (iVar35 < 6) {
        iVar29 = iVar35 + -1;
      }
      local_710 = (longlong ******)(lVar27 + (longlong)iVar29 * 0x98);
    }
    local_6c0 = local_710;
    if (lVar27 == 0) goto LAB_1800409fc;
LAB_180040a2c:
    iVar35 = param_3[0x8c];
    if (iVar35 == 0) {
      local_708 = (longlong ******)(param_3 + 0x8e);
    }
    else {
      iVar29 = 6;
      if (iVar35 < 7) {
        iVar29 = iVar35 + -1;
      }
      local_708 = (longlong ******)(lVar27 + (longlong)iVar29 * 0x98);
    }
    local_6b8 = local_708;
    if (lVar27 == 0) goto LAB_180040a64;
LAB_180040a8c:
    iVar35 = param_3[0x8c];
    if (iVar35 == 0) {
      plVar33 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar29 = 7;
      if (iVar35 < 8) {
        iVar29 = iVar35 + -1;
      }
      plVar33 = (longlong *)(lVar27 + (longlong)iVar29 * 0x98);
    }
    if (lVar27 == 0) goto LAB_180040abc;
LAB_180040ae4:
    iVar35 = param_3[0x8c];
    if (iVar35 == 0) {
      plVar31 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar29 = 8;
      if (iVar35 < 9) {
        iVar29 = iVar35 + -1;
      }
      plVar31 = (longlong *)(lVar27 + (longlong)iVar29 * 0x98);
    }
    if (lVar27 == 0) goto LAB_180040b14;
LAB_180040b34:
    iVar35 = param_3[0x8c];
    if (iVar35 == 0) {
      piVar36 = param_3 + 0x8e;
    }
    else {
      iVar29 = 9;
      if (iVar35 < 10) {
        iVar29 = iVar35 + -1;
      }
      piVar36 = (int *)(lVar27 + (longlong)iVar29 * 0x98);
    }
  }
  local_5d0[0] = PitSessions::vftable;
  if (param_3[0x2b] != 0) {
    FUN_1800079f8((longlong *)(param_3 + 0x46),0x1800d7b18,0xe);
    param_3[0x49] = 1;
    if ((*(char **)(param_3 + 0xce) != (char *)0x0) && (**(char **)(param_3 + 0xce) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0xce),0x1800d4ecd,0);
      param_3[0xd1] = 1;
    }
    local_730 = (longlong ******)&DAT_1800d4ecd;
    local_728 = 0;
    local_720 = (longlong *****)&DAT_1800d4ecd;
    pvVar9 = GetProcessHeap();
    ppppppplVar10 = (longlong *******)HeapAlloc(pvVar9,0,0x15);
    if (ppppppplVar10 == (longlong *******)0x0) {
      local_730 = (longlong ******)&DAT_1800d4ecd;
      ppppppplVar32 = lpMem_01;
    }
    else {
      param_6 = 0x14;
      ppppppplVar10[1] = (longlong ******)0x0;
      *ppppppplVar10 = (longlong ******)0x0;
      *(int *)(ppppppplVar10 + 2) = 0;
      *(undefined1 *)((longlong)ppppppplVar10 + 0x14) = 0;
      local_730 = (longlong ******)ppppppplVar10;
      FUN_180099d78((char *)ppppppplVar10,0x15,0x1800d6c40,0x14);
      local_728 = 0x100000001;
      ppppppplVar32 = ppppppplVar10;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_730);
    if ((ppppppplVar10 != (longlong *******)0x0) && (ppppppplVar32 != (longlong *******)0x0)) {
      pvVar9 = GetProcessHeap();
      HeapFree(pvVar9,0,ppppppplVar32);
    }
    local_730 = (longlong ******)&DAT_1800d4ecd;
    local_728 = 0;
    local_720 = (longlong *****)&DAT_1800d4ecd;
    pvVar9 = GetProcessHeap();
    ppppppplVar10 = (longlong *******)HeapAlloc(pvVar9,0,0x8d);
    if (ppppppplVar10 == (longlong *******)0x0) {
      local_730 = (longlong ******)&DAT_1800d4ecd;
      ppppppplVar32 = lpMem_01;
    }
    else {
      param_6 = 0x8c;
      ppppppplVar10[1] = (longlong ******)0x0;
      *ppppppplVar10 = (longlong ******)0x0;
      ppppppplVar10[3] = (longlong ******)0x0;
      ppppppplVar10[2] = (longlong ******)0x0;
      ppppppplVar10[5] = (longlong ******)0x0;
      ppppppplVar10[4] = (longlong ******)0x0;
      ppppppplVar10[7] = (longlong ******)0x0;
      ppppppplVar10[6] = (longlong ******)0x0;
      ppppppplVar10[9] = (longlong ******)0x0;
      ppppppplVar10[8] = (longlong ******)0x0;
      ppppppplVar10[0xb] = (longlong ******)0x0;
      ppppppplVar10[10] = (longlong ******)0x0;
      ppppppplVar10[0xd] = (longlong ******)0x0;
      ppppppplVar10[0xc] = (longlong ******)0x0;
      ppppppplVar10[0xf] = (longlong ******)0x0;
      ppppppplVar10[0xe] = (longlong ******)0x0;
      ppppppplVar10[0x10] = (longlong ******)0x0;
      *(int *)(ppppppplVar10 + 0x11) = 0;
      *(undefined1 *)((longlong)ppppppplVar10 + 0x8c) = 0;
      local_730 = (longlong ******)ppppppplVar10;
      FUN_180099d78((char *)ppppppplVar10,0x8d,0x1800d6bb0,0x8c);
      local_728 = 0x100000001;
      ppppppplVar32 = ppppppplVar10;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_730);
    auVar37 = extraout_q0_00;
    if ((ppppppplVar10 != (longlong *******)0x0) && (ppppppplVar32 != (longlong *******)0x0)) {
      pvVar9 = GetProcessHeap();
      HeapFree(pvVar9,0,ppppppplVar32);
      local_730 = (longlong ******)0x0;
      local_728 = 0;
      auVar37 = extraout_q0_01;
    }
    ppppppplVar10 = *(longlong ********)(param_3 + 0x46);
    if (*(longlong ********)(param_3 + 0x46) == (longlong *******)0x0) {
      ppppppplVar10 = lpMem_01;
    }
    FUN_180026368(auVar37,CONCAT44(uVar50,uVar48),(undefined8 *)(param_3 + 0xce),0x1800d6c60,
                  ppppppplVar10,param_6,param_7,param_8,param_9,param_10);
    local_730 = (longlong ******)&DAT_1800d4ecd;
    local_728 = 0;
    local_720 = (longlong *****)&DAT_1800d4ecd;
    pvVar9 = GetProcessHeap();
    ppppppplVar10 = (longlong *******)HeapAlloc(pvVar9,0,7);
    if (ppppppplVar10 == (longlong *******)0x0) {
      local_730 = (longlong ******)&DAT_1800d4ecd;
    }
    else {
      *(int *)ppppppplVar10 = 0;
      *(undefined2 *)((longlong)ppppppplVar10 + 4) = 0;
      *(undefined1 *)((longlong)ppppppplVar10 + 6) = 0;
      local_730 = (longlong ******)ppppppplVar10;
      FUN_180099d78((char *)ppppppplVar10,7,0x1800d6c58,6);
      local_728 = 0x100000001;
      lpMem_01 = ppppppplVar10;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_730);
    if ((ppppppplVar10 != (longlong *******)0x0) && (lpMem_01 != (longlong *******)0x0)) {
      pvVar9 = GetProcessHeap();
      HeapFree(pvVar9,0,lpMem_01);
      local_730 = (longlong ******)0x0;
      local_728 = 0;
    }
    param_3[1] = 0;
    param_3[4] = 0;
    param_3[0xdf] = 1;
    param_3[0x32e] = 1;
    FUN_1800079f8(local_6f0,0x1800d7b38,0x14);
    *(int *)((longlong)local_6e8 + 0xc) = 1;
    *(undefined1 *)(local_6e8 + 3) = 0x15;
    ((int *)((longlong)local_6e8 + 0x1c))[0] = 0;
    ((int *)((longlong)local_6e8 + 0x1c))[1] = 0;
    FUN_1800079f8((longlong *)local_6f8,0x1800d7b28,10);
    *(undefined4 *)((longlong)local_6e0 + 0xc) = 1;
    if ((longlong ******)local_6e0[10] != (longlong ******)0x0) {
      (*(code *)local_6e0[10])(*(undefined4 *)((longlong)local_6e0 + 0x4c),"Current;Developing");
    }
    *(undefined1 *)(local_6e0 + 3) = 0x16;
    *(undefined4 *)((longlong)local_6e0 + 0x1c) = 0;
    FUN_1800079f8((longlong *)local_740,0x1800d7b50,10);
    *(int *)((longlong)local_6d8 + 0xc) = 1;
    *(int *)((longlong)local_6d8 + 0x1c) = 0xffffff;
    *(undefined1 *)(local_6d8 + 3) = 0xe;
    FUN_1800079f8((longlong *)local_738,0x1800d7b90,0x13);
    *(int *)((longlong)local_6d0 + 0xc) = 1;
    *(undefined1 *)(local_6d0 + 3) = 5;
    *(int *)((longlong)local_6d0 + 0x1c) = 1;
    FUN_1800079f8((longlong *)local_718,0x1800d7b78,0x11);
    *(int *)((longlong)local_6c8 + 0xc) = 1;
    *(undefined1 *)(local_6c8 + 3) = 0xe;
    *(int *)((longlong)local_6c8 + 0x1c) = 0xff8000;
    FUN_1800079f8((longlong *)local_710,0x1800d7bc8,0x11);
    *(int *)((longlong)local_6c0 + 0xc) = 1;
    *(int *)((longlong)local_6c0 + 0x1c) = 0xff;
    *(undefined1 *)(local_6c0 + 3) = 0xe;
    FUN_1800079f8(plVar33,0x1800d7ba8,0x19);
    *(undefined4 *)((longlong)plVar33 + 0xc) = 1;
    *(undefined4 *)((longlong)plVar33 + 0x1c) = 0x5f;
    *(undefined1 *)(plVar33 + 3) = 0xb;
    FUN_1800079f8((longlong *)local_708,0x1800d7bf0,0x1b);
    *(int *)((longlong)local_6b8 + 0xc) = 1;
    *(undefined1 *)(local_6b8 + 3) = 0xb;
    *(int *)((longlong)local_6b8 + 0x1c) = 5;
    FUN_1800079f8(plVar31,0x1800d7be0,0xe);
    *(undefined4 *)((longlong)plVar31 + 0xc) = 1;
    *(undefined4 *)((longlong)plVar31 + 0x1c) = 8;
    *(undefined1 *)(plVar31 + 3) = 0xb;
    puVar11 = FUN_180029680((longlong *)local_5d0,&local_6b0);
    if (0xf < (ulonglong)puVar11[3]) {
      puVar11 = (undefined8 *)*puVar11;
    }
    if (*(code **)(piVar36 + 0x14) != (code *)0x0) {
      (**(code **)(piVar36 + 0x14))(piVar36[0x13],puVar11);
      *(undefined1 *)(piVar36 + 6) = 0x16;
    }
    if (0xf < local_698) {
      FUN_1800966b8(local_6b0);
    }
    *(undefined1 *)(piVar36 + 6) = 0x16;
    piVar36[7] = 0;
    return;
  }
  if (param_3[0xe1] == 0) {
    uVar12 = FUN_1800254e8(auVar37,CONCAT44(uVar50,uVar48),(longlong)param_3,uVar12,param_5,param_6,
                           param_7,param_8,param_9,param_10);
    *(int *)local_700 = (int)uVar12;
  }
  if (*(int *)local_700 != 0) {
    return;
  }
  local_520[1] = 0;
  local_520[0] = 0;
  local_520[3] = 0;
  local_520[2] = 0;
  local_520[5] = 0;
  local_520[4] = 0;
  local_520[7] = 0;
  local_520[6] = 0;
  local_520[9] = 0;
  local_520[8] = 0;
  local_520[0xb] = 0;
  local_520[10] = 0;
  local_520[0xd] = 0;
  local_520[0xc] = 0;
  local_520[0xf] = 0;
  local_520[0xe] = 0;
  local_520[0x10] = 0;
  uVar7 = FUN_180026550((longlong)piVar36);
  uVar21 = (ulonglong)uVar7;
  FUN_180011fd0(local_520,(longlong)param_3,param_3[0xe1],uVar7);
  if (param_3[0x1b] != 0) {
    return;
  }
  if (param_3[0xe1] < *param_3 + -1) {
    return;
  }
  bVar4 = *(byte *)(local_6e8 + 3);
  uVar7 = (uint)bVar4;
  if (bVar4 == 0x12 || bVar4 == 0x15) {
    uVar14 = (ulonglong)*(uint *)((longlong)local_6e8 + 0x1c);
    if (bVar4 != 0x12) goto LAB_1800410b0;
LAB_1800410d0:
    uVar7 = *(uint *)(local_6e8 + 4);
  }
  else if (bVar4 == 0x13) {
    uVar7 = FUN_180026550((longlong)local_6f0);
    uVar14 = extraout_x12;
  }
  else {
    uVar14 = FUN_180026708((longlong)local_6f0);
    uVar14 = uVar14 & 0xffffffff;
    uVar7 = extraout_w11;
LAB_1800410b0:
    if ((uVar7 + 0xec & 0xff) < 2) goto LAB_1800410d0;
    uVar7 = FUN_180026550((longlong)local_6f0);
    uVar14 = extraout_x12_00;
  }
  local_5e0 = &DAT_1800d4ecd;
  local_5f0 = (longlong ******)&DAT_1800d4ecd;
  uStack_5e8 = 0;
  local_530 = &DAT_1800d4ecd;
  local_540 = &DAT_1800d4ecd;
  uStack_538 = 0;
  local_5b0 = &DAT_1800d4ecd;
  local_5c0 = (longlong ******)&DAT_1800d4ecd;
  uStack_5b8 = 0;
  plVar13 = (longlong *)(**(code **)(param_3 + 0x45c))(uVar14 & 0xffffffff,uVar7,3);
  pfVar30 = (float *)*plVar13;
  if (pfVar30 == (float *)0x0) {
    return;
  }
  fVar57 = pfVar30[2];
  fVar51 = pfVar30[3];
  iVar35 = (int)*pfVar30;
  uVar34 = (uint)pfVar30[1];
  uVar7 = FUN_180026550((longlong)local_6f8);
  if ((uVar7 != 0) || ((uVar34 & 0xfffffffa) != 0)) {
    uVar7 = FUN_180026550((longlong)local_6f8);
    if ((uVar7 != 1) || ((uVar34 & extraout_w12) != 0)) {
      uVar7 = FUN_180026550((longlong)local_6f8);
      if (uVar7 == 0 && uVar34 == 2) {
        fVar52 = *(float *)(extraout_x13_01 + 0x10);
        uVar7 = *(uint *)(extraout_x13_01 + 0x14);
        bVar6 = FUN_180026690((longlong)local_738);
        if (bVar6) {
          local_740 = (longlong ******)&local_718;
          if (fVar51 < extraout_s17) {
            local_740 = (longlong ******)&local_710;
          }
          local_740 = (longlong ******)*local_740;
        }
        uVar14 = FUN_180026708((longlong)local_740);
        uVar15 = FUN_180026708((longlong)local_708);
        uVar16 = FUN_180026708((longlong)plVar33);
        uVar18 = FUN_180026708((longlong)plVar31);
        local_700 = (longlong ******)&local_730;
        local_720 = (longlong *****)&DAT_1800d4ecd;
        local_730 = (longlong ******)&DAT_1800d4ecd;
        local_728 = 0;
        pvVar9 = GetProcessHeap();
        local_730 = (longlong ******)HeapAlloc(pvVar9,0,8);
        if (local_730 == (longlong ******)0x0) {
          local_730 = (longlong ******)local_720;
        }
        else {
          *local_730 = (longlong *****)0x0;
          uVar21 = 7;
          FUN_180099d78((char *)local_730,8,0x1800d7d08,7);
          local_728 = 0x100000001;
        }
        FUN_18000e8f8(fVar52,(double)(ulonglong)uVar7,fVar57,(longlong)param_3,
                      (param_3[0x270] + param_3[9] * 10000) * 10 + 2,&local_730,uVar21,(int)uVar14,
                      (int)uVar18,(uint)uVar16,(uint)uVar15,iVar35,&local_5c0);
        ppppppplVar10 = (longlong *******)local_5c0;
        goto LAB_180041eb8;
      }
      uVar7 = FUN_180026550(extraout_x11);
      if (uVar7 == 1 && uVar34 == 2) {
        fVar52 = *(float *)(extraout_x13_02 + 0x18);
        uVar7 = *(uint *)(extraout_x13_02 + 0x1c);
        bVar6 = FUN_180026690((longlong)local_738);
        if (bVar6) {
          local_740 = (longlong ******)&local_718;
          if (fVar51 < extraout_s17_00) {
            local_740 = (longlong ******)&local_710;
          }
          local_740 = (longlong ******)*local_740;
        }
        uVar14 = FUN_180026708((longlong)local_740);
        uVar15 = FUN_180026708((longlong)local_708);
        uVar16 = FUN_180026708((longlong)plVar33);
        uVar18 = FUN_180026708((longlong)plVar31);
        local_700 = (longlong ******)&local_730;
        local_720 = (longlong *****)&DAT_1800d4ecd;
        local_730 = (longlong ******)&DAT_1800d4ecd;
        local_728 = 0;
        pvVar9 = GetProcessHeap();
        local_730 = (longlong ******)HeapAlloc(pvVar9,0,0xb);
        if (local_730 == (longlong ******)0x0) {
          local_730 = (longlong ******)local_720;
        }
        else {
          *local_730 = (longlong *****)0x0;
          *(undefined2 *)(local_730 + 1) = 0;
          *(char *)((longlong)local_730 + 10) = '\0';
          uVar21 = 10;
          FUN_180099d78((char *)local_730,0xb,0x1800d7b08,10);
          local_728 = 0x100000001;
        }
        FUN_18000e8f8(fVar52,(double)(ulonglong)uVar7,fVar57,(longlong)param_3,
                      (param_3[0x270] + param_3[9] * 10000) * 10 + 2,&local_730,uVar21,(int)uVar14,
                      (int)uVar18,(uint)uVar16,(uint)uVar15,iVar35,&local_5c0);
        ppppppplVar10 = (longlong *******)local_5c0;
        goto LAB_180041eb8;
      }
      uVar7 = FUN_180026550(extraout_x11_00);
      ppppppplVar10 = lpMem_01;
      if (uVar7 != 0 || uVar34 != 3) goto LAB_180041eb8;
      fVar52 = *(float *)(extraout_x13_03 + 0x10);
      fVar53 = *(float *)(extraout_x13_03 + 0x18);
      fVar55 = *(float *)(extraout_x13_03 + 0x1c);
      bVar6 = FUN_180026690((longlong)local_738);
      if (bVar6) {
        local_740 = (longlong ******)&local_718;
        if (fVar51 < fVar52) {
          local_740 = (longlong ******)&local_710;
        }
        local_740 = (longlong ******)*local_740;
      }
      uVar21 = FUN_180026708((longlong)local_740);
      uVar14 = FUN_180026708((longlong)local_708);
      uVar15 = FUN_180026708((longlong)plVar33);
      uVar16 = FUN_180026708((longlong)plVar31);
      local_700 = (longlong ******)&local_730;
      local_720 = (longlong *****)&DAT_1800d4ecd;
      local_730 = (longlong ******)&DAT_1800d4ecd;
      local_728 = 0;
      pvVar9 = GetProcessHeap();
      local_730 = (longlong ******)HeapAlloc(pvVar9,0,3);
      if (local_730 == (longlong ******)0x0) {
        local_730 = (longlong ******)local_720;
      }
      else {
        *(undefined2 *)local_730 = 0;
        *(char *)((longlong)local_730 + 2) = '\0';
        FUN_180099d78((char *)local_730,3,0x1800d7d04,2);
        local_728 = 0x100000001;
      }
      local_6e0 = (longlong ******)&local_730;
      iVar29 = param_3[0x270];
      iVar3 = param_3[9];
      fVar51 = fVar53 + fVar52;
      fVar53 = fVar52 - fVar53;
      iVar8 = FUN_1800be8b0(fVar55 + fVar51);
      dVar49 = (double)CONCAT44(uVar50,uVar48);
      if ((short)iVar8 == 1) {
        fVar56 = 0.0;
      }
      else {
        dVar49 = (double)fVar57;
        auVar37._0_8_ = (double)(fVar55 + fVar51);
        auVar37._8_8_ = 0;
        auVar37 = FUN_180007c30(auVar37,dVar49);
        fVar56 = (float)auVar37._0_8_;
      }
      iVar8 = FUN_1800be8b0(fVar53 - fVar55);
      if ((short)iVar8 == 1) {
        fVar58 = 0.0;
      }
      else {
        dVar49 = (double)fVar57;
        auVar38._0_8_ = (double)(fVar53 - fVar55);
        auVar38._8_8_ = 0;
        auVar37 = FUN_180007c30(auVar38,dVar49);
        fVar58 = (float)auVar37._0_8_;
      }
      fVar54 = fVar51 + fVar55 + fVar55;
      iVar8 = FUN_1800be8b0(fVar54);
      if ((short)iVar8 == 1) {
        fVar54 = 0.0;
      }
      else {
        dVar49 = (double)fVar57;
        auVar39._0_8_ = (double)fVar54;
        auVar39._8_8_ = 0;
        auVar37 = FUN_180007c30(auVar39,dVar49);
        fVar54 = (float)auVar37._0_8_;
      }
      fVar55 = fVar53 - (fVar55 + fVar55);
      iVar8 = FUN_1800be8b0(fVar55);
      if ((short)iVar8 == 1) {
        fVar57 = 0.0;
      }
      else {
        dVar49 = (double)fVar57;
        auVar40._0_8_ = (double)fVar55;
        auVar40._8_8_ = 0;
        auVar37 = FUN_180007c30(auVar40,dVar49);
        fVar57 = (float)auVar37._0_8_;
      }
      FUN_1800bf500(acStack_490,'\0',0x3e0);
      FUN_180009f70((undefined8 *)acStack_490);
      FUN_18000a310((longlong)acStack_490);
      local_48c = param_3[9];
      local_3a8 = 6;
      local_468 = param_3[4];
      ppcVar19 = (char **)(**(code **)(param_3 + 0x32))(&local_5a0);
      if (ppcVar19 != &local_390) {
        pcVar17 = *ppcVar19;
        if ((pcVar17 == (char *)0x0) || (*pcVar17 == '\0')) {
          if ((local_390 == (char *)0x0) || (*local_390 == '\0')) goto LAB_180041900;
          if (pcVar17 != (char *)0x0) goto LAB_180041888;
          if (((int)local_388 != 0) && (local_390 != (char *)0x0)) {
            pvVar9 = GetProcessHeap();
            HeapFree(pvVar9,0,local_390);
          }
          local_390 = local_380;
          local_388._0_4_ = 0;
        }
        else {
LAB_180041888:
          cVar5 = *pcVar17;
          pcVar28 = pcVar17;
          while (cVar5 != '\0') {
            pcVar28 = pcVar28 + 1;
            cVar5 = *pcVar28;
          }
          iVar8 = 0x7ffffffe;
          if ((ulonglong)((longlong)pcVar28 - (longlong)pcVar17) < 0x7fffffff) {
            iVar8 = (int)((longlong)pcVar28 - (longlong)pcVar17);
          }
          FUN_1800079f8((longlong *)&local_390,(longlong)pcVar17,iVar8);
        }
        local_388 = CONCAT44(1,(int)local_388);
      }
LAB_180041900:
      if (((int)uStack_598 != 0) && ((longlong *******)local_5a0 != (longlong *******)0x0)) {
        pvVar9 = GetProcessHeap();
        HeapFree(pvVar9,0,local_5a0);
      }
      local_470 = (float)(uVar15 & 0xffffffff);
      local_378 = 0;
      local_398 = 0;
      local_3a0 = 3;
      local_36c = 1;
      if ((double)(uVar14 & 0xffffffff) == 0.0) {
        local_3d0 = 0;
      }
      else {
        dVar59 = (double)(uVar14 & 0xffffffff) * 86400000000.0;
        if (0.0 <= dVar59) {
          dVar59 = dVar59 + 0.5;
        }
        else {
          dVar59 = dVar59 - 0.5;
        }
        local_3d0 = (longlong)dVar59;
      }
      local_148 = 0;
      local_5a0 = (longlong ******)&DAT_1800d4ecd;
      uStack_598 = 0;
      local_590 = &DAT_1800d4ecd;
      local_484 = (iVar29 + iVar3 * 10000) * 10 + 3;
      local_464 = (int)uVar21;
      local_458 = (int)uVar16;
      pvVar9 = GetProcessHeap();
      ppppppplVar10 = (longlong *******)HeapAlloc(pvVar9,0,7);
      if (ppppppplVar10 == (longlong *******)0x0) {
        local_5a0 = (longlong ******)&DAT_1800d4ecd;
        ppppppplVar32 = lpMem_01;
      }
      else {
        *(int *)ppppppplVar10 = 0;
        *(undefined2 *)((longlong)ppppppplVar10 + 4) = 0;
        *(undefined1 *)((longlong)ppppppplVar10 + 6) = 0;
        local_5a0 = (longlong ******)ppppppplVar10;
        FUN_180099d78((char *)ppppppplVar10,7,0x1800d6518,6);
        uStack_598 = 0x100000001;
        ppppppplVar32 = ppppppplVar10;
      }
      local_580 = (longlong ******)&DAT_1800d4ecd;
      uStack_578 = 0;
      local_570 = &DAT_1800d4ecd;
      pvVar9 = GetProcessHeap();
      ppppppplVar20 = (longlong *******)HeapAlloc(pvVar9,0,0x44);
      if (ppppppplVar20 == (longlong *******)0x0) {
        local_580 = (longlong ******)&DAT_1800d4ecd;
        lpMem_00 = lpMem_01;
      }
      else {
        ppppppplVar20[1] = (longlong ******)0x0;
        *ppppppplVar20 = (longlong ******)0x0;
        ppppppplVar20[3] = (longlong ******)0x0;
        ppppppplVar20[2] = (longlong ******)0x0;
        ppppppplVar20[5] = (longlong ******)0x0;
        ppppppplVar20[4] = (longlong ******)0x0;
        ppppppplVar20[7] = (longlong ******)0x0;
        ppppppplVar20[6] = (longlong ******)0x0;
        *(int *)(ppppppplVar20 + 8) = 0;
        local_580 = (longlong ******)ppppppplVar20;
        FUN_180099d78((char *)ppppppplVar20,0x44,0x1800d6520,0x43);
        uStack_578 = 0x100000001;
        lpMem_00 = ppppppplVar20;
      }
      auVar41._0_8_ = (double)fVar57;
      auVar41._8_8_ = 0;
      puVar11 = (undefined8 *)(**(code **)(param_3 + 0x1c0))(auVar41,&local_6b0,iVar35);
      auVar42._0_8_ = (double)fVar58;
      auVar42._8_8_ = 0;
      local_700 = (longlong ******)*puVar11;
      if ((longlong *******)*puVar11 == (longlong *******)0x0) {
        local_700 = (longlong ******)lpMem_01;
      }
      puVar11 = (undefined8 *)(**(code **)(param_3 + 0x1c0))(auVar42,&local_618,iVar35);
      auVar43._0_8_ = (double)fVar53;
      auVar43._8_8_ = 0;
      local_6b8 = (longlong ******)*puVar11;
      if ((longlong *******)*puVar11 == (longlong *******)0x0) {
        local_6b8 = (longlong ******)lpMem_01;
      }
      puVar11 = (undefined8 *)(**(code **)(param_3 + 0x1c0))(auVar43,&local_630,iVar35);
      auVar44._0_8_ = (double)fVar52;
      auVar44._8_8_ = 0;
      local_6c0 = (longlong ******)*puVar11;
      if ((longlong *******)*puVar11 == (longlong *******)0x0) {
        local_6c0 = (longlong ******)lpMem_01;
      }
      puVar11 = (undefined8 *)(**(code **)(param_3 + 0x1c0))(auVar44,&local_648,iVar35);
      auVar45._0_8_ = (double)fVar51;
      auVar45._8_8_ = 0;
      local_6c8 = (longlong ******)*puVar11;
      if ((longlong *******)*puVar11 == (longlong *******)0x0) {
        local_6c8 = (longlong ******)lpMem_01;
      }
      puVar11 = (undefined8 *)(**(code **)(param_3 + 0x1c0))(auVar45,&local_660,iVar35);
      auVar46._0_8_ = (double)fVar54;
      auVar46._8_8_ = 0;
      local_6d0 = (longlong ******)*puVar11;
      if ((longlong *******)*puVar11 == (longlong *******)0x0) {
        local_6d0 = (longlong ******)lpMem_01;
      }
      puVar11 = (undefined8 *)(**(code **)(param_3 + 0x1c0))(auVar46,&local_678,iVar35);
      auVar47._0_8_ = (double)fVar56;
      auVar47._8_8_ = 0;
      local_6d8 = (longlong ******)*puVar11;
      if ((longlong *******)*puVar11 == (longlong *******)0x0) {
        local_6d8 = (longlong ******)lpMem_01;
      }
      puVar11 = (undefined8 *)(**(code **)(param_3 + 0x1c0))(auVar47,&local_690,iVar35);
      ppppppplVar22 = (longlong *******)*puVar11;
      if ((longlong *******)*puVar11 == (longlong *******)0x0) {
        ppppppplVar22 = lpMem_01;
      }
      ppppppplVar2 = ppppppplVar32;
      if (ppppppplVar32 == (longlong *******)0x0) {
        ppppppplVar2 = lpMem_01;
      }
      ppppppplVar1 = lpMem_00;
      if (lpMem_00 == (longlong *******)0x0) {
        ppppppplVar1 = lpMem_01;
      }
      ppppppplVar23 = (longlong *******)local_6d8;
      ppppppplVar24 = (longlong *******)local_6d0;
      ppppppplVar25 = (longlong *******)local_6c8;
      ppppppplVar26 = (longlong *******)local_6c0;
      FUN_180006050(extraout_q0_02,dVar49,&local_3c0,(longlong)ppppppplVar1,ppppppplVar2,
                    ppppppplVar22,local_6d8,local_6d0,local_6c8,local_6c0);
      if (((int)local_688 != 0) && (local_690 != (LPVOID)0x0)) {
        pvVar9 = GetProcessHeap();
        HeapFree(pvVar9,0,local_690);
        local_690 = (LPVOID)0x0;
        local_688 = 0;
      }
      if (((int)local_670 != 0) && (local_678 != (LPVOID)0x0)) {
        pvVar9 = GetProcessHeap();
        HeapFree(pvVar9,0,local_678);
        local_678 = (LPVOID)0x0;
        local_670 = 0;
      }
      if (((int)local_658 != 0) && (local_660 != (LPVOID)0x0)) {
        pvVar9 = GetProcessHeap();
        HeapFree(pvVar9,0,local_660);
        local_660 = (LPVOID)0x0;
        local_658 = 0;
      }
      if (((int)local_640 != 0) && (local_648 != (LPVOID)0x0)) {
        pvVar9 = GetProcessHeap();
        HeapFree(pvVar9,0,local_648);
        local_648 = (LPVOID)0x0;
        local_640 = 0;
      }
      if (((int)local_628 != 0) && (local_630 != (LPVOID)0x0)) {
        pvVar9 = GetProcessHeap();
        HeapFree(pvVar9,0,local_630);
        local_630 = (LPVOID)0x0;
        local_628 = 0;
      }
      if (((int)local_610 != 0) && (local_618 != (LPVOID)0x0)) {
        pvVar9 = GetProcessHeap();
        HeapFree(pvVar9,0,local_618);
        local_618 = (LPVOID)0x0;
        local_610 = 0;
      }
      if (((int)local_6a8 != 0) && ((longlong ******)local_6b0 != (longlong ******)0x0)) {
        pvVar9 = GetProcessHeap();
        HeapFree(pvVar9,0,local_6b0);
        local_6b0 = (longlong *****)0x0;
        local_6a8 = 0;
      }
      auVar37 = (**(code **)(param_3 + 0x3c))(acStack_490);
      ppppppplVar2 = (longlong *******)local_3c0;
      if ((longlong *******)local_3c0 == (longlong *******)0x0) {
        ppppppplVar2 = lpMem_01;
      }
      FUN_180006050(auVar37,dVar49,&local_5c0,0x1800d6514,ppppppplVar2,ppppppplVar22,ppppppplVar23,
                    ppppppplVar24,ppppppplVar25,ppppppplVar26);
      if ((ppppppplVar20 != (longlong *******)0x0) && (lpMem_00 != (longlong *******)0x0)) {
        pvVar9 = GetProcessHeap();
        HeapFree(pvVar9,0,lpMem_00);
        local_580 = (longlong ******)0x0;
        uStack_578 = 0;
      }
      if ((ppppppplVar10 != (longlong *******)0x0) && (ppppppplVar32 != (longlong *******)0x0)) {
        pvVar9 = GetProcessHeap();
        HeapFree(pvVar9,0,ppppppplVar32);
        local_5a0 = (longlong ******)0x0;
        uStack_598 = 0;
      }
      if (((int)local_f0 != 0) && (local_f8 != (LPVOID)0x0)) {
        pvVar9 = GetProcessHeap();
        HeapFree(pvVar9,0,local_f8);
        local_f8 = (LPVOID)0x0;
        local_f0 = 0;
      }
      pcVar17 = local_390;
      if (((int)local_388 != 0) && (local_390 != (char *)0x0)) {
        pvVar9 = GetProcessHeap();
        HeapFree(pvVar9,0,pcVar17);
        local_390 = (char *)0x0;
        local_388 = 0;
      }
      if (((int)local_3b8 != 0) && ((longlong *******)local_3c0 != (longlong *******)0x0)) {
        pvVar9 = GetProcessHeap();
        HeapFree(pvVar9,0,local_3c0);
        local_3c0 = (longlong ******)0x0;
        local_3b8 = 0;
      }
      lpMem = local_730;
      lpMem_01 = (longlong *******)&DAT_1800d4ecd;
      ppppppplVar10 = (longlong *******)local_5c0;
      if (((int)local_728 != 0) && (local_730 != (longlong ******)0x0)) {
        pvVar9 = GetProcessHeap();
        HeapFree(pvVar9,0,lpMem);
        ppppppplVar10 = (longlong *******)local_5c0;
      }
      goto LAB_180041eb8;
    }
    fVar58 = *(float *)(extraout_x13_00 + 0x24);
    uVar7 = *(uint *)(extraout_x13_00 + 0x28);
    fVar56 = *(float *)(extraout_x13_00 + 0x2c);
    fVar55 = *(float *)(extraout_x13_00 + 0x30);
    fVar53 = *(float *)(extraout_x13_00 + 0x34);
    fVar52 = *(float *)(extraout_x13_00 + 0x38);
    fVar57 = *(float *)(extraout_x13_00 + 0x3c);
    if (uVar34 == 0) {
      pcVar17 = "Developing Weekly Pivot Levels";
      iVar29 = 0x1e;
LAB_18004131c:
      FUN_1800079f8((longlong *)&local_5f0,(longlong)pcVar17,iVar29);
      uStack_5e8 = CONCAT44(1,(int)uStack_5e8);
      lpMem_01 = (longlong *******)local_5f0;
    }
    else {
      if (uVar34 == 4) {
        pcVar17 = "Developing Monthly Pivot Levels";
        iVar29 = 0x1f;
        goto LAB_18004131c;
      }
      if (uVar34 == 5) {
        pcVar17 = "Developing Quarterly Pivot Levels";
        iVar29 = 0x21;
        goto LAB_18004131c;
      }
      if (uVar34 == 1) {
        pcVar17 = "Developing Pivot Levels";
        iVar29 = 0x17;
        goto LAB_18004131c;
      }
    }
    bVar6 = FUN_180026690((longlong)local_738);
    if (bVar6) {
      local_740 = (longlong ******)&local_718;
      if (fVar51 < fVar58) {
        local_740 = (longlong ******)&local_710;
      }
      local_740 = (longlong ******)*local_740;
    }
    uVar21 = FUN_180026708((longlong)local_740);
    uVar14 = FUN_180026708((longlong)local_708);
    uVar15 = FUN_180026708((longlong)plVar33);
    uVar16 = FUN_180026708((longlong)plVar31);
    local_700 = &local_6b0;
    pcVar17 = (char *)FUN_180007828(&local_6b0,(longlong *)&local_5f0);
    FUN_18000dec8(fVar58,ZEXT416(uVar7),fVar56,fVar55,fVar53,fVar52,fVar57,(longlong)param_3,
                  uVar34 + (param_3[0x270] + param_3[9] * 10000) * 10,pcVar17,(int)uVar21,
                  (int)uVar16,(uint)uVar15,(uint)uVar14,iVar35,&local_5c0);
    ppppppplVar10 = (longlong *******)local_5c0;
    goto LAB_180041eb8;
  }
  fVar58 = *(float *)(extraout_x13 + 0x10);
  uVar7 = *(uint *)(extraout_x13 + 0x14);
  fVar56 = *(float *)(extraout_x13 + 0x18);
  fVar55 = *(float *)(extraout_x13 + 0x1c);
  fVar53 = *(float *)(extraout_x13 + 0x20);
  fVar52 = *(float *)(extraout_x13 + 0x24);
  fVar57 = *(float *)(extraout_x13 + 0x28);
  if (uVar34 == 0) {
    pcVar17 = "Current Weekly Pivot Levels";
    iVar29 = 0x1b;
LAB_1800411c8:
    FUN_1800079f8((longlong *)&local_5f0,(longlong)pcVar17,iVar29);
    uStack_5e8 = CONCAT44(1,(int)uStack_5e8);
    lpMem_01 = (longlong *******)local_5f0;
  }
  else {
    if (uVar34 == 4) {
      pcVar17 = "Current Monthly Pivot Levels";
      iVar29 = 0x1c;
      goto LAB_1800411c8;
    }
    if (uVar34 == 5) {
      pcVar17 = "Current Quarterly Pivot Levels";
      iVar29 = 0x1e;
      goto LAB_1800411c8;
    }
    if (uVar34 == 1) {
      pcVar17 = "Current Pivot Levels";
      iVar29 = 0x14;
      goto LAB_1800411c8;
    }
  }
  bVar6 = FUN_180026690((longlong)local_738);
  if (bVar6) {
    local_740 = (longlong ******)&local_718;
    if (fVar51 < fVar58) {
      local_740 = (longlong ******)&local_710;
    }
    local_740 = (longlong ******)*local_740;
  }
  uVar21 = FUN_180026708((longlong)local_740);
  uVar14 = FUN_180026708((longlong)local_708);
  uVar15 = FUN_180026708((longlong)plVar33);
  uVar16 = FUN_180026708((longlong)plVar31);
  local_700 = &local_6b0;
  pcVar17 = (char *)FUN_180007828(&local_6b0,(longlong *)&local_5f0);
  FUN_18000dec8(fVar58,ZEXT416(uVar7),fVar56,fVar55,fVar53,fVar52,fVar57,(longlong)param_3,
                uVar34 + (param_3[0x270] + param_3[9] * 10000) * 10,pcVar17,(int)uVar21,(int)uVar16,
                (uint)uVar15,(uint)uVar14,iVar35,&local_5c0);
  ppppppplVar10 = (longlong *******)local_5c0;
LAB_180041eb8:
  if (((int)uStack_5b8 != 0) && (ppppppplVar10 != (longlong *******)0x0)) {
    pvVar9 = GetProcessHeap();
    HeapFree(pvVar9,0,ppppppplVar10);
    local_5c0 = (longlong ******)0x0;
    uStack_5b8 = 0;
  }
  if (((int)uStack_5e8 != 0) && (lpMem_01 != (longlong *******)0x0)) {
    pvVar9 = GetProcessHeap();
    HeapFree(pvVar9,0,lpMem_01);
  }
  return;
}

