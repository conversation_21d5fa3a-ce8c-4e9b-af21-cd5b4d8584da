# OrderflowLabs Decompiled Studies Analysis

## Common Function Patterns and Memory Offsets

### Study Interface Structure (param_3)
Based on analysis of all studies, param_3 appears to be the main SCStudyInterfaceRef structure:

#### Key Memory Offsets:
- `param_3 + 0x18b8`: Function pointer for getting study interface (likely sc.GetPersistentPointer)
- `param_3 + 0xac`: SetupPeriod flag (0 = setup, non-zero = calculation)
- `param_3 + 900`: Current bar index (sc.Index)
- `param_3 + 0x500`: Subgraph array base pointer
- `param_3 + 0x510`: Subgraph function pointer
- `param_3 + 0x520`: Number of subgraphs
- `param_3 + 0x528`: Default subgraph structure
- `param_3 + 0x210`: Input array base pointer  
- `param_3 + 0x220`: Input function pointer
- `param_3 + 0x230`: Number of inputs
- `param_3 + 0x238`: Default input structure

### Subgraph Structure Offsets (0x170 bytes each):
- `+0x00`: Name string pointer
- `+0x0c`: DrawStyle (1 = line, 4 = histogram, etc.)
- `+0x18`: Color (RGB values)
- `+0x24`: LineWidth
- `+0x80`: Data array pointer
- `+0x90`: Data array function pointer
- `+0xa0`: Array size
- `+0xa8`: Default data value

### Input Structure Offsets (0x98 bytes each):
- `+0x00`: Name string pointer
- `+0x0c`: Input type
- `+0x18`: Input data type (0xb = integer, 2 = float, etc.)
- `+0x1c`: Default/current value
- `+0x4c`: Function pointer for string inputs

### Common Function Mappings:

#### Memory Management:
- `GetProcessHeap()`: Windows heap functions
- `HeapAlloc()`: Allocate memory
- `HeapFree()`: Free memory

#### String Functions:
- `FUN_180099d78()`: String copy/format function
- `FUN_1800079f8()`: Set subgraph/input names
- `FUN_1800263b0()`: String assignment
- `FUN_180026368()`: String formatting with parameters

#### Data Access Functions:
- `FUN_180026708()`: Get input value (integer)
- `FUN_180026608()`: Get input value (float)
- `FUN_180026550()`: Get input boolean value
- `FUN_18000f448()`: Get subgraph data pointer for index
- `FUN_180005d08()`: Array resize/allocation

#### Study Calculation Functions:
- `FUN_1800254e8()`: Main study setup/initialization
- `(**(code **)(param_3 + 0x390))`: Array copy function
- `(**(code **)(param_3 + 0x6c0))`: Get previous bar value function

### Study Names and String Constants:
Based on string analysis in the decompiled code:
- "OrderFlowLabs.com": DLL name
- Various study names like "Exhaustion Absorption", "Delta Dominance", etc.
- Input parameter names like "Default;Fast", threshold values

### Color Constants:
- `0x80ff`: Red color
- `0xc08000`: Orange color  
- `0xff00`: Green color
- `0xff`: Blue color

### Draw Styles:
- `4`: Histogram/bar style
- `5`: Line style
- `0x20`, `0x21`: Special drawing modes

## Completed Reverse Engineering:

### 1. Exhaustion Absorption Detector (ExhaustionAbsorptionDetector.cpp)
- Detects market exhaustion and absorption patterns
- Monitors aggressive buying/selling that fails to move price
- Uses delta calculations and volume analysis
- Implements threshold-based detection with configurable multipliers
- Generates buy/sell signals based on absorption patterns

### 2. Delta Dominance Detector (DeltaDominanceDetector.cpp)
- Identifies when one side (buy/sell) dominates order flow
- Analyzes cumulative delta patterns over lookback periods
- Detects shifts in market sentiment using statistical thresholds
- Provides visual signals for trend changes
- Calculates dominance ratios and strength metrics

### 3. Delta Map (DeltaMap.cpp)
- Visual representation of delta by price level
- Creates heat map style visualization using Volume at Price data
- Shows delta concentration at specific price levels
- Helps identify support/resistance based on order flow
- Uses color coding for positive/negative delta

### 4. MGI - Market Generated Information (MGI_MarketGeneratedInformation.cpp)
- Comprehensive market profile analysis
- Calculates Value Area High/Low (70% of volume around POC)
- Identifies Point of Control (highest volume price)
- Tracks Initial Balance and session OHLC
- Supports multiple timeframes and volume types

### 5. ATR Ranges (ATR_Ranges.cpp)
- Dynamic support/resistance based on Average True Range
- Calculates multiple ATR-based levels with configurable multipliers
- Provides dynamic ranges that adjust based on volatility
- Supports multiple base price calculations (Close, Open, HL2, etc.)
- Includes breakout alerts and range extension features

### 6. Reconstructed Tape (ReconstructedTape.cpp)
- Simulates order book reconstruction from trade data
- Classifies volume into aggressive vs passive orders
- Estimates market maker vs taker activity
- Calculates order flow imbalance and tape strength
- Provides insights into institutional order flow patterns

## Function Mapping Summary:
- `FUN_180026708()` = Get integer input value
- `FUN_180026608()` = Get float input value
- `FUN_180026550()` = Get boolean input value
- `FUN_1800079f8()` = Set subgraph/input names
- `FUN_18000f448()` = Get subgraph data pointer for index
- `FUN_180005d08()` = Array resize/allocation
- `(**(code **)(param_3 + 0x390))` = Array copy function
- `(**(code **)(param_3 + 0x6c0))` = Get previous bar value function

## Key Insights:
- All studies use sophisticated order flow analysis
- Heavy reliance on delta calculations and volume distribution
- Complex threshold-based signal generation
- Extensive use of Volume at Price data structures
- Professional-grade market microstructure analysis tools
