#include "sierrachart.h"

/*==========================================================================*/
SCSFExport scsf_ATR_Ranges(SCStudyInterfaceRef sc)
{
    // Subgraph declarations for ATR-based ranges
    SCSubgraphRef Subgraph_ATRUpper1 = sc.Subgraph[0];
    SCSubgraphRef Subgraph_ATRLower1 = sc.Subgraph[1];
    SCSubgraphRef Subgraph_ATRUpper2 = sc.Subgraph[2];
    SCSubgraphRef Subgraph_ATRLower2 = sc.Subgraph[3];
    SCSubgraphRef Subgraph_ATRUpper3 = sc.Subgraph[4];
    SCSubgraphRef Subgraph_ATRLower3 = sc.Subgraph[5];
    SCSubgraphRef Subgraph_ATRMiddle = sc.Subgraph[6];
    SCSubgraphRef Subgraph_ATRValue = sc.Subgraph[7];
    
    // Internal arrays for calculations
    SCSubgraphRef Subgraph_TrueRange = sc.Subgraph[8];
    SCSubgraphRef Subgraph_ATR = sc.Subgraph[9];
    
    // Input declarations
    SCInputRef Input_ATRLength = sc.Input[0];
    SCInputRef Input_ATRMultiplier1 = sc.Input[1];
    SCInputRef Input_ATRMultiplier2 = sc.Input[2];
    SCInputRef Input_ATRMultiplier3 = sc.Input[3];
    SCInputRef Input_MovingAverageType = sc.Input[4];
    SCInputRef Input_BasePrice = sc.Input[5];
    SCInputRef Input_DisplayRanges = sc.Input[6];
    SCInputRef Input_ExtendRanges = sc.Input[7];
    SCInputRef Input_AlertOnBreakout = sc.Input[8];
    
    // Persistent variables
    int& r_LastAlertBar = sc.GetPersistentInt(1);
    
    if (sc.SetDefaults)
    {
        // Set study configuration
        sc.GraphName = "ATR Ranges";
        sc.StudyDescription = "Dynamic support/resistance levels based on Average True Range";
        sc.AutoLoop = 1;
        sc.GraphRegion = 0;
        sc.ValueFormat = 2;
        sc.ScaleRangeType = SCALE_SAMEASREGION;
        
        // Configure subgraphs
        Subgraph_ATRUpper1.Name = "ATR Upper 1";
        Subgraph_ATRUpper1.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_ATRUpper1.PrimaryColor = RGB(255, 0, 0); // Red
        Subgraph_ATRUpper1.LineWidth = 2;
        Subgraph_ATRUpper1.DrawZeros = false;
        
        Subgraph_ATRLower1.Name = "ATR Lower 1";
        Subgraph_ATRLower1.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_ATRLower1.PrimaryColor = RGB(255, 0, 0); // Red
        Subgraph_ATRLower1.LineWidth = 2;
        Subgraph_ATRLower1.DrawZeros = false;
        
        Subgraph_ATRUpper2.Name = "ATR Upper 2";
        Subgraph_ATRUpper2.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_ATRUpper2.PrimaryColor = RGB(255, 128, 0); // Orange
        Subgraph_ATRUpper2.LineWidth = 1;
        Subgraph_ATRUpper2.DrawZeros = false;
        
        Subgraph_ATRLower2.Name = "ATR Lower 2";
        Subgraph_ATRLower2.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_ATRLower2.PrimaryColor = RGB(255, 128, 0); // Orange
        Subgraph_ATRLower2.LineWidth = 1;
        Subgraph_ATRLower2.DrawZeros = false;
        
        Subgraph_ATRUpper3.Name = "ATR Upper 3";
        Subgraph_ATRUpper3.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_ATRUpper3.PrimaryColor = RGB(128, 128, 128); // Gray
        Subgraph_ATRUpper3.LineWidth = 1;
        Subgraph_ATRUpper3.DrawZeros = false;
        
        Subgraph_ATRLower3.Name = "ATR Lower 3";
        Subgraph_ATRLower3.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_ATRLower3.PrimaryColor = RGB(128, 128, 128); // Gray
        Subgraph_ATRLower3.LineWidth = 1;
        Subgraph_ATRLower3.DrawZeros = false;
        
        Subgraph_ATRMiddle.Name = "ATR Middle";
        Subgraph_ATRMiddle.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_ATRMiddle.PrimaryColor = RGB(0, 255, 0); // Green
        Subgraph_ATRMiddle.LineWidth = 2;
        Subgraph_ATRMiddle.DrawZeros = false;
        
        Subgraph_ATRValue.Name = "ATR Value";
        Subgraph_ATRValue.DrawStyle = DRAWSTYLE_IGNORE;
        
        // Internal calculation subgraphs
        Subgraph_TrueRange.Name = "True Range";
        Subgraph_TrueRange.DrawStyle = DRAWSTYLE_IGNORE;
        
        Subgraph_ATR.Name = "ATR";
        Subgraph_ATR.DrawStyle = DRAWSTYLE_IGNORE;
        
        // Configure inputs
        Input_ATRLength.Name = "ATR Length";
        Input_ATRLength.SetInt(14);
        Input_ATRLength.SetIntLimits(1, 100);
        
        Input_ATRMultiplier1.Name = "ATR Multiplier 1";
        Input_ATRMultiplier1.SetFloat(1.0f);
        Input_ATRMultiplier1.SetFloatLimits(0.1f, 10.0f);
        
        Input_ATRMultiplier2.Name = "ATR Multiplier 2";
        Input_ATRMultiplier2.SetFloat(2.0f);
        Input_ATRMultiplier2.SetFloatLimits(0.1f, 10.0f);
        
        Input_ATRMultiplier3.Name = "ATR Multiplier 3";
        Input_ATRMultiplier3.SetFloat(3.0f);
        Input_ATRMultiplier3.SetFloatLimits(0.1f, 10.0f);
        
        Input_MovingAverageType.Name = "Moving Average Type";
        Input_MovingAverageType.SetMovAvgType(MOVAVGTYPE_SIMPLE);
        
        Input_BasePrice.Name = "Base Price";
        Input_BasePrice.SetCustomInputStrings("Close;Open;High;Low;HL2;HLC3;OHLC4");
        Input_BasePrice.SetCustomInputIndex(0);
        
        Input_DisplayRanges.Name = "Display All Ranges";
        Input_DisplayRanges.SetYesNo(true);
        
        Input_ExtendRanges.Name = "Extend Ranges Forward";
        Input_ExtendRanges.SetYesNo(false);
        
        Input_AlertOnBreakout.Name = "Alert on Range Breakout";
        Input_AlertOnBreakout.SetYesNo(false);
        
        return;
    }
    
    // Calculate True Range
    if (sc.Index == 0)
    {
        Subgraph_TrueRange[sc.Index] = sc.High[sc.Index] - sc.Low[sc.Index];
    }
    else
    {
        float TR1 = sc.High[sc.Index] - sc.Low[sc.Index];
        float TR2 = abs(sc.High[sc.Index] - sc.Close[sc.Index - 1]);
        float TR3 = abs(sc.Low[sc.Index] - sc.Close[sc.Index - 1]);
        
        Subgraph_TrueRange[sc.Index] = max(TR1, max(TR2, TR3));
    }
    
    // Calculate ATR using specified moving average type
    sc.MovingAverage(Subgraph_TrueRange, Subgraph_ATR, Input_MovingAverageType.GetMovAvgType(), Input_ATRLength.GetInt());
    
    // Store ATR value for reference
    Subgraph_ATRValue[sc.Index] = Subgraph_ATR[sc.Index];
    
    // Determine base price
    float BasePrice = 0;
    switch (Input_BasePrice.GetIndex())
    {
        case 0: // Close
            BasePrice = sc.Close[sc.Index];
            break;
        case 1: // Open
            BasePrice = sc.Open[sc.Index];
            break;
        case 2: // High
            BasePrice = sc.High[sc.Index];
            break;
        case 3: // Low
            BasePrice = sc.Low[sc.Index];
            break;
        case 4: // HL2
            BasePrice = (sc.High[sc.Index] + sc.Low[sc.Index]) / 2;
            break;
        case 5: // HLC3
            BasePrice = (sc.High[sc.Index] + sc.Low[sc.Index] + sc.Close[sc.Index]) / 3;
            break;
        case 6: // OHLC4
            BasePrice = (sc.Open[sc.Index] + sc.High[sc.Index] + sc.Low[sc.Index] + sc.Close[sc.Index]) / 4;
            break;
    }
    
    // Calculate ATR ranges
    float ATRValue = Subgraph_ATR[sc.Index];
    
    if (ATRValue > 0)
    {
        // Set middle line
        Subgraph_ATRMiddle[sc.Index] = BasePrice;
        
        if (Input_DisplayRanges.GetYesNo())
        {
            // Calculate upper and lower ranges
            Subgraph_ATRUpper1[sc.Index] = BasePrice + (ATRValue * Input_ATRMultiplier1.GetFloat());
            Subgraph_ATRLower1[sc.Index] = BasePrice - (ATRValue * Input_ATRMultiplier1.GetFloat());
            
            Subgraph_ATRUpper2[sc.Index] = BasePrice + (ATRValue * Input_ATRMultiplier2.GetFloat());
            Subgraph_ATRLower2[sc.Index] = BasePrice - (ATRValue * Input_ATRMultiplier2.GetFloat());
            
            Subgraph_ATRUpper3[sc.Index] = BasePrice + (ATRValue * Input_ATRMultiplier3.GetFloat());
            Subgraph_ATRLower3[sc.Index] = BasePrice - (ATRValue * Input_ATRMultiplier3.GetFloat());
        }
        
        // Check for range breakouts and generate alerts
        if (Input_AlertOnBreakout.GetYesNo() && sc.Index > r_LastAlertBar)
        {
            float CurrentPrice = sc.Close[sc.Index];
            float Upper1 = Subgraph_ATRUpper1[sc.Index];
            float Lower1 = Subgraph_ATRLower1[sc.Index];
            
            if (CurrentPrice > Upper1)
            {
                sc.SetAlert(1, "Price broke above ATR Upper Range 1");
                r_LastAlertBar = sc.Index;
            }
            else if (CurrentPrice < Lower1)
            {
                sc.SetAlert(2, "Price broke below ATR Lower Range 1");
                r_LastAlertBar = sc.Index;
            }
        }
    }
    
    // Extend ranges forward if requested
    if (Input_ExtendRanges.GetYesNo() && sc.Index == sc.ArraySize - 1)
    {
        // Extend the last calculated values forward
        for (int FutureIndex = sc.Index + 1; FutureIndex < sc.Index + 50; FutureIndex++)
        {
            if (FutureIndex < sc.ArraySize)
            {
                Subgraph_ATRUpper1[FutureIndex] = Subgraph_ATRUpper1[sc.Index];
                Subgraph_ATRLower1[FutureIndex] = Subgraph_ATRLower1[sc.Index];
                Subgraph_ATRUpper2[FutureIndex] = Subgraph_ATRUpper2[sc.Index];
                Subgraph_ATRLower2[FutureIndex] = Subgraph_ATRLower2[sc.Index];
                Subgraph_ATRUpper3[FutureIndex] = Subgraph_ATRUpper3[sc.Index];
                Subgraph_ATRLower3[FutureIndex] = Subgraph_ATRLower3[sc.Index];
                Subgraph_ATRMiddle[FutureIndex] = Subgraph_ATRMiddle[sc.Index];
            }
        }
    }
}
