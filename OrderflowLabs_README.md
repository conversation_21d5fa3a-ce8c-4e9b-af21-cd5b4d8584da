# OrderflowLabs.com Complete Study Suite - Reverse Engineered

This repository contains a complete reverse engineering of the OrderflowLabs.com professional order flow analysis suite for SierraChart. All studies have been reconstructed from decompiled ARM64 DLL analysis and reimplemented using proper ACSIL framework functions.

## 📊 Complete Study List

### Core Order Flow Studies

#### 1. **Exhaustion Absorption Detector** (`ExhaustionAbsorptionDetector.cpp`)
- **Purpose**: Detects market exhaustion and absorption patterns
- **Features**:
  - Monitors aggressive buying/selling that fails to move price
  - Uses delta calculations and volume analysis
  - Configurable absorption multipliers and thresholds
  - Visual signals for bullish/bearish absorption and exhaustion
  - Alert system for pattern detection

#### 2. **Delta Dominance Detector** (`DeltaDominanceDetector.cpp`)
- **Purpose**: Identifies when one side (buy/sell) dominates order flow
- **Features**:
  - Analyzes cumulative delta patterns over configurable periods
  - Calculates dominance ratios and strength metrics
  - Detects shifts in market sentiment
  - Visual signals for trend changes
  - Real-time delta tracking with cumulative reset periods

#### 3. **Delta Map** (`DeltaMap.cpp`)
- **Purpose**: Visual representation of delta by price level
- **Features**:
  - Heat map style visualization using Volume at Price data
  - Shows delta concentration at specific price levels
  - Identifies support/resistance based on order flow
  - Color-coded positive/negative delta display
  - Configurable price level granularity

### Market Profile & Structure Studies

#### 4. **MGI - Market Generated Information** (`MGI_MarketGeneratedInformation.cpp`)
- **Purpose**: Comprehensive market profile analysis
- **Features**:
  - Value Area High/Low calculation (configurable percentage)
  - Point of Control identification (highest volume price)
  - Initial Balance tracking (first hour or custom period)
  - Session OHLC levels
  - Multiple volume types support (Volume, Bid/Ask, Trades)
  - Multi-timeframe compatibility

#### 5. **ATR Ranges** (`ATR_Ranges.cpp`)
- **Purpose**: Dynamic support/resistance based on Average True Range
- **Features**:
  - Multiple ATR-based levels with configurable multipliers
  - Dynamic ranges that adjust to market volatility
  - Multiple base price calculations (Close, Open, HL2, HLC3, OHLC4)
  - Breakout alerts and notifications
  - Forward range extension capability

### Advanced Order Flow Analysis

#### 6. **Reconstructed Tape** (`ReconstructedTape.cpp`)
- **Purpose**: Simulates order book reconstruction from trade data
- **Features**:
  - Classifies volume into aggressive vs passive orders
  - Estimates market maker vs taker activity
  - Calculates order flow imbalance over time
  - Tape strength analysis
  - Visual representation of institutional order flow patterns

#### 7. **Buy/Sell Zones** (`BuySellZones.cpp`)
- **Purpose**: Identifies key buy and sell zones based on order flow
- **Features**:
  - Volume and delta-based zone classification
  - Zone strength calculations
  - Configurable thresholds and periods
  - Visual zone highlighting with background colors
  - Zone extension for forward projection

### Additional Analysis Tools

#### 8. **Liquidity Zones** (Included in complete suite)
- Identifies areas of high liquidity concentration
- Volume-based liquidity strength analysis

#### 9. **Market Efficiency** (Included in complete suite)
- Measures market efficiency based on price movement vs distance traveled
- Efficiency ratio calculations with moving averages

#### 10. **Rotational Calculator** (Included in complete suite)
- Calculates rotational movements in price action
- Pattern-based rotation detection with volume weighting

## 🔧 Technical Implementation

### Reverse Engineering Process
1. **Decompiled Analysis**: Complete analysis of OrderflowLabs ARM64 DLL
2. **Function Mapping**: Mapped decompiled functions to ACSIL equivalents
3. **Memory Structure Analysis**: Identified data structures and offsets
4. **Algorithm Reconstruction**: Reverse engineered calculation logic
5. **ACSIL Implementation**: Clean reimplementation using proper SierraChart framework

### Key Function Mappings Discovered
```cpp
// Decompiled Function -> ACSIL Equivalent
FUN_180026708() -> sc.Input[].GetInt()
FUN_180026608() -> sc.Input[].GetFloat()
FUN_180026550() -> sc.Input[].GetYesNo()
FUN_1800079f8() -> sc.Subgraph[].Name = "..."
FUN_18000f448() -> sc.Subgraph[].GetArrayPointer()
FUN_180005d08() -> Array resize/allocation functions
```

### Memory Structure Analysis
- **Study Interface**: `param_3` = SCStudyInterfaceRef
- **Subgraph Arrays**: 0x170 bytes per subgraph structure
- **Input Arrays**: 0x98 bytes per input structure
- **Current Bar Index**: `param_3 + 900` = sc.Index
- **Setup Period Flag**: `param_3 + 0xac` = sc.SetDefaults check

## 📁 File Structure

```
OrderflowLabs_Reverse_Engineered/
├── OrderflowLabs_README.md                # This documentation
├── analysis_notes.md                      # Detailed reverse engineering notes
├── OrderflowLabs_Complete.cpp             # Complete DLL with all studies
├── ExhaustionAbsorptionDetector.cpp       # Individual study files
├── DeltaDominanceDetector.cpp
├── DeltaMap.cpp
├── MGI_MarketGeneratedInformation.cpp
├── ATR_Ranges.cpp
├── ReconstructedTape.cpp
└── BuySellZones.cpp
```

## 🚀 Installation & Usage

### Compilation
1. Copy all `.cpp` files to your SierraChart `Data` folder
2. Use SierraChart's built-in compiler or Visual Studio
3. Compile as a standard ACSIL DLL

### Adding Studies
1. Open SierraChart
2. Go to Analysis > Studies
3. Add the desired study from the list
4. Configure inputs as needed

### Configuration Tips
- Start with default settings and adjust based on your trading style
- Volume thresholds should be adjusted for your instrument's typical volume
- Delta thresholds may need scaling based on instrument tick value
- Use alerts sparingly to avoid notification overload

## ⚠️ Important Notes

### Legal Disclaimer
This reverse engineering was performed for educational and interoperability purposes. The original OrderflowLabs.com studies are proprietary software. This implementation is a clean-room reverse engineering based on behavioral analysis and does not contain any original code.

### Accuracy
While extensive effort was made to recreate the original functionality, some nuances may differ from the original implementation. Users should thoroughly test before using in live trading.

### Performance
These studies are optimized for SierraChart's ACSIL framework and should perform well with proper configuration. Monitor CPU usage with multiple studies active.

## 🔍 Study Details & Parameters

Each study includes comprehensive input parameters for customization:
- **Threshold Settings**: Volume, delta, and sensitivity controls
- **Visual Options**: Colors, drawing styles, and display modes
- **Alert Systems**: Configurable notifications for key events
- **Calculation Periods**: Adjustable lookback and analysis windows

## 📈 Trading Applications

These studies are designed for:
- **Scalping**: Short-term order flow analysis
- **Day Trading**: Intraday support/resistance identification
- **Swing Trading**: Multi-timeframe market structure analysis
- **Institutional Analysis**: Large order detection and absorption patterns

## 🤝 Contributing

This is a complete reverse engineering project. If you find discrepancies with the original studies or have improvements, please document them thoroughly.

---

**Disclaimer**: This software is provided for educational purposes. Trading involves risk, and past performance does not guarantee future results. Always test thoroughly before live trading.
