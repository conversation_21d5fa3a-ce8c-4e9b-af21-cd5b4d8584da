#include "sierrachart.h"

/*==========================================================================*/
SCSFExport scsf_BuySellZones(SCStudyInterfaceRef sc)
{
    // Subgraph declarations for buy/sell zones
    SCSubgraphRef Subgraph_BuyZone = sc.Subgraph[0];
    SCSubgraphRef Subgraph_SellZone = sc.Subgraph[1];
    SCSubgraphRef Subgraph_NeutralZone = sc.Subgraph[2];
    SCSubgraphRef Subgraph_ZoneStrength = sc.Subgraph[3];
    SCSubgraphRef Subgraph_VolumeProfile = sc.Subgraph[4];
    SCSubgraphRef Subgraph_DeltaZone = sc.Subgraph[5];
    
    // Input declarations
    SCInputRef Input_ZonePeriod = sc.Input[0];
    SCInputRef Input_VolumeThreshold = sc.Input[1];
    SCInputRef Input_DeltaThreshold = sc.Input[2];
    SCInputRef Input_ZoneExtension = sc.Input[3];
    SCInputRef Input_DisplayMode = sc.Input[4];
    
    // Persistent storage for zone data
    c_VAPContainer* p_VolumeAtPriceData = (c_VAPContainer*)sc.GetPersistentPointer(1);
    
    if (sc.SetDefaults)
    {
        // Set study configuration
        sc.GraphName = "Buy/Sell Zones";
        sc.StudyDescription = "Identifies key buy and sell zones based on order flow and volume analysis";
        sc.AutoLoop = 0; // Manual looping for zone calculations
        sc.GraphRegion = 0;
        sc.ValueFormat = 2;
        sc.ScaleRangeType = SCALE_SAMEASREGION;
        sc.MaintainVolumeAtPriceData = 1;
        
        // Configure subgraphs
        Subgraph_BuyZone.Name = "Buy Zone";
        Subgraph_BuyZone.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_BuyZone.PrimaryColor = RGB(0, 255, 0); // Green
        Subgraph_BuyZone.SecondaryColor = RGB(128, 255, 128); // Light Green
        Subgraph_BuyZone.LineWidth = 3;
        Subgraph_BuyZone.DrawZeros = false;
        
        Subgraph_SellZone.Name = "Sell Zone";
        Subgraph_SellZone.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_SellZone.PrimaryColor = RGB(255, 0, 0); // Red
        Subgraph_SellZone.SecondaryColor = RGB(255, 128, 128); // Light Red
        Subgraph_SellZone.LineWidth = 3;
        Subgraph_SellZone.DrawZeros = false;
        
        Subgraph_NeutralZone.Name = "Neutral Zone";
        Subgraph_NeutralZone.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_NeutralZone.PrimaryColor = RGB(128, 128, 128); // Gray
        Subgraph_NeutralZone.LineWidth = 1;
        Subgraph_NeutralZone.DrawZeros = false;
        
        Subgraph_ZoneStrength.Name = "Zone Strength";
        Subgraph_ZoneStrength.DrawStyle = DRAWSTYLE_IGNORE;
        
        Subgraph_VolumeProfile.Name = "Volume Profile";
        Subgraph_VolumeProfile.DrawStyle = DRAWSTYLE_IGNORE;
        
        Subgraph_DeltaZone.Name = "Delta Zone";
        Subgraph_DeltaZone.DrawStyle = DRAWSTYLE_IGNORE;
        
        // Configure inputs
        Input_ZonePeriod.Name = "Zone Calculation Period";
        Input_ZonePeriod.SetInt(20);
        Input_ZonePeriod.SetIntLimits(5, 100);
        
        Input_VolumeThreshold.Name = "Volume Threshold";
        Input_VolumeThreshold.SetInt(1000);
        Input_VolumeThreshold.SetIntLimits(100, 100000);
        
        Input_DeltaThreshold.Name = "Delta Threshold";
        Input_DeltaThreshold.SetInt(100);
        Input_DeltaThreshold.SetIntLimits(10, 10000);
        
        Input_ZoneExtension.Name = "Zone Extension Bars";
        Input_ZoneExtension.SetInt(10);
        Input_ZoneExtension.SetIntLimits(1, 50);
        
        Input_DisplayMode.Name = "Display Mode";
        Input_DisplayMode.SetCustomInputStrings("All Zones;Buy Zones Only;Sell Zones Only");
        Input_DisplayMode.SetCustomInputIndex(0);
        
        return;
    }
    
    // Initialize persistent data structure
    if (p_VolumeAtPriceData == NULL)
    {
        p_VolumeAtPriceData = new c_VAPContainer();
        sc.SetPersistentPointer(1, p_VolumeAtPriceData);
    }
    
    // Clear previous data on full recalculation
    if (sc.IsFullRecalculation && sc.UpdateStartIndex == 0)
    {
        p_VolumeAtPriceData->Clear();
        for (int SubgraphIndex = 0; SubgraphIndex <= 5; SubgraphIndex++)
            sc.Subgraph[SubgraphIndex].Clear();
    }
    
    // Calculate zones for the specified period
    int StartIndex = max(0, sc.ArraySize - 1 - Input_ZonePeriod.GetInt());
    int EndIndex = sc.ArraySize - 1;
    
    if (EndIndex < StartIndex)
        return;
    
    // Analyze volume and delta patterns over the period
    float TotalVolume = 0;
    float TotalBuyVolume = 0;
    float TotalSellVolume = 0;
    float HighestPrice = 0;
    float LowestPrice = FLT_MAX;
    
    // Collect data for zone analysis
    for (int Index = StartIndex; Index <= EndIndex; Index++)
    {
        float BarVolume = sc.Volume[Index];
        float BarBidVolume = sc.BidVolume[Index];
        float BarAskVolume = sc.AskVolume[Index];
        float BarHigh = sc.High[Index];
        float BarLow = sc.Low[Index];
        
        TotalVolume += BarVolume;
        TotalBuyVolume += BarAskVolume;
        TotalSellVolume += BarBidVolume;
        
        if (BarHigh > HighestPrice) HighestPrice = BarHigh;
        if (BarLow < LowestPrice) LowestPrice = BarLow;
        
        // Add to Volume at Price data
        s_VolumeAtPriceV2 VAPData;
        VAPData.PriceInTicks = sc.PriceValueToTicks((BarHigh + BarLow) / 2);
        VAPData.Volume = (unsigned int)BarVolume;
        VAPData.BidVolume = (unsigned int)BarBidVolume;
        VAPData.AskVolume = (unsigned int)BarAskVolume;
        
        p_VolumeAtPriceData->InsertVolumeAtPrice(VAPData);
    }
    
    // Calculate overall delta and volume metrics
    float NetDelta = TotalBuyVolume - TotalSellVolume;
    float DeltaRatio = (TotalVolume > 0) ? (NetDelta / TotalVolume) * 100 : 0;
    
    // Determine zone classification
    bool IsBuyZone = false;
    bool IsSellZone = false;
    bool IsNeutralZone = false;
    
    // Zone classification logic
    if (TotalVolume >= Input_VolumeThreshold.GetInt())
    {
        if (NetDelta > Input_DeltaThreshold.GetInt() && DeltaRatio > 10.0f)
        {
            IsBuyZone = true;
        }
        else if (NetDelta < -Input_DeltaThreshold.GetInt() && DeltaRatio < -10.0f)
        {
            IsSellZone = true;
        }
        else
        {
            IsNeutralZone = true;
        }
    }
    
    // Calculate zone strength based on volume concentration
    const s_VolumeAtPriceV2* p_VAPArray = NULL;
    int VAPArraySize = 0;
    p_VolumeAtPriceData->GetVAPElementsForRange(sc.PriceValueToTicks(LowestPrice), 
                                               sc.PriceValueToTicks(HighestPrice), 
                                               p_VAPArray, VAPArraySize);
    
    float ZoneStrength = 0;
    if (VAPArraySize > 0)
    {
        // Find the price level with highest volume
        unsigned int MaxVolume = 0;
        for (int i = 0; i < VAPArraySize; i++)
        {
            if (p_VAPArray[i].Volume > MaxVolume)
                MaxVolume = p_VAPArray[i].Volume;
        }
        
        ZoneStrength = (TotalVolume > 0) ? ((float)MaxVolume / TotalVolume) * 100 : 0;
    }
    
    // Set subgraph values for the zone period and extension
    int ExtensionEnd = min(EndIndex + Input_ZoneExtension.GetInt(), sc.ArraySize - 1);
    
    for (int Index = StartIndex; Index <= ExtensionEnd; Index++)
    {
        // Clear previous values
        Subgraph_BuyZone[Index] = 0;
        Subgraph_SellZone[Index] = 0;
        Subgraph_NeutralZone[Index] = 0;
        
        // Set zone values based on classification and display mode
        switch (Input_DisplayMode.GetIndex())
        {
            case 0: // All Zones
                if (IsBuyZone)
                    Subgraph_BuyZone[Index] = 1;
                else if (IsSellZone)
                    Subgraph_SellZone[Index] = 1;
                else if (IsNeutralZone)
                    Subgraph_NeutralZone[Index] = 1;
                break;
                
            case 1: // Buy Zones Only
                if (IsBuyZone)
                    Subgraph_BuyZone[Index] = 1;
                break;
                
            case 2: // Sell Zones Only
                if (IsSellZone)
                    Subgraph_SellZone[Index] = 1;
                break;
        }
        
        // Set analysis values
        Subgraph_ZoneStrength[Index] = ZoneStrength;
        Subgraph_VolumeProfile[Index] = TotalVolume;
        Subgraph_DeltaZone[Index] = DeltaRatio;
    }
}
