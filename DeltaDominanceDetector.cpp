#include "sierrachart.h"

/*==========================================================================*/
SCSFExport scsf_DeltaDominanceDetector(SCStudyInterfaceRef sc)
{
    // Subgraph declarations
    SCSubgraphRef Subgraph_BullishDominance = sc.Subgraph[0];
    SCSubgraphRef Subgraph_BearishDominance = sc.Subgraph[1];
    SCSubgraphRef Subgraph_DeltaLine = sc.Subgraph[2];
    SCSubgraphRef Subgraph_CumulativeDelta = sc.Subgraph[3];
    SCSubgraphRef Subgraph_DominanceStrength = sc.Subgraph[4];
    
    // Input declarations
    SCInputRef Input_DominanceThreshold = sc.Input[0];
    SCInputRef Input_LookbackPeriod = sc.Input[1];
    SCInputRef Input_MinimumVolume = sc.Input[2];
    SCInputRef Input_CumulativePeriod = sc.Input[3];
    SCInputRef Input_AlertsEnabled = sc.Input[4];
    
    // Persistent variables for cumulative calculations
    float& r_CumulativeDelta = sc.GetPersistentFloat(1);
    int& r_LastProcessedIndex = sc.GetPersistentInt(1);
    
    if (sc.SetDefaults)
    {
        // Set study configuration
        sc.GraphName = "Delta Dominance Detector";
        sc.StudyDescription = "Detects when one side (buy/sell) dominates order flow";
        sc.AutoLoop = 1;
        sc.GraphRegion = 1; // Separate region
        sc.ValueFormat = 0;
        sc.ScaleRangeType = SCALE_INDEPENDENT;
        
        // Configure subgraphs
        Subgraph_BullishDominance.Name = "Bullish Dominance";
        Subgraph_BullishDominance.DrawStyle = DRAWSTYLE_POINT;
        Subgraph_BullishDominance.PrimaryColor = RGB(0, 255, 0); // Green
        Subgraph_BullishDominance.LineWidth = 8;
        Subgraph_BullishDominance.DrawZeros = false;
        
        Subgraph_BearishDominance.Name = "Bearish Dominance";
        Subgraph_BearishDominance.DrawStyle = DRAWSTYLE_POINT;
        Subgraph_BearishDominance.PrimaryColor = RGB(255, 0, 0); // Red
        Subgraph_BearishDominance.LineWidth = 8;
        Subgraph_BearishDominance.DrawZeros = false;
        
        Subgraph_DeltaLine.Name = "Delta Line";
        Subgraph_DeltaLine.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_DeltaLine.PrimaryColor = RGB(128, 128, 128); // Gray
        Subgraph_DeltaLine.LineWidth = 1;
        Subgraph_DeltaLine.DrawZeros = true;
        
        Subgraph_CumulativeDelta.Name = "Cumulative Delta";
        Subgraph_CumulativeDelta.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_CumulativeDelta.PrimaryColor = RGB(255, 255, 0); // Yellow
        Subgraph_CumulativeDelta.LineWidth = 2;
        Subgraph_CumulativeDelta.DrawZeros = true;
        
        Subgraph_DominanceStrength.Name = "Dominance Strength";
        Subgraph_DominanceStrength.DrawStyle = DRAWSTYLE_IGNORE;
        
        // Configure inputs
        Input_DominanceThreshold.Name = "Dominance Threshold";
        Input_DominanceThreshold.SetInt(50);
        Input_DominanceThreshold.SetIntLimits(10, 1000);
        
        Input_LookbackPeriod.Name = "Lookback Period";
        Input_LookbackPeriod.SetInt(10);
        Input_LookbackPeriod.SetIntLimits(3, 100);
        
        Input_MinimumVolume.Name = "Minimum Volume";
        Input_MinimumVolume.SetInt(100);
        Input_MinimumVolume.SetIntLimits(1, 10000);
        
        Input_CumulativePeriod.Name = "Cumulative Reset Period";
        Input_CumulativePeriod.SetInt(50);
        Input_CumulativePeriod.SetIntLimits(10, 500);
        
        Input_AlertsEnabled.Name = "Enable Alerts";
        Input_AlertsEnabled.SetYesNo(false);
        
        return;
    }
    
    // Reset cumulative delta at start of new session or after reset period
    if (sc.Index == 0 || (sc.Index % Input_CumulativePeriod.GetInt() == 0))
    {
        r_CumulativeDelta = 0;
        r_LastProcessedIndex = 0;
    }
    
    // Get current bar data
    float CurrentVolume = sc.Volume[sc.Index];
    float CurrentBidVolume = sc.BidVolume[sc.Index];
    float CurrentAskVolume = sc.AskVolume[sc.Index];
    
    // Skip if insufficient volume
    if (CurrentVolume < Input_MinimumVolume.GetInt())
        return;
    
    // Calculate current delta
    float CurrentDelta = CurrentAskVolume - CurrentBidVolume;
    
    // Update cumulative delta
    if (sc.Index > r_LastProcessedIndex)
    {
        r_CumulativeDelta += CurrentDelta;
        r_LastProcessedIndex = sc.Index;
    }
    
    // Set delta line value
    Subgraph_DeltaLine[sc.Index] = CurrentDelta;
    Subgraph_CumulativeDelta[sc.Index] = r_CumulativeDelta;
    
    // Calculate dominance over lookback period
    if (sc.Index >= Input_LookbackPeriod.GetInt())
    {
        float TotalBullishDelta = 0;
        float TotalBearishDelta = 0;
        float TotalVolume = 0;
        
        // Sum delta over lookback period
        for (int i = 0; i < Input_LookbackPeriod.GetInt(); i++)
        {
            int LookbackIndex = sc.Index - i;
            if (LookbackIndex >= 0)
            {
                float LookbackBidVol = sc.BidVolume[LookbackIndex];
                float LookbackAskVol = sc.AskVolume[LookbackIndex];
                float LookbackDelta = LookbackAskVol - LookbackBidVol;
                float LookbackVolume = sc.Volume[LookbackIndex];
                
                if (LookbackDelta > 0)
                    TotalBullishDelta += LookbackDelta;
                else
                    TotalBearishDelta += abs(LookbackDelta);
                    
                TotalVolume += LookbackVolume;
            }
        }
        
        // Calculate dominance ratios
        float BullishRatio = (TotalVolume > 0) ? (TotalBullishDelta / TotalVolume) * 100 : 0;
        float BearishRatio = (TotalVolume > 0) ? (TotalBearishDelta / TotalVolume) * 100 : 0;
        
        // Store dominance strength
        float DominanceStrength = BullishRatio - BearishRatio;
        Subgraph_DominanceStrength[sc.Index] = DominanceStrength;
        
        // Detect dominance patterns
        bool BullishDominance = false;
        bool BearishDominance = false;
        
        // Check for bullish dominance
        if (TotalBullishDelta > Input_DominanceThreshold.GetInt() && 
            BullishRatio > 60.0f && 
            CurrentDelta > 0)
        {
            BullishDominance = true;
            Subgraph_BullishDominance[sc.Index] = 1;
        }
        
        // Check for bearish dominance
        if (TotalBearishDelta > Input_DominanceThreshold.GetInt() && 
            BearishRatio > 60.0f && 
            CurrentDelta < 0)
        {
            BearishDominance = true;
            Subgraph_BearishDominance[sc.Index] = -1;
        }
        
        // Generate alerts if enabled
        if (Input_AlertsEnabled.GetYesNo())
        {
            if (BullishDominance)
                sc.SetAlert(1, "Bullish Delta Dominance Detected");
            if (BearishDominance)
                sc.SetAlert(2, "Bearish Delta Dominance Detected");
        }
    }
}
