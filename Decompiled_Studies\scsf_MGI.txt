
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_MGI(undefined1 param_1 [16],ulonglong param_2,int *param_3,undefined8 param_4,
             undefined8 param_5,undefined8 param_6,undefined8 param_7,undefined8 param_8,
             undefined8 param_9,undefined8 param_10)

{
  undefined2 uVar1;
  undefined1 auVar2 [16];
  undefined1 auVar3 [16];
  undefined1 auVar4 [16];
  float *pfVar5;
  undefined8 *puVar6;
  float *pfVar7;
  int *piVar8;
  int *piVar9;
  longlong *plVar10;
  bool bVar11;
  int iVar12;
  uint uVar13;
  undefined8 *puVar14;
  float **ppfVar15;
  HANDLE pvVar16;
  undefined8 uVar17;
  ulonglong uVar18;
  ulonglong uVar19;
  int *piVar20;
  longlong *plVar21;
  float *pfVar22;
  float *pfVar23;
  float *pfVar24;
  char *pcVar25;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  undefined8 extraout_x1_10;
  undefined8 extraout_x1_11;
  undefined8 extraout_x1_12;
  undefined8 extraout_x1_13;
  undefined8 extraout_x1_14;
  undefined8 extraout_x1_15;
  undefined8 extraout_x1_16;
  undefined8 extraout_x1_17;
  undefined8 extraout_x1_18;
  undefined8 extraout_x1_19;
  undefined8 extraout_x1_20;
  undefined8 extraout_x1_21;
  undefined8 extraout_x1_22;
  undefined8 extraout_x1_23;
  undefined8 extraout_x1_24;
  undefined8 extraout_x1_25;
  undefined8 extraout_x1_26;
  undefined8 extraout_x1_27;
  undefined8 extraout_x1_28;
  undefined8 extraout_x1_29;
  undefined8 extraout_x1_30;
  undefined8 extraout_x1_31;
  undefined8 extraout_x1_32;
  undefined8 extraout_x1_33;
  undefined8 extraout_x1_34;
  undefined8 extraout_x1_35;
  undefined8 extraout_x1_36;
  undefined8 extraout_x1_37;
  undefined8 extraout_x1_38;
  undefined8 extraout_x1_39;
  undefined8 extraout_x1_40;
  undefined8 extraout_x1_41;
  undefined8 extraout_x1_42;
  undefined8 extraout_x1_43;
  undefined8 extraout_x1_44;
  undefined8 extraout_x1_45;
  undefined8 extraout_x1_46;
  undefined8 extraout_x1_47;
  undefined8 extraout_x1_48;
  undefined8 extraout_x1_49;
  undefined8 extraout_x1_50;
  undefined8 extraout_x1_51;
  undefined8 extraout_x1_52;
  undefined8 extraout_x1_53;
  undefined8 extraout_x1_54;
  undefined8 extraout_x1_55;
  undefined8 extraout_x1_56;
  undefined8 extraout_x1_57;
  undefined8 extraout_x1_58;
  ulonglong extraout_x1_59;
  int *piVar26;
  ulonglong extraout_x1_60;
  ulonglong extraout_x1_61;
  undefined8 extraout_x1_62;
  undefined8 extraout_x1_63;
  undefined8 extraout_x1_64;
  undefined8 extraout_x1_65;
  undefined8 extraout_x1_66;
  undefined8 extraout_x1_67;
  undefined8 extraout_x1_68;
  undefined8 extraout_x1_69;
  undefined8 extraout_x1_70;
  undefined8 extraout_x1_71;
  undefined8 extraout_x1_72;
  undefined8 extraout_x1_73;
  undefined8 extraout_x1_74;
  undefined8 extraout_x1_75;
  undefined8 extraout_x1_76;
  undefined8 extraout_x1_77;
  undefined8 extraout_x1_78;
  undefined8 extraout_x1_79;
  undefined8 extraout_x1_80;
  undefined8 extraout_x1_81;
  undefined8 extraout_x1_82;
  undefined8 extraout_x1_83;
  undefined8 extraout_x1_84;
  undefined8 extraout_x1_85;
  undefined8 extraout_x1_86;
  undefined8 extraout_x1_87;
  undefined8 extraout_x1_88;
  undefined8 extraout_x1_89;
  undefined8 extraout_x1_90;
  undefined8 extraout_x1_91;
  undefined8 extraout_x1_92;
  undefined8 extraout_x1_93;
  undefined8 extraout_x1_94;
  undefined8 extraout_x1_95;
  undefined8 extraout_x1_96;
  undefined8 extraout_x1_97;
  undefined8 extraout_x1_98;
  undefined8 extraout_x1_99;
  undefined8 extraout_x1_x00100;
  undefined8 extraout_x1_x00101;
  undefined8 extraout_x1_x00102;
  undefined8 extraout_x1_x00103;
  undefined8 extraout_x1_x00104;
  undefined8 extraout_x1_x00105;
  undefined8 extraout_x1_x00106;
  undefined8 extraout_x1_x00107;
  undefined8 extraout_x1_x00108;
  undefined8 extraout_x1_x00109;
  undefined8 extraout_x1_x00110;
  undefined8 extraout_x1_x00111;
  undefined8 extraout_x1_x00112;
  undefined8 extraout_x1_x00113;
  undefined8 extraout_x1_x00114;
  undefined8 extraout_x1_x00115;
  undefined8 extraout_x1_x00116;
  undefined8 extraout_x1_x00117;
  undefined8 extraout_x1_x00118;
  undefined8 extraout_x1_x00119;
  undefined8 extraout_x1_x00120;
  undefined8 extraout_x1_x00121;
  undefined8 extraout_x1_x00122;
  undefined8 extraout_x1_x00123;
  undefined8 extraout_x1_x00124;
  undefined8 extraout_x1_x00125;
  undefined8 extraout_x1_x00126;
  undefined8 extraout_x1_x00127;
  undefined8 extraout_x1_x00128;
  undefined8 extraout_x1_x00129;
  undefined8 extraout_x1_x00130;
  undefined8 extraout_x1_x00131;
  undefined8 extraout_x1_x00132;
  undefined8 extraout_x1_x00133;
  undefined8 extraout_x1_x00134;
  undefined8 extraout_x1_x00135;
  undefined8 extraout_x1_x00136;
  undefined8 extraout_x1_x00137;
  undefined8 extraout_x1_x00138;
  undefined8 extraout_x1_x00139;
  undefined8 extraout_x1_x00140;
  undefined8 extraout_x1_x00141;
  undefined8 extraout_x1_x00142;
  undefined8 extraout_x1_x00143;
  undefined8 extraout_x1_x00144;
  undefined8 extraout_x1_x00145;
  undefined8 extraout_x1_x00146;
  undefined8 extraout_x1_x00147;
  undefined8 extraout_x1_x00148;
  undefined8 extraout_x1_x00149;
  undefined8 extraout_x1_x00150;
  int iVar27;
  undefined8 *puVar28;
  longlong lVar29;
  code *extraout_x11;
  code *extraout_x11_00;
  code *extraout_x11_01;
  float *extraout_x11_02;
  longlong extraout_x11_03;
  float *extraout_x13;
  longlong extraout_x13_00;
  longlong extraout_x14;
  longlong extraout_x14_00;
  int iVar30;
  float **ppfVar31;
  undefined4 *puVar32;
  char cVar33;
  longlong *plVar34;
  float fVar35;
  longlong *plVar36;
  LPVOID pvVar37;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  undefined4 extraout_s0_02;
  undefined4 extraout_s0_03;
  undefined4 extraout_s0_04;
  undefined4 extraout_s0_05;
  undefined4 extraout_s0_06;
  undefined4 extraout_s0_07;
  undefined4 extraout_s0_08;
  undefined4 extraout_s0_09;
  undefined4 extraout_s0_10;
  undefined4 extraout_s0_11;
  undefined4 extraout_s0_12;
  undefined4 extraout_s0_13;
  undefined4 extraout_s0_14;
  undefined4 extraout_s0_15;
  undefined4 extraout_s0_16;
  undefined4 extraout_s0_17;
  undefined4 extraout_s0_18;
  undefined4 extraout_s0_19;
  undefined4 extraout_s0_20;
  undefined4 extraout_s0_21;
  undefined4 extraout_s0_22;
  undefined4 extraout_s0_23;
  undefined4 extraout_s0_24;
  undefined4 extraout_s0_25;
  undefined4 extraout_s0_26;
  undefined4 extraout_s0_27;
  undefined4 extraout_s0_28;
  undefined4 extraout_s0_29;
  undefined4 extraout_s0_30;
  undefined4 extraout_s0_31;
  undefined4 extraout_s0_32;
  undefined4 extraout_s0_33;
  undefined4 extraout_s0_34;
  undefined4 extraout_s0_35;
  undefined4 extraout_s0_36;
  undefined4 extraout_s0_37;
  undefined4 extraout_s0_38;
  undefined4 extraout_s0_39;
  undefined4 extraout_s0_40;
  undefined4 extraout_s0_41;
  undefined4 extraout_s0_42;
  undefined4 extraout_s0_43;
  undefined4 extraout_s0_44;
  undefined4 extraout_s0_45;
  undefined4 extraout_s0_46;
  undefined4 extraout_s0_47;
  undefined4 extraout_s0_48;
  undefined4 extraout_s0_49;
  undefined4 extraout_s0_50;
  undefined4 extraout_s0_51;
  undefined4 extraout_s0_52;
  undefined4 extraout_s0_53;
  undefined4 extraout_s0_54;
  undefined4 extraout_s0_55;
  undefined4 extraout_s0_56;
  undefined4 extraout_s0_57;
  undefined4 extraout_s0_58;
  undefined4 extraout_s0_59;
  undefined4 extraout_s0_60;
  undefined4 extraout_s0_61;
  float fVar38;
  undefined4 extraout_s0_62;
  undefined4 uVar39;
  float fVar40;
  float fVar41;
  undefined4 extraout_s0_63;
  undefined4 extraout_var;
  undefined4 extraout_var_00;
  undefined4 extraout_var_01;
  undefined4 extraout_var_02;
  undefined4 extraout_var_03;
  undefined4 extraout_var_04;
  undefined4 extraout_var_05;
  undefined4 extraout_var_06;
  undefined4 extraout_var_07;
  undefined4 extraout_var_08;
  undefined4 extraout_var_09;
  undefined4 extraout_var_10;
  undefined4 extraout_var_11;
  undefined4 extraout_var_12;
  undefined4 extraout_var_13;
  undefined4 extraout_var_14;
  undefined4 extraout_var_15;
  undefined4 extraout_var_16;
  undefined4 extraout_var_17;
  undefined4 extraout_var_18;
  undefined4 extraout_var_19;
  undefined4 extraout_var_20;
  undefined4 extraout_var_21;
  undefined4 extraout_var_22;
  undefined4 extraout_var_23;
  undefined4 extraout_var_24;
  undefined4 extraout_var_25;
  undefined4 extraout_var_26;
  undefined4 extraout_var_27;
  undefined4 extraout_var_28;
  undefined4 extraout_var_29;
  undefined4 extraout_var_30;
  undefined4 extraout_var_31;
  undefined4 extraout_var_32;
  undefined4 extraout_var_33;
  undefined4 extraout_var_34;
  undefined4 extraout_var_35;
  undefined4 extraout_var_36;
  undefined4 extraout_var_37;
  undefined4 extraout_var_38;
  undefined4 extraout_var_39;
  undefined4 extraout_var_40;
  undefined4 extraout_var_41;
  undefined4 extraout_var_42;
  undefined4 extraout_var_43;
  undefined4 extraout_var_44;
  undefined4 extraout_var_45;
  undefined4 extraout_var_46;
  undefined4 extraout_var_47;
  undefined4 extraout_var_48;
  undefined4 extraout_var_49;
  undefined4 extraout_var_50;
  undefined4 extraout_var_51;
  undefined4 extraout_var_52;
  undefined4 extraout_var_53;
  undefined4 extraout_var_54;
  undefined4 extraout_var_55;
  undefined4 extraout_var_56;
  undefined4 extraout_var_57;
  undefined4 extraout_var_58;
  undefined4 extraout_var_59;
  undefined4 extraout_var_60;
  undefined4 extraout_var_61;
  undefined4 extraout_var_62;
  undefined4 uVar42;
  undefined4 extraout_var_63;
  undefined8 extraout_var_64;
  undefined8 extraout_var_65;
  undefined8 extraout_var_66;
  undefined8 extraout_var_67;
  undefined8 extraout_var_68;
  undefined8 extraout_var_69;
  undefined8 extraout_var_70;
  undefined8 extraout_var_71;
  undefined8 extraout_var_72;
  undefined8 extraout_var_73;
  undefined8 extraout_var_74;
  undefined8 extraout_var_75;
  undefined8 extraout_var_76;
  undefined8 extraout_var_77;
  undefined8 extraout_var_78;
  undefined8 extraout_var_79;
  undefined8 extraout_var_80;
  undefined8 extraout_var_81;
  undefined8 extraout_var_82;
  undefined8 extraout_var_83;
  undefined8 extraout_var_84;
  undefined8 extraout_var_85;
  undefined8 extraout_var_86;
  undefined8 extraout_var_87;
  undefined8 extraout_var_88;
  undefined8 extraout_var_89;
  undefined8 extraout_var_90;
  undefined8 extraout_var_91;
  undefined8 extraout_var_92;
  undefined8 extraout_var_93;
  undefined8 extraout_var_94;
  undefined8 extraout_var_95;
  undefined8 extraout_var_96;
  undefined8 extraout_var_97;
  undefined8 extraout_var_98;
  undefined8 extraout_var_99;
  undefined8 extraout_var_x00100;
  undefined8 extraout_var_x00101;
  undefined8 extraout_var_x00102;
  undefined8 extraout_var_x00103;
  undefined8 extraout_var_x00104;
  undefined8 extraout_var_x00105;
  undefined8 extraout_var_x00106;
  undefined8 extraout_var_x00107;
  undefined8 extraout_var_x00108;
  undefined8 extraout_var_x00109;
  undefined8 extraout_var_x00110;
  undefined8 extraout_var_x00111;
  undefined8 extraout_var_x00112;
  undefined8 extraout_var_x00113;
  undefined8 extraout_var_x00114;
  undefined8 extraout_var_x00115;
  undefined8 extraout_var_x00116;
  undefined8 extraout_var_x00117;
  undefined8 extraout_var_x00118;
  undefined8 extraout_var_x00119;
  undefined8 extraout_var_x00120;
  undefined8 extraout_var_x00121;
  undefined8 extraout_var_x00122;
  undefined8 extraout_var_x00123;
  undefined8 uVar43;
  undefined8 extraout_var_x00124;
  undefined8 extraout_var_x00125;
  undefined8 extraout_var_x00126;
  undefined8 extraout_var_x00127;
  undefined8 extraout_var_x00128;
  undefined1 uVar44;
  undefined1 uVar45;
  undefined1 uVar46;
  undefined1 uVar47;
  float extraout_s18;
  undefined1 auVar48 [16];
  byte local_6e8;
  float *local_6e0;
  float *local_6d8;
  float *local_6d0;
  char local_6c8;
  float *local_6c0;
  float *local_6b8;
  float *local_6b0;
  float *local_6a8;
  longlong *local_6a0;
  longlong *local_698;
  float *local_690;
  longlong *local_688;
  float *local_680;
  longlong *local_678;
  longlong *local_670;
  float *local_668;
  undefined8 local_660;
  undefined8 local_658;
  float *local_650;
  float *local_648;
  longlong *local_640;
  longlong *local_638;
  float *local_630;
  float *local_628;
  float *local_620;
  float *local_618;
  float *local_610;
  float *local_608;
  float *local_600;
  float *local_5f8;
  float *local_5f0;
  float *local_5e8;
  float *local_5e0;
  longlong *local_5d8;
  int *local_5d0;
  int *local_5c8;
  int *local_5c0;
  float *local_5b8;
  float *local_5b0;
  float *local_5a8;
  float *local_5a0;
  longlong *local_598;
  longlong *local_590;
  float *local_588;
  float *local_580;
  float *local_578;
  float *local_570;
  float *local_568;
  float *local_560;
  float *local_558;
  float *local_550;
  float *local_548;
  float **local_540;
  undefined8 local_538;
  undefined1 *local_530;
  float *local_528;
  float *local_520;
  longlong *local_518;
  float *local_510;
  longlong *local_508;
  float *local_500;
  float *local_4f8;
  longlong *local_4f0;
  float *local_4e8;
  float *local_4e0;
  float *local_4d8;
  float *local_4d0;
  float *local_4c8;
  longlong *local_4c0;
  longlong *local_4b8;
  longlong *local_4b0;
  float *local_4a8;
  float *local_4a0;
  float *local_498;
  float *local_490;
  longlong *local_488;
  longlong *local_480;
  float *local_478;
  longlong *local_470;
  longlong *local_468;
  float *local_460;
  longlong *local_458;
  longlong *local_450;
  float *local_448;
  float *local_440;
  float *local_438;
  float *local_430;
  float *local_428;
  float *local_420;
  float *local_418;
  float *local_410;
  float *local_408;
  float *local_400;
  float *local_3f8;
  float *local_3f0;
  float *local_3e8;
  float *local_3e0;
  float *local_3d8;
  float *local_3d0;
  float *local_3c8;
  float *local_3c0;
  longlong *local_3b8;
  int *local_3b0;
  int *local_3a8;
  int *local_3a0;
  longlong *local_398;
  longlong *local_390;
  longlong *local_388;
  longlong *local_380;
  float *local_378;
  float *local_370;
  longlong *local_368;
  float *local_360;
  longlong *local_358;
  longlong *local_350;
  float *local_348;
  int *local_340;
  float *local_338;
  float *local_330;
  float *local_328;
  int *local_320;
  int *local_318;
  int *local_310;
  float *local_308;
  float *local_300;
  float *local_2f8;
  float *local_2f0;
  float *local_2e8;
  float **local_2e0;
  undefined8 local_2d8;
  float **local_2d0;
  undefined8 local_2c8;
  float **local_2c0;
  undefined8 local_2b8;
  float **local_2b0;
  undefined8 local_2a8;
  float **local_2a0;
  undefined8 local_298;
  float **local_290;
  undefined8 local_288;
  float *local_280;
  longlong local_278;
  undefined1 local_270;
  undefined7 uStack_26f;
  undefined8 local_260;
  ulonglong local_258;
  undefined8 *local_250;
  undefined8 local_248;
  undefined8 auStack_240 [2];
  float *local_230;
  longlong lStack_228;
  undefined **local_220 [2];
  undefined8 *local_210;
  undefined8 uStack_208;
  longlong local_200;
  undefined8 uStack_1f8;
  float *local_1f0;
  longlong local_1e8;
  undefined8 local_1e0;
  undefined8 uStack_1d8;
  undefined8 uStack_1d0;
  float *local_1c8;
  undefined8 local_1c0;
  undefined8 uStack_1b8;
  float *local_1b0;
  longlong local_1a8;
  float *local_1a0;
  undefined8 uStack_198;
  undefined8 uStack_190;
  undefined8 local_188;
  undefined8 local_180;
  undefined1 *local_170;
  undefined8 uStack_168;
  undefined1 *local_160;
  undefined1 *local_150;
  undefined8 uStack_148;
  undefined1 *local_140;
  longlong local_130;
  undefined8 uStack_128;
  undefined8 uStack_120;
  undefined8 uStack_118;
  undefined8 local_110;
  undefined8 uStack_108;
  undefined8 uStack_100;
  float *local_f8;
  undefined8 local_f0;
  undefined8 uStack_e8;
  float *local_e0;
  undefined8 uStack_d8;
  undefined8 local_d0;
  undefined8 uStack_c8;
  undefined8 uStack_c0;
  undefined8 uStack_b8;
  undefined8 local_b0;
  
                    /* 0x50128  13  scsf_MGI */
  local_248 = 0xfffffffffffffffe;
  local_160 = &DAT_1800d4ecd;
  local_170 = &DAT_1800d4ecd;
  uStack_168 = 0;
  local_660 = (int *)(**(code **)(param_3 + 0x62e))(param_1._0_4_,0);
  local_340 = (int *)(**(code **)(param_3 + 0x62e))(1);
  local_310 = (int *)(**(code **)(param_3 + 0x62e))(2);
  local_338 = (float *)(**(code **)(param_3 + 0x62e))(3);
  local_320 = (int *)(**(code **)(param_3 + 0x62e))(4);
  local_4e8 = (float *)(**(code **)(param_3 + 0x62e))(5);
  local_2e8 = (float *)(**(code **)(param_3 + 0x62e))(6);
  local_2f0 = (float *)(**(code **)(param_3 + 0x62e))(7);
  local_6c0 = (float *)(**(code **)(param_3 + 0x62e))(8);
  puVar14 = (undefined8 *)(**(code **)(param_3 + 0x446))(2);
  local_318 = (int *)*puVar14;
  puVar14 = (undefined8 *)(**(code **)(param_3 + 0x446))(3);
  local_658 = (float *)*puVar14;
  local_370 = local_658;
  puVar14 = (undefined8 *)(**(code **)(param_3 + 0x446))(4);
  local_360 = (float *)*puVar14;
  local_330 = local_360;
  puVar14 = (undefined8 *)(**(code **)(param_3 + 0x446))(5);
  local_4a8 = (float *)*puVar14;
  local_328 = local_4a8;
  puVar14 = (undefined8 *)(**(code **)(param_3 + 0x446))(6);
  local_500 = (float *)*puVar14;
  local_348 = local_500;
  puVar14 = (undefined8 *)(**(code **)(param_3 + 0x446))(7);
  local_6e0 = (float *)*puVar14;
  puVar14 = (undefined8 *)(**(code **)(param_3 + 0x446))(8);
  lVar29 = *(longlong *)(param_3 + 0x140);
  local_6d8 = (float *)*puVar14;
  uVar17 = extraout_x1;
  uVar39 = extraout_s0;
  uVar42 = extraout_var;
  uVar43 = extraout_var_64;
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_00;
      uVar39 = extraout_s0_00;
      uVar42 = extraout_var_00;
      uVar43 = extraout_var_65;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050304;
    local_4c8 = (float *)(param_3 + 0x14a);
LAB_180050338:
    local_610 = local_4c8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_01;
      uVar39 = extraout_s0_01;
      uVar42 = extraout_var_01;
      uVar43 = extraout_var_66;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050368;
    local_4f8 = (float *)(param_3 + 0x14a);
LAB_1800503a0:
    local_618 = local_4f8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_02;
      uVar39 = extraout_s0_02;
      uVar42 = extraout_var_02;
      uVar43 = extraout_var_67;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_1800503d0;
    local_4e0 = (float *)(param_3 + 0x14a);
LAB_180050408:
    local_620 = local_4e0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_03;
      uVar39 = extraout_s0_03;
      uVar42 = extraout_var_03;
      uVar43 = extraout_var_68;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050438;
    local_4d8 = (float *)(param_3 + 0x14a);
LAB_180050470:
    local_628 = local_4d8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_04;
      uVar39 = extraout_s0_04;
      uVar42 = extraout_var_04;
      uVar43 = extraout_var_69;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_1800504a0;
    local_4d0 = (float *)(param_3 + 0x14a);
LAB_1800504d8:
    local_630 = local_4d0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_05;
      uVar39 = extraout_s0_05;
      uVar42 = extraout_var_05;
      uVar43 = extraout_var_70;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050508;
    local_398 = (longlong *)(param_3 + 0x14a);
LAB_180050540:
    local_638 = local_398;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_06;
      uVar39 = extraout_s0_06;
      uVar42 = extraout_var_06;
      uVar43 = extraout_var_71;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050570;
    local_3a8 = param_3 + 0x14a;
LAB_1800505a8:
    local_5c0 = local_3a8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_07;
      uVar39 = extraout_s0_07;
      uVar42 = extraout_var_07;
      uVar43 = extraout_var_72;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_1800505d8;
    local_3b0 = param_3 + 0x14a;
LAB_180050610:
    local_5c8 = local_3b0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_08;
      uVar39 = extraout_s0_08;
      uVar42 = extraout_var_08;
      uVar43 = extraout_var_73;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050640;
    local_3a0 = param_3 + 0x14a;
LAB_180050678:
    local_5d0 = local_3a0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_09;
      uVar39 = extraout_s0_09;
      uVar42 = extraout_var_09;
      uVar43 = extraout_var_74;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_1800506a8;
    local_3b8 = (longlong *)(param_3 + 0x14a);
LAB_1800506e0:
    local_640 = local_3b8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_10;
      uVar39 = extraout_s0_10;
      uVar42 = extraout_var_10;
      uVar43 = extraout_var_75;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050714;
    local_308 = (float *)(param_3 + 0x14a);
LAB_180050750:
    local_6a8 = local_308;
    local_520 = local_308;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_11;
      uVar39 = extraout_s0_11;
      uVar42 = extraout_var_11;
      uVar43 = extraout_var_76;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050784;
    local_2f8 = (float *)(param_3 + 0x14a);
LAB_1800507c0:
    local_6b0 = local_2f8;
    local_528 = local_2f8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_12;
      uVar39 = extraout_s0_12;
      uVar42 = extraout_var_12;
      uVar43 = extraout_var_77;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_1800507f4;
    local_300 = (float *)(param_3 + 0x14a);
LAB_180050830:
    local_6b8 = local_300;
    local_510 = local_300;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_13;
      uVar39 = extraout_s0_13;
      uVar42 = extraout_var_13;
      uVar43 = extraout_var_78;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050860;
    local_378 = (float *)(param_3 + 0x14a);
LAB_180050898:
    local_5b8 = local_378;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_14;
      uVar39 = extraout_s0_14;
      uVar42 = extraout_var_14;
      uVar43 = extraout_var_79;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_1800508c8;
    local_3c0 = (float *)(param_3 + 0x14a);
LAB_180050900:
    local_5b0 = local_3c0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_15;
      uVar39 = extraout_s0_15;
      uVar42 = extraout_var_15;
      uVar43 = extraout_var_80;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050930;
    local_3c8 = (float *)(param_3 + 0x14a);
LAB_180050968:
    local_5a8 = local_3c8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_16;
      uVar39 = extraout_s0_16;
      uVar42 = extraout_var_16;
      uVar43 = extraout_var_81;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050998;
    local_3d8 = (float *)(param_3 + 0x14a);
LAB_1800509d0:
    local_608 = local_3d8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_17;
      uVar39 = extraout_s0_17;
      uVar42 = extraout_var_17;
      uVar43 = extraout_var_82;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050a00;
    local_3e0 = (float *)(param_3 + 0x14a);
LAB_180050a38:
    local_600 = local_3e0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_18;
      uVar39 = extraout_s0_18;
      uVar42 = extraout_var_18;
      uVar43 = extraout_var_83;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050a68;
    local_3e8 = (float *)(param_3 + 0x14a);
LAB_180050aa0:
    local_548 = local_3e8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_19;
      uVar39 = extraout_s0_19;
      uVar42 = extraout_var_19;
      uVar43 = extraout_var_84;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050ad0;
    local_3f0 = (float *)(param_3 + 0x14a);
LAB_180050b08:
    local_550 = local_3f0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_20;
      uVar39 = extraout_s0_20;
      uVar42 = extraout_var_20;
      uVar43 = extraout_var_85;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050b38;
    local_3f8 = (float *)(param_3 + 0x14a);
LAB_180050b70:
    local_558 = local_3f8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_21;
      uVar39 = extraout_s0_21;
      uVar42 = extraout_var_21;
      uVar43 = extraout_var_86;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050ba0;
    local_400 = (float *)(param_3 + 0x14a);
LAB_180050bd8:
    local_650 = local_400;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_22;
      uVar39 = extraout_s0_22;
      uVar42 = extraout_var_22;
      uVar43 = extraout_var_87;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050c08;
    local_408 = (float *)(param_3 + 0x14a);
LAB_180050c40:
    local_5f8 = local_408;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_23;
      uVar39 = extraout_s0_23;
      uVar42 = extraout_var_23;
      uVar43 = extraout_var_88;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050c70;
    local_418 = (float *)(param_3 + 0x14a);
LAB_180050ca8:
    local_5e8 = local_418;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_24;
      uVar39 = extraout_s0_24;
      uVar42 = extraout_var_24;
      uVar43 = extraout_var_89;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050cd8;
    local_420 = (float *)(param_3 + 0x14a);
LAB_180050d10:
    local_5e0 = local_420;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_25;
      uVar39 = extraout_s0_25;
      uVar42 = extraout_var_25;
      uVar43 = extraout_var_90;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050d40;
    local_428 = (float *)(param_3 + 0x14a);
LAB_180050d78:
    local_560 = local_428;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_26;
      uVar39 = extraout_s0_26;
      uVar42 = extraout_var_26;
      uVar43 = extraout_var_91;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050da8;
    local_430 = (float *)(param_3 + 0x14a);
LAB_180050de0:
    local_568 = local_430;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_27;
      uVar39 = extraout_s0_27;
      uVar42 = extraout_var_27;
      uVar43 = extraout_var_92;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050e10;
    local_438 = (float *)(param_3 + 0x14a);
LAB_180050e48:
    local_570 = local_438;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_28;
      uVar39 = extraout_s0_28;
      uVar42 = extraout_var_28;
      uVar43 = extraout_var_93;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050e78;
    local_440 = (float *)(param_3 + 0x14a);
LAB_180050eb0:
    local_578 = local_440;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_29;
      uVar39 = extraout_s0_29;
      uVar42 = extraout_var_29;
      uVar43 = extraout_var_94;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050ee0;
    local_448 = (float *)(param_3 + 0x14a);
LAB_180050f18:
    local_580 = local_448;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_30;
      uVar39 = extraout_s0_30;
      uVar42 = extraout_var_30;
      uVar43 = extraout_var_95;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050f48;
    local_498 = (float *)(param_3 + 0x14a);
LAB_180050f80:
    local_588 = local_498;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_31;
      uVar39 = extraout_s0_31;
      uVar42 = extraout_var_31;
      uVar43 = extraout_var_96;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180050fb0;
    local_450 = (longlong *)(param_3 + 0x14a);
LAB_180050fe8:
    local_590 = local_450;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_32;
      uVar39 = extraout_s0_32;
      uVar42 = extraout_var_32;
      uVar43 = extraout_var_97;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180051018;
    local_458 = (longlong *)(param_3 + 0x14a);
LAB_180051050:
    local_598 = local_458;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_33;
      uVar39 = extraout_s0_33;
      uVar42 = extraout_var_33;
      uVar43 = extraout_var_98;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180051080;
    local_460 = (float *)(param_3 + 0x14a);
LAB_1800510b8:
    local_690 = local_460;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_34;
      uVar39 = extraout_s0_34;
      uVar42 = extraout_var_34;
      uVar43 = extraout_var_99;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_1800510e8;
    local_468 = (longlong *)(param_3 + 0x14a);
LAB_180051120:
    local_698 = local_468;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_35;
      uVar39 = extraout_s0_35;
      uVar42 = extraout_var_35;
      uVar43 = extraout_var_x00100;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180051150;
    local_470 = (longlong *)(param_3 + 0x14a);
LAB_180051188:
    local_6a0 = local_470;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_36;
      uVar39 = extraout_s0_36;
      uVar42 = extraout_var_36;
      uVar43 = extraout_var_x00101;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_1800511b8;
    local_478 = (float *)(param_3 + 0x14a);
LAB_1800511f0:
    local_668 = local_478;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_37;
      uVar39 = extraout_s0_37;
      uVar42 = extraout_var_37;
      uVar43 = extraout_var_x00102;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180051220;
    local_480 = (longlong *)(param_3 + 0x14a);
LAB_180051258:
    local_670 = local_480;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_38;
      uVar39 = extraout_s0_38;
      uVar42 = extraout_var_38;
      uVar43 = extraout_var_x00103;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180051288;
    local_488 = (longlong *)(param_3 + 0x14a);
LAB_1800512c0:
    local_678 = local_488;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_39;
      uVar39 = extraout_s0_39;
      uVar42 = extraout_var_39;
      uVar43 = extraout_var_x00104;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_1800512f0;
    local_490 = (float *)(param_3 + 0x14a);
LAB_180051328:
    local_680 = local_490;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_40;
      uVar39 = extraout_s0_40;
      uVar42 = extraout_var_40;
      uVar43 = extraout_var_x00105;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180051358;
    local_4b0 = (longlong *)(param_3 + 0x14a);
LAB_180051390:
    local_688 = local_4b0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_41;
      uVar39 = extraout_s0_41;
      uVar42 = extraout_var_41;
      uVar43 = extraout_var_x00106;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_1800513c0;
    local_4b8 = (longlong *)(param_3 + 0x14a);
LAB_1800513f8:
    local_518 = local_4b8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_42;
      uVar39 = extraout_s0_42;
      uVar42 = extraout_var_42;
      uVar43 = extraout_var_x00107;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180051428;
    local_3d0 = (float *)(param_3 + 0x14a);
LAB_180051460:
    local_5a0 = local_3d0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_43;
      uVar39 = extraout_s0_43;
      uVar42 = extraout_var_43;
      uVar43 = extraout_var_x00108;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_180051490;
    local_410 = (float *)(param_3 + 0x14a);
LAB_1800514c8:
    local_5f0 = local_410;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_44;
      uVar39 = extraout_s0_44;
      uVar42 = extraout_var_44;
      uVar43 = extraout_var_x00109;
    }
    lVar29 = *(longlong *)(param_3 + 0x140);
    if (lVar29 != 0) goto LAB_1800514e8;
    local_4a0 = (float *)(param_3 + 0x14a);
  }
  else {
LAB_180050304:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_610 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0;
      if (iVar12 < 1) {
        iVar27 = iVar12 + -1;
      }
      local_610 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_4c8 = local_610;
    if (lVar29 == 0) goto LAB_180050338;
LAB_180050368:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_618 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 1;
      if (iVar12 < 2) {
        iVar27 = iVar12 + -1;
      }
      local_618 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_4f8 = local_618;
    if (lVar29 == 0) goto LAB_1800503a0;
LAB_1800503d0:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_620 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 2;
      if (iVar12 < 3) {
        iVar27 = iVar12 + -1;
      }
      local_620 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_4e0 = local_620;
    if (lVar29 == 0) goto LAB_180050408;
LAB_180050438:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_628 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 3;
      if (iVar12 < 4) {
        iVar27 = iVar12 + -1;
      }
      local_628 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_4d8 = local_628;
    if (lVar29 == 0) goto LAB_180050470;
LAB_1800504a0:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_630 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 4;
      if (iVar12 < 5) {
        iVar27 = iVar12 + -1;
      }
      local_630 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_4d0 = local_630;
    if (lVar29 == 0) goto LAB_1800504d8;
LAB_180050508:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_638 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 5;
      if (iVar12 < 6) {
        iVar27 = iVar12 + -1;
      }
      local_638 = (longlong *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_398 = local_638;
    if (lVar29 == 0) goto LAB_180050540;
LAB_180050570:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_5c0 = param_3 + 0x14a;
    }
    else {
      iVar27 = 6;
      if (iVar12 < 7) {
        iVar27 = iVar12 + -1;
      }
      local_5c0 = (int *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_3a8 = local_5c0;
    if (lVar29 == 0) goto LAB_1800505a8;
LAB_1800505d8:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_5c8 = param_3 + 0x14a;
    }
    else {
      iVar27 = 7;
      if (iVar12 < 8) {
        iVar27 = iVar12 + -1;
      }
      local_5c8 = (int *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_3b0 = local_5c8;
    if (lVar29 == 0) goto LAB_180050610;
LAB_180050640:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_5d0 = param_3 + 0x14a;
    }
    else {
      iVar27 = 8;
      if (iVar12 < 9) {
        iVar27 = iVar12 + -1;
      }
      local_5d0 = (int *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_3a0 = local_5d0;
    if (lVar29 == 0) goto LAB_180050678;
LAB_1800506a8:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_640 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 9;
      if (iVar12 < 10) {
        iVar27 = iVar12 + -1;
      }
      local_640 = (longlong *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_3b8 = local_640;
    if (lVar29 == 0) goto LAB_1800506e0;
LAB_180050714:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_6a8 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 10;
      if (iVar12 < 0xb) {
        iVar27 = iVar12 + -1;
      }
      local_6a8 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_520 = local_6a8;
    local_308 = local_6a8;
    if (lVar29 == 0) goto LAB_180050750;
LAB_180050784:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_6b0 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0xb;
      if (iVar12 < 0xc) {
        iVar27 = iVar12 + -1;
      }
      local_6b0 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_528 = local_6b0;
    local_2f8 = local_6b0;
    if (lVar29 == 0) goto LAB_1800507c0;
LAB_1800507f4:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_6b8 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0xc;
      if (iVar12 < 0xd) {
        iVar27 = iVar12 + -1;
      }
      local_6b8 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_510 = local_6b8;
    local_300 = local_6b8;
    if (lVar29 == 0) goto LAB_180050830;
LAB_180050860:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_5b8 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0xd;
      if (iVar12 < 0xe) {
        iVar27 = iVar12 + -1;
      }
      local_5b8 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_378 = local_5b8;
    if (lVar29 == 0) goto LAB_180050898;
LAB_1800508c8:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_5b0 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0xe;
      if (iVar12 < 0xf) {
        iVar27 = iVar12 + -1;
      }
      local_5b0 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_3c0 = local_5b0;
    if (lVar29 == 0) goto LAB_180050900;
LAB_180050930:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_5a8 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0xf;
      if (iVar12 < 0x10) {
        iVar27 = iVar12 + -1;
      }
      local_5a8 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_3c8 = local_5a8;
    if (lVar29 == 0) goto LAB_180050968;
LAB_180050998:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_608 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x10;
      if (iVar12 < 0x11) {
        iVar27 = iVar12 + -1;
      }
      local_608 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_3d8 = local_608;
    if (lVar29 == 0) goto LAB_1800509d0;
LAB_180050a00:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_600 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x11;
      if (iVar12 < 0x12) {
        iVar27 = iVar12 + -1;
      }
      local_600 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_3e0 = local_600;
    if (lVar29 == 0) goto LAB_180050a38;
LAB_180050a68:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_548 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x12;
      if (iVar12 < 0x13) {
        iVar27 = iVar12 + -1;
      }
      local_548 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_3e8 = local_548;
    if (lVar29 == 0) goto LAB_180050aa0;
LAB_180050ad0:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_550 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x13;
      if (iVar12 < 0x14) {
        iVar27 = iVar12 + -1;
      }
      local_550 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_3f0 = local_550;
    if (lVar29 == 0) goto LAB_180050b08;
LAB_180050b38:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_558 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x14;
      if (iVar12 < 0x15) {
        iVar27 = iVar12 + -1;
      }
      local_558 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_3f8 = local_558;
    if (lVar29 == 0) goto LAB_180050b70;
LAB_180050ba0:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_650 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x15;
      if (iVar12 < 0x16) {
        iVar27 = iVar12 + -1;
      }
      local_650 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_400 = local_650;
    if (lVar29 == 0) goto LAB_180050bd8;
LAB_180050c08:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_5f8 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x16;
      if (iVar12 < 0x17) {
        iVar27 = iVar12 + -1;
      }
      local_5f8 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_408 = local_5f8;
    if (lVar29 == 0) goto LAB_180050c40;
LAB_180050c70:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_5e8 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x17;
      if (iVar12 < 0x18) {
        iVar27 = iVar12 + -1;
      }
      local_5e8 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_418 = local_5e8;
    if (lVar29 == 0) goto LAB_180050ca8;
LAB_180050cd8:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_5e0 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x18;
      if (iVar12 < 0x19) {
        iVar27 = iVar12 + -1;
      }
      local_5e0 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_420 = local_5e0;
    if (lVar29 == 0) goto LAB_180050d10;
LAB_180050d40:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_560 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x19;
      if (iVar12 < 0x1a) {
        iVar27 = iVar12 + -1;
      }
      local_560 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_428 = local_560;
    if (lVar29 == 0) goto LAB_180050d78;
LAB_180050da8:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_568 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x1a;
      if (iVar12 < 0x1b) {
        iVar27 = iVar12 + -1;
      }
      local_568 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_430 = local_568;
    if (lVar29 == 0) goto LAB_180050de0;
LAB_180050e10:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_570 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x1b;
      if (iVar12 < 0x1c) {
        iVar27 = iVar12 + -1;
      }
      local_570 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_438 = local_570;
    if (lVar29 == 0) goto LAB_180050e48;
LAB_180050e78:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_578 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x1c;
      if (iVar12 < 0x1d) {
        iVar27 = iVar12 + -1;
      }
      local_578 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_440 = local_578;
    if (lVar29 == 0) goto LAB_180050eb0;
LAB_180050ee0:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_580 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x1d;
      if (iVar12 < 0x1e) {
        iVar27 = iVar12 + -1;
      }
      local_580 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_448 = local_580;
    if (lVar29 == 0) goto LAB_180050f18;
LAB_180050f48:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_588 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x1e;
      if (iVar12 < 0x1f) {
        iVar27 = iVar12 + -1;
      }
      local_588 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_498 = local_588;
    if (lVar29 == 0) goto LAB_180050f80;
LAB_180050fb0:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_590 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x1f;
      if (iVar12 < 0x20) {
        iVar27 = iVar12 + -1;
      }
      local_590 = (longlong *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_450 = local_590;
    if (lVar29 == 0) goto LAB_180050fe8;
LAB_180051018:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_598 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x20;
      if (iVar12 < 0x21) {
        iVar27 = iVar12 + -1;
      }
      local_598 = (longlong *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_458 = local_598;
    if (lVar29 == 0) goto LAB_180051050;
LAB_180051080:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_690 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x21;
      if (iVar12 < 0x22) {
        iVar27 = iVar12 + -1;
      }
      local_690 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_460 = local_690;
    if (lVar29 == 0) goto LAB_1800510b8;
LAB_1800510e8:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_698 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x22;
      if (iVar12 < 0x23) {
        iVar27 = iVar12 + -1;
      }
      local_698 = (longlong *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_468 = local_698;
    if (lVar29 == 0) goto LAB_180051120;
LAB_180051150:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_6a0 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x23;
      if (iVar12 < 0x24) {
        iVar27 = iVar12 + -1;
      }
      local_6a0 = (longlong *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_470 = local_6a0;
    if (lVar29 == 0) goto LAB_180051188;
LAB_1800511b8:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_668 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x24;
      if (iVar12 < 0x25) {
        iVar27 = iVar12 + -1;
      }
      local_668 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_478 = local_668;
    if (lVar29 == 0) goto LAB_1800511f0;
LAB_180051220:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_670 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x25;
      if (iVar12 < 0x26) {
        iVar27 = iVar12 + -1;
      }
      local_670 = (longlong *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_480 = local_670;
    if (lVar29 == 0) goto LAB_180051258;
LAB_180051288:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_678 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x26;
      if (iVar12 < 0x27) {
        iVar27 = iVar12 + -1;
      }
      local_678 = (longlong *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_488 = local_678;
    if (lVar29 == 0) goto LAB_1800512c0;
LAB_1800512f0:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_680 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x27;
      if (iVar12 < 0x28) {
        iVar27 = iVar12 + -1;
      }
      local_680 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_490 = local_680;
    if (lVar29 == 0) goto LAB_180051328;
LAB_180051358:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_688 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x28;
      if (iVar12 < 0x29) {
        iVar27 = iVar12 + -1;
      }
      local_688 = (longlong *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_4b0 = local_688;
    if (lVar29 == 0) goto LAB_180051390;
LAB_1800513c0:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_518 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x29;
      if (iVar12 < 0x2a) {
        iVar27 = iVar12 + -1;
      }
      local_518 = (longlong *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_4b8 = local_518;
    if (lVar29 == 0) goto LAB_1800513f8;
LAB_180051428:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_5a0 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x2a;
      if (iVar12 < 0x2b) {
        iVar27 = iVar12 + -1;
      }
      local_5a0 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_3d0 = local_5a0;
    if (lVar29 == 0) goto LAB_180051460;
LAB_180051490:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_5f0 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x2b;
      if (iVar12 < 0x2c) {
        iVar27 = iVar12 + -1;
      }
      local_5f0 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
    local_410 = local_5f0;
    if (lVar29 == 0) goto LAB_1800514c8;
LAB_1800514e8:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_4a0 = (float *)(param_3 + 0x14a);
    }
    else {
      iVar27 = 0x2c;
      if (iVar12 < 0x2d) {
        iVar27 = iVar12 + -1;
      }
      local_4a0 = (float *)(lVar29 + (longlong)iVar27 * 0x170);
    }
  }
  lVar29 = *(longlong *)(param_3 + 0x84);
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_45;
      uVar39 = extraout_s0_45;
      uVar42 = extraout_var_45;
      uVar43 = extraout_var_x00110;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18005153c;
    local_4f0 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_18005153c:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_4f0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar27 = 0;
      if (iVar12 < 1) {
        iVar27 = iVar12 + -1;
      }
      local_4f0 = (longlong *)(lVar29 + (longlong)iVar27 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_4f0 + 0x1a) = 1;
  lVar29 = *(longlong *)(param_3 + 0x84);
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_46;
      uVar39 = extraout_s0_46;
      uVar42 = extraout_var_46;
      uVar43 = extraout_var_x00111;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_180051590;
    local_4c0 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_180051590:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_4c0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar27 = 1;
      if (iVar12 < 2) {
        iVar27 = iVar12 + -1;
      }
      local_4c0 = (longlong *)(lVar29 + (longlong)iVar27 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_4c0 + 0x1a) = 2;
  lVar29 = *(longlong *)(param_3 + 0x84);
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_47;
      uVar39 = extraout_s0_47;
      uVar42 = extraout_var_47;
      uVar43 = extraout_var_x00112;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_1800515e8;
    local_390 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_1800515e8:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_390 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar27 = 2;
      if (iVar12 < 3) {
        iVar27 = iVar12 + -1;
      }
      local_390 = (longlong *)(lVar29 + (longlong)iVar27 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_390 + 0x1a) = 3;
  lVar29 = *(longlong *)(param_3 + 0x84);
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_48;
      uVar39 = extraout_s0_48;
      uVar42 = extraout_var_48;
      uVar43 = extraout_var_x00113;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_180051640;
    local_350 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_180051640:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_350 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar27 = 3;
      if (iVar12 < 4) {
        iVar27 = iVar12 + -1;
      }
      local_350 = (longlong *)(lVar29 + (longlong)iVar27 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_350 + 0x1a) = 4;
  lVar29 = *(longlong *)(param_3 + 0x84);
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_49;
      uVar39 = extraout_s0_49;
      uVar42 = extraout_var_49;
      uVar43 = extraout_var_x00114;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_180051694;
    local_358 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_180051694:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_358 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar27 = 4;
      if (iVar12 < 5) {
        iVar27 = iVar12 + -1;
      }
      local_358 = (longlong *)(lVar29 + (longlong)iVar27 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_358 + 0x1a) = 5;
  lVar29 = *(longlong *)(param_3 + 0x84);
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_50;
      uVar39 = extraout_s0_50;
      uVar42 = extraout_var_50;
      uVar43 = extraout_var_x00115;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_1800516ec;
    local_388 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_1800516ec:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_388 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar27 = 5;
      if (iVar12 < 6) {
        iVar27 = iVar12 + -1;
      }
      local_388 = (longlong *)(lVar29 + (longlong)iVar27 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_388 + 0x1a) = 6;
  lVar29 = *(longlong *)(param_3 + 0x84);
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_51;
      uVar39 = extraout_s0_51;
      uVar42 = extraout_var_51;
      uVar43 = extraout_var_x00116;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_180051740;
    local_648 = (float *)(param_3 + 0x8e);
  }
  else {
LAB_180051740:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_648 = (float *)(param_3 + 0x8e);
    }
    else {
      iVar27 = 6;
      if (iVar12 < 7) {
        iVar27 = iVar12 + -1;
      }
      local_648 = (float *)(lVar29 + (longlong)iVar27 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_648 + 0x1a) = 7;
  lVar29 = *(longlong *)(param_3 + 0x84);
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_52;
      uVar39 = extraout_s0_52;
      uVar42 = extraout_var_52;
      uVar43 = extraout_var_x00117;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_180051798;
    local_380 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_180051798:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_380 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar27 = 7;
      if (iVar12 < 8) {
        iVar27 = iVar12 + -1;
      }
      local_380 = (longlong *)(lVar29 + (longlong)iVar27 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_380 + 0x1a) = 8;
  lVar29 = *(longlong *)(param_3 + 0x84);
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_53;
      uVar39 = extraout_s0_53;
      uVar42 = extraout_var_53;
      uVar43 = extraout_var_x00118;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_1800517f0;
    local_5d8 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_1800517f0:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_5d8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar27 = 8;
      if (iVar12 < 9) {
        iVar27 = iVar12 + -1;
      }
      local_5d8 = (longlong *)(lVar29 + (longlong)iVar27 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_5d8 + 0x1a) = 9;
  lVar29 = *(longlong *)(param_3 + 0x84);
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_54;
      uVar39 = extraout_s0_54;
      uVar42 = extraout_var_54;
      uVar43 = extraout_var_x00119;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_180051848;
    plVar21 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_180051848:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      plVar21 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar27 = 9;
      if (iVar12 < 10) {
        iVar27 = iVar12 + -1;
      }
      plVar21 = (longlong *)(lVar29 + (longlong)iVar27 * 0x98);
    }
  }
  *(undefined2 *)((longlong)plVar21 + 0x1a) = 10;
  lVar29 = *(longlong *)(param_3 + 0x84);
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_55;
      uVar39 = extraout_s0_55;
      uVar42 = extraout_var_55;
      uVar43 = extraout_var_x00120;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18005189c;
    plVar36 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_18005189c:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      plVar36 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar27 = 10;
      if (iVar12 < 0xb) {
        iVar27 = iVar12 + -1;
      }
      plVar36 = (longlong *)(lVar29 + (longlong)iVar27 * 0x98);
    }
  }
  *(undefined2 *)((longlong)plVar36 + 0x1a) = 0xb;
  lVar29 = *(longlong *)(param_3 + 0x84);
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_56;
      uVar39 = extraout_s0_56;
      uVar42 = extraout_var_56;
      uVar43 = extraout_var_x00121;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_1800518f0;
    pfVar23 = (float *)(param_3 + 0x8e);
  }
  else {
LAB_1800518f0:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      pfVar23 = (float *)(param_3 + 0x8e);
    }
    else {
      iVar27 = 0xb;
      if (iVar12 < 0xc) {
        iVar27 = iVar12 + -1;
      }
      pfVar23 = (float *)(lVar29 + (longlong)iVar27 * 0x98);
    }
  }
  *(undefined2 *)((longlong)pfVar23 + 0x1a) = 0xc;
  lVar29 = *(longlong *)(param_3 + 0x84);
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_57;
      uVar39 = extraout_s0_57;
      uVar42 = extraout_var_57;
      uVar43 = extraout_var_x00122;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_180051944;
    local_368 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_180051944:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_368 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar27 = 0xc;
      if (iVar12 < 0xd) {
        iVar27 = iVar12 + -1;
      }
      local_368 = (longlong *)(lVar29 + (longlong)iVar27 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_368 + 0x1a) = 0xd;
  lVar29 = *(longlong *)(param_3 + 0x84);
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_58;
      uVar39 = extraout_s0_58;
      uVar42 = extraout_var_58;
      uVar43 = extraout_var_x00123;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18005199c;
    local_508 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_18005199c:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_508 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar27 = 0xd;
      if (iVar12 < 0xe) {
        iVar27 = iVar12 + -1;
      }
      local_508 = (longlong *)(lVar29 + (longlong)iVar27 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_508 + 0x1a) = 0xe;
  local_220[0] = PitSessions::vftable;
  if (param_3[0x2b] != 0) {
    FUN_1800079f8((longlong *)(param_3 + 0x46),0x1800d8808,3);
    param_3[0x49] = 1;
    if ((*(char **)(param_3 + 0xce) != (char *)0x0) && (**(char **)(param_3 + 0xce) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0xce),0x1800d4ecd,0);
      param_3[0xd1] = 1;
    }
    ppfVar31 = (float **)&DAT_1800d4ecd;
    local_530 = &DAT_1800d4ecd;
    local_540 = (float **)&DAT_1800d4ecd;
    local_538 = 0;
    ppfVar15 = (float **)FUN_180004620(0x15);
    if (ppfVar15 == (float **)0x0) {
      local_540 = (float **)&DAT_1800d4ecd;
    }
    else {
      param_6 = 0x14;
      local_540 = ppfVar15;
      FUN_180099d78((char *)ppfVar15,0x15,0x1800d6c40,0x14);
      local_538 = 0x100000001;
      ppfVar31 = ppfVar15;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_540);
    if ((ppfVar15 != (float **)0x0) && (ppfVar31 != (float **)0x0)) {
      pvVar16 = GetProcessHeap();
      HeapFree(pvVar16,0,ppfVar31);
    }
    ppfVar31 = (float **)&DAT_1800d4ecd;
    local_540 = (float **)&DAT_1800d4ecd;
    local_538 = 0;
    local_530 = &DAT_1800d4ecd;
    pvVar16 = GetProcessHeap();
    ppfVar15 = (float **)HeapAlloc(pvVar16,0,0x8d);
    if (ppfVar15 == (float **)0x0) {
      local_540 = (float **)&DAT_1800d4ecd;
    }
    else {
      param_6 = 0x8c;
      ppfVar15[1] = (float *)0x0;
      *ppfVar15 = (float *)0x0;
      ppfVar15[3] = (float *)0x0;
      ppfVar15[2] = (float *)0x0;
      ppfVar15[5] = (float *)0x0;
      ppfVar15[4] = (float *)0x0;
      ppfVar15[7] = (float *)0x0;
      ppfVar15[6] = (float *)0x0;
      ppfVar15[9] = (float *)0x0;
      ppfVar15[8] = (float *)0x0;
      ppfVar15[0xb] = (float *)0x0;
      ppfVar15[10] = (float *)0x0;
      ppfVar15[0xd] = (float *)0x0;
      ppfVar15[0xc] = (float *)0x0;
      ppfVar15[0xf] = (float *)0x0;
      ppfVar15[0xe] = (float *)0x0;
      ppfVar15[0x10] = (float *)0x0;
      *(undefined4 *)(ppfVar15 + 0x11) = 0;
      *(char *)((longlong)ppfVar15 + 0x8c) = '\0';
      local_540 = ppfVar15;
      FUN_180099d78((char *)ppfVar15,0x8d,0x1800d6bb0,0x8c);
      local_538 = 0x100000001;
      ppfVar31 = ppfVar15;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_540);
    uVar39 = extraout_s0_59;
    uVar42 = extraout_var_59;
    uVar17 = extraout_var_x00124;
    if ((ppfVar15 != (float **)0x0) && (ppfVar31 != (float **)0x0)) {
      pvVar16 = GetProcessHeap();
      HeapFree(pvVar16,0,ppfVar31);
      local_540 = (float **)0x0;
      local_538 = 0;
      uVar39 = extraout_s0_60;
      uVar42 = extraout_var_60;
      uVar17 = extraout_var_x00125;
    }
    ppfVar31 = (float **)&DAT_1800d4ecd;
    ppfVar15 = *(float ***)(param_3 + 0x46);
    if (*(float ***)(param_3 + 0x46) == (float **)0x0) {
      ppfVar15 = ppfVar31;
    }
    auVar48._4_4_ = uVar42;
    auVar48._0_4_ = uVar39;
    auVar48._8_8_ = uVar17;
    FUN_180026368(auVar48,param_2,(undefined8 *)(param_3 + 0xce),0x1800d6c60,ppfVar15,param_6,
                  param_7,param_8,param_9,param_10);
    local_540 = (float **)&DAT_1800d4ecd;
    local_538 = 0;
    local_530 = &DAT_1800d4ecd;
    pvVar16 = GetProcessHeap();
    ppfVar15 = (float **)HeapAlloc(pvVar16,0,7);
    if (ppfVar15 == (float **)0x0) {
      local_540 = (float **)&DAT_1800d4ecd;
    }
    else {
      *(undefined4 *)ppfVar15 = 0;
      ((char *)((longlong)ppfVar15 + 4))[0] = '\0';
      ((char *)((longlong)ppfVar15 + 4))[1] = '\0';
      *(char *)((longlong)ppfVar15 + 6) = '\0';
      local_540 = ppfVar15;
      FUN_180099d78((char *)ppfVar15,7,0x1800d6c58,6);
      local_538 = 0x100000001;
      ppfVar31 = ppfVar15;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_540);
    if ((ppfVar15 != (float **)0x0) && (ppfVar31 != (float **)0x0)) {
      pvVar16 = GetProcessHeap();
      HeapFree(pvVar16,0,ppfVar31);
      local_540 = (float **)0x0;
      local_538 = 0;
    }
    param_3[0xdf] = 0;
    param_3[0x2c] = 1;
    param_3[1] = 0;
    param_3[4] = 0;
    param_3[0x32e] = 1;
    param_3[0x276] = 1;
    (**(code **)(param_3 + 0x70e))(param_3[9],param_3[0x270],0x37);
    FUN_1800079f8((longlong *)local_610,0x1800d8800,4);
    local_4c8[3] = 1.4013e-45;
    local_4c8[0x38] = 4.62428e-44;
    local_4c8[9] = 1.83678e-40;
    *(undefined2 *)(local_4c8 + 10) = 1;
    local_4c8[6] = 1.7701561e-38;
    FUN_1800079f8((longlong *)local_618,0x1800d8814,4);
    local_4f8[3] = 1.4013e-45;
    local_4f8[0x38] = 4.62428e-44;
    local_4f8[9] = 1.83678e-40;
    *(undefined2 *)(local_4f8 + 10) = 1;
    local_4f8[6] = 1.7701561e-38;
    FUN_1800079f8((longlong *)local_620,0x1800d880c,4);
    local_4e0[3] = 1.4013e-45;
    local_4e0[0x38] = 4.62428e-44;
    local_4e0[9] = 1.83678e-40;
    *(undefined2 *)(local_4e0 + 10) = 1;
    local_4e0[6] = 1.95845e-40;
    FUN_1800079f8((longlong *)local_628,0x1800d8824,4);
    local_4d8[3] = 1.4013e-45;
    local_4d8[0x38] = 4.62428e-44;
    local_4d8[9] = 1.83678e-40;
    *(undefined2 *)(local_4d8 + 10) = 1;
    local_4d8[6] = 1.7701561e-38;
    FUN_1800079f8((longlong *)local_630,0x1800d881c,4);
    local_4d0[3] = 1.4013e-45;
    local_4d0[0x38] = 4.62428e-44;
    local_4d0[9] = 1.83678e-40;
    *(undefined2 *)(local_4d0 + 10) = 1;
    local_4d0[6] = 1.7701561e-38;
    FUN_1800079f8(local_638,0x1800d883c,4);
    *(int *)((longlong)local_398 + 0xc) = 1;
    *(int *)(local_398 + 0x1c) = 0x21;
    *(int *)((longlong)local_398 + 0x24) = 0x20005;
    *(undefined2 *)(local_398 + 5) = 1;
    *(int *)(local_398 + 3) = 0x221f0;
    *(undefined2 *)(local_3a0 + 9) = 3;
    *(undefined2 *)(local_3a0 + 10) = 1;
    local_3a0[6] = 0xc0c0c0;
    *(undefined2 *)(local_3a8 + 9) = 3;
    *(undefined2 *)(local_3a8 + 10) = 1;
    local_3a8[6] = 0xa9a9a9;
    *(undefined2 *)(local_3b0 + 9) = 3;
    *(undefined2 *)(local_3b0 + 10) = 1;
    local_3b0[6] = 0xa9a9a9;
    FUN_1800079f8(local_640,0x1800d8830,8);
    *(int *)((longlong)local_3b8 + 0xc) = 1;
    *(int *)(local_3b8 + 0x1c) = 0;
    *(int *)((longlong)local_3b8 + 0x24) = 3;
    *(undefined2 *)(local_3b8 + 5) = 1;
    *(int *)(local_3b8 + 3) = 0xf0a60d;
    FUN_1800079f8((longlong *)local_6a8,0x1800d8850,7);
    local_520[3] = 1.4013e-45;
    local_520[0x38] = 0.0;
    *(undefined2 *)(local_520 + 9) = 0x23;
    *(undefined2 *)(local_520 + 10) = 1;
    local_520[6] = 5.803637e-39;
    local_520[7] = 5.803637e-39;
    local_520[8] = 1.4013e-45;
    FUN_1800079f8((longlong *)local_6b0,0x1800d8844,6);
    local_528[3] = 1.4013e-45;
    local_528[0x38] = 0.0;
    *(undefined2 *)(local_528 + 9) = 0x22;
    *(undefined2 *)(local_528 + 10) = 1;
    local_528[6] = 5.803637e-39;
    local_528[7] = 5.803637e-39;
    local_528[8] = 1.4013e-45;
    FUN_1800079f8((longlong *)local_6b8,0x1800d885c,6);
    local_510[3] = 1.4013e-45;
    local_510[0x38] = 0.0;
    local_510[9] = 4.2039e-45;
    *(undefined2 *)(local_510 + 10) = 1;
    local_510[6] = 9.07019e-39;
    local_510[7] = 9.07019e-39;
    local_510[8] = 1.4013e-45;
    FUN_1800079f8((longlong *)local_5b8,0x1800d8858,3);
    local_378[3] = 1.4013e-45;
    local_378[0x38] = 4.62428e-44;
    local_378[9] = 1.83675e-40;
    *(undefined2 *)(local_378 + 10) = 1;
    local_378[6] = 1.95845e-40;
    FUN_1800079f8((longlong *)local_5b0,0x1800d8868,3);
    local_3c0[3] = 1.4013e-45;
    local_3c0[0x38] = 4.62428e-44;
    local_3c0[9] = 1.83675e-40;
    *(undefined2 *)(local_3c0 + 10) = 1;
    local_3c0[6] = 1.95845e-40;
    FUN_1800079f8((longlong *)local_5a8,0x1800d8864,3);
    local_3c8[3] = 1.4013e-45;
    local_3c8[0x38] = 4.62428e-44;
    local_3c8[9] = 1.83675e-40;
    *(undefined2 *)(local_3c8 + 10) = 1;
    local_3c8[6] = 1.7357174e-38;
    FUN_1800079f8((longlong *)local_5a0,0x1800d8870,5);
    local_3d0[3] = 1.4013e-45;
    local_3d0[0x38] = 4.62428e-44;
    local_3d0[9] = 1.83678e-40;
    *(undefined2 *)(local_3d0 + 10) = 1;
    local_3d0[6] = 1.95845e-40;
    FUN_1800079f8((longlong *)local_608,0x1800d886c,3);
    local_3d8[3] = 1.4013e-45;
    local_3d8[0x38] = 4.62428e-44;
    local_3d8[9] = 1.83675e-40;
    *(undefined2 *)(local_3d8 + 10) = 1;
    local_3d8[6] = 2.2100087e-38;
    FUN_1800079f8((longlong *)local_600,0x1800d887c,3);
    local_3e0[3] = 1.4013e-45;
    local_3e0[0x38] = 4.62428e-44;
    local_3e0[9] = 1.83675e-40;
    *(undefined2 *)(local_3e0 + 10) = 1;
    local_3e0[6] = 2.2100087e-38;
    FUN_1800079f8((longlong *)local_548,0x1800d8878,3);
    local_3e8[3] = 1.4013e-45;
    local_3e8[0x38] = 4.62428e-44;
    local_3e8[9] = 1.83678e-40;
    *(undefined2 *)(local_3e8 + 10) = 1;
    local_3e8[6] = 2.3509886e-38;
    FUN_1800079f8((longlong *)local_550,0x1800d8884,6);
    local_3f0[3] = 1.4013e-45;
    local_3f0[0x38] = 4.62428e-44;
    local_3f0[9] = 1.83678e-40;
    *(undefined2 *)(local_3f0 + 10) = 1;
    local_3f0[6] = 2.3509886e-38;
    FUN_1800079f8((longlong *)local_558,0x1800d8880,3);
    local_3f8[3] = 1.4013e-45;
    local_3f8[0x38] = 4.62428e-44;
    local_3f8[9] = 1.83678e-40;
    *(undefined2 *)(local_3f8 + 10) = 1;
    local_3f8[6] = 2.3509886e-38;
    FUN_1800079f8((longlong *)local_650,0x1800d8898,8);
    local_400[3] = 1.4013e-45;
    local_400[0x38] = 4.62428e-44;
    local_400[9] = 1.83678e-40;
    *(undefined2 *)(local_400 + 10) = 1;
    local_400[6] = 2.3509528e-38;
    FUN_1800079f8((longlong *)local_5f8,0x1800d8890,7);
    local_408[3] = 1.4013e-45;
    local_408[0x38] = 4.62428e-44;
    local_408[9] = 1.83671e-40;
    *(undefined2 *)(local_408 + 10) = 1;
    local_408[6] = 2.3474371e-38;
    local_410[0x38] = 4.62428e-44;
    local_410[9] = 1.83678e-40;
    *(undefined2 *)(local_410 + 10) = 1;
    local_410[6] = 2.3509528e-38;
    local_4a0[0x38] = 4.62428e-44;
    local_4a0[9] = 1.83678e-40;
    *(undefined2 *)(local_4a0 + 10) = 1;
    local_4a0[6] = 2.3474371e-38;
    FUN_1800079f8((longlong *)local_5e8,0x1800d88b0,7);
    local_418[3] = 1.4013e-45;
    local_418[0x38] = 4.62428e-44;
    local_418[9] = 1.83678e-40;
    *(undefined2 *)(local_418 + 10) = 1;
    local_418[6] = 1.95845e-40;
    FUN_1800079f8((longlong *)local_5e0,0x1800d88a4,6);
    local_420[3] = 1.4013e-45;
    local_420[0x38] = 4.62428e-44;
    local_420[9] = 1.83678e-40;
    *(undefined2 *)(local_420 + 10) = 1;
    local_420[6] = 1.180104e-38;
    FUN_1800079f8((longlong *)local_560,0x1800d88c0,5);
    local_428[3] = 1.4013e-45;
    local_428[0x38] = 4.62428e-44;
    local_428[9] = 1.83678e-40;
    *(undefined2 *)(local_428 + 10) = 1;
    local_428[6] = 1.0598883e-38;
    FUN_1800079f8((longlong *)local_568,0x1800d88b8,5);
    local_430[3] = 1.4013e-45;
    local_430[0x38] = 4.62428e-44;
    local_430[9] = 1.83678e-40;
    *(undefined2 *)(local_430 + 10) = 1;
    local_430[6] = 1.0598883e-38;
    FUN_1800079f8((longlong *)local_570,0x1800d88d0,5);
    local_438[3] = 1.4013e-45;
    local_438[0x38] = 4.62428e-44;
    local_438[9] = 1.83678e-40;
    *(undefined2 *)(local_438 + 10) = 1;
    local_438[6] = 1.0598883e-38;
    FUN_1800079f8((longlong *)local_578,0x1800d88c8,5);
    local_440[3] = 1.4013e-45;
    local_440[0x38] = 4.62428e-44;
    local_440[9] = 1.83678e-40;
    *(undefined2 *)(local_440 + 10) = 1;
    local_440[6] = 1.0598883e-38;
    FUN_1800079f8((longlong *)local_580,0x1800d88e0,5);
    local_448[3] = 1.4013e-45;
    local_448[0x38] = 4.62428e-44;
    local_448[9] = 1.83678e-40;
    *(undefined2 *)(local_448 + 10) = 1;
    local_448[6] = 1.0598883e-38;
    FUN_1800079f8((longlong *)local_588,0x1800d88d8,5);
    local_498[3] = 1.4013e-45;
    local_498[0x38] = 4.62428e-44;
    local_498[9] = 1.83678e-40;
    *(undefined2 *)(local_498 + 10) = 1;
    local_498[6] = 1.0598883e-38;
    FUN_1800079f8(local_590,0x1800d88f0,5);
    *(int *)((longlong)local_450 + 0xc) = 1;
    *(int *)(local_450 + 0x1c) = 0x21;
    *(int *)((longlong)local_450 + 0x24) = 0x20005;
    *(undefined2 *)(local_450 + 5) = 1;
    *(int *)(local_450 + 3) = 0x736960;
    FUN_1800079f8(local_598,0x1800d88e8,5);
    *(int *)((longlong)local_458 + 0xc) = 1;
    *(int *)(local_458 + 0x1c) = 0x21;
    *(int *)((longlong)local_458 + 0x24) = 0x20005;
    *(undefined2 *)(local_458 + 5) = 1;
    *(int *)(local_458 + 3) = 0x736960;
    FUN_1800079f8((longlong *)local_690,0x1800d8900,5);
    local_460[3] = 1.4013e-45;
    local_460[0x38] = 4.62428e-44;
    local_460[9] = 1.83678e-40;
    *(undefined2 *)(local_460 + 10) = 1;
    local_460[6] = 1.5745252e-38;
    FUN_1800079f8(local_698,0x1800d88f8,5);
    *(int *)((longlong)local_468 + 0xc) = 1;
    *(int *)(local_468 + 0x1c) = 0x21;
    *(int *)((longlong)local_468 + 0x24) = 0x20005;
    *(undefined2 *)(local_468 + 5) = 1;
    *(int *)(local_468 + 3) = 0xab735b;
    FUN_1800079f8(local_6a0,0x1800d8910,5);
    *(int *)((longlong)local_470 + 0xc) = 1;
    *(int *)(local_470 + 0x1c) = 0x21;
    *(int *)((longlong)local_470 + 0x24) = 0x20005;
    *(undefined2 *)(local_470 + 5) = 1;
    *(int *)(local_470 + 3) = 0xab735b;
    FUN_1800079f8((longlong *)local_668,0x1800d8908,5);
    local_478[3] = 1.4013e-45;
    local_478[0x38] = 4.62428e-44;
    local_478[9] = 1.83678e-40;
    *(undefined2 *)(local_478 + 10) = 1;
    local_478[6] = 1.5745252e-38;
    FUN_1800079f8(local_670,0x1800d8920,5);
    *(int *)((longlong)local_480 + 0xc) = 1;
    *(int *)(local_480 + 0x1c) = 0x21;
    *(int *)((longlong)local_480 + 0x24) = 0x20005;
    *(undefined2 *)(local_480 + 5) = 1;
    *(int *)(local_480 + 3) = 0xab735b;
    FUN_1800079f8(local_678,0x1800d8918,5);
    *(int *)((longlong)local_488 + 0xc) = 1;
    *(int *)(local_488 + 0x1c) = 0x21;
    *(int *)((longlong)local_488 + 0x24) = 0x20005;
    *(undefined2 *)(local_488 + 5) = 1;
    *(int *)(local_488 + 3) = 0xab735b;
    FUN_1800079f8((longlong *)local_680,0x1800d8930,5);
    local_490[3] = 1.4013e-45;
    local_490[0x38] = 4.62428e-44;
    local_490[9] = 1.83678e-40;
    *(undefined2 *)(local_490 + 10) = 1;
    local_490[6] = 1.5745252e-38;
    FUN_1800079f8(local_688,0x1800d8928,5);
    plVar34 = local_4f0;
    *(int *)((longlong)local_4b0 + 0xc) = 1;
    *(int *)(local_4b0 + 0x1c) = 0x21;
    *(int *)((longlong)local_4b0 + 0x24) = 0x20005;
    *(undefined2 *)(local_4b0 + 5) = 1;
    *(int *)(local_4b0 + 3) = 0xab735b;
    *(int *)(local_4b8 + 0x1c) = 0x21;
    *(int *)((longlong)local_4b8 + 0x24) = 0x10005;
    *(undefined2 *)(local_4b8 + 5) = 1;
    *(int *)(local_4b8 + 3) = 0xff00ff;
    FUN_1800079f8(local_4f0,0x1800d6cb0,0x11);
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    puVar14 = FUN_180029680((longlong *)local_220,(undefined8 *)&local_270);
    plVar34 = local_4f0;
    if (0xf < (ulonglong)puVar14[3]) {
      puVar14 = (undefined8 *)*puVar14;
    }
    if ((code *)local_4f0[10] != (code *)0x0) {
      (*(code *)local_4f0[10])(*(undefined4 *)((longlong)local_4f0 + 0x4c),puVar14);
      *(undefined1 *)(plVar34 + 3) = 0x16;
    }
    if (0xf < local_258) {
      FUN_1800966b8((LPVOID)CONCAT71(uStack_26f,local_270));
    }
    plVar34 = local_4c0;
    local_270 = 0;
    local_260 = 0;
    local_258 = 0xf;
    *(undefined1 *)(local_4f0 + 3) = 0x16;
    *(undefined4 *)((longlong)local_4f0 + 0x1c) = 0;
    FUN_1800079f8(local_4c0,0x1800d6d30,0x14);
    plVar10 = local_390;
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined1 *)(plVar34 + 3) = 5;
    *(undefined4 *)((longlong)plVar34 + 0x1c) = 0;
    FUN_1800079f8(local_390,0x1800d8970,0x16);
    *(undefined4 *)((longlong)plVar10 + 0xc) = 1;
    if ((code *)plVar10[10] != (code *)0x0) {
      (*(code *)plVar10[10])
                (*(undefined4 *)((longlong)plVar10 + 0x4c),
                 "30 Second;1 Minute;5 Minute;15 Minute;30 Minute;1 Hour");
    }
    plVar34 = local_350;
    *(undefined1 *)(local_390 + 3) = 0x16;
    *(undefined4 *)((longlong)local_390 + 0x1c) = 0;
    FUN_1800079f8(local_350,0x1800d89a0,0x1b);
    plVar10 = local_358;
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined1 *)(plVar34 + 3) = 5;
    *(undefined4 *)((longlong)plVar34 + 0x1c) = 1;
    FUN_1800079f8(local_358,0x1800d8988,0x15);
    plVar34 = local_388;
    *(undefined4 *)((longlong)plVar10 + 0xc) = 1;
    *(undefined1 *)(plVar10 + 3) = 5;
    *(undefined4 *)((longlong)plVar10 + 0x1c) = 1;
    FUN_1800079f8(local_388,0x1800d89e0,0x1d);
    pfVar24 = local_648;
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined1 *)(plVar34 + 3) = 5;
    *(undefined4 *)((longlong)plVar34 + 0x1c) = 1;
    FUN_1800079f8((longlong *)local_648,0x1800d89c0,0x18);
    pfVar24[3] = 1.4013e-45;
    if (*(code **)(pfVar24 + 0x14) != (code *)0x0) {
      (**(code **)(pfVar24 + 0x14))(pfVar24[0x13],"Normal;Use SG1 Draw Style");
    }
    plVar10 = local_380;
    *(undefined1 *)(local_648 + 6) = 0x16;
    local_648[7] = 0.0;
    FUN_1800079f8(local_380,0x1800d8a00,0x1a);
    plVar34 = local_5d8;
    *(undefined4 *)((longlong)plVar10 + 0xc) = 1;
    *(undefined1 *)(plVar10 + 3) = 2;
    *(undefined4 *)((longlong)plVar10 + 0x1c) = 0x3e800000;
    *(undefined4 *)((longlong)plVar10 + 0x2c) = 0x3dcccccd;
    *(undefined4 *)((longlong)plVar10 + 0x3c) = 0x41200000;
    FUN_1800079f8(local_5d8,0x1800d8a60,0x18);
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined1 *)(plVar34 + 3) = 2;
    *(undefined4 *)((longlong)plVar34 + 0x1c) = 0x3f000000;
    *(undefined4 *)((longlong)plVar34 + 0x2c) = 0x3dcccccd;
    *(undefined4 *)((longlong)plVar34 + 0x3c) = 0x41200000;
    FUN_1800079f8(plVar21,0x1800d8a40,0x1f);
    *(undefined4 *)((longlong)plVar21 + 0xc) = 1;
    if ((code *)plVar21[10] != (code *)0x0) {
      (*(code *)plVar21[10])
                (*(undefined4 *)((longlong)plVar21 + 0x4c),"Default;Use Proximity Range");
    }
    *(undefined1 *)(plVar21 + 3) = 0x16;
    *(undefined4 *)((longlong)plVar21 + 0x1c) = 0;
    FUN_1800079f8(plVar36,0x1800d8a80,0x28);
    plVar21 = local_368;
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    *(undefined1 *)(plVar36 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar36 + 0x1c) = 0x28;
    *(undefined4 *)((longlong)plVar36 + 0x2c) = 0;
    *(undefined4 *)((longlong)plVar36 + 0x3c) = 0x7fffffff;
    *(undefined1 *)(pfVar23 + 6) = 0xb;
    pfVar23[7] = 0.0;
    pfVar23[0xb] = 0.0;
    pfVar23[0xf] = 2.10195e-43;
    FUN_1800079f8(local_368,0x1800d8b00,0x29);
    *(undefined4 *)((longlong)plVar21 + 0xc) = 1;
    FUN_18004fdd0((longlong)plVar21,"");
    plVar21 = local_508;
    FUN_1800079f8(local_508,0x1800d8ad0,0x28);
    *(undefined4 *)((longlong)plVar21 + 0xc) = 1;
    *(undefined1 *)(plVar21 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar21 + 0x1c) = 0;
    *(undefined4 *)((longlong)plVar21 + 0x2c) = 0;
    *(undefined4 *)((longlong)plVar21 + 0x3c) = 300;
    return;
  }
  if (param_3[3] == 0) {
    auVar2._4_4_ = uVar42;
    auVar2._0_4_ = uVar39;
    auVar2._8_8_ = uVar43;
    uVar17 = FUN_1800254e8(auVar2,param_2,(longlong)param_3,uVar17,param_5,param_6,param_7,param_8,
                           param_9,param_10);
    *local_660 = (int)uVar17;
  }
  if (*local_660 != 0) {
    return;
  }
  iVar12 = (**(code **)(param_3 + 0x3ec))(param_3[0x270]);
  if (iVar12 == 0) {
    return;
  }
  local_4b0 = *(longlong **)(param_3 + 0x1c);
  if (*(char *)local_4b0 == '\0') {
    *(char *)local_4b0 = '\x01';
  }
  local_6e8 = 0;
  fVar35 = (float)param_3[3];
  local_660._0_4_ = fVar35;
  if (*local_2f0 == 1.4013e-45) {
    if (param_3[0x47e] != 0) {
      *local_2f0 = 0.0;
    }
    goto LAB_180055278;
  }
  if ((param_3[0x47e] != 0) && (fVar35 == 0.0)) {
    local_660._0_4_ = 0.0;
    *local_2f0 = 0.0;
  }
  uVar18 = FUN_180026708((longlong)pfVar23);
  if ((int)uVar18 == 0) {
    uVar13 = FUN_180026550((longlong)plVar21);
    *(uint *)((longlong)local_4b0 + 4) = uVar13;
  }
  else {
    uVar18 = FUN_180026708((longlong)pfVar23);
    if (param_3[0x36e] == (int)uVar18) {
      local_6e8 = 1;
      local_660._0_4_ = 0.0;
      *(uint *)((longlong)local_4b0 + 4) = (uint)(param_3[0x36f] == 4);
      if (param_3[0x47e] == 0) {
        param_3[0x433] = 0;
      }
    }
  }
  uVar19 = FUN_180026708((longlong)pfVar23);
  uVar18 = extraout_x1_59;
  if ((int)uVar19 == 0) {
LAB_1800528f4:
    if ((float)local_660 == 0.0) goto LAB_1800528fc;
  }
  else {
    FUN_180026708((longlong)pfVar23);
    uVar13 = (*extraout_x11)();
    uVar18 = (ulonglong)*(uint *)((longlong)local_4b0 + 4);
    if (uVar13 == *(uint *)((longlong)local_4b0 + 4)) goto LAB_1800528f4;
    FUN_180026708((longlong)pfVar23);
    (*extraout_x11_00)();
    local_660._0_4_ = 0.0;
    if (param_3[0x47e] == 0) {
      param_3[0x433] = 0;
    }
LAB_1800528fc:
    local_6e8 = 1;
    uStack_128 = 0;
    local_130 = 0;
    uStack_118 = 0;
    uStack_120 = 0;
    *local_6c0 = 0.0;
    uStack_108 = 0;
    local_110 = 0;
    local_f8 = (float *)0x0;
    uStack_100 = 0;
    uStack_e8 = 0;
    local_f0 = 0;
    uStack_d8 = 0;
    local_e0 = (float *)0x0;
    uStack_c8 = 0;
    local_d0 = 0;
    uStack_b8 = 0;
    uStack_c0 = 0;
    local_b0 = 0;
    uVar13 = FUN_180026550((longlong)local_4f0);
    uVar17 = 0;
    FUN_180011fd0(&local_130,(longlong)param_3,0,uVar13);
    *local_338 = -NAN;
    auVar48 = (**(code **)(param_3 + 0x446))(2);
    puVar32 = (undefined4 *)*auVar48._0_8_;
    if (puVar32 != (undefined4 *)0x0) {
      uVar17 = 0;
      FUN_18001cec0(puVar32,auVar48._8_8_,0);
      FUN_18004a2d0(puVar32);
      (**(code **)(param_3 + 0x448))(2,0);
    }
    auVar48 = FUN_180096840(0x80);
    local_6d0 = auVar48._0_8_;
    if (local_6d0 == (float *)0x0) {
      piVar20 = (int *)0x0;
    }
    else {
      param_2 = 0x40000000;
      uVar17 = 1;
      piVar20 = FUN_18001cd30(0x3e4ccccd,0x40000000,local_6d0,auVar48._8_8_,1,1);
    }
    piVar26 = piVar20;
    if (piVar20 == (int *)0x0) {
      piVar26 = (int *)0x0;
    }
    (**(code **)(param_3 + 0x448))(2,piVar26);
    pfVar24 = local_f8;
    local_6c0 = local_f8;
    local_318 = piVar20;
    local_658 = (float *)FUN_1800554d0((longlong)param_3,3,uVar17,&local_6c0);
    pfVar5 = local_e0;
    local_6c0 = local_e0;
    local_370 = local_658;
    local_360 = (float *)FUN_1800554d0((longlong)param_3,4,uVar17,&local_6c0);
    local_6c0 = pfVar24;
    local_330 = local_360;
    local_4a8 = (float *)FUN_1800555a8((longlong)param_3,5,uVar17,&local_6c0);
    local_6c0 = pfVar5;
    local_328 = local_4a8;
    local_500 = (float *)FUN_1800555a8((longlong)param_3,6,uVar17,&local_6c0);
    local_348 = local_500;
    uVar18 = FUN_180026708((longlong)plVar36);
    puVar14 = (undefined8 *)(**(code **)(param_3 + 0x446))(7);
    if ((LPVOID)*puVar14 != (LPVOID)0x0) {
      FUN_1800966b8((LPVOID)*puVar14);
      (**(code **)(param_3 + 0x448))(7,0);
    }
    local_6d0 = (float *)FUN_180096840(0x10);
    if (local_6d0 == (float *)0x0) {
      local_6e0 = (float *)0x0;
    }
    else {
      local_6d0[0] = 1.4013e-44;
      local_6d0[1] = 1.54143e-44;
      local_6d0[2] = (float)uVar18;
      local_6e0 = local_6d0;
    }
    pfVar24 = local_6e0;
    if (local_6e0 == (float *)0x0) {
      pfVar24 = (float *)0x0;
    }
    (**(code **)(param_3 + 0x448))(7,pfVar24);
    puVar14 = (undefined8 *)(**(code **)(param_3 + 0x446))(8);
    puVar32 = (undefined4 *)*puVar14;
    if (puVar32 != (undefined4 *)0x0) {
      FUN_18004feb8(puVar32);
      FUN_180055758(puVar32);
      (**(code **)(param_3 + 0x448))(8,0);
    }
    local_6d8 = (float *)FUN_180096840(0x58);
    local_6d0 = local_6d8;
    if (local_6d8 == (float *)0x0) {
      local_6d8 = (float *)0x0;
    }
    else {
      local_6d8[0xc] = 0.0;
      local_6d8[0xd] = 0.0;
      local_6d8[0xe] = 0.0;
      local_6d8[0xf] = 0.0;
      lVar29 = FUN_180096150(0x18);
      *(longlong *)lVar29 = lVar29;
      *(longlong *)(lVar29 + 8) = lVar29;
      *(longlong *)(local_6d8 + 0xc) = lVar29;
      local_6d8[0x10] = 0.0;
      local_6d8[0x11] = 0.0;
      local_6d8[0x12] = 0.0;
      local_6d8[0x13] = 0.0;
      lVar29 = FUN_180096150(0x18);
      *(longlong *)lVar29 = lVar29;
      *(longlong *)(lVar29 + 8) = lVar29;
      *(longlong *)(local_6d8 + 0x10) = lVar29;
      local_6d8[0x14] = 0.0;
      FUN_18004feb8(local_6d8);
    }
    pfVar24 = local_6d8;
    if (local_6d8 == (float *)0x0) {
      pfVar24 = (float *)0x0;
    }
    (**(code **)(param_3 + 0x448))(8,pfVar24);
    if ((((((piVar20 == (int *)0x0) || (local_370 == (float *)0x0)) || (local_360 == (float *)0x0))
         || ((local_4a8 == (float *)0x0 || (local_348 == (float *)0x0)))) ||
        (local_6e0 == (float *)0x0)) || (local_6d8 == (float *)0x0)) goto LAB_180055278;
    *local_6d8 = -2.1474836e+09;
    local_6d8[1] = 2.1474836e+09;
    local_6d8[2] = -2.1474836e+09;
    local_6d8[3] = 2.1474836e+09;
    local_6d8[4] = -2.1474836e+09;
    local_6d8[5] = 2.1474836e+09;
    local_6d8[6] = -2.1474836e+09;
    local_6d8[7] = 2.1474836e+09;
    local_6d8[8] = 0.0;
    local_6d8[9] = -2.1474836e+09;
    local_6d8[10] = 2.1474836e+09;
    *local_340 = 0;
    *local_310 = 0;
    *local_338 = 0.0;
    *local_320 = 0;
    *local_4e8 = 0.0;
    *local_2e8 = 0.0;
    iVar27 = *param_3;
    iVar12 = 0;
    local_6c0 = pfVar23;
    do {
      iVar30 = 0;
      if (0 < iVar27) {
        do {
          plVar21 = FUN_18000f880((longlong *)(param_3 + 0x140),iVar12);
          puVar32 = (undefined4 *)FUN_18000f448((longlong)plVar21,iVar30);
          iVar30 = iVar30 + 1;
          *puVar32 = 0;
          iVar27 = *param_3;
        } while (iVar30 < iVar27);
      }
      pfVar7 = local_6a8;
      pfVar5 = local_6b0;
      pfVar24 = local_6b8;
      pfVar23 = local_6c0;
      iVar12 = iVar12 + 1;
    } while (iVar12 < 0x3c);
    iVar12 = 0;
    do {
      local_6c0 = (float *)FUN_18000f880((longlong *)(param_3 + 0x140),iVar12);
      FUN_180055320((longlong *)(local_6d8 + 0xc),(longlong *)&local_6c0);
      pfVar22 = (float *)FUN_18000f880((longlong *)(param_3 + 0x140),iVar12);
      if (((pfVar22 != pfVar7) &&
          (pfVar22 = (float *)FUN_18000f880((longlong *)(param_3 + 0x140),iVar12), pfVar22 != pfVar5
          )) && (pfVar22 = (float *)FUN_18000f880((longlong *)(param_3 + 0x140),iVar12),
                pfVar22 != pfVar24)) {
        local_6c0 = (float *)FUN_18000f880((longlong *)(param_3 + 0x140),iVar12);
        FUN_180055320((longlong *)(local_6d8 + 0x10),(longlong *)&local_6c0);
      }
      plVar21 = local_508;
      iVar12 = iVar12 + 1;
    } while (iVar12 < 0x2d);
    plVar34 = *(longlong **)(local_6d8 + 0xc);
    for (plVar36 = (longlong *)*plVar34; plVar36 != plVar34; plVar36 = (longlong *)*plVar36) {
      lVar29 = plVar36[2];
      uVar18 = FUN_180026708((longlong)plVar21);
      FUN_1800067b8((uint *)local_6e0,param_3,lVar29,(uint)uVar18);
    }
    uVar19 = FUN_180026708((longlong)pfVar23);
    uVar18 = extraout_x1_60;
    if ((int)uVar19 != 0) {
      uVar18 = FUN_180026708((longlong)pfVar23);
      (*extraout_x11_01)(uVar18,"MGI Proximity");
      uVar18 = extraout_x1_61;
    }
  }
  piVar20 = local_318;
  if (param_3[0x1b] == 0) {
    if (((((local_318 != (int *)0x0) && (local_370 != (float *)0x0)) && (local_360 != (float *)0x0))
        && ((local_4a8 != (float *)0x0 && (local_348 != (float *)0x0)))) &&
       ((local_6e0 != (float *)0x0 && (local_6d8 != (float *)0x0)))) {
      uVar13 = FUN_180026550((longlong)local_648);
      if (uVar13 == 1) {
        plVar21 = FUN_18000f880((longlong *)(param_3 + 0x140),1);
        plVar36 = FUN_18000f880((longlong *)(param_3 + 0x140),0);
        if (*(short *)((longlong)plVar36 + 0x24) != *(short *)((longlong)plVar21 + 0x24)) {
          iVar12 = 1;
          do {
            plVar21 = FUN_18000f880((longlong *)(param_3 + 0x140),0);
            uVar1 = *(undefined2 *)((longlong)plVar21 + 0x24);
            plVar21 = FUN_18000f880((longlong *)(param_3 + 0x140),iVar12);
            iVar12 = iVar12 + 1;
            *(undefined2 *)((longlong)plVar21 + 0x24) = uVar1;
          } while (iVar12 < 0x3c);
        }
      }
      fVar35 = (float)local_660;
      cVar33 = '\0';
      if ((int)(float)local_660 < *param_3) {
        local_6c8 = '\0';
        local_658 = (float *)CONCAT44(local_658._4_4_,(float)local_660);
        local_2f0 = local_548;
        local_660._0_4_ = local_648._0_4_;
        local_328 = local_558;
        local_330 = local_550;
        do {
          uStack_1f8 = 0;
          local_200 = 0;
          local_1e8 = 0;
          local_1f0 = (float *)0x0;
          uStack_1d8 = 0;
          local_1e0 = 0;
          local_1c8 = (float *)0x0;
          uStack_1d0 = 0;
          uStack_1b8 = 0;
          local_1c0 = 0;
          local_1a8 = 0;
          local_1b0 = (float *)0x0;
          uStack_198 = 0;
          local_1a0 = (float *)0x0;
          local_188 = 0;
          uStack_190 = 0;
          local_180 = 0;
          uVar13 = FUN_180026550((longlong)local_4f0);
          FUN_180011fd0(&local_200,(longlong)param_3,(int)fVar35,uVar13);
          local_508 = (longlong *)CONCAT44(local_508._4_4_,*local_340);
          local_648 = (float *)CONCAT44(local_648._4_4_,*local_4e8);
          uVar18 = FUN_180012730((longlong)&local_200);
          if ((uVar18 & 1) != 0) {
            *local_2e8 = fVar35;
          }
          local_4b8 = (longlong *)CONCAT44(local_4b8._4_4_,*local_338);
          uVar39 = extraout_s0_61;
          uVar42 = extraout_var_61;
          uVar17 = extraout_var_x00126;
          if (0 < (int)fVar35) {
            plVar36 = *(longlong **)(local_6d8 + 0xc);
            uVar43 = extraout_x1_62;
            for (plVar21 = (longlong *)*plVar36; plVar21 != plVar36; plVar21 = (longlong *)*plVar21)
            {
              lVar29 = plVar21[2];
              fVar38 = (float)FUN_180006bb0((int *)local_6e0,uVar43,lVar29,(int)fVar35 + -1);
              FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_63,lVar29,(int)fVar35);
              uVar43 = extraout_x1_64;
              uVar39 = extraout_s0_62;
              uVar42 = extraout_var_62;
              uVar17 = extraout_var_x00127;
            }
          }
          piVar8 = local_5c0;
          piVar26 = local_5c8;
          piVar20 = local_5d0;
          lVar29 = (longlong)local_1b0 % 86400000000;
          iVar12 = ((int)(lVar29 / 1000000) + (int)(lVar29 >> 0x3f)) -
                   (SUB164(SEXT816(lVar29) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
          if (86399999999 < lVar29) {
            iVar12 = 0;
          }
          lVar29 = (longlong)local_1c8 % 86400000000;
          uVar13 = ((int)(lVar29 / 1000000) + (int)(lVar29 >> 0x3f)) -
                   (SUB164(SEXT816(lVar29) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
          if (86399999999 < lVar29) {
            uVar13 = 0;
          }
          auVar3._4_4_ = uVar42;
          auVar3._0_4_ = uVar39;
          auVar3._8_8_ = uVar17;
          FUN_18001cf60(auVar3,param_2,local_318,param_3,(uint)fVar35,(longlong)local_5d0,
                        (longlong)local_5c0,(longlong)local_5c8,1,(ulonglong)uVar13,'\x01',iVar12);
          pfVar23 = (float *)FUN_18000f448((longlong)piVar20,(int)fVar35);
          FUN_1800069e8(*pfVar23,(int *)local_6e0,extraout_x1_65,(longlong)piVar20,(int)fVar35);
          pfVar23 = (float *)FUN_18000f448((longlong)piVar8,(int)fVar35);
          FUN_1800069e8(*pfVar23,(int *)local_6e0,extraout_x1_66,(longlong)piVar8,(int)fVar35);
          pfVar23 = (float *)FUN_18000f448((longlong)piVar26,(int)fVar35);
          FUN_1800069e8(*pfVar23,(int *)local_6e0,extraout_x1_67,(longlong)piVar26,(int)fVar35);
          uVar17 = extraout_x1_68;
          if (local_4b8._0_4_ != fVar35) {
            uVar18 = FUN_1800125b0((longlong)&local_200);
            if ((uVar18 & 1) != 0) {
              *local_310 = (int)fVar35 + -1;
              *local_320 = 1;
            }
            bVar11 = FUN_180012858((longlong)&local_200);
            uVar17 = extraout_x1_69;
            if (bVar11) {
              *local_340 = (int)fVar35 + -1;
              *local_320 = 2;
            }
          }
          piVar26 = local_340;
          piVar20 = local_5d0;
          if (*local_320 == 0) {
            FUN_1800069e8(0.0,(int *)local_6e0,uVar17,(longlong)local_620,(int)fVar35);
            FUN_1800069e8(0.0,(int *)local_6e0,extraout_x1_70,(longlong)local_610,(int)fVar35);
            fVar38 = 0.0;
            uVar17 = extraout_x1_71;
LAB_180053270:
            FUN_1800069e8(fVar38,(int *)local_6e0,uVar17,(longlong)local_618,(int)fVar35);
            FUN_1800069e8(0.0,(int *)local_6e0,extraout_x1_88,(longlong)local_638,(int)fVar35);
            FUN_1800069e8(0.0,(int *)local_6e0,extraout_x1_89,(longlong)local_628,(int)fVar35);
            fVar38 = 0.0;
            uVar17 = extraout_x1_90;
          }
          else {
            if (*local_320 != 1) {
              fVar38 = (float)FUN_180006bb0((int *)local_6e0,uVar17,(longlong)local_5d0,*local_340);
              FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_83,(longlong)local_620,(int)fVar35);
              fVar38 = (float)FUN_180006bb0((int *)local_6e0,extraout_x1_84,(longlong)local_5c0,
                                            *piVar26);
              FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_85,(longlong)local_610,(int)fVar35);
              fVar38 = (float)FUN_180006bb0((int *)local_6e0,extraout_x1_86,(longlong)local_5c8,
                                            *piVar26);
              uVar17 = extraout_x1_87;
              goto LAB_180053270;
            }
            fVar38 = (float)FUN_180006bb0((int *)local_6e0,uVar17,(longlong)local_5d0,*local_340);
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_72,(longlong)local_620,(int)fVar35);
            piVar9 = local_5c0;
            fVar38 = (float)FUN_180006bb0((int *)local_6e0,extraout_x1_73,(longlong)local_5c0,
                                          *piVar26);
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_74,(longlong)local_610,(int)fVar35);
            piVar8 = local_5c8;
            fVar38 = (float)FUN_180006bb0((int *)local_6e0,extraout_x1_75,(longlong)local_5c8,
                                          *piVar26);
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_76,(longlong)local_618,(int)fVar35);
            piVar26 = local_310;
            fVar38 = (float)FUN_180006bb0((int *)local_6e0,extraout_x1_77,(longlong)piVar20,
                                          *local_310);
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_78,(longlong)local_638,(int)fVar35);
            fVar38 = (float)FUN_180006bb0((int *)local_6e0,extraout_x1_79,(longlong)piVar9,*piVar26)
            ;
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_80,(longlong)local_628,(int)fVar35);
            fVar38 = (float)FUN_180006bb0((int *)local_6e0,extraout_x1_81,(longlong)piVar8,*piVar26)
            ;
            uVar17 = extraout_x1_82;
          }
          FUN_1800069e8(fVar38,(int *)local_6e0,uVar17,(longlong)local_630,(int)fVar35);
          if (((local_4b8._0_4_ != fVar35) && (bVar11 = FUN_180012858((longlong)&local_200), bVar11)
              ) && (bVar11 = FUN_180026690((longlong)local_4c0), pfVar7 = local_618,
                   pfVar5 = local_620, pfVar24 = local_628, pfVar23 = local_630, plVar21 = local_638
                   , bVar11)) {
            uVar17 = extraout_x1_91;
            pfVar22 = local_658;
            for (iVar12 = (int)local_508; iVar12 < (int)fVar35; iVar12 = iVar12 + 1) {
              local_658 = pfVar22;
              FUN_1800069e8(0.0,(int *)local_6e0,uVar17,(longlong)pfVar5,iVar12);
              FUN_1800069e8(0.0,(int *)local_6e0,extraout_x1_92,(longlong)local_610,iVar12);
              FUN_1800069e8(0.0,(int *)local_6e0,extraout_x1_93,(longlong)pfVar7,iVar12);
              FUN_1800069e8(0.0,(int *)local_6e0,extraout_x1_94,(longlong)plVar21,iVar12);
              FUN_1800069e8(0.0,(int *)local_6e0,extraout_x1_95,(longlong)pfVar24,iVar12);
              FUN_1800069e8(0.0,(int *)local_6e0,extraout_x1_96,(longlong)pfVar23,iVar12);
              uVar17 = extraout_x1_97;
              pfVar22 = local_658;
            }
            local_658._0_4_ = SUB84(pfVar22,0);
            if ((int)local_508 <= (int)(float)local_658) {
              local_658._0_4_ = (float)(int)local_508;
            }
            local_658._4_4_ = (undefined4)((ulonglong)pfVar22 >> 0x20);
          }
          uVar13 = FUN_180026550((longlong)local_390);
          plVar21 = local_640;
          switch(uVar13) {
          case 0:
            local_660._0_4_ = 4.2039e-44;
            break;
          case 1:
            local_660._0_4_ = 8.40779e-44;
            break;
          case 2:
            local_660._0_4_ = 4.2039e-43;
            break;
          case 3:
            local_660._0_4_ = 1.26117e-42;
            break;
          case 4:
            local_660._0_4_ = 2.52234e-42;
            break;
          case 5:
            local_660._0_4_ = 5.04467e-42;
          }
          local_500 = local_1c8;
          pfVar23 = local_1c8 + (longlong)(int)(float)local_660 * 250000;
          if (local_1f0 == local_1c8) {
            *local_4e8 = fVar35;
            pfVar24 = (float *)FUN_180005d08((longlong *)(param_3 + 0x28e),(int)fVar35);
            fVar38 = *pfVar24;
            uVar17 = extraout_x1_99;
            plVar21 = local_640;
LAB_180053460:
            FUN_1800069e8(fVar38,(int *)local_6e0,uVar17,(longlong)plVar21,(int)fVar35);
          }
          else if (fVar35 != *local_4e8) {
            fVar38 = (float)FUN_180006bb0((int *)local_6e0,extraout_x1_98,(longlong)local_640,
                                          (int)fVar35 + -1);
            uVar17 = extraout_x1_x00100;
            goto LAB_180053460;
          }
          local_6c0 = local_1f0;
          bVar11 = FUN_180011f60((longlong *)&local_6c0);
          plVar21 = local_640;
          pfVar7 = local_6a8;
          pfVar5 = local_6b0;
          pfVar24 = local_6b8;
          if (bVar11) {
            fVar38 = (float)FUN_180006bb0((int *)local_6e0,extraout_x1_x00101,(longlong)local_640,
                                          (int)fVar35 + -1);
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_x00102,(longlong)plVar21,(int)fVar35);
            fVar38 = FUN_180026608((longlong)local_5d8);
            pfVar23 = local_6b0;
            fVar40 = (float)FUN_180006bb0((int *)local_6e0,extraout_x1_x00103,(longlong)local_6b0,
                                          (int)fVar35 + -1);
            pfVar24 = local_6a8;
            fVar41 = (float)FUN_180006bb0((int *)local_6e0,extraout_x1_x00104,(longlong)local_6a8,
                                          (int)fVar35 + -1);
            param_2 = (ulonglong)(uint)fVar40;
            FUN_18004ff40(fVar41,fVar40,fVar38,(longlong)param_3,(ulonglong)(uint)fVar35,
                          (int *)local_6e0,(longlong)pfVar24,(longlong)pfVar23,(longlong)local_6b8,
                          (longlong)local_690,(longlong)local_698,(longlong)local_6a0,
                          (longlong)local_668,(longlong)local_670,(longlong)local_678,
                          (longlong)local_680,(longlong)local_688);
LAB_18005391c:
            fVar38 = local_520[6];
            pfVar23 = (float *)FUN_180005d08((longlong *)(local_308 + 0x16),(int)fVar35);
            *pfVar23 = fVar38;
            fVar38 = local_510[6];
            pfVar23 = (float *)FUN_180005d08((longlong *)(local_300 + 0x16),(int)fVar35);
            *pfVar23 = fVar38;
            fVar38 = local_528[6];
          }
          else {
            if (extraout_x13 == local_500) {
              pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),(int)fVar35);
              local_6d8[9] = *pfVar23;
              pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)fVar35);
              param_2 = (ulonglong)(uint)*pfVar23;
              local_6d8[10] = *pfVar23;
              bVar11 = FUN_180026690((longlong)local_388);
              if (bVar11) {
                fVar38 = FUN_180026608((longlong)local_5d8);
                FUN_18004ff40(local_6d8[9],(float)param_2,fVar38,(longlong)param_3,
                              (ulonglong)(uint)fVar35,(int *)local_6e0,(longlong)local_6a8,
                              (longlong)local_6b0,(longlong)local_6b8,(longlong)local_690,
                              (longlong)local_698,(longlong)local_6a0,(longlong)local_668,
                              (longlong)local_670,(longlong)local_678,(longlong)local_680,
                              (longlong)local_688);
              }
              else {
                fVar38 = FUN_180026608((longlong)local_5d8);
                param_2 = 0;
                FUN_18004ff40(0.0,0.0,fVar38,(longlong)param_3,(ulonglong)(uint)fVar35,
                              (int *)local_6e0,(longlong)local_6a8,(longlong)local_6b0,
                              (longlong)local_6b8,(longlong)local_690,(longlong)local_698,
                              (longlong)local_6a0,(longlong)local_668,(longlong)local_670,
                              (longlong)local_678,(longlong)local_680,(longlong)local_688);
              }
            }
            else {
              if (((longlong)extraout_x13 <= (longlong)local_500) ||
                 ((longlong)pfVar23 <= (longlong)extraout_x13)) {
                if ((local_1e8 < (longlong)pfVar23) && ((longlong)pfVar23 <= (longlong)extraout_x13)
                   ) {
                  for (fVar38 = *local_4e8; (int)fVar38 < (int)fVar35;
                      fVar38 = (float)((int)fVar38 + 1)) {
                    fVar40 = FUN_180026608((longlong)local_5d8);
                    FUN_18004ff40(local_6d8[9],local_6d8[10],fVar40,(longlong)param_3,
                                  (ulonglong)(uint)fVar38,(int *)local_6e0,(longlong)pfVar7,
                                  (longlong)pfVar5,(longlong)pfVar24,(longlong)local_690,
                                  (longlong)local_698,(longlong)local_6a0,(longlong)local_668,
                                  (longlong)local_670,(longlong)local_678,(longlong)local_680,
                                  (longlong)local_688);
                  }
                  fVar38 = *local_4e8;
                  if ((int)(float)local_658 < (int)*local_4e8) {
                    fVar38 = (float)local_658;
                  }
                  local_658._4_4_ = (undefined4)((ulonglong)local_658 >> 0x20);
                  local_658 = (float *)CONCAT44(local_658._4_4_,fVar38);
                }
                fVar38 = FUN_180026608((longlong)local_5d8);
                param_2 = (ulonglong)(uint)local_6d8[10];
                FUN_18004ff40(local_6d8[9],local_6d8[10],fVar38,(longlong)param_3,
                              (ulonglong)(uint)fVar35,(int *)local_6e0,(longlong)local_6a8,
                              (longlong)local_6b0,(longlong)local_6b8,(longlong)local_690,
                              (longlong)local_698,(longlong)local_6a0,(longlong)local_668,
                              (longlong)local_670,(longlong)local_678,(longlong)local_680,
                              (longlong)local_688);
                goto LAB_18005391c;
              }
              pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),(int)fVar35);
              fVar38 = local_6d8[9];
              uVar44 = SUB41(fVar38,0);
              uVar45 = (undefined1)((uint)fVar38 >> 8);
              uVar46 = (undefined1)((uint)fVar38 >> 0x10);
              uVar47 = (undefined1)((uint)fVar38 >> 0x18);
              if (fVar38 < *pfVar23) {
                puVar32 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x298),(int)fVar35);
                uVar39 = *puVar32;
                uVar44 = (undefined1)uVar39;
                uVar45 = (undefined1)((uint)uVar39 >> 8);
                uVar46 = (undefined1)((uint)uVar39 >> 0x10);
                uVar47 = (undefined1)((uint)uVar39 >> 0x18);
              }
              local_6d8[9] = (float)CONCAT13(uVar47,CONCAT12(uVar46,CONCAT11(uVar45,uVar44)));
              pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)fVar35);
              fVar38 = local_6d8[10];
              uVar44 = SUB41(fVar38,0);
              uVar45 = (undefined1)((uint)fVar38 >> 8);
              uVar46 = (undefined1)((uint)fVar38 >> 0x10);
              uVar47 = (undefined1)((uint)fVar38 >> 0x18);
              if (*pfVar23 < fVar38) {
                puVar32 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)fVar35);
                uVar39 = *puVar32;
                uVar44 = (undefined1)uVar39;
                uVar45 = (undefined1)((uint)uVar39 >> 8);
                uVar46 = (undefined1)((uint)uVar39 >> 0x10);
                uVar47 = (undefined1)((uint)uVar39 >> 0x18);
              }
              local_6d8[10] = (float)CONCAT13(uVar47,CONCAT12(uVar46,CONCAT11(uVar45,uVar44)));
              bVar11 = FUN_180026690((longlong)local_388);
              pfVar5 = local_6a8;
              pfVar24 = local_6b0;
              pfVar23 = local_6b8;
              if (bVar11) {
                for (fVar38 = *local_4e8; (int)fVar38 <= (int)fVar35;
                    fVar38 = (float)((int)fVar38 + 1)) {
                  fVar40 = FUN_180026608((longlong)local_5d8);
                  param_2 = (ulonglong)(uint)local_6d8[10];
                  FUN_18004ff40(local_6d8[9],local_6d8[10],fVar40,(longlong)param_3,
                                (ulonglong)(uint)fVar38,(int *)local_6e0,(longlong)pfVar5,
                                (longlong)pfVar24,(longlong)pfVar23,(longlong)local_690,
                                (longlong)local_698,(longlong)local_6a0,(longlong)local_668,
                                (longlong)local_670,(longlong)local_678,(longlong)local_680,
                                (longlong)local_688);
                }
                if ((int)*local_4e8 <= (int)(float)local_658) {
                  local_658._4_4_ = (undefined4)((ulonglong)local_658 >> 0x20);
                  local_658 = (float *)CONCAT44(local_658._4_4_,*local_4e8);
                }
              }
              else {
                fVar38 = FUN_180026608((longlong)local_5d8);
                param_2 = 0;
                FUN_18004ff40(0.0,0.0,fVar38,(longlong)param_3,(ulonglong)(uint)fVar35,
                              (int *)local_6e0,(longlong)local_6a8,(longlong)local_6b0,
                              (longlong)local_6b8,(longlong)local_690,(longlong)local_698,
                              (longlong)local_6a0,(longlong)local_668,(longlong)local_670,
                              (longlong)local_678,(longlong)local_680,(longlong)local_688);
              }
            }
            fVar38 = local_520[7];
            pfVar23 = (float *)FUN_180005d08((longlong *)(local_308 + 0x16),(int)fVar35);
            *pfVar23 = fVar38;
            fVar38 = local_510[7];
            pfVar23 = (float *)FUN_180005d08((longlong *)(local_300 + 0x16),(int)fVar35);
            *pfVar23 = fVar38;
            fVar38 = local_528[7];
          }
          pfVar23 = (float *)FUN_180005d08((longlong *)(local_2f8 + 0x16),(int)fVar35);
          *pfVar23 = fVar38;
          uVar17 = extraout_x1_x00105;
          pfVar23 = local_1f0;
          if (((local_4b8._0_4_ != fVar35) && (local_1f0 == local_500)) &&
             (bVar11 = FUN_180026690((longlong)local_4c0), plVar21 = local_5d8, pfVar7 = local_6a8,
             pfVar5 = local_6b0, pfVar24 = local_6b8, uVar17 = extraout_x1_x00106,
             pfVar23 = extraout_x11_02, bVar11)) {
            pfVar23 = extraout_x11_02;
            pfVar22 = local_658;
            for (uVar13 = (uint)local_648._0_4_; (int)uVar13 < (int)fVar35; uVar13 = uVar13 + 1) {
              local_658 = pfVar22;
              FUN_1800069e8(0.0,(int *)local_6e0,uVar17,(longlong)local_640,uVar13);
              fVar38 = FUN_180026608((longlong)plVar21);
              param_2 = 0;
              FUN_18004ff40(0.0,0.0,fVar38,(longlong)param_3,(ulonglong)uVar13,(int *)local_6e0,
                            (longlong)pfVar7,(longlong)pfVar5,(longlong)pfVar24,(longlong)local_690,
                            (longlong)local_698,(longlong)local_6a0,(longlong)local_668,
                            (longlong)local_670,(longlong)local_678,(longlong)local_680,
                            (longlong)local_688);
              uVar17 = extraout_x1_x00107;
              pfVar23 = local_1f0;
              pfVar22 = local_658;
            }
            local_658._0_4_ = SUB84(pfVar22,0);
            if ((int)local_648._0_4_ <= (int)(float)local_658) {
              local_658._0_4_ = local_648._0_4_;
            }
            local_658._4_4_ = (undefined4)((ulonglong)pfVar22 >> 0x20);
          }
          local_4c8 = local_610;
          local_4f8 = local_618;
          local_4e0 = local_620;
          local_4d8 = local_628;
          local_4d0 = local_630;
          local_398 = local_638;
          local_3a0 = local_5c0;
          local_3a8 = local_5c8;
          local_3b0 = local_5d0;
          local_3b8 = local_640;
          local_378 = local_520;
          local_3c0 = local_6a8;
          local_3c8 = local_528;
          local_3d0 = local_6b0;
          local_3d8 = local_510;
          local_3e0 = local_6b8;
          local_3e8 = local_5b8;
          local_3f0 = local_5b0;
          local_3f8 = local_5a8;
          local_400 = local_608;
          local_408 = local_600;
          local_428 = local_650;
          local_430 = local_5f8;
          local_438 = local_5e8;
          local_440 = local_5e0;
          local_498 = local_690;
          local_450 = local_698;
          local_458 = local_6a0;
          local_460 = local_668;
          local_468 = local_670;
          local_470 = local_678;
          local_478 = local_680;
          local_480 = local_688;
          local_488 = local_518;
          local_490 = local_5a0;
          local_648 = local_5f0;
          if (local_1a8 < (longlong)local_1c8) {
            if (((longlong)local_1c8 <= (longlong)pfVar23) ||
               (bVar11 = false, (longlong)pfVar23 < local_1a8)) goto LAB_180053bb0;
          }
          else if (((longlong)pfVar23 < (longlong)local_1c8) || (local_1a8 <= (longlong)pfVar23)) {
            bVar11 = false;
          }
          else {
LAB_180053bb0:
            bVar11 = true;
          }
          if (bVar11) {
            pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),(int)fVar35);
            fVar38 = *local_6d8;
            uVar44 = SUB41(fVar38,0);
            uVar45 = (undefined1)((uint)fVar38 >> 8);
            uVar46 = (undefined1)((uint)fVar38 >> 0x10);
            uVar47 = (undefined1)((uint)fVar38 >> 0x18);
            if (fVar38 <= *pfVar23) {
              puVar32 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x298),(int)fVar35);
              uVar39 = *puVar32;
              uVar44 = (undefined1)uVar39;
              uVar45 = (undefined1)((uint)uVar39 >> 8);
              uVar46 = (undefined1)((uint)uVar39 >> 0x10);
              uVar47 = (undefined1)((uint)uVar39 >> 0x18);
            }
            *local_6d8 = (float)CONCAT13(uVar47,CONCAT12(uVar46,CONCAT11(uVar45,uVar44)));
            pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)fVar35);
            fVar38 = local_6d8[1];
            uVar44 = SUB41(fVar38,0);
            uVar45 = (undefined1)((uint)fVar38 >> 8);
            uVar46 = (undefined1)((uint)fVar38 >> 0x10);
            uVar47 = (undefined1)((uint)fVar38 >> 0x18);
            if (*pfVar23 <= fVar38) {
              puVar32 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)fVar35);
              uVar39 = *puVar32;
              uVar44 = (undefined1)uVar39;
              uVar45 = (undefined1)((uint)uVar39 >> 8);
              uVar46 = (undefined1)((uint)uVar39 >> 0x10);
              uVar47 = (undefined1)((uint)uVar39 >> 0x18);
            }
            local_6d8[1] = (float)CONCAT13(uVar47,CONCAT12(uVar46,CONCAT11(uVar45,uVar44)));
            pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2ac),(int)fVar35);
            local_6d8[8] = *pfVar23;
            uVar17 = extraout_x1_x00108;
            pfVar23 = local_1f0;
          }
          if ((longlong)local_1a0 < (longlong)local_1c8) {
            if (((longlong)local_1c8 <= (longlong)pfVar23) ||
               (bVar11 = false, (longlong)pfVar23 < (longlong)local_1a0)) goto LAB_180053c84;
          }
          else if (((longlong)pfVar23 < (longlong)local_1c8) ||
                  ((longlong)local_1a0 <= (longlong)pfVar23)) {
            bVar11 = false;
          }
          else {
LAB_180053c84:
            bVar11 = true;
          }
          if (bVar11) {
            pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),(int)fVar35);
            fVar38 = local_6d8[4];
            uVar44 = SUB41(fVar38,0);
            uVar45 = (undefined1)((uint)fVar38 >> 8);
            uVar46 = (undefined1)((uint)fVar38 >> 0x10);
            uVar47 = (undefined1)((uint)fVar38 >> 0x18);
            if (fVar38 <= *pfVar23) {
              puVar32 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x298),(int)fVar35);
              uVar39 = *puVar32;
              uVar44 = (undefined1)uVar39;
              uVar45 = (undefined1)((uint)uVar39 >> 8);
              uVar46 = (undefined1)((uint)uVar39 >> 0x10);
              uVar47 = (undefined1)((uint)uVar39 >> 0x18);
            }
            local_6d8[4] = (float)CONCAT13(uVar47,CONCAT12(uVar46,CONCAT11(uVar45,uVar44)));
            pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)fVar35);
            fVar38 = local_6d8[5];
            uVar44 = SUB41(fVar38,0);
            uVar45 = (undefined1)((uint)fVar38 >> 8);
            uVar46 = (undefined1)((uint)fVar38 >> 0x10);
            uVar47 = (undefined1)((uint)fVar38 >> 0x18);
            uVar17 = extraout_x1_x00109;
            if (*pfVar23 <= fVar38) {
              puVar32 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)fVar35);
              uVar39 = *puVar32;
              uVar44 = (undefined1)uVar39;
              uVar45 = (undefined1)((uint)uVar39 >> 8);
              uVar46 = (undefined1)((uint)uVar39 >> 0x10);
              uVar47 = (undefined1)((uint)uVar39 >> 0x18);
              uVar17 = extraout_x1_x00110;
            }
            local_6d8[5] = (float)CONCAT13(uVar47,CONCAT12(uVar46,CONCAT11(uVar45,uVar44)));
            pfVar23 = local_1f0;
          }
          if ((longlong)local_1c8 < (longlong)local_1b0) {
            if (((longlong)local_1b0 <= (longlong)pfVar23) ||
               (bVar11 = false, (longlong)pfVar23 < (longlong)local_1c8)) goto LAB_180053d40;
          }
          else if (((longlong)pfVar23 < (longlong)local_1b0) ||
                  ((longlong)local_1c8 <= (longlong)pfVar23)) {
            bVar11 = false;
          }
          else {
LAB_180053d40:
            bVar11 = true;
          }
          if (bVar11) {
            pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),(int)fVar35);
            fVar38 = local_6d8[2];
            uVar44 = SUB41(fVar38,0);
            uVar45 = (undefined1)((uint)fVar38 >> 8);
            uVar46 = (undefined1)((uint)fVar38 >> 0x10);
            uVar47 = (undefined1)((uint)fVar38 >> 0x18);
            if (fVar38 <= *pfVar23) {
              puVar32 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x298),(int)fVar35);
              uVar39 = *puVar32;
              uVar44 = (undefined1)uVar39;
              uVar45 = (undefined1)((uint)uVar39 >> 8);
              uVar46 = (undefined1)((uint)uVar39 >> 0x10);
              uVar47 = (undefined1)((uint)uVar39 >> 0x18);
            }
            local_6d8[2] = (float)CONCAT13(uVar47,CONCAT12(uVar46,CONCAT11(uVar45,uVar44)));
            pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)fVar35);
            fVar38 = local_6d8[3];
            uVar44 = SUB41(fVar38,0);
            uVar45 = (undefined1)((uint)fVar38 >> 8);
            uVar46 = (undefined1)((uint)fVar38 >> 0x10);
            uVar47 = (undefined1)((uint)fVar38 >> 0x18);
            uVar17 = extraout_x1_x00111;
            if (*pfVar23 <= fVar38) {
              puVar32 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)fVar35);
              uVar39 = *puVar32;
              uVar44 = (undefined1)uVar39;
              uVar45 = (undefined1)((uint)uVar39 >> 8);
              uVar46 = (undefined1)((uint)uVar39 >> 0x10);
              uVar47 = (undefined1)((uint)uVar39 >> 0x18);
              uVar17 = extraout_x1_x00112;
            }
            local_6d8[3] = (float)CONCAT13(uVar47,CONCAT12(uVar46,CONCAT11(uVar45,uVar44)));
            pfVar23 = local_1f0;
          }
          if (pfVar23 == local_1c8) {
            FUN_1800069e8(local_6d8[2],(int *)local_6e0,uVar17,(longlong)local_608,(int)fVar35);
            FUN_1800069e8(local_6d8[3],(int *)local_6e0,extraout_x1_x00113,(longlong)local_600,
                          (int)fVar35);
          }
          local_230 = (float *)0x0;
          lStack_228 = 0;
          local_230 = (float *)FUN_180096150(0x18);
          *(float **)local_230 = local_230;
          *(float **)(local_230 + 2) = local_230;
          FUN_180055320((longlong *)&local_230,(longlong *)&local_2f0);
          FUN_180055320((longlong *)&local_230,(longlong *)&local_328);
          FUN_180055320((longlong *)&local_230,(longlong *)&local_330);
          pfVar23 = local_230;
          if (lStack_228 == 0xaaaaaaaaaaaaaaa) {
LAB_1800552c0:
                    /* WARNING: Subroutine does not return */
            FUN_1800941a8(0x1800d5268);
          }
          local_2e0 = &local_230;
          local_2d8 = 0;
          puVar14 = (undefined8 *)FUN_180096150(0x18);
          puVar14[2] = local_560;
          local_2d8 = 0;
          lStack_228 = lStack_228 + 1;
          puVar28 = *(undefined8 **)(pfVar23 + 2);
          *puVar14 = pfVar23;
          puVar14[1] = puVar28;
          *(undefined8 **)(pfVar23 + 2) = puVar14;
          *puVar28 = puVar14;
          local_6c0 = local_568;
          FUN_180055320((longlong *)&local_230,(longlong *)&local_6c0);
          pfVar23 = local_230;
          if (lStack_228 == 0xaaaaaaaaaaaaaaa) goto LAB_1800552c0;
          local_2d0 = &local_230;
          local_2c8 = 0;
          puVar14 = (undefined8 *)FUN_180096150(0x18);
          pfVar24 = local_230;
          puVar14[2] = local_570;
          local_2c8 = 0;
          lStack_228 = lStack_228 + 1;
          puVar28 = *(undefined8 **)(pfVar23 + 2);
          *puVar14 = pfVar23;
          puVar14[1] = puVar28;
          *(undefined8 **)(pfVar23 + 2) = puVar14;
          *puVar28 = puVar14;
          if (lStack_228 == 0xaaaaaaaaaaaaaaa) goto LAB_1800552c0;
          local_2c0 = &local_230;
          local_2b8 = 0;
          puVar14 = (undefined8 *)FUN_180096150(0x18);
          pfVar23 = local_230;
          puVar14[2] = local_578;
          local_2b8 = 0;
          lStack_228 = lStack_228 + 1;
          puVar28 = *(undefined8 **)(pfVar24 + 2);
          *puVar14 = pfVar24;
          puVar14[1] = puVar28;
          *(undefined8 **)(pfVar24 + 2) = puVar14;
          *puVar28 = puVar14;
          if (lStack_228 == 0xaaaaaaaaaaaaaaa) goto LAB_1800552c0;
          local_2b0 = &local_230;
          local_2a8 = 0;
          puVar14 = (undefined8 *)FUN_180096150(0x18);
          pfVar24 = local_230;
          puVar14[2] = local_580;
          local_2a8 = 0;
          lStack_228 = lStack_228 + 1;
          puVar28 = *(undefined8 **)(pfVar23 + 2);
          *puVar14 = pfVar23;
          puVar14[1] = puVar28;
          *(undefined8 **)(pfVar23 + 2) = puVar14;
          *puVar28 = puVar14;
          if (lStack_228 == 0xaaaaaaaaaaaaaaa) goto LAB_1800552c0;
          local_2a0 = &local_230;
          local_298 = 0;
          puVar14 = (undefined8 *)FUN_180096150(0x18);
          pfVar23 = local_230;
          puVar14[2] = local_588;
          local_298 = 0;
          lStack_228 = lStack_228 + 1;
          puVar28 = *(undefined8 **)(pfVar24 + 2);
          *puVar14 = pfVar24;
          puVar14[1] = puVar28;
          *(undefined8 **)(pfVar24 + 2) = puVar14;
          *puVar28 = puVar14;
          if (lStack_228 == 0xaaaaaaaaaaaaaaa) goto LAB_1800552c0;
          local_290 = &local_230;
          local_288 = 0;
          puVar14 = (undefined8 *)FUN_180096150(0x18);
          pfVar24 = local_230;
          puVar14[2] = local_590;
          local_288 = 0;
          lStack_228 = lStack_228 + 1;
          puVar28 = *(undefined8 **)(pfVar23 + 2);
          *puVar14 = pfVar23;
          puVar14[1] = puVar28;
          *(undefined8 **)(pfVar23 + 2) = puVar14;
          *puVar28 = puVar14;
          if (lStack_228 == 0xaaaaaaaaaaaaaaa) goto LAB_1800552c0;
          local_540 = &local_230;
          local_538 = 0;
          auVar48 = FUN_180096150(0x18);
          pfVar23 = local_230;
          uVar17 = auVar48._8_8_;
          puVar14 = auVar48._0_8_;
          puVar14[2] = local_598;
          local_538 = 0;
          lStack_228 = lStack_228 + 1;
          puVar28 = *(undefined8 **)(pfVar24 + 2);
          *puVar14 = pfVar24;
          puVar14[1] = puVar28;
          *(undefined8 **)(pfVar24 + 2) = puVar14;
          *puVar28 = puVar14;
          if (local_188._1_1_ == '\0') {
            local_140 = &DAT_1800d4ecd;
            local_150 = &DAT_1800d4ecd;
            uStack_148 = 0;
            lVar29 = (longlong)local_1a0 % 86400000000;
            iVar12 = ((int)(lVar29 / 1000000) + (int)(lVar29 >> 0x3f)) -
                     (SUB164(SEXT816(lVar29) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
            if (86399999999 < lVar29) {
              iVar12 = 0;
            }
            lVar29 = (longlong)iVar12 * 1000000 + (local_1e8 / 86400000000) * 86400000000;
            local_188 = CONCAT62(local_188._2_6_,0x100);
            if (local_1f0 == local_1a0) {
LAB_180054164:
              local_188 = CONCAT62(local_188._2_6_,0x101);
            }
            else if (local_1e8 != lVar29) {
              if ((longlong)local_1f0 < local_1e8) {
                if ((local_1e8 <= lVar29) || (bVar11 = false, lVar29 < (longlong)local_1f0))
                goto LAB_18005415c;
              }
              else if ((lVar29 < local_1e8) || ((longlong)local_1f0 <= lVar29)) {
                bVar11 = false;
              }
              else {
LAB_18005415c:
                bVar11 = true;
              }
              if (bVar11) goto LAB_180054164;
            }
            if (local_188._1_1_ != '\0') goto LAB_180054174;
LAB_1800544dc:
            local_410 = local_548;
            local_418 = local_550;
            local_420 = local_558;
            local_448 = local_560;
            if ((longlong)local_1a0 < (longlong)local_1c8) {
              if (((longlong)local_1c8 <= (longlong)local_1f0) ||
                 (bVar11 = false, (longlong)local_1f0 < (longlong)local_1a0)) goto LAB_180054548;
            }
            else if (((longlong)local_1f0 < (longlong)local_1c8) ||
                    ((longlong)local_1a0 <= (longlong)local_1f0)) {
              bVar11 = false;
            }
            else {
LAB_180054548:
              bVar11 = true;
            }
            if (!bVar11) {
              if ((longlong)local_1c8 < local_1a8) {
                if ((local_1a8 <= (longlong)local_1f0) ||
                   (bVar11 = false, (longlong)local_1f0 < (longlong)local_1c8)) goto LAB_180054588;
              }
              else if (((longlong)local_1f0 < local_1a8) ||
                      ((longlong)local_1c8 <= (longlong)local_1f0)) {
                bVar11 = false;
              }
              else {
LAB_180054588:
                bVar11 = true;
              }
              local_5f0 = local_648;
              local_5a0 = local_490;
              local_518 = local_488;
              local_688 = local_480;
              local_680 = local_478;
              local_678 = local_470;
              local_670 = local_468;
              local_668 = local_460;
              local_6a0 = local_458;
              local_698 = local_450;
              local_690 = local_498;
              local_5e0 = local_440;
              local_5e8 = local_438;
              local_5f8 = local_430;
              local_650 = local_428;
              local_600 = local_408;
              local_608 = local_400;
              local_5a8 = local_3f8;
              local_5b0 = local_3f0;
              local_5b8 = local_3e8;
              local_6b8 = local_3e0;
              local_510 = local_3d8;
              local_6b0 = local_3d0;
              local_528 = local_3c8;
              local_6a8 = local_3c0;
              local_520 = local_378;
              local_640 = local_3b8;
              local_5d0 = local_3b0;
              local_5c8 = local_3a8;
              local_5c0 = local_3a0;
              local_638 = local_398;
              local_630 = local_4d0;
              local_628 = local_4d8;
              local_620 = local_4e0;
              local_618 = local_4f8;
              local_610 = local_4c8;
              if (!bVar11) goto LAB_1800549c4;
            }
            pfVar24 = *(float **)local_230;
            local_500 = local_230;
            local_6d0 = local_230;
            local_5f0 = local_648;
            local_5a0 = local_490;
            local_518 = local_488;
            local_688 = local_480;
            local_680 = local_478;
            local_678 = local_470;
            local_670 = local_468;
            local_668 = local_460;
            local_6a0 = local_458;
            local_698 = local_450;
            local_690 = local_498;
            local_5e0 = local_440;
            local_5e8 = local_438;
            local_5f8 = local_430;
            local_650 = local_428;
            local_600 = local_408;
            local_608 = local_400;
            local_5a8 = local_3f8;
            local_5b0 = local_3f0;
            local_5b8 = local_3e8;
            local_6b8 = local_3e0;
            local_510 = local_3d8;
            local_6b0 = local_3d0;
            local_528 = local_3c8;
            local_6a8 = local_3c0;
            local_520 = local_378;
            local_640 = local_3b8;
            local_5d0 = local_3b0;
            local_5c8 = local_3a8;
            local_5c0 = local_3a0;
            local_638 = local_398;
            local_630 = local_4d0;
            local_628 = local_4d8;
            local_620 = local_4e0;
            local_618 = local_4f8;
            local_610 = local_4c8;
            local_6c0 = pfVar24;
            local_280 = pfVar24;
            if (pfVar24 != local_230) {
              do {
                FUN_1800069e8(0.0,(int *)local_6e0,uVar17,*(longlong *)(pfVar24 + 4),(int)fVar35);
                pfVar24 = *(float **)pfVar24;
                uVar17 = extraout_x1_x00124;
              } while (pfVar24 != pfVar23);
            }
          }
          else {
LAB_180054174:
            if ((char)local_188 == '\0') goto LAB_1800544dc;
            local_4d0 = local_548;
            FUN_1800069e8(local_6d8[4],(int *)local_6e0,uVar17,(longlong)local_548,(int)fVar35);
            local_4e0 = local_558;
            FUN_1800069e8(local_6d8[5],(int *)local_6e0,extraout_x1_x00114,(longlong)local_558,
                          (int)fVar35);
            local_4d8 = local_550;
            FUN_1800069e8((local_6d8[4] + local_6d8[5]) * 0.5,(int *)local_6e0,extraout_x1_x00115,
                          (longlong)local_550,(int)fVar35);
            plVar21 = local_380;
            fVar40 = local_6d8[4] - local_6d8[5];
            fVar38 = FUN_180026608((longlong)local_380);
            fVar38 = FUN_180011838(fVar38 * fVar40 + extraout_s18,(longlong)param_3);
            local_4f8 = local_560;
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_x00116,(longlong)local_560,(int)fVar35
                         );
            fVar38 = FUN_180026608((longlong)plVar21);
            fVar38 = FUN_180011838(local_6d8[5] - fVar38 * fVar40,(longlong)param_3);
            local_4c8 = local_568;
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_x00117,(longlong)local_568,(int)fVar35
                         );
            fVar38 = FUN_180026608((longlong)plVar21);
            fVar38 = FUN_180011838(fVar38 * fVar40 + fVar38 * fVar40 + local_6d8[4],
                                   (longlong)param_3);
            local_500 = local_570;
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_x00118,(longlong)local_570,(int)fVar35
                         );
            fVar38 = FUN_180026608((longlong)plVar21);
            fVar38 = FUN_180011838(local_6d8[5] - (fVar38 * fVar40 + fVar38 * fVar40),
                                   (longlong)param_3);
            local_6c0 = local_578;
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_x00119,(longlong)local_578,(int)fVar35
                         );
            fVar38 = FUN_180026608((longlong)plVar21);
            fVar38 = FUN_180011838(fVar38 * fVar40 * 3.0 + local_6d8[4],(longlong)param_3);
            pfVar24 = local_580;
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_x00120,(longlong)local_580,(int)fVar35
                         );
            fVar38 = FUN_180026608((longlong)plVar21);
            fVar38 = FUN_180011838(local_6d8[5] - fVar38 * fVar40 * 3.0,(longlong)param_3);
            pfVar23 = local_588;
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_x00121,(longlong)local_588,(int)fVar35
                         );
            fVar38 = FUN_180026608((longlong)plVar21);
            fVar38 = FUN_180011838(fVar38 * fVar40 * 4.0 + local_6d8[4],(longlong)param_3);
            plVar36 = local_590;
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_x00122,(longlong)local_590,(int)fVar35
                         );
            fVar38 = FUN_180026608((longlong)plVar21);
            fVar38 = FUN_180011838(local_6d8[5] - fVar38 * fVar40 * 4.0,(longlong)param_3);
            plVar21 = local_598;
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_x00123,(longlong)local_598,(int)fVar35
                         );
            local_578 = local_6c0;
            local_570 = local_500;
            local_568 = local_4c8;
            local_560 = local_4f8;
            local_558 = local_4e0;
            local_550 = local_4d8;
            local_548 = local_4d0;
            local_598 = plVar21;
            local_590 = plVar36;
            local_588 = pfVar23;
            local_580 = pfVar24;
          }
LAB_1800549c4:
          bVar11 = FUN_180012858((longlong)&local_200);
          if (bVar11) {
            FUN_1800069e8(*local_6d8,(int *)local_6e0,extraout_x1_x00125,(longlong)local_5b8,
                          (int)fVar35);
            FUN_1800069e8(local_6d8[1],(int *)local_6e0,extraout_x1_x00126,(longlong)local_5b0,
                          (int)fVar35);
            FUN_1800069e8(local_6d8[8],(int *)local_6e0,extraout_x1_x00127,(longlong)local_5a8,
                          (int)fVar35);
            fVar38 = FUN_180011838((local_6d8[1] + *local_6d8) * 0.5,(longlong)param_3);
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_x00128,(longlong)local_5a0,(int)fVar35
                         );
            pfVar23 = (float *)FUN_18000f448((longlong)local_650,(int)fVar35);
            FUN_1800069e8(*pfVar23,(int *)local_6e0,extraout_x1_x00129,(longlong)local_518,
                          (int)fVar35);
            FUN_1800069e8(0.0,(int *)local_6e0,extraout_x1_x00130,(longlong)local_608,(int)fVar35);
            FUN_1800069e8(0.0,(int *)local_6e0,extraout_x1_x00131,(longlong)local_600,(int)fVar35);
          }
          uVar18 = FUN_180012730((longlong)&local_200);
          if ((uVar18 & 1) != 0) {
            pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),(int)fVar35);
            local_6d8[6] = *pfVar23;
            pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)fVar35);
            local_6d8[7] = *pfVar23;
          }
          local_6d0 = local_1f0;
          bVar11 = FUN_180011f60((longlong *)&local_6d0);
          uVar17 = extraout_x1_x00132;
          if (bVar11) {
LAB_180054bc8:
            FUN_1800069e8(0.0,(int *)local_6e0,uVar17,(longlong)local_650,(int)fVar35);
            FUN_180021950(local_370,param_3,fVar35);
            FUN_1800069e8(0.0,(int *)local_6e0,extraout_x1_x00137,(longlong)local_5f0,(int)fVar35);
            FUN_180012e30(local_4a8,param_3,fVar35);
          }
          else {
            iVar12 = FUN_180004430(&local_278);
            if (iVar12 == 1) {
              if ((longlong)local_1c8 < extraout_x13_00) {
                if ((extraout_x13_00 <= extraout_x14) ||
                   (bVar11 = false, extraout_x14 < (longlong)local_1c8)) goto LAB_180054b28;
              }
              else if ((extraout_x14 < extraout_x13_00) || ((longlong)local_1c8 <= extraout_x14)) {
                bVar11 = false;
              }
              else {
LAB_180054b28:
                bVar11 = true;
              }
              uVar17 = extraout_x1_x00133;
              if (bVar11) goto LAB_180054bc8;
            }
            bVar11 = FUN_180026690((longlong)local_350);
            if (bVar11) {
              if (extraout_x11_03 < local_1a8) {
                if ((local_1a8 <= extraout_x14_00) ||
                   (bVar11 = false, extraout_x14_00 < extraout_x11_03)) goto LAB_180054b74;
              }
              else if ((extraout_x14_00 < local_1a8) || (extraout_x11_03 <= extraout_x14_00)) {
                bVar11 = false;
              }
              else {
LAB_180054b74:
                bVar11 = true;
              }
              uVar17 = extraout_x1_x00134;
              if (bVar11) goto LAB_180054bc8;
            }
            fVar38 = FUN_180021950(local_370,param_3,fVar35);
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_x00135,(longlong)local_650,(int)fVar35
                         );
            fVar38 = (float)FUN_180012e30(local_4a8,param_3,fVar35);
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_x00136,(longlong)local_5f0,(int)fVar35
                         );
          }
          pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),(int)fVar35);
          fVar38 = local_6d8[6];
          uVar44 = SUB41(fVar38,0);
          uVar45 = (undefined1)((uint)fVar38 >> 8);
          uVar46 = (undefined1)((uint)fVar38 >> 0x10);
          uVar47 = (undefined1)((uint)fVar38 >> 0x18);
          if (fVar38 <= *pfVar23) {
            puVar32 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x298),(int)fVar35);
            uVar39 = *puVar32;
            uVar44 = (undefined1)uVar39;
            uVar45 = (undefined1)((uint)uVar39 >> 8);
            uVar46 = (undefined1)((uint)uVar39 >> 0x10);
            uVar47 = (undefined1)((uint)uVar39 >> 0x18);
          }
          local_6d8[6] = (float)CONCAT13(uVar47,CONCAT12(uVar46,CONCAT11(uVar45,uVar44)));
          pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)fVar35);
          fVar38 = local_6d8[7];
          uVar44 = SUB41(fVar38,0);
          uVar45 = (undefined1)((uint)fVar38 >> 8);
          uVar46 = (undefined1)((uint)fVar38 >> 0x10);
          uVar47 = (undefined1)((uint)fVar38 >> 0x18);
          if (*pfVar23 <= fVar38) {
            puVar32 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)fVar35);
            uVar39 = *puVar32;
            uVar44 = (undefined1)uVar39;
            uVar45 = (undefined1)((uint)uVar39 >> 8);
            uVar46 = (undefined1)((uint)uVar39 >> 0x10);
            uVar47 = (undefined1)((uint)uVar39 >> 0x18);
          }
          local_6d8[7] = (float)CONCAT13(uVar47,CONCAT12(uVar46,CONCAT11(uVar45,uVar44)));
          bVar11 = FUN_180026690((longlong)local_358);
          if (bVar11) {
            if ((longlong)local_1c8 < local_1a8) {
              if ((local_1a8 <= (longlong)local_1f0) ||
                 (bVar11 = false, (longlong)local_1f0 < (longlong)local_1c8)) goto LAB_180054cd8;
            }
            else if (((longlong)local_1f0 < local_1a8) ||
                    ((longlong)local_1c8 <= (longlong)local_1f0)) {
              bVar11 = false;
            }
            else {
LAB_180054cd8:
              bVar11 = true;
            }
            if (!bVar11) goto LAB_180054ce8;
            fVar38 = 0.0;
          }
          else {
LAB_180054ce8:
            fVar38 = (local_6d8[1] + *local_6d8) * 0.5;
          }
          FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_x00138,(longlong)local_5e8,(int)fVar35);
          if ((longlong)local_1b0 < local_1a8) {
            if ((local_1a8 <= (longlong)local_1f0) ||
               (bVar11 = false, (longlong)local_1f0 < (longlong)local_1b0)) goto LAB_180054d48;
          }
          else if (((longlong)local_1f0 < local_1a8) || ((longlong)local_1b0 <= (longlong)local_1f0)
                  ) {
            bVar11 = false;
          }
          else {
LAB_180054d48:
            bVar11 = true;
          }
          if (bVar11) {
            FUN_1800069e8(0.0,(int *)local_6e0,extraout_x1_x00139,(longlong)local_5e0,(int)fVar35);
            FUN_1800069e8(0.0,(int *)local_6e0,extraout_x1_x00140,(longlong)local_5f8,(int)fVar35);
            FUN_180021950(local_360,param_3,fVar35);
            FUN_1800069e8(0.0,(int *)local_6e0,extraout_x1_x00141,(longlong)local_4a0,(int)fVar35);
            FUN_180012e30(local_348,param_3,fVar35);
          }
          else {
            fVar38 = FUN_180021950(local_360,param_3,fVar35);
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_x00142,(longlong)local_5f8,(int)fVar35
                         );
            fVar38 = (float)FUN_180012e30(local_348,param_3,fVar35);
            FUN_1800069e8(fVar38,(int *)local_6e0,extraout_x1_x00143,(longlong)local_4a0,(int)fVar35
                         );
            FUN_1800069e8((local_6d8[7] + local_6d8[6]) * 0.5,(int *)local_6e0,extraout_x1_x00144,
                          (longlong)local_5e0,(int)fVar35);
          }
          if (local_1f0 == local_1c8) {
            pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),(int)fVar35);
            *local_6d8 = *pfVar23;
            pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)fVar35);
            local_6d8[1] = *pfVar23;
            pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),(int)fVar35);
            local_6d8[4] = *pfVar23;
            pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)fVar35);
            local_6d8[5] = *pfVar23;
          }
          if (local_1f0 == local_1b0) {
            pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),(int)fVar35);
            local_6d8[2] = *pfVar23;
            pfVar23 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)fVar35);
            local_6d8[3] = *pfVar23;
          }
          if (((local_4b8._0_4_ != fVar35) && (bVar11 = FUN_180012858((longlong)&local_200), bVar11)
              ) && (bVar11 = FUN_180026690((longlong)local_4c0), bVar11)) {
            local_210 = (undefined8 *)0x0;
            uStack_208 = 0;
            local_210 = (undefined8 *)FUN_180096150(0x18);
            *local_210 = local_210;
            local_210[1] = local_210;
            local_6d0 = local_5b8;
            FUN_180055320((longlong *)&local_210,(longlong *)&local_6d0);
            local_6d0 = local_5b0;
            FUN_180055320((longlong *)&local_210,(longlong *)&local_6d0);
            local_6d0 = local_5a8;
            FUN_180055320((longlong *)&local_210,(longlong *)&local_6d0);
            local_6d0 = local_5a0;
            FUN_180055320((longlong *)&local_210,(longlong *)&local_6d0);
            local_6d0 = local_608;
            FUN_180055320((longlong *)&local_210,(longlong *)&local_6d0);
            local_6d0 = local_600;
            FUN_180055320((longlong *)&local_210,(longlong *)&local_6d0);
            local_6d0 = local_650;
            FUN_180055320((longlong *)&local_210,(longlong *)&local_6d0);
            local_6d0 = local_5f8;
            FUN_180055320((longlong *)&local_210,(longlong *)&local_6d0);
            local_6d0 = local_5f0;
            FUN_180055320((longlong *)&local_210,(longlong *)&local_6d0);
            local_6d0 = local_4a0;
            FUN_180055320((longlong *)&local_210,(longlong *)&local_6d0);
            local_6d0 = local_5e8;
            FUN_180055320((longlong *)&local_210,(longlong *)&local_6d0);
            local_6d0 = local_5e0;
            FUN_180055320((longlong *)&local_210,(longlong *)&local_6d0);
            uVar17 = extraout_x1_x00145;
            pfVar23 = local_658;
            pfVar24 = local_230;
            puVar14 = local_210;
            for (iVar12 = (int)local_508; iVar12 < (int)fVar35; iVar12 = iVar12 + 1) {
              pfVar5 = pfVar24;
              puVar6 = puVar14;
              local_658 = pfVar23;
              for (puVar28 = (undefined8 *)*puVar14; puVar28 != puVar14;
                  puVar28 = (undefined8 *)*puVar28) {
                FUN_1800069e8(0.0,(int *)local_6e0,uVar17,puVar28[2],iVar12);
                uVar17 = extraout_x1_x00146;
                pfVar5 = local_230;
                puVar6 = local_210;
              }
              pfVar24 = pfVar5;
              puVar14 = puVar6;
              for (pfVar23 = *(float **)pfVar5; pfVar23 != pfVar5; pfVar23 = *(float **)pfVar23) {
                FUN_1800069e8(0.0,(int *)local_6e0,uVar17,*(longlong *)(pfVar23 + 4),iVar12);
                uVar17 = extraout_x1_x00147;
                pfVar24 = local_230;
                puVar14 = local_210;
              }
              pfVar23 = local_658;
            }
            local_658._0_4_ = SUB84(pfVar23,0);
            if ((int)local_508 <= (int)(float)local_658) {
              local_658._0_4_ = (float)(int)local_508;
            }
            local_658._4_4_ = (undefined4)((ulonglong)pfVar23 >> 0x20);
            *(undefined8 *)local_210[1] = 0;
            puVar14 = (undefined8 *)*local_210;
            while (puVar14 != (undefined8 *)0x0) {
              pvVar37 = (LPVOID)*puVar14;
              FUN_1800966b8(puVar14);
              puVar14 = (undefined8 *)pvVar37;
            }
            FUN_1800966b8(local_210);
          }
          if ((*(int *)((longlong)local_4b0 + 4) == 1) && (fVar35 == (float)(*param_3 + -1))) {
            local_250 = auStack_240;
            puVar14 = FUN_1800553b8(auStack_240,(undefined8 *)(local_6d8 + 0x10));
            uVar13 = FUN_180006c90((uint *)local_6e0,param_3,puVar14,(int)fVar35,(uint)local_6e8);
            if ((uVar13 & 1) != 0) {
              local_658 = (float *)((ulonglong)local_658 & 0xffffffff00000000);
              local_6c8 = '\x01';
            }
          }
          iVar12 = 0;
          do {
            plVar21 = FUN_18000f880((longlong *)(param_3 + 0x140),iVar12);
            fVar38 = (float)FUN_180006bb0((int *)local_6e0,extraout_x1_x00148,(longlong)plVar21,
                                          (int)fVar35);
            if (fVar38 == -2.1474836e+09) {
LAB_1800551a0:
              plVar21 = FUN_18000f880((longlong *)(param_3 + 0x140),iVar12);
              FUN_1800069e8(0.0,(int *)local_6e0,extraout_x1_x00150,(longlong)plVar21,(int)fVar35);
            }
            else {
              plVar21 = FUN_18000f880((longlong *)(param_3 + 0x140),iVar12);
              fVar38 = (float)FUN_180006bb0((int *)local_6e0,extraout_x1_x00149,(longlong)plVar21,
                                            (int)fVar35);
              if (fVar38 == 2.1474836e+09) goto LAB_1800551a0;
            }
            iVar12 = iVar12 + 1;
          } while (iVar12 < 0x3c);
          *local_338 = fVar35;
          **(undefined8 **)(local_230 + 2) = 0;
          puVar14 = (undefined8 *)*(LPVOID *)local_230;
          while (puVar14 != (undefined8 *)0x0) {
            pvVar37 = (LPVOID)*puVar14;
            FUN_1800966b8(puVar14);
            puVar14 = (undefined8 *)pvVar37;
          }
          FUN_1800966b8(local_230);
          fVar35 = (float)((int)fVar35 + 1);
        } while ((int)fVar35 < *param_3);
        fVar35 = (float)local_658;
        cVar33 = local_6c8;
      }
      if (param_3[0x47e] == 0) {
        param_3[0x433] = (int)fVar35;
      }
      if ((cVar33 != '\0') || (local_6e8 != 0)) {
        pcVar25 = FUN_18004fd98((longlong)local_368);
        FUN_18000dd58(&local_150,pcVar25);
        auVar4._4_4_ = extraout_var_63;
        auVar4._0_4_ = extraout_s0_63;
        auVar4._8_8_ = extraout_var_x00128;
        FUN_180025678(auVar4,param_2,(longlong)param_3,&local_150);
        FUN_18000dd08(&local_150);
      }
    }
  }
  else {
    if (local_318 != (int *)0x0) {
      FUN_18001cec0(local_318,uVar18,0);
      FUN_18004a2d0(piVar20);
      (**(code **)(param_3 + 0x448))(2,0);
    }
    FUN_180055680((longlong)param_3,3,&local_658);
    FUN_180055680((longlong)param_3,4,&local_330);
    FUN_1800556e8((longlong)param_3,5,&local_328);
    FUN_1800556e8((longlong)param_3,6,&local_500);
    if (local_6e0 != (float *)0x0) {
      FUN_1800966b8(local_6e0);
      (**(code **)(param_3 + 0x448))(7,0);
    }
    if (local_6d8 != (float *)0x0) {
      FUN_18004feb8(local_6d8);
      FUN_180055758(local_6d8);
      (**(code **)(param_3 + 0x448))(8,0);
    }
  }
LAB_180055278:
  local_220[0] = CustomInput<>::vftable;
  FUN_180004590(&local_170);
  return;
}

