#include "sierrachart.h"

/*==========================================================================*/
SCSFExport scsf_ReconstructedTape(SCStudyInterfaceRef sc)
{
    // Subgraph declarations for reconstructed tape analysis
    SCSubgraphRef Subgraph_AggressiveBuys = sc.Subgraph[0];
    SCSubgraphRef Subgraph_AggressiveSells = sc.Subgraph[1];
    SCSubgraphRef Subgraph_PassiveBuys = sc.Subgraph[2];
    SCSubgraphRef Subgraph_PassiveSells = sc.Subgraph[3];
    SCSubgraphRef Subgraph_NetAggression = sc.Subgraph[4];
    SCSubgraphRef Subgraph_TapeStrength = sc.Subgraph[5];
    SCSubgraphRef Subgraph_VolumeClassification = sc.Subgraph[6];
    SCSubgraphRef Subgraph_OrderFlowImbalance = sc.Subgraph[7];
    SCSubgraphRef Subgraph_MarketMakerActivity = sc.Subgraph[8];
    
    // Input declarations
    SCInputRef Input_TickThreshold = sc.Input[0];
    SCInputRef Input_VolumeThreshold = sc.Input[1];
    SCInputRef Input_AggressionSensitivity = sc.Input[2];
    SCInputRef Input_LookbackPeriod = sc.Input[3];
    SCInputRef Input_DisplayMode = sc.Input[4];
    
    // Persistent variables for tape reconstruction
    float& r_PreviousPrice = sc.GetPersistentFloat(1);
    float& r_PreviousVolume = sc.GetPersistentFloat(2);
    int& r_LastProcessedIndex = sc.GetPersistentInt(1);
    
    if (sc.SetDefaults)
    {
        // Set study configuration
        sc.GraphName = "Reconstructed Tape";
        sc.StudyDescription = "Simulates order book reconstruction from trade data to identify aggressive vs passive orders";
        sc.AutoLoop = 1;
        sc.GraphRegion = 1; // Separate region
        sc.ValueFormat = 0;
        sc.ScaleRangeType = SCALE_INDEPENDENT;
        
        // Configure subgraphs
        Subgraph_AggressiveBuys.Name = "Aggressive Buys";
        Subgraph_AggressiveBuys.DrawStyle = DRAWSTYLE_BAR;
        Subgraph_AggressiveBuys.PrimaryColor = RGB(0, 255, 0); // Green
        Subgraph_AggressiveBuys.LineWidth = 2;
        Subgraph_AggressiveBuys.DrawZeros = false;
        
        Subgraph_AggressiveSells.Name = "Aggressive Sells";
        Subgraph_AggressiveSells.DrawStyle = DRAWSTYLE_BAR;
        Subgraph_AggressiveSells.PrimaryColor = RGB(255, 0, 0); // Red
        Subgraph_AggressiveSells.LineWidth = 2;
        Subgraph_AggressiveSells.DrawZeros = false;
        
        Subgraph_PassiveBuys.Name = "Passive Buys";
        Subgraph_PassiveBuys.DrawStyle = DRAWSTYLE_BAR;
        Subgraph_PassiveBuys.PrimaryColor = RGB(128, 255, 128); // Light Green
        Subgraph_PassiveBuys.LineWidth = 1;
        Subgraph_PassiveBuys.DrawZeros = false;
        
        Subgraph_PassiveSells.Name = "Passive Sells";
        Subgraph_PassiveSells.DrawStyle = DRAWSTYLE_BAR;
        Subgraph_PassiveSells.PrimaryColor = RGB(255, 128, 128); // Light Red
        Subgraph_PassiveSells.LineWidth = 1;
        Subgraph_PassiveSells.DrawZeros = false;
        
        Subgraph_NetAggression.Name = "Net Aggression";
        Subgraph_NetAggression.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_NetAggression.PrimaryColor = RGB(255, 255, 0); // Yellow
        Subgraph_NetAggression.LineWidth = 2;
        Subgraph_NetAggression.DrawZeros = true;
        
        Subgraph_TapeStrength.Name = "Tape Strength";
        Subgraph_TapeStrength.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_TapeStrength.PrimaryColor = RGB(128, 128, 255); // Light Blue
        Subgraph_TapeStrength.LineWidth = 1;
        Subgraph_TapeStrength.DrawZeros = true;
        
        Subgraph_VolumeClassification.Name = "Volume Classification";
        Subgraph_VolumeClassification.DrawStyle = DRAWSTYLE_IGNORE;
        
        Subgraph_OrderFlowImbalance.Name = "Order Flow Imbalance";
        Subgraph_OrderFlowImbalance.DrawStyle = DRAWSTYLE_IGNORE;
        
        Subgraph_MarketMakerActivity.Name = "Market Maker Activity";
        Subgraph_MarketMakerActivity.DrawStyle = DRAWSTYLE_IGNORE;
        
        // Configure inputs
        Input_TickThreshold.Name = "Tick Threshold for Aggression";
        Input_TickThreshold.SetInt(1);
        Input_TickThreshold.SetIntLimits(1, 10);
        
        Input_VolumeThreshold.Name = "Minimum Volume Threshold";
        Input_VolumeThreshold.SetInt(10);
        Input_VolumeThreshold.SetIntLimits(1, 1000);
        
        Input_AggressionSensitivity.Name = "Aggression Sensitivity";
        Input_AggressionSensitivity.SetFloat(1.5f);
        Input_AggressionSensitivity.SetFloatLimits(0.5f, 5.0f);
        
        Input_LookbackPeriod.Name = "Lookback Period";
        Input_LookbackPeriod.SetInt(10);
        Input_LookbackPeriod.SetIntLimits(3, 100);
        
        Input_DisplayMode.Name = "Display Mode";
        Input_DisplayMode.SetCustomInputStrings("All;Aggressive Only;Net Only");
        Input_DisplayMode.SetCustomInputIndex(0);
        
        return;
    }
    
    // Skip if insufficient data
    if (sc.Index < 1)
        return;
        
    // Get current bar data
    float CurrentPrice = sc.Close[sc.Index];
    float CurrentVolume = sc.Volume[sc.Index];
    float CurrentBidVolume = sc.BidVolume[sc.Index];
    float CurrentAskVolume = sc.AskVolume[sc.Index];
    
    // Get previous bar data
    float PreviousPrice = sc.Close[sc.Index - 1];
    float PreviousVolume = sc.Volume[sc.Index - 1];
    
    // Skip if insufficient volume
    if (CurrentVolume < Input_VolumeThreshold.GetInt())
        return;
    
    // Calculate price movement
    float PriceChange = CurrentPrice - PreviousPrice;
    float TickSize = sc.TickSize;
    if (TickSize <= 0) TickSize = 0.01f;
    
    int TickMovement = (int)(PriceChange / TickSize);
    
    // Initialize volume classifications
    float AggressiveBuyVolume = 0;
    float AggressiveSellVolume = 0;
    float PassiveBuyVolume = 0;
    float PassiveSellVolume = 0;
    
    // Reconstruct tape based on price movement and volume distribution
    if (abs(TickMovement) >= Input_TickThreshold.GetInt())
    {
        // Significant price movement indicates aggressive trading
        if (TickMovement > 0)
        {
            // Price moved up - aggressive buying
            AggressiveBuyVolume = CurrentAskVolume * Input_AggressionSensitivity.GetFloat();
            PassiveSellVolume = CurrentBidVolume;
            
            // Adjust for over-allocation
            if (AggressiveBuyVolume > CurrentVolume)
                AggressiveBuyVolume = CurrentVolume * 0.7f;
        }
        else
        {
            // Price moved down - aggressive selling
            AggressiveSellVolume = CurrentBidVolume * Input_AggressionSensitivity.GetFloat();
            PassiveBuyVolume = CurrentAskVolume;
            
            // Adjust for over-allocation
            if (AggressiveSellVolume > CurrentVolume)
                AggressiveSellVolume = CurrentVolume * 0.7f;
        }
    }
    else
    {
        // Minimal price movement - more passive trading
        float VolumeRatio = (PreviousVolume > 0) ? CurrentVolume / PreviousVolume : 1.0f;
        
        if (CurrentAskVolume > CurrentBidVolume)
        {
            // More ask volume - some aggressive buying
            AggressiveBuyVolume = (CurrentAskVolume - CurrentBidVolume) * 0.6f;
            PassiveBuyVolume = CurrentBidVolume;
            PassiveSellVolume = CurrentAskVolume - AggressiveBuyVolume;
        }
        else if (CurrentBidVolume > CurrentAskVolume)
        {
            // More bid volume - some aggressive selling
            AggressiveSellVolume = (CurrentBidVolume - CurrentAskVolume) * 0.6f;
            PassiveSellVolume = CurrentAskVolume;
            PassiveBuyVolume = CurrentBidVolume - AggressiveSellVolume;
        }
        else
        {
            // Balanced - mostly passive
            PassiveBuyVolume = CurrentVolume * 0.5f;
            PassiveSellVolume = CurrentVolume * 0.5f;
        }
    }
    
    // Calculate net aggression and tape strength
    float NetAggression = AggressiveBuyVolume - AggressiveSellVolume;
    float TotalAggressive = AggressiveBuyVolume + AggressiveSellVolume;
    float TapeStrength = (CurrentVolume > 0) ? (TotalAggressive / CurrentVolume) * 100 : 0;
    
    // Calculate order flow imbalance over lookback period
    float TotalBuyAggression = 0;
    float TotalSellAggression = 0;
    
    for (int i = 0; i < Input_LookbackPeriod.GetInt() && (sc.Index - i) >= 0; i++)
    {
        int LookbackIndex = sc.Index - i;
        TotalBuyAggression += Subgraph_AggressiveBuys[LookbackIndex];
        TotalSellAggression += Subgraph_AggressiveSells[LookbackIndex];
    }
    
    float OrderFlowImbalance = (TotalBuyAggression + TotalSellAggression > 0) ? 
        ((TotalBuyAggression - TotalSellAggression) / (TotalBuyAggression + TotalSellAggression)) * 100 : 0;
    
    // Estimate market maker activity (high passive volume relative to aggressive)
    float TotalPassive = PassiveBuyVolume + PassiveSellVolume;
    float MarketMakerActivity = (CurrentVolume > 0) ? (TotalPassive / CurrentVolume) * 100 : 0;
    
    // Set subgraph values based on display mode
    switch (Input_DisplayMode.GetIndex())
    {
        case 0: // All
            Subgraph_AggressiveBuys[sc.Index] = AggressiveBuyVolume;
            Subgraph_AggressiveSells[sc.Index] = -AggressiveSellVolume; // Negative for visual separation
            Subgraph_PassiveBuys[sc.Index] = PassiveBuyVolume;
            Subgraph_PassiveSells[sc.Index] = -PassiveSellVolume; // Negative for visual separation
            break;
            
        case 1: // Aggressive Only
            Subgraph_AggressiveBuys[sc.Index] = AggressiveBuyVolume;
            Subgraph_AggressiveSells[sc.Index] = -AggressiveSellVolume;
            break;
            
        case 2: // Net Only
            // Only show net aggression
            break;
    }
    
    // Always calculate these for analysis
    Subgraph_NetAggression[sc.Index] = NetAggression;
    Subgraph_TapeStrength[sc.Index] = TapeStrength;
    Subgraph_VolumeClassification[sc.Index] = TotalAggressive;
    Subgraph_OrderFlowImbalance[sc.Index] = OrderFlowImbalance;
    Subgraph_MarketMakerActivity[sc.Index] = MarketMakerActivity;
    
    // Update persistent variables
    r_PreviousPrice = CurrentPrice;
    r_PreviousVolume = CurrentVolume;
    r_LastProcessedIndex = sc.Index;
}
