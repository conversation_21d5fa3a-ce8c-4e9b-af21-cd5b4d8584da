
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_Exhaustion_Absorption_Detector
               (undefined1 param_1 [16],undefined8 param_2,longlong param_3,undefined8 param_4,
               undefined8 param_5,undefined8 param_6,undefined8 param_7,undefined8 param_8,
               undefined8 param_9,undefined8 param_10)

{
  int iVar1;
  undefined1 auVar2 [16];
  undefined1 *lpMem;
  uint uVar3;
  HANDLE pvVar4;
  char *pcVar5;
  undefined8 uVar6;
  ulonglong uVar7;
  float *pfVar8;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  longlong lVar9;
  longlong lVar10;
  float *pfVar11;
  undefined4 *puVar12;
  int iVar13;
  uint uVar14;
  float *extraout_x11;
  float *extraout_x11_00;
  float *extraout_x11_01;
  float *extraout_x11_02;
  float *extraout_x11_03;
  float *extraout_x11_04;
  char *lpMem_00;
  longlong lVar15;
  longlong *plVar16;
  longlong lVar17;
  longlong lVar18;
  char *pcVar19;
  longlong *plVar20;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  undefined4 uVar21;
  undefined1 extraout_var [12];
  undefined1 auVar22 [16];
  undefined1 extraout_var_00 [12];
  undefined1 extraout_var_01 [12];
  undefined1 auVar23 [12];
  float fVar24;
  float fVar25;
  float extraout_s18;
  float extraout_s18_00;
  char *local_120;
  undefined8 local_118;
  undefined1 *local_110;
  longlong *local_108;
  longlong *local_100;
  longlong local_f8;
  longlong *local_f0;
  longlong *local_e8;
  longlong *local_e0;
  longlong *local_d8;
  longlong *local_d0;
  longlong *local_c8;
  longlong *local_c0;
  int *local_b8;
  undefined8 local_b0;
  undefined1 *local_a0;
  undefined8 uStack_98;
  undefined1 *local_90;
  
                    /* 0x3f3b8  8  scsf_Exhaustion_Absorption_Detector */
  local_b0 = 0xfffffffffffffffe;
  local_f8 = param_3;
  local_b8 = (int *)(**(code **)(param_3 + 0x18b8))(param_1._0_4_,0);
  auVar22._4_12_ = extraout_var;
  auVar22._0_4_ = extraout_s0;
  lVar9 = *(longlong *)(param_3 + 0x500);
  uVar6 = extraout_x1;
  if (lVar9 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar6 = extraout_x1_00;
    }
    lVar9 = *(longlong *)(param_3 + 0x500);
    if (lVar9 != 0) goto LAB_18003f43c;
    local_d8 = (longlong *)(param_3 + 0x528);
LAB_18003f46c:
    local_e0 = local_d8;
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar6 = extraout_x1_01;
    }
    lVar9 = *(longlong *)(param_3 + 0x500);
    if (lVar9 != 0) goto LAB_18003f49c;
    local_d0 = (longlong *)(param_3 + 0x528);
LAB_18003f4d4:
    local_108 = local_d0;
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar6 = extraout_x1_02;
    }
    lVar9 = *(longlong *)(param_3 + 0x500);
    if (lVar9 != 0) goto LAB_18003f504;
    local_c8 = (longlong *)(param_3 + 0x528);
LAB_18003f53c:
    local_100 = local_c8;
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar6 = extraout_x1_03;
    }
    lVar9 = *(longlong *)(param_3 + 0x500);
    if (lVar9 != 0) goto LAB_18003f56c;
    local_c0 = (longlong *)(param_3 + 0x528);
LAB_18003f5a4:
    local_f0 = local_c0;
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar6 = extraout_x1_04;
    }
    lVar9 = *(longlong *)(param_3 + 0x500);
    if (lVar9 != 0) goto LAB_18003f5c4;
    lVar9 = param_3 + 0x528;
  }
  else {
LAB_18003f43c:
    iVar1 = *(int *)(param_3 + 0x520);
    if (iVar1 == 0) {
      local_e0 = (longlong *)(param_3 + 0x528);
    }
    else {
      iVar13 = 0;
      if (iVar1 < 1) {
        iVar13 = iVar1 + -1;
      }
      local_e0 = (longlong *)(lVar9 + (longlong)iVar13 * 0x170);
    }
    local_d8 = local_e0;
    if (lVar9 == 0) goto LAB_18003f46c;
LAB_18003f49c:
    iVar1 = *(int *)(param_3 + 0x520);
    if (iVar1 == 0) {
      local_108 = (longlong *)(param_3 + 0x528);
    }
    else {
      iVar13 = 1;
      if (iVar1 < 2) {
        iVar13 = iVar1 + -1;
      }
      local_108 = (longlong *)(lVar9 + (longlong)iVar13 * 0x170);
    }
    local_d0 = local_108;
    if (lVar9 == 0) goto LAB_18003f4d4;
LAB_18003f504:
    iVar1 = *(int *)(param_3 + 0x520);
    if (iVar1 == 0) {
      local_100 = (longlong *)(param_3 + 0x528);
    }
    else {
      iVar13 = 2;
      if (iVar1 < 3) {
        iVar13 = iVar1 + -1;
      }
      local_100 = (longlong *)(lVar9 + (longlong)iVar13 * 0x170);
    }
    local_c8 = local_100;
    if (lVar9 == 0) goto LAB_18003f53c;
LAB_18003f56c:
    iVar1 = *(int *)(param_3 + 0x520);
    if (iVar1 == 0) {
      local_f0 = (longlong *)(param_3 + 0x528);
    }
    else {
      iVar13 = 3;
      if (iVar1 < 4) {
        iVar13 = iVar1 + -1;
      }
      local_f0 = (longlong *)(lVar9 + (longlong)iVar13 * 0x170);
    }
    local_c0 = local_f0;
    if (lVar9 == 0) goto LAB_18003f5a4;
LAB_18003f5c4:
    iVar1 = *(int *)(param_3 + 0x520);
    if (iVar1 == 0) {
      lVar9 = param_3 + 0x528;
    }
    else {
      iVar13 = 4;
      if (iVar1 < 5) {
        iVar13 = iVar1 + -1;
      }
      lVar9 = lVar9 + (longlong)iVar13 * 0x170;
    }
  }
  lVar10 = *(longlong *)(param_3 + 0x210);
  if (lVar10 == 0) {
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar6 = extraout_x1_05;
    }
    lVar10 = *(longlong *)(param_3 + 0x210);
    if (lVar10 != 0) goto LAB_18003f618;
    plVar20 = (longlong *)(param_3 + 0x238);
LAB_18003f648:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar6 = extraout_x1_06;
    }
    lVar10 = *(longlong *)(param_3 + 0x210);
    if (lVar10 != 0) goto LAB_18003f670;
    lVar15 = param_3 + 0x238;
LAB_18003f6a4:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar6 = extraout_x1_07;
    }
    lVar10 = *(longlong *)(param_3 + 0x210);
    if (lVar10 != 0) goto LAB_18003f6cc;
    lVar18 = param_3 + 0x238;
LAB_18003f700:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar6 = extraout_x1_08;
    }
    lVar10 = *(longlong *)(param_3 + 0x210);
    if (lVar10 != 0) goto LAB_18003f728;
    lVar17 = param_3 + 0x238;
LAB_18003f75c:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar22 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar6 = extraout_x1_09;
    }
    lVar10 = *(longlong *)(param_3 + 0x210);
    if (lVar10 != 0) goto LAB_18003f77c;
    local_e8 = (longlong *)(param_3 + 0x238);
  }
  else {
LAB_18003f618:
    iVar1 = *(int *)(param_3 + 0x230);
    if (iVar1 == 0) {
      plVar20 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar13 = 0;
      if (iVar1 < 1) {
        iVar13 = iVar1 + -1;
      }
      plVar20 = (longlong *)(lVar10 + (longlong)iVar13 * 0x98);
    }
    if (lVar10 == 0) goto LAB_18003f648;
LAB_18003f670:
    iVar1 = *(int *)(param_3 + 0x230);
    if (iVar1 == 0) {
      lVar15 = param_3 + 0x238;
    }
    else {
      iVar13 = 1;
      if (iVar1 < 2) {
        iVar13 = iVar1 + -1;
      }
      lVar15 = lVar10 + (longlong)iVar13 * 0x98;
    }
    if (lVar10 == 0) goto LAB_18003f6a4;
LAB_18003f6cc:
    iVar1 = *(int *)(param_3 + 0x230);
    if (iVar1 == 0) {
      lVar18 = param_3 + 0x238;
    }
    else {
      iVar13 = 2;
      if (iVar1 < 3) {
        iVar13 = iVar1 + -1;
      }
      lVar18 = lVar10 + (longlong)iVar13 * 0x98;
    }
    if (lVar10 == 0) goto LAB_18003f700;
LAB_18003f728:
    iVar1 = *(int *)(param_3 + 0x230);
    if (iVar1 == 0) {
      lVar17 = param_3 + 0x238;
    }
    else {
      iVar13 = 3;
      if (iVar1 < 4) {
        iVar13 = iVar1 + -1;
      }
      lVar17 = lVar10 + (longlong)iVar13 * 0x98;
    }
    if (lVar10 == 0) goto LAB_18003f75c;
LAB_18003f77c:
    iVar1 = *(int *)(param_3 + 0x230);
    if (iVar1 == 0) {
      local_e8 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar13 = 4;
      if (iVar1 < 5) {
        iVar13 = iVar1 + -1;
      }
      local_e8 = (longlong *)(lVar10 + (longlong)iVar13 * 0x98);
    }
  }
  if (*(int *)(param_3 + 0xac) != 0) {
    lpMem_00 = "";
    local_90 = &DAT_1800d4ecd;
    local_a0 = &DAT_1800d4ecd;
    uStack_98 = 0;
    FUN_180006050(auVar22,param_2,&local_a0,0x1800d7400,param_5,param_6,param_7,param_8,param_9,
                  param_10);
    FUN_1800079f8((longlong *)(param_3 + 0x118),0x1800d7a80,0x1e);
    *(undefined4 *)(param_3 + 0x124) = 1;
    if ((*(char **)(param_3 + 0x338) != (char *)0x0) && (**(char **)(param_3 + 0x338) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0x338),0x1800d4ecd,0);
      *(undefined4 *)(param_3 + 0x344) = 1;
    }
    local_120 = "";
    local_118 = 0;
    local_110 = &DAT_1800d4ecd;
    pvVar4 = GetProcessHeap();
    pcVar5 = (char *)HeapAlloc(pvVar4,0,0x15);
    if (pcVar5 == (char *)0x0) {
      local_120 = "";
      pcVar19 = lpMem_00;
    }
    else {
      param_6 = 0x14;
      pcVar5[8] = '\0';
      pcVar5[9] = '\0';
      pcVar5[10] = '\0';
      pcVar5[0xb] = '\0';
      pcVar5[0xc] = '\0';
      pcVar5[0xd] = '\0';
      pcVar5[0xe] = '\0';
      pcVar5[0xf] = '\0';
      pcVar5[0] = '\0';
      pcVar5[1] = '\0';
      pcVar5[2] = '\0';
      pcVar5[3] = '\0';
      pcVar5[4] = '\0';
      pcVar5[5] = '\0';
      pcVar5[6] = '\0';
      pcVar5[7] = '\0';
      pcVar5[0x10] = '\0';
      pcVar5[0x11] = '\0';
      pcVar5[0x12] = '\0';
      pcVar5[0x13] = '\0';
      pcVar5[0x14] = '\0';
      local_120 = pcVar5;
      FUN_180099d78(pcVar5,0x15,0x1800d6c40,0x14);
      local_118 = 0x100000001;
      pcVar19 = pcVar5;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_120);
    if ((pcVar5 != (char *)0x0) && (pcVar19 != (char *)0x0)) {
      pvVar4 = GetProcessHeap();
      HeapFree(pvVar4,0,pcVar19);
    }
    local_120 = "";
    local_118 = 0;
    local_110 = &DAT_1800d4ecd;
    pvVar4 = GetProcessHeap();
    pcVar5 = (char *)HeapAlloc(pvVar4,0,0x8d);
    if (pcVar5 == (char *)0x0) {
      local_120 = "";
      pcVar19 = lpMem_00;
    }
    else {
      param_6 = 0x8c;
      pcVar5[8] = '\0';
      pcVar5[9] = '\0';
      pcVar5[10] = '\0';
      pcVar5[0xb] = '\0';
      pcVar5[0xc] = '\0';
      pcVar5[0xd] = '\0';
      pcVar5[0xe] = '\0';
      pcVar5[0xf] = '\0';
      pcVar5[0] = '\0';
      pcVar5[1] = '\0';
      pcVar5[2] = '\0';
      pcVar5[3] = '\0';
      pcVar5[4] = '\0';
      pcVar5[5] = '\0';
      pcVar5[6] = '\0';
      pcVar5[7] = '\0';
      pcVar5[0x18] = '\0';
      pcVar5[0x19] = '\0';
      pcVar5[0x1a] = '\0';
      pcVar5[0x1b] = '\0';
      pcVar5[0x1c] = '\0';
      pcVar5[0x1d] = '\0';
      pcVar5[0x1e] = '\0';
      pcVar5[0x1f] = '\0';
      pcVar5[0x10] = '\0';
      pcVar5[0x11] = '\0';
      pcVar5[0x12] = '\0';
      pcVar5[0x13] = '\0';
      pcVar5[0x14] = '\0';
      pcVar5[0x15] = '\0';
      pcVar5[0x16] = '\0';
      pcVar5[0x17] = '\0';
      pcVar5[0x28] = '\0';
      pcVar5[0x29] = '\0';
      pcVar5[0x2a] = '\0';
      pcVar5[0x2b] = '\0';
      pcVar5[0x2c] = '\0';
      pcVar5[0x2d] = '\0';
      pcVar5[0x2e] = '\0';
      pcVar5[0x2f] = '\0';
      pcVar5[0x20] = '\0';
      pcVar5[0x21] = '\0';
      pcVar5[0x22] = '\0';
      pcVar5[0x23] = '\0';
      pcVar5[0x24] = '\0';
      pcVar5[0x25] = '\0';
      pcVar5[0x26] = '\0';
      pcVar5[0x27] = '\0';
      pcVar5[0x38] = '\0';
      pcVar5[0x39] = '\0';
      pcVar5[0x3a] = '\0';
      pcVar5[0x3b] = '\0';
      pcVar5[0x3c] = '\0';
      pcVar5[0x3d] = '\0';
      pcVar5[0x3e] = '\0';
      pcVar5[0x3f] = '\0';
      pcVar5[0x30] = '\0';
      pcVar5[0x31] = '\0';
      pcVar5[0x32] = '\0';
      pcVar5[0x33] = '\0';
      pcVar5[0x34] = '\0';
      pcVar5[0x35] = '\0';
      pcVar5[0x36] = '\0';
      pcVar5[0x37] = '\0';
      pcVar5[0x48] = '\0';
      pcVar5[0x49] = '\0';
      pcVar5[0x4a] = '\0';
      pcVar5[0x4b] = '\0';
      pcVar5[0x4c] = '\0';
      pcVar5[0x4d] = '\0';
      pcVar5[0x4e] = '\0';
      pcVar5[0x4f] = '\0';
      pcVar5[0x40] = '\0';
      pcVar5[0x41] = '\0';
      pcVar5[0x42] = '\0';
      pcVar5[0x43] = '\0';
      pcVar5[0x44] = '\0';
      pcVar5[0x45] = '\0';
      pcVar5[0x46] = '\0';
      pcVar5[0x47] = '\0';
      pcVar5[0x58] = '\0';
      pcVar5[0x59] = '\0';
      pcVar5[0x5a] = '\0';
      pcVar5[0x5b] = '\0';
      pcVar5[0x5c] = '\0';
      pcVar5[0x5d] = '\0';
      pcVar5[0x5e] = '\0';
      pcVar5[0x5f] = '\0';
      pcVar5[0x50] = '\0';
      pcVar5[0x51] = '\0';
      pcVar5[0x52] = '\0';
      pcVar5[0x53] = '\0';
      pcVar5[0x54] = '\0';
      pcVar5[0x55] = '\0';
      pcVar5[0x56] = '\0';
      pcVar5[0x57] = '\0';
      pcVar5[0x68] = '\0';
      pcVar5[0x69] = '\0';
      pcVar5[0x6a] = '\0';
      pcVar5[0x6b] = '\0';
      pcVar5[0x6c] = '\0';
      pcVar5[0x6d] = '\0';
      pcVar5[0x6e] = '\0';
      pcVar5[0x6f] = '\0';
      pcVar5[0x60] = '\0';
      pcVar5[0x61] = '\0';
      pcVar5[0x62] = '\0';
      pcVar5[99] = '\0';
      pcVar5[100] = '\0';
      pcVar5[0x65] = '\0';
      pcVar5[0x66] = '\0';
      pcVar5[0x67] = '\0';
      pcVar5[0x78] = '\0';
      pcVar5[0x79] = '\0';
      pcVar5[0x7a] = '\0';
      pcVar5[0x7b] = '\0';
      pcVar5[0x7c] = '\0';
      pcVar5[0x7d] = '\0';
      pcVar5[0x7e] = '\0';
      pcVar5[0x7f] = '\0';
      pcVar5[0x70] = '\0';
      pcVar5[0x71] = '\0';
      pcVar5[0x72] = '\0';
      pcVar5[0x73] = '\0';
      pcVar5[0x74] = '\0';
      pcVar5[0x75] = '\0';
      pcVar5[0x76] = '\0';
      pcVar5[0x77] = '\0';
      pcVar5[0x80] = '\0';
      pcVar5[0x81] = '\0';
      pcVar5[0x82] = '\0';
      pcVar5[0x83] = '\0';
      pcVar5[0x84] = '\0';
      pcVar5[0x85] = '\0';
      pcVar5[0x86] = '\0';
      pcVar5[0x87] = '\0';
      pcVar5[0x88] = '\0';
      pcVar5[0x89] = '\0';
      pcVar5[0x8a] = '\0';
      pcVar5[0x8b] = '\0';
      pcVar5[0x8c] = '\0';
      local_120 = pcVar5;
      FUN_180099d78(pcVar5,0x8d,0x1800d6bb0,0x8c);
      local_118 = 0x100000001;
      pcVar19 = pcVar5;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_120);
    uVar21 = extraout_s0_00;
    auVar23 = extraout_var_00;
    if ((pcVar5 != (char *)0x0) && (pcVar19 != (char *)0x0)) {
      pvVar4 = GetProcessHeap();
      HeapFree(pvVar4,0,pcVar19);
      local_120 = (char *)0x0;
      local_118 = 0;
      uVar21 = extraout_s0_01;
      auVar23 = extraout_var_01;
    }
    pcVar5 = *(char **)(param_3 + 0x118);
    if (*(char **)(param_3 + 0x118) == (char *)0x0) {
      pcVar5 = lpMem_00;
    }
    auVar2._4_12_ = auVar23;
    auVar2._0_4_ = uVar21;
    FUN_180026368(auVar2,param_2,(undefined8 *)(param_3 + 0x338),0x1800d6c60,pcVar5,param_6,param_7,
                  param_8,param_9,param_10);
    local_120 = "";
    local_118 = 0;
    local_110 = &DAT_1800d4ecd;
    pvVar4 = GetProcessHeap();
    pcVar5 = (char *)HeapAlloc(pvVar4,0,7);
    if (pcVar5 == (char *)0x0) {
      local_120 = "";
    }
    else {
      pcVar5[0] = '\0';
      pcVar5[1] = '\0';
      pcVar5[2] = '\0';
      pcVar5[3] = '\0';
      pcVar5[4] = '\0';
      pcVar5[5] = '\0';
      pcVar5[6] = '\0';
      local_120 = pcVar5;
      FUN_180099d78(pcVar5,7,0x1800d6c58,6);
      local_118 = 0x100000001;
      lpMem_00 = pcVar5;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_120);
    if ((pcVar5 != (char *)0x0) && (lpMem_00 != (char *)0x0)) {
      pvVar4 = GetProcessHeap();
      HeapFree(pvVar4,0,lpMem_00);
      local_120 = (char *)0x0;
      local_118 = 0;
    }
    *(undefined4 *)(param_3 + 4) = 0;
    *(undefined4 *)(param_3 + 0x10) = 0;
    *(undefined4 *)(param_3 + 0x37c) = 1;
    *(undefined4 *)(param_3 + 0xcb8) = 0;
    FUN_1800079f8(local_e0,0x1800d7ab0,0xe);
    *(undefined4 *)((longlong)local_d8 + 0xc) = 1;
    *(undefined2 *)(local_d8 + 5) = 4;
    *(undefined4 *)(local_d8 + 3) = 0x80ff;
    *(undefined2 *)((longlong)local_d8 + 0x24) = 5;
    FUN_1800079f8(local_108,0x1800d7aa0,0xf);
    *(undefined4 *)((longlong)local_d0 + 0xc) = 1;
    *(undefined2 *)(local_d0 + 5) = 4;
    *(undefined4 *)(local_d0 + 3) = 0xc08000;
    *(undefined2 *)((longlong)local_d0 + 0x24) = 5;
    FUN_1800079f8(local_100,0x1800d7ad8,0x13);
    *(undefined4 *)((longlong)local_c8 + 0xc) = 1;
    *(undefined2 *)((longlong)local_c8 + 0x24) = 0x20;
    *(undefined2 *)(local_c8 + 5) = 4;
    *(undefined4 *)(local_c8 + 3) = 0xff00;
    FUN_1800079f8(local_f0,0x1800d7ac0,0x14);
    *(undefined4 *)((longlong)local_c0 + 0xc) = 1;
    *(undefined2 *)((longlong)local_c0 + 0x24) = 0x21;
    *(undefined2 *)(local_c0 + 5) = 4;
    *(undefined4 *)(local_c0 + 3) = 0xff;
    FUN_1800079f8(plVar20,0x1800d7b00,4);
    *(undefined4 *)((longlong)plVar20 + 0xc) = 1;
    if ((code *)plVar20[10] != (code *)0x0) {
      (*(code *)plVar20[10])(*(undefined4 *)((longlong)plVar20 + 0x4c),"Default;Fast");
    }
    plVar16 = local_e8;
    *(undefined4 *)((longlong)plVar20 + 0x1c) = 0;
    *(undefined1 *)(plVar20 + 3) = 0x16;
    *(undefined4 *)(lVar15 + 0x1c) = 3;
    *(undefined1 *)(lVar15 + 0x18) = 0xb;
    *(undefined4 *)(lVar18 + 0x1c) = 10;
    *(undefined1 *)(lVar18 + 0x18) = 0xb;
    *(undefined4 *)(lVar17 + 0x1c) = 0x3f2ac083;
    *(undefined1 *)(lVar17 + 0x18) = 2;
    FUN_1800079f8(local_e8,0x1800d74a8,0x12);
    lpMem = local_a0;
    *(undefined4 *)((longlong)plVar16 + 0xc) = 1;
    *(undefined1 *)(plVar16 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar16 + 0x1c) = 2;
    if ((int)uStack_98 == 0) {
      return;
    }
    if (local_a0 == (undefined1 *)0x0) {
      return;
    }
    pvVar4 = GetProcessHeap();
    HeapFree(pvVar4,0,lpMem);
    return;
  }
  if (*(int *)(param_3 + 900) == 0) {
    uVar6 = FUN_1800254e8(auVar22,param_2,param_3,uVar6,param_5,param_6,param_7,param_8,param_9,
                          param_10);
    *local_b8 = (int)uVar6;
  }
  if (*local_b8 != 0) {
    return;
  }
  if ((*(int *)(param_3 + 0x11f8) != 0) && (*(int *)(param_3 + 900) == 0)) {
    uVar3 = FUN_180026550((longlong)plVar20);
    *(undefined1 *)(lVar15 + 0x18) = 0xb;
    if (uVar3 == 0) {
      *(undefined4 *)(lVar15 + 0x1c) = 3;
      uVar21 = 10;
    }
    else {
      *(undefined4 *)(lVar15 + 0x1c) = 6;
      uVar21 = 0x14;
    }
    *(undefined1 *)(lVar18 + 0x18) = 0xb;
    *(undefined4 *)(lVar18 + 0x1c) = uVar21;
    *(undefined4 *)(lVar17 + 0x1c) = 0x3f2ac083;
    *(undefined1 *)(lVar17 + 0x18) = 2;
  }
  uVar7 = FUN_180026708(lVar15);
  lVar10 = *(longlong *)(lVar9 + 0x80);
  if (lVar10 == 0) {
    if (*(code **)(lVar9 + 0x90) != (code *)0x0) {
      (**(code **)(lVar9 + 0x90))(*(undefined4 *)(lVar9 + 0x98));
    }
    lVar10 = *(longlong *)(lVar9 + 0x80);
    if (lVar10 != 0) goto LAB_18003fcac;
    lVar10 = lVar9 + 0xa8;
  }
  else {
LAB_18003fcac:
    iVar1 = *(int *)(lVar9 + 0xa0);
    if (iVar1 == 0) {
      lVar10 = lVar9 + 0xa8;
    }
    else {
      iVar13 = 0;
      if (iVar1 < 1) {
        iVar13 = iVar1 + -1;
      }
      lVar10 = lVar10 + (longlong)iVar13 * 0x28;
    }
  }
  (**(code **)(param_3 + 0x390))
            (param_3 + 0xbc8,lVar10,*(undefined4 *)(param_3 + 900),uVar7 & 0xffffffff);
  uVar7 = FUN_180026708(lVar15);
  lVar10 = *(longlong *)(lVar9 + 0x80);
  if (lVar10 == 0) {
    if (*(code **)(lVar9 + 0x90) != (code *)0x0) {
      (**(code **)(lVar9 + 0x90))(*(undefined4 *)(lVar9 + 0x98));
    }
    lVar10 = *(longlong *)(lVar9 + 0x80);
    if (lVar10 != 0) goto LAB_18003fd14;
    lVar10 = lVar9 + 0xa8;
  }
  else {
LAB_18003fd14:
    iVar1 = *(int *)(lVar9 + 0xa0);
    if (iVar1 == 0) {
      lVar10 = lVar9 + 0xa8;
    }
    else {
      iVar13 = 1;
      if (iVar1 < 2) {
        iVar13 = iVar1 + -1;
      }
      lVar10 = lVar10 + (longlong)iVar13 * 0x28;
    }
  }
  (**(code **)(param_3 + 0x390))
            (param_3 + 0xbf0,lVar10,*(undefined4 *)(param_3 + 900),uVar7 & 0xffffffff);
  uVar3 = *(uint *)(param_3 + 900);
  lVar10 = *(longlong *)(param_3 + 0xbf0);
  uVar14 = uVar3;
  if (lVar10 == 0) {
    if (*(code **)(param_3 + 0xc00) != (code *)0x0) {
      (**(code **)(param_3 + 0xc00))(*(undefined4 *)(param_3 + 0xc08));
      uVar14 = *(uint *)(local_f8 + 900);
    }
    lVar10 = *(longlong *)(param_3 + 0xbf0);
    if (lVar10 != 0) goto LAB_18003fd84;
    pfVar8 = (float *)(param_3 + 0xc14);
  }
  else {
LAB_18003fd84:
    iVar1 = *(int *)(param_3 + 0xc10);
    if (iVar1 == 0) {
      pfVar8 = (float *)(param_3 + 0xc14);
    }
    else {
      uVar3 = uVar3 & ((int)uVar3 >> 0x1f ^ 0xffffffffU);
      if (iVar1 <= (int)uVar3) {
        uVar3 = iVar1 - 1;
      }
      pfVar8 = (float *)(lVar10 + (longlong)(int)uVar3 * 4);
    }
  }
  lVar10 = *(longlong *)(param_3 + 0xbc8);
  uVar3 = uVar14;
  if (lVar10 == 0) {
    if (*(code **)(param_3 + 0xbd8) != (code *)0x0) {
      (**(code **)(param_3 + 0xbd8))(*(undefined4 *)(param_3 + 0xbe0));
      uVar3 = *(uint *)(local_f8 + 900);
    }
    lVar10 = *(longlong *)(param_3 + 0xbc8);
    if (lVar10 != 0) goto LAB_18003fddc;
    pfVar11 = (float *)(param_3 + 0xbec);
  }
  else {
LAB_18003fddc:
    iVar1 = *(int *)(param_3 + 0xbe8);
    if (iVar1 == 0) {
      pfVar11 = (float *)(param_3 + 0xbec);
    }
    else {
      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
      if (iVar1 <= (int)uVar14) {
        uVar14 = iVar1 - 1;
      }
      pfVar11 = (float *)(lVar10 + (longlong)(int)uVar14 * 4);
    }
  }
  fVar25 = *pfVar11;
  fVar24 = *pfVar8;
  lVar10 = *(longlong *)(lVar9 + 0x80);
  if (lVar10 == 0) {
    if (*(code **)(lVar9 + 0x90) != (code *)0x0) {
      (**(code **)(lVar9 + 0x90))(*(undefined4 *)(lVar9 + 0x98));
      uVar3 = *(uint *)(local_f8 + 900);
    }
    lVar10 = *(longlong *)(lVar9 + 0x80);
    if (lVar10 != 0) goto LAB_18003fe40;
    plVar20 = (longlong *)(lVar9 + 0xa8);
  }
  else {
LAB_18003fe40:
    iVar1 = *(int *)(lVar9 + 0xa0);
    if (iVar1 == 0) {
      plVar20 = (longlong *)(lVar9 + 0xa8);
    }
    else {
      iVar13 = 3;
      if (iVar1 < 4) {
        iVar13 = iVar1 + -1;
      }
      plVar20 = (longlong *)(lVar10 + (longlong)iVar13 * 0x28);
    }
  }
  lVar10 = *plVar20;
  if (lVar10 == 0) {
    if ((code *)plVar20[2] != (code *)0x0) {
      (*(code *)plVar20[2])((int)plVar20[3]);
    }
    lVar10 = *plVar20;
    if (lVar10 != 0) goto LAB_18003fe8c;
    pfVar8 = (float *)((longlong)plVar20 + 0x24);
  }
  else {
LAB_18003fe8c:
    iVar1 = (int)plVar20[4];
    if (iVar1 == 0) {
      pfVar8 = (float *)((longlong)plVar20 + 0x24);
    }
    else {
      uVar3 = uVar3 & ((int)uVar3 >> 0x1f ^ 0xffffffffU);
      if (iVar1 <= (int)uVar3) {
        uVar3 = iVar1 - 1;
      }
      pfVar8 = (float *)(lVar10 + (longlong)(int)uVar3 * 4);
    }
  }
  *pfVar8 = fVar25 + fVar24;
  uVar7 = FUN_180026708(lVar18);
  lVar10 = *(longlong *)(lVar9 + 0x80);
  if (lVar10 == 0) {
    if (*(code **)(lVar9 + 0x90) != (code *)0x0) {
      (**(code **)(lVar9 + 0x90))(*(undefined4 *)(lVar9 + 0x98));
    }
    lVar10 = *(longlong *)(lVar9 + 0x80);
    if (lVar10 != 0) goto LAB_18003fef0;
    lVar15 = lVar9 + 0xa8;
LAB_18003ff20:
    if (*(code **)(lVar9 + 0x90) != (code *)0x0) {
      (**(code **)(lVar9 + 0x90))(*(undefined4 *)(lVar9 + 0x98));
    }
    lVar10 = *(longlong *)(lVar9 + 0x80);
    if (lVar10 != 0) goto LAB_18003ff40;
    lVar10 = lVar9 + 0xa8;
  }
  else {
LAB_18003fef0:
    iVar1 = *(int *)(lVar9 + 0xa0);
    if (iVar1 == 0) {
      lVar15 = lVar9 + 0xa8;
    }
    else {
      iVar13 = 4;
      if (iVar1 < 5) {
        iVar13 = iVar1 + -1;
      }
      lVar15 = lVar10 + (longlong)iVar13 * 0x28;
    }
    if (lVar10 == 0) goto LAB_18003ff20;
LAB_18003ff40:
    iVar1 = *(int *)(lVar9 + 0xa0);
    if (iVar1 == 0) {
      lVar10 = lVar9 + 0xa8;
    }
    else {
      iVar13 = 3;
      if (iVar1 < 4) {
        iVar13 = iVar1 + -1;
      }
      lVar10 = lVar10 + (longlong)iVar13 * 0x28;
    }
  }
  (**(code **)(param_3 + 0x390))(lVar10,lVar15,*(undefined4 *)(param_3 + 900),uVar7 & 0xffffffff);
  lVar10 = *(longlong *)(lVar9 + 0x80);
  if (lVar10 == 0) {
    if (*(code **)(lVar9 + 0x90) != (code *)0x0) {
      (**(code **)(lVar9 + 0x90))(*(undefined4 *)(lVar9 + 0x98));
    }
    lVar10 = *(longlong *)(lVar9 + 0x80);
    if (lVar10 != 0) goto LAB_18003ffb4;
    uVar3 = *(uint *)(param_3 + 900);
    plVar20 = (longlong *)(lVar9 + 0xa8);
    local_d8 = (longlong *)CONCAT44(local_d8._4_4_,uVar3);
LAB_18003ffe8:
    if (*(code **)(lVar9 + 0x90) != (code *)0x0) {
      (**(code **)(lVar9 + 0x90))(*(undefined4 *)(lVar9 + 0x98));
      uVar3 = *(uint *)(local_f8 + 900);
    }
    lVar10 = *(longlong *)(lVar9 + 0x80);
    if (lVar10 != 0) goto LAB_180040010;
    plVar16 = (longlong *)(lVar9 + 0xa8);
  }
  else {
LAB_18003ffb4:
    iVar1 = *(int *)(lVar9 + 0xa0);
    if (iVar1 == 0) {
      plVar20 = (longlong *)(lVar9 + 0xa8);
    }
    else {
      iVar13 = 0;
      if (iVar1 < 1) {
        iVar13 = iVar1 + -1;
      }
      plVar20 = (longlong *)(lVar10 + (longlong)iVar13 * 0x28);
    }
    uVar3 = *(uint *)(param_3 + 900);
    local_d8 = (longlong *)CONCAT44(local_d8._4_4_,uVar3);
    if (lVar10 == 0) goto LAB_18003ffe8;
LAB_180040010:
    iVar1 = *(int *)(lVar9 + 0xa0);
    if (iVar1 == 0) {
      plVar16 = (longlong *)(lVar9 + 0xa8);
    }
    else {
      iVar13 = 4;
      if (iVar1 < 5) {
        iVar13 = iVar1 + -1;
      }
      plVar16 = (longlong *)(lVar10 + (longlong)iVar13 * 0x28);
    }
  }
  lVar10 = *plVar16;
  if (lVar10 == 0) {
    if ((code *)plVar16[2] != (code *)0x0) {
      (*(code *)plVar16[2])((int)plVar16[3]);
    }
    lVar10 = *plVar16;
    if (lVar10 != 0) goto LAB_18004005c;
    pfVar8 = (float *)((longlong)plVar16 + 0x24);
  }
  else {
LAB_18004005c:
    iVar1 = (int)plVar16[4];
    if (iVar1 == 0) {
      pfVar8 = (float *)((longlong)plVar16 + 0x24);
    }
    else {
      uVar3 = uVar3 & ((int)uVar3 >> 0x1f ^ 0xffffffffU);
      if (iVar1 <= (int)uVar3) {
        uVar3 = iVar1 - 1;
      }
      pfVar8 = (float *)(lVar10 + (longlong)(int)uVar3 * 4);
    }
  }
  if ((*plVar20 == 0) && ((code *)plVar20[2] != (code *)0x0)) {
    (*(code *)plVar20[2])((int)plVar20[3]);
  }
  fVar24 = FUN_180026608(lVar17);
  if (*extraout_x11 <= fVar24 * *pfVar8) {
    lVar10 = *(longlong *)(lVar9 + 0x80);
    if (lVar10 == 0) {
      if (*(code **)(lVar9 + 0x90) != (code *)0x0) {
        (**(code **)(lVar9 + 0x90))(*(undefined4 *)(lVar9 + 0x98));
      }
      lVar10 = *(longlong *)(lVar9 + 0x80);
      if (lVar10 != 0) goto LAB_180040204;
      uVar3 = *(uint *)(param_3 + 900);
      plVar20 = (longlong *)(lVar9 + 0xa8);
LAB_180040240:
      if (*(code **)(lVar9 + 0x90) != (code *)0x0) {
        (**(code **)(lVar9 + 0x90))(*(undefined4 *)(lVar9 + 0x98));
        uVar3 = *(uint *)(local_f8 + 900);
      }
      lVar10 = *(longlong *)(lVar9 + 0x80);
      if (lVar10 != 0) goto LAB_18004026c;
      plVar16 = (longlong *)(lVar9 + 0xa8);
    }
    else {
LAB_180040204:
      iVar1 = *(int *)(lVar9 + 0xa0);
      if (iVar1 == 0) {
        plVar20 = (longlong *)(lVar9 + 0xa8);
      }
      else {
        iVar13 = 1;
        if (iVar1 < 2) {
          iVar13 = iVar1 + -1;
        }
        plVar20 = (longlong *)(lVar10 + (longlong)iVar13 * 0x28);
      }
      uVar3 = *(uint *)(param_3 + 900);
      if (lVar10 == 0) goto LAB_180040240;
LAB_18004026c:
      iVar1 = *(int *)(lVar9 + 0xa0);
      if (iVar1 == 0) {
        plVar16 = (longlong *)(lVar9 + 0xa8);
      }
      else {
        iVar13 = 4;
        if (iVar1 < 5) {
          iVar13 = iVar1 + -1;
        }
        plVar16 = (longlong *)(lVar10 + (longlong)iVar13 * 0x28);
      }
    }
    lVar9 = *plVar16;
    if (lVar9 == 0) {
      if ((code *)plVar16[2] != (code *)0x0) {
        (*(code *)plVar16[2])((int)plVar16[3]);
      }
      lVar9 = *plVar16;
      if (lVar9 != 0) goto LAB_1800402b8;
      pfVar8 = (float *)((longlong)plVar16 + 0x24);
    }
    else {
LAB_1800402b8:
      iVar1 = (int)plVar16[4];
      if (iVar1 == 0) {
        pfVar8 = (float *)((longlong)plVar16 + 0x24);
      }
      else {
        uVar3 = uVar3 & ((int)uVar3 >> 0x1f ^ 0xffffffffU);
        if (iVar1 <= (int)uVar3) {
          uVar3 = iVar1 - 1;
        }
        pfVar8 = (float *)(lVar9 + (longlong)(int)uVar3 * 4);
      }
    }
    if ((*plVar20 == 0) && ((code *)plVar20[2] != (code *)0x0)) {
      (*(code *)plVar20[2])((int)plVar20[3]);
    }
    fVar24 = FUN_180026608(lVar17);
    if (fVar24 * *pfVar8 < *extraout_x11_01) {
      FUN_180005d08((longlong *)(param_3 + 0xa60),*(int *)(param_3 + 900));
      uVar7 = FUN_180026708((longlong)local_e8);
      fVar24 = *(float *)(param_3 + 0x698);
      fVar25 = *extraout_x11_02;
      pfVar8 = (float *)FUN_18000f448((longlong)local_e0,*(int *)(param_3 + 900));
      *pfVar8 = (float)(int)uVar7 * fVar24 + fVar25;
    }
  }
  else {
    uVar3 = *(uint *)(param_3 + 900);
    if ((*(longlong *)(param_3 + 0xa88) == 0) && (*(code **)(param_3 + 0xa98) != (code *)0x0)) {
      (**(code **)(param_3 + 0xa98))(*(undefined4 *)(param_3 + 0xaa0));
      uVar3 = *(uint *)(local_f8 + 900);
    }
    uVar7 = FUN_180026708((longlong)local_e8);
    lVar9 = local_108[6];
    fVar24 = *extraout_x11_00 - (float)(int)uVar7 * *(float *)(param_3 + 0x698);
    if (lVar9 == 0) {
      if ((code *)local_108[8] != (code *)0x0) {
        (*(code *)local_108[8])((int)local_108[9]);
      }
      lVar9 = local_108[6];
      if (lVar9 == 0) {
        *(float *)((longlong)local_108 + 0x54) = fVar24;
        goto LAB_180040380;
      }
    }
    iVar1 = (int)local_108[10];
    if (iVar1 == 0) {
      *(float *)((longlong)local_108 + 0x54) = fVar24;
    }
    else {
      uVar3 = uVar3 & ((int)uVar3 >> 0x1f ^ 0xffffffffU);
      if (iVar1 <= (int)uVar3) {
        uVar3 = iVar1 - 1;
      }
      *(float *)(lVar9 + (longlong)(int)uVar3 * 4) = fVar24;
    }
  }
LAB_180040380:
  fVar24 = (float)(**(code **)(param_3 + 0x6c0))(local_108 + 6,*(undefined4 *)(param_3 + 900),6);
  if (fVar24 <= 0.0) {
LAB_1800404d4:
    uVar3 = *(uint *)(param_3 + 900);
    lVar9 = local_100[6];
    if (lVar9 == 0) {
      if ((code *)local_100[8] != (code *)0x0) {
        (*(code *)local_100[8])((int)local_100[9]);
      }
      lVar9 = local_100[6];
      if (lVar9 != 0) goto LAB_180040508;
      puVar12 = (undefined4 *)((longlong)local_100 + 0x54);
    }
    else {
LAB_180040508:
      iVar1 = (int)local_100[10];
      if (iVar1 == 0) {
        puVar12 = (undefined4 *)((longlong)local_100 + 0x54);
      }
      else {
        uVar3 = uVar3 & ((int)uVar3 >> 0x1f ^ 0xffffffffU);
        if (iVar1 <= (int)uVar3) {
          uVar3 = iVar1 - 1;
        }
        puVar12 = (undefined4 *)(lVar9 + (longlong)(int)uVar3 * 4);
      }
    }
    *puVar12 = 0;
  }
  else {
    uVar3 = *(uint *)(param_3 + 900);
    lVar9 = *(longlong *)(param_3 + 0xbf0);
    uVar14 = uVar3;
    if (lVar9 == 0) {
      if (*(code **)(param_3 + 0xc00) != (code *)0x0) {
        (**(code **)(param_3 + 0xc00))(*(undefined4 *)(param_3 + 0xc08));
        uVar14 = *(uint *)(local_f8 + 900);
      }
      lVar9 = *(longlong *)(param_3 + 0xbf0);
      if (lVar9 != 0) goto LAB_1800403dc;
      pfVar8 = (float *)(param_3 + 0xc14);
    }
    else {
LAB_1800403dc:
      iVar1 = *(int *)(param_3 + 0xc10);
      if (iVar1 == 0) {
        pfVar8 = (float *)(param_3 + 0xc14);
      }
      else {
        uVar3 = uVar3 & ((int)uVar3 >> 0x1f ^ 0xffffffffU);
        if (iVar1 <= (int)uVar3) {
          uVar3 = iVar1 - 1;
        }
        pfVar8 = (float *)(lVar9 + (longlong)(int)uVar3 * 4);
      }
    }
    lVar9 = *(longlong *)(param_3 + 0xbc8);
    if (lVar9 == 0) {
      if (*(code **)(param_3 + 0xbd8) != (code *)0x0) {
        (**(code **)(param_3 + 0xbd8))(*(undefined4 *)(param_3 + 0xbe0));
      }
      lVar9 = *(longlong *)(param_3 + 0xbc8);
      if (lVar9 != 0) goto LAB_180040428;
      pfVar11 = (float *)(param_3 + 0xbec);
    }
    else {
LAB_180040428:
      iVar1 = *(int *)(param_3 + 0xbe8);
      if (iVar1 == 0) {
        pfVar11 = (float *)(param_3 + 0xbec);
      }
      else {
        uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
        if (iVar1 <= (int)uVar14) {
          uVar14 = iVar1 - 1;
        }
        pfVar11 = (float *)(lVar9 + (longlong)(int)uVar14 * 4);
      }
    }
    plVar20 = local_100;
    if ((*pfVar8 < *pfVar11 * 1.2) ||
       (fVar24 = (float)(**(code **)(param_3 + 0x6c0))(local_100 + 6,*(int *)(param_3 + 900) + -1,5)
       , fVar24 != 0.0)) goto LAB_1800404d4;
    FUN_180005d08((longlong *)(param_3 + 0xa88),*(int *)(param_3 + 900));
    uVar7 = FUN_180026708((longlong)local_e8);
    fVar24 = *extraout_x11_03;
    pfVar8 = (float *)FUN_18000f448((longlong)plVar20,*(int *)(param_3 + 900));
    *pfVar8 = (fVar24 - (float)(int)uVar7 * extraout_s18) - (extraout_s18 + extraout_s18);
  }
  fVar24 = (float)(**(code **)(param_3 + 0x6c0))(local_e0 + 6,*(undefined4 *)(param_3 + 900),6);
  if (0.0 < fVar24) {
    uVar3 = *(uint *)(param_3 + 900);
    lVar9 = *(longlong *)(param_3 + 0xbf0);
    uVar14 = uVar3;
    if (lVar9 == 0) {
      if (*(code **)(param_3 + 0xc00) != (code *)0x0) {
        (**(code **)(param_3 + 0xc00))(*(undefined4 *)(param_3 + 0xc08));
        uVar14 = *(uint *)(local_f8 + 900);
      }
      lVar9 = *(longlong *)(param_3 + 0xbf0);
      if (lVar9 != 0) goto LAB_18004058c;
      pfVar8 = (float *)(param_3 + 0xc14);
    }
    else {
LAB_18004058c:
      iVar1 = *(int *)(param_3 + 0xc10);
      if (iVar1 == 0) {
        pfVar8 = (float *)(param_3 + 0xc14);
      }
      else {
        uVar3 = uVar3 & ((int)uVar3 >> 0x1f ^ 0xffffffffU);
        if (iVar1 <= (int)uVar3) {
          uVar3 = iVar1 - 1;
        }
        pfVar8 = (float *)(lVar9 + (longlong)(int)uVar3 * 4);
      }
    }
    lVar9 = *(longlong *)(param_3 + 0xbc8);
    if (lVar9 == 0) {
      if (*(code **)(param_3 + 0xbd8) != (code *)0x0) {
        (**(code **)(param_3 + 0xbd8))(*(undefined4 *)(param_3 + 0xbe0));
      }
      lVar9 = *(longlong *)(param_3 + 0xbc8);
      if (lVar9 != 0) goto LAB_1800405d8;
      pfVar11 = (float *)(param_3 + 0xbec);
    }
    else {
LAB_1800405d8:
      iVar1 = *(int *)(param_3 + 0xbe8);
      if (iVar1 == 0) {
        pfVar11 = (float *)(param_3 + 0xbec);
      }
      else {
        uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
        if (iVar1 <= (int)uVar14) {
          uVar14 = iVar1 - 1;
        }
        pfVar11 = (float *)(lVar9 + (longlong)(int)uVar14 * 4);
      }
    }
    plVar20 = local_f0;
    if ((*pfVar8 * 1.2 <= *pfVar11) &&
       (fVar24 = (float)(**(code **)(param_3 + 0x6c0))(local_f0 + 6,*(int *)(param_3 + 900) + -1,5),
       fVar24 == 0.0)) {
      FUN_180005d08((longlong *)(param_3 + 0xa60),*(int *)(param_3 + 900));
      uVar7 = FUN_180026708((longlong)local_e8);
      fVar24 = *extraout_x11_04;
      pfVar8 = (float *)FUN_18000f448((longlong)plVar20,*(int *)(param_3 + 900));
      *pfVar8 = (float)(int)uVar7 * extraout_s18_00 + fVar24 + extraout_s18_00 + extraout_s18_00;
      return;
    }
  }
  uVar3 = *(uint *)(param_3 + 900);
  lVar9 = local_f0[6];
  if (lVar9 == 0) {
    if ((code *)local_f0[8] != (code *)0x0) {
      (*(code *)local_f0[8])((int)local_f0[9]);
    }
    lVar9 = local_f0[6];
    if (lVar9 != 0) goto LAB_1800406b8;
  }
  else {
LAB_1800406b8:
    iVar1 = (int)local_f0[10];
    if (iVar1 != 0) {
      uVar3 = uVar3 & ((int)uVar3 >> 0x1f ^ 0xffffffffU);
      if (iVar1 <= (int)uVar3) {
        uVar3 = iVar1 - 1;
      }
      puVar12 = (undefined4 *)(lVar9 + (longlong)(int)uVar3 * 4);
      goto LAB_1800406d8;
    }
  }
  puVar12 = (undefined4 *)((longlong)local_f0 + 0x54);
LAB_1800406d8:
  *puVar12 = 0;
  return;
}

