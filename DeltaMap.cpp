#include "sierrachart.h"

/*==========================================================================*/
SCSFExport scsf_DeltaMap(SCStudyInterfaceRef sc)
{
    // Subgraph declarations - Delta by price level visualization
    SCSubgraphRef Subgraph_DeltaHeatMap = sc.Subgraph[0];
    SCSubgraphRef Subgraph_PositiveDelta = sc.Subgraph[1];
    SCSubgraphRef Subgraph_NegativeDelta = sc.Subgraph[2];
    SCSubgraphRef Subgraph_DeltaByPrice = sc.Subgraph[3];
    SCSubgraphRef Subgraph_VolumeByPrice = sc.Subgraph[4];
    SCSubgraphRef Subgraph_NetDelta = sc.Subgraph[5];
    SCSubgraphRef Subgraph_DeltaRatio = sc.Subgraph[6];
    SCSubgraphRef Subgraph_PriceLevel = sc.Subgraph[7];
    
    // Input declarations
    SCInputRef Input_NumberOfBars = sc.Input[0];
    SCInputRef Input_TicksPerLevel = sc.Input[1];
    SCInputRef Input_MinimumVolume = sc.Input[2];
    
    // Persistent storage for delta map data
    c_VAPContainer* p_VolumeAtPriceData = (c_VAPContainer*)sc.GetPersistentPointer(1);
    
    if (sc.SetDefaults)
    {
        // Set study configuration
        sc.GraphName = "Delta Map";
        sc.StudyDescription = "Visual representation of delta by price level showing order flow concentration";
        sc.AutoLoop = 0; // Manual looping for complex calculations
        sc.GraphRegion = 0;
        sc.ValueFormat = 2;
        sc.ScaleRangeType = SCALE_INDEPENDENT;
        
        // Configure subgraphs
        Subgraph_DeltaHeatMap.Name = "Delta Heat Map";
        Subgraph_DeltaHeatMap.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_DeltaHeatMap.PrimaryColor = RGB(192, 128, 64); // Brown base
        Subgraph_DeltaHeatMap.SecondaryColor = RGB(128, 128, 220); // Blue-gray
        Subgraph_DeltaHeatMap.DrawZeros = false;
        
        Subgraph_PositiveDelta.Name = "Positive Delta Levels";
        Subgraph_PositiveDelta.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_PositiveDelta.PrimaryColor = RGB(192, 128, 64); // Brown
        Subgraph_PositiveDelta.SecondaryColor = RGB(128, 128, 220); // Blue-gray
        Subgraph_PositiveDelta.LineWidth = 1;
        Subgraph_PositiveDelta.LineStyle = 2;
        
        Subgraph_NegativeDelta.Name = "Negative Delta Levels";
        Subgraph_NegativeDelta.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_NegativeDelta.PrimaryColor = RGB(192, 128, 64); // Brown
        Subgraph_NegativeDelta.LineWidth = 3;
        
        Subgraph_DeltaByPrice.Name = "Delta by Price Level";
        Subgraph_DeltaByPrice.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_DeltaByPrice.PrimaryColor = RGB(192, 128, 64); // Brown
        Subgraph_DeltaByPrice.SecondaryColor = RGB(128, 128, 220); // Blue-gray
        Subgraph_DeltaByPrice.LineWidth = 1;
        Subgraph_DeltaByPrice.LineStyle = 20;
        
        Subgraph_VolumeByPrice.Name = "Volume by Price Concentration";
        Subgraph_VolumeByPrice.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_VolumeByPrice.PrimaryColor = RGB(192, 128, 64); // Brown
        Subgraph_VolumeByPrice.LineWidth = 3;
        
        Subgraph_NetDelta.Name = "Net Delta Accumulation";
        Subgraph_NetDelta.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_NetDelta.PrimaryColor = RGB(128, 128, 220); // Blue-gray
        Subgraph_NetDelta.LineWidth = 3;
        
        Subgraph_DeltaRatio.Name = "Delta Ratio Analysis";
        Subgraph_DeltaRatio.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_DeltaRatio.PrimaryColor = RGB(192, 128, 64); // Brown
        
        // Configure inputs
        Input_NumberOfBars.Name = "Number of Bars";
        Input_NumberOfBars.SetInt(50);
        Input_NumberOfBars.SetIntLimits(10, 500);
        
        Input_TicksPerLevel.Name = "Ticks Per Level";
        Input_TicksPerLevel.SetInt(1);
        Input_TicksPerLevel.SetIntLimits(1, 10);
        
        Input_MinimumVolume.Name = "Minimum Volume Filter";
        Input_MinimumVolume.SetInt(10);
        Input_MinimumVolume.SetIntLimits(1, 1000);
        
        return;
    }
    
    // Initialize persistent data structure
    if (p_VolumeAtPriceData == NULL)
    {
        p_VolumeAtPriceData = new c_VAPContainer();
        sc.SetPersistentPointer(1, p_VolumeAtPriceData);
    }
    
    // Clear previous data on full recalculation
    if (sc.IsFullRecalculation && sc.UpdateStartIndex == 0)
    {
        p_VolumeAtPriceData->Clear();
        for (int SubgraphIndex = 0; SubgraphIndex <= 7; SubgraphIndex++)
            sc.Subgraph[SubgraphIndex].Clear();
    }
    
    // Calculate delta map for the specified range
    int StartIndex = max(0, sc.ArraySize - 1 - Input_NumberOfBars.GetInt());
    int EndIndex = sc.ArraySize - 1;
    
    if (EndIndex < 0)
        return;
    
    // Process each bar in the range
    for (int BarIndex = StartIndex; BarIndex <= EndIndex; BarIndex++)
    {
        if (BarIndex < 0)
            continue;
            
        float BarHigh = sc.High[BarIndex];
        float BarLow = sc.Low[BarIndex];
        float BarVolume = sc.Volume[BarIndex];
        float BarBidVolume = sc.BidVolume[BarIndex];
        float BarAskVolume = sc.AskVolume[BarIndex];
        
        if (BarVolume < Input_MinimumVolume.GetInt())
            continue;
            
        float BarDelta = BarAskVolume - BarBidVolume;
        
        // Calculate price levels within the bar
        float TickSize = sc.TickSize;
        if (TickSize <= 0)
            TickSize = 0.01f;
            
        float LevelSize = TickSize * Input_TicksPerLevel.GetInt();
        
        // Distribute delta across price levels within the bar
        int NumLevels = (int)((BarHigh - BarLow) / LevelSize) + 1;
        if (NumLevels <= 0)
            NumLevels = 1;
            
        float DeltaPerLevel = BarDelta / NumLevels;
        float VolumePerLevel = BarVolume / NumLevels;
        
        // Add delta data for each price level
        for (int Level = 0; Level < NumLevels; Level++)
        {
            float PriceLevel = BarLow + (Level * LevelSize);
            
            // Store delta by price level data
            s_VolumeAtPriceV2 VAPData;
            VAPData.PriceInTicks = sc.PriceValueToTicks(PriceLevel);
            VAPData.Volume = (unsigned int)VolumePerLevel;
            VAPData.BidVolume = (unsigned int)(BarBidVolume / NumLevels);
            VAPData.AskVolume = (unsigned int)(BarAskVolume / NumLevels);
            
            p_VolumeAtPriceData->InsertVolumeAtPrice(VAPData);
        }
    }
    
    // Generate visual output based on accumulated delta data
    const s_VolumeAtPriceV2* p_VAPArray = NULL;
    int VAPArraySize = 0;
    p_VolumeAtPriceData->GetVAPElementsForRange(sc.PriceValueToTicks(sc.Low[EndIndex]), 
                                               sc.PriceValueToTicks(sc.High[EndIndex]), 
                                               p_VAPArray, VAPArraySize);
    
    // Clear previous subgraph data
    for (int SubgraphIndex = 0; SubgraphIndex <= 7; SubgraphIndex++)
    {
        for (int Index = StartIndex; Index <= EndIndex; Index++)
            sc.Subgraph[SubgraphIndex][Index] = 0;
    }
    
    // Populate subgraphs with delta map data
    for (int VAPIndex = 0; VAPIndex < VAPArraySize; VAPIndex++)
    {
        const s_VolumeAtPriceV2& VAPElement = p_VAPArray[VAPIndex];
        float Price = sc.TicksToPrice(VAPElement.PriceInTicks);
        float Delta = (float)VAPElement.AskVolume - (float)VAPElement.BidVolume;
        float Volume = (float)VAPElement.Volume;
        
        // Find the appropriate bar index for this price level
        int TargetIndex = EndIndex;
        for (int Index = StartIndex; Index <= EndIndex; Index++)
        {
            if (Price >= sc.Low[Index] && Price <= sc.High[Index])
            {
                TargetIndex = Index;
                break;
            }
        }
        
        // Set subgraph values
        if (Delta > 0)
            Subgraph_PositiveDelta[TargetIndex] = Delta;
        else if (Delta < 0)
            Subgraph_NegativeDelta[TargetIndex] = Delta;
            
        Subgraph_DeltaByPrice[TargetIndex] = Delta;
        Subgraph_VolumeByPrice[TargetIndex] = Volume;
        Subgraph_NetDelta[TargetIndex] += Delta;
        
        if (Volume > 0)
            Subgraph_DeltaRatio[TargetIndex] = Delta / Volume;
    }
}
