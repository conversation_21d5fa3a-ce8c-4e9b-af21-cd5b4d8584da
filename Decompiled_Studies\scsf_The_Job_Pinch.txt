
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_The_Job_Pinch(undefined1 param_1 [16],undefined8 param_2,longlong param_3,
                       undefined8 param_4,undefined8 param_5,undefined8 param_6,undefined8 param_7,
                       undefined8 param_8,undefined8 param_9,undefined8 param_10)

{
  undefined1 auVar1 [16];
  undefined1 auVar2 [16];
  undefined1 auVar3 [16];
  undefined1 *lpMem;
  bool bVar4;
  uint uVar5;
  HANDLE pvVar6;
  char *pcVar7;
  undefined8 uVar8;
  ulonglong uVar9;
  ulonglong uVar10;
  float *pfVar11;
  longlong *plVar12;
  undefined4 *puVar13;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  undefined8 extraout_x1_10;
  undefined8 extraout_x1_11;
  undefined8 extraout_x1_12;
  undefined8 extraout_x1_13;
  undefined8 extraout_x1_14;
  undefined8 extraout_x1_15;
  undefined8 extraout_x1_16;
  longlong lVar14;
  longlong lVar15;
  float *pfVar16;
  int iVar17;
  uint uVar18;
  longlong lVar19;
  uint uVar20;
  int extraout_w11;
  longlong extraout_x11;
  float *extraout_x11_00;
  float *extraout_x11_01;
  char *pcVar21;
  longlong lVar22;
  int iVar23;
  longlong lVar24;
  char *lpMem_00;
  longlong lVar25;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  undefined4 extraout_s0_02;
  undefined4 extraout_s0_03;
  undefined4 extraout_s0_04;
  undefined4 extraout_s0_05;
  undefined4 extraout_s0_06;
  undefined4 extraout_s0_07;
  undefined4 extraout_s0_08;
  undefined4 extraout_s0_09;
  undefined4 extraout_s0_10;
  undefined4 extraout_s0_11;
  undefined4 extraout_s0_12;
  undefined4 extraout_s0_13;
  undefined4 extraout_s0_14;
  undefined4 extraout_s0_15;
  undefined4 extraout_s0_16;
  undefined4 extraout_s0_17;
  undefined4 extraout_s0_18;
  float fVar26;
  undefined4 extraout_var;
  undefined4 extraout_var_00;
  undefined4 extraout_var_01;
  undefined4 extraout_var_02;
  undefined4 extraout_var_03;
  undefined4 extraout_var_04;
  undefined4 extraout_var_05;
  undefined4 extraout_var_06;
  undefined4 extraout_var_07;
  undefined4 extraout_var_08;
  undefined4 extraout_var_09;
  undefined4 extraout_var_10;
  undefined4 extraout_var_11;
  undefined4 extraout_var_12;
  undefined4 extraout_var_13;
  undefined4 extraout_var_14;
  undefined4 extraout_var_15;
  undefined4 extraout_var_16;
  undefined4 extraout_var_17;
  undefined4 extraout_var_18;
  undefined4 uVar27;
  undefined8 extraout_var_19;
  undefined8 extraout_var_20;
  undefined8 extraout_var_21;
  undefined8 extraout_var_22;
  undefined8 extraout_var_23;
  undefined8 extraout_var_24;
  undefined8 extraout_var_25;
  undefined8 extraout_var_26;
  undefined8 extraout_var_27;
  undefined8 extraout_var_28;
  undefined8 extraout_var_29;
  undefined8 extraout_var_30;
  undefined8 extraout_var_31;
  undefined8 extraout_var_32;
  undefined8 extraout_var_33;
  undefined8 extraout_var_34;
  undefined8 extraout_var_35;
  undefined8 extraout_var_36;
  undefined8 uVar28;
  undefined8 extraout_var_37;
  undefined8 extraout_var_38;
  undefined4 uVar29;
  float fVar30;
  float extraout_s18;
  float extraout_s18_00;
  longlong local_1a8;
  longlong local_198;
  longlong local_188;
  char *local_180;
  undefined8 local_178;
  undefined1 *local_170;
  longlong *local_168;
  longlong *local_160;
  longlong local_158;
  longlong local_150;
  longlong local_148;
  longlong *local_140;
  longlong local_138;
  longlong local_130;
  longlong local_128;
  longlong local_120;
  longlong local_118;
  longlong local_110;
  longlong local_108;
  longlong local_100;
  longlong *local_f8;
  longlong *local_f0;
  longlong *local_e8;
  longlong local_e0;
  longlong local_d8;
  longlong local_d0;
  int *local_c8;
  longlong local_c0;
  longlong local_b8;
  undefined8 local_b0;
  undefined1 *local_a0;
  undefined8 uStack_98;
  undefined1 *local_90;
  
                    /* 0x41f58  26  scsf_The_Job_Pinch */
  local_b0 = 0xfffffffffffffffe;
  local_118 = param_3;
  local_100 = param_3;
  local_c8 = (int *)(**(code **)(param_3 + 0x18b8))(param_1._0_4_,0);
  lVar19 = *(longlong *)(param_3 + 0x500);
  uVar8 = extraout_x1;
  uVar29 = extraout_s0;
  uVar27 = extraout_var;
  uVar28 = extraout_var_19;
  if (lVar19 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar8 = extraout_x1_00;
      uVar29 = extraout_s0_00;
      uVar27 = extraout_var_00;
      uVar28 = extraout_var_20;
    }
    lVar19 = *(longlong *)(param_3 + 0x500);
    if (lVar19 != 0) goto LAB_180041ff8;
    local_f8 = (longlong *)(param_3 + 0x528);
LAB_180042030:
    local_168 = local_f8;
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar8 = extraout_x1_01;
      uVar29 = extraout_s0_01;
      uVar27 = extraout_var_01;
      uVar28 = extraout_var_21;
    }
    lVar19 = *(longlong *)(param_3 + 0x500);
    if (lVar19 != 0) goto LAB_180042060;
    local_f0 = (longlong *)(param_3 + 0x528);
LAB_180042098:
    local_140 = local_f0;
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar8 = extraout_x1_02;
      uVar29 = extraout_s0_02;
      uVar27 = extraout_var_02;
      uVar28 = extraout_var_22;
    }
    lVar19 = *(longlong *)(param_3 + 0x500);
    if (lVar19 != 0) goto LAB_1800420c8;
    local_c0 = param_3 + 0x528;
LAB_180042100:
    local_198 = local_c0;
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar8 = extraout_x1_03;
      uVar29 = extraout_s0_03;
      uVar27 = extraout_var_03;
      uVar28 = extraout_var_23;
    }
    lVar19 = *(longlong *)(param_3 + 0x500);
    if (lVar19 != 0) goto LAB_18004212c;
    local_158 = param_3 + 0x528;
LAB_180042160:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar8 = extraout_x1_04;
      uVar29 = extraout_s0_04;
      uVar27 = extraout_var_04;
      uVar28 = extraout_var_24;
    }
    lVar19 = *(longlong *)(param_3 + 0x500);
    if (lVar19 != 0) goto LAB_18004218c;
    local_188 = param_3 + 0x528;
LAB_1800421c0:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar8 = extraout_x1_05;
      uVar29 = extraout_s0_05;
      uVar27 = extraout_var_05;
      uVar28 = extraout_var_25;
    }
    lVar19 = *(longlong *)(param_3 + 0x500);
    if (lVar19 != 0) goto LAB_1800421ec;
    local_108 = param_3 + 0x528;
LAB_180042220:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar8 = extraout_x1_06;
      uVar29 = extraout_s0_06;
      uVar27 = extraout_var_06;
      uVar28 = extraout_var_26;
    }
    lVar19 = *(longlong *)(param_3 + 0x500);
    if (lVar19 != 0) goto LAB_180042254;
    local_b8 = param_3 + 0x528;
LAB_180042290:
    local_1a8 = local_b8;
    local_120 = local_b8;
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar8 = extraout_x1_07;
      uVar29 = extraout_s0_07;
      uVar27 = extraout_var_07;
      uVar28 = extraout_var_27;
    }
    lVar19 = *(longlong *)(param_3 + 0x500);
    if (lVar19 != 0) goto LAB_1800422bc;
    lVar15 = param_3 + 0x528;
LAB_1800422f0:
    local_110 = lVar15;
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar8 = extraout_x1_08;
      uVar29 = extraout_s0_08;
      uVar27 = extraout_var_08;
      uVar28 = extraout_var_28;
    }
    lVar19 = *(longlong *)(param_3 + 0x500);
    if (lVar19 != 0) goto LAB_180042310;
    lVar19 = param_3 + 0x528;
  }
  else {
LAB_180041ff8:
    iVar23 = *(int *)(param_3 + 0x520);
    if (iVar23 == 0) {
      local_168 = (longlong *)(param_3 + 0x528);
    }
    else {
      iVar17 = 0;
      if (iVar23 < 1) {
        iVar17 = iVar23 + -1;
      }
      local_168 = (longlong *)(lVar19 + (longlong)iVar17 * 0x170);
    }
    local_f8 = local_168;
    if (lVar19 == 0) goto LAB_180042030;
LAB_180042060:
    iVar23 = *(int *)(param_3 + 0x520);
    if (iVar23 == 0) {
      local_140 = (longlong *)(param_3 + 0x528);
    }
    else {
      iVar17 = 1;
      if (iVar23 < 2) {
        iVar17 = iVar23 + -1;
      }
      local_140 = (longlong *)(lVar19 + (longlong)iVar17 * 0x170);
    }
    local_f0 = local_140;
    if (lVar19 == 0) goto LAB_180042098;
LAB_1800420c8:
    iVar23 = *(int *)(param_3 + 0x520);
    if (iVar23 == 0) {
      local_198 = param_3 + 0x528;
    }
    else {
      iVar17 = 2;
      if (iVar23 < 3) {
        iVar17 = iVar23 + -1;
      }
      local_198 = lVar19 + (longlong)iVar17 * 0x170;
    }
    local_c0 = local_198;
    if (lVar19 == 0) goto LAB_180042100;
LAB_18004212c:
    iVar23 = *(int *)(param_3 + 0x520);
    if (iVar23 == 0) {
      local_158 = param_3 + 0x528;
    }
    else {
      iVar17 = 3;
      if (iVar23 < 4) {
        iVar17 = iVar23 + -1;
      }
      local_158 = lVar19 + (longlong)iVar17 * 0x170;
    }
    if (lVar19 == 0) goto LAB_180042160;
LAB_18004218c:
    iVar23 = *(int *)(param_3 + 0x520);
    if (iVar23 == 0) {
      local_188 = param_3 + 0x528;
    }
    else {
      iVar17 = 4;
      if (iVar23 < 5) {
        iVar17 = iVar23 + -1;
      }
      local_188 = lVar19 + (longlong)iVar17 * 0x170;
    }
    if (lVar19 == 0) goto LAB_1800421c0;
LAB_1800421ec:
    iVar23 = *(int *)(param_3 + 0x520);
    if (iVar23 == 0) {
      local_108 = param_3 + 0x528;
    }
    else {
      iVar17 = 5;
      if (iVar23 < 6) {
        iVar17 = iVar23 + -1;
      }
      local_108 = lVar19 + (longlong)iVar17 * 0x170;
    }
    if (lVar19 == 0) goto LAB_180042220;
LAB_180042254:
    iVar23 = *(int *)(param_3 + 0x520);
    if (iVar23 == 0) {
      local_1a8 = param_3 + 0x528;
    }
    else {
      iVar17 = 6;
      if (iVar23 < 7) {
        iVar17 = iVar23 + -1;
      }
      local_1a8 = lVar19 + (longlong)iVar17 * 0x170;
    }
    local_120 = local_1a8;
    local_b8 = local_1a8;
    if (lVar19 == 0) goto LAB_180042290;
LAB_1800422bc:
    iVar23 = *(int *)(param_3 + 0x520);
    if (iVar23 == 0) {
      lVar15 = param_3 + 0x528;
    }
    else {
      iVar17 = 7;
      if (iVar23 < 8) {
        iVar17 = iVar23 + -1;
      }
      lVar15 = lVar19 + (longlong)iVar17 * 0x170;
    }
    local_110 = lVar15;
    if (lVar19 == 0) goto LAB_1800422f0;
LAB_180042310:
    iVar23 = *(int *)(param_3 + 0x520);
    if (iVar23 == 0) {
      lVar19 = param_3 + 0x528;
    }
    else {
      iVar17 = 8;
      if (iVar23 < 9) {
        iVar17 = iVar23 + -1;
      }
      lVar19 = lVar19 + (longlong)iVar17 * 0x170;
    }
  }
  local_128 = *(longlong *)(param_3 + 0x210);
  if (local_128 == 0) {
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar8 = extraout_x1_09;
      uVar29 = extraout_s0_09;
      uVar27 = extraout_var_09;
      uVar28 = extraout_var_29;
    }
    local_128 = *(longlong *)(param_3 + 0x210);
    if (local_128 != 0) goto LAB_180042370;
    local_e8 = (longlong *)(param_3 + 0x238);
LAB_1800423a8:
    local_160 = local_e8;
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar8 = extraout_x1_10;
      uVar29 = extraout_s0_10;
      uVar27 = extraout_var_10;
      uVar28 = extraout_var_30;
    }
    local_128 = *(longlong *)(param_3 + 0x210);
    if (local_128 != 0) goto LAB_1800423d8;
    local_e0 = param_3 + 0x238;
LAB_180042414:
    local_150 = local_e0;
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar8 = extraout_x1_11;
      uVar29 = extraout_s0_11;
      uVar27 = extraout_var_11;
      uVar28 = extraout_var_31;
    }
    local_128 = *(longlong *)(param_3 + 0x210);
    if (local_128 != 0) goto LAB_18004243c;
    lVar14 = param_3 + 0x238;
LAB_180042470:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar8 = extraout_x1_12;
      uVar29 = extraout_s0_12;
      uVar27 = extraout_var_12;
      uVar28 = extraout_var_32;
    }
    local_128 = *(longlong *)(param_3 + 0x210);
    if (local_128 != 0) goto LAB_18004249c;
    lVar22 = param_3 + 0x238;
LAB_1800424d4:
    local_d8 = lVar22;
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar8 = extraout_x1_13;
      uVar29 = extraout_s0_13;
      uVar27 = extraout_var_13;
      uVar28 = extraout_var_33;
    }
    local_128 = *(longlong *)(param_3 + 0x210);
    if (local_128 != 0) goto LAB_180042504;
    local_d0 = param_3 + 0x238;
LAB_180042540:
    local_148 = local_d0;
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar8 = extraout_x1_14;
      uVar29 = extraout_s0_14;
      uVar27 = extraout_var_14;
      uVar28 = extraout_var_34;
    }
    local_128 = *(longlong *)(param_3 + 0x210);
    if (local_128 != 0) goto LAB_18004256c;
    lVar25 = param_3 + 0x238;
LAB_1800425a8:
    local_138 = lVar25;
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar8 = extraout_x1_15;
      uVar29 = extraout_s0_15;
      uVar27 = extraout_var_15;
      uVar28 = extraout_var_35;
    }
    local_128 = *(longlong *)(param_3 + 0x210);
    if (local_128 != 0) goto LAB_1800425d4;
    lVar24 = param_3 + 0x238;
LAB_18004260c:
    local_130 = lVar24;
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar8 = extraout_x1_16;
      uVar29 = extraout_s0_16;
      uVar27 = extraout_var_16;
      uVar28 = extraout_var_36;
    }
    local_128 = *(longlong *)(param_3 + 0x210);
    if (local_128 != 0) goto LAB_18004262c;
    local_128 = param_3 + 0x238;
  }
  else {
LAB_180042370:
    iVar23 = *(int *)(param_3 + 0x230);
    if (iVar23 == 0) {
      local_160 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar17 = 0;
      if (iVar23 < 1) {
        iVar17 = iVar23 + -1;
      }
      local_160 = (longlong *)(local_128 + (longlong)iVar17 * 0x98);
    }
    local_e8 = local_160;
    if (local_128 == 0) goto LAB_1800423a8;
LAB_1800423d8:
    iVar23 = *(int *)(param_3 + 0x230);
    if (iVar23 == 0) {
      local_150 = param_3 + 0x238;
    }
    else {
      iVar17 = 1;
      if (iVar23 < 2) {
        iVar17 = iVar23 + -1;
      }
      local_150 = local_128 + (longlong)iVar17 * 0x98;
    }
    local_e0 = local_150;
    if (local_128 == 0) goto LAB_180042414;
LAB_18004243c:
    iVar23 = *(int *)(param_3 + 0x230);
    if (iVar23 == 0) {
      lVar14 = param_3 + 0x238;
    }
    else {
      iVar17 = 2;
      if (iVar23 < 3) {
        iVar17 = iVar23 + -1;
      }
      lVar14 = local_128 + (longlong)iVar17 * 0x98;
    }
    if (local_128 == 0) goto LAB_180042470;
LAB_18004249c:
    iVar23 = *(int *)(param_3 + 0x230);
    if (iVar23 == 0) {
      lVar22 = param_3 + 0x238;
    }
    else {
      iVar17 = 3;
      if (iVar23 < 4) {
        iVar17 = iVar23 + -1;
      }
      lVar22 = local_128 + (longlong)iVar17 * 0x98;
    }
    local_d8 = lVar22;
    if (local_128 == 0) goto LAB_1800424d4;
LAB_180042504:
    iVar23 = *(int *)(param_3 + 0x230);
    if (iVar23 == 0) {
      local_148 = param_3 + 0x238;
    }
    else {
      iVar17 = 4;
      if (iVar23 < 5) {
        iVar17 = iVar23 + -1;
      }
      local_148 = local_128 + (longlong)iVar17 * 0x98;
    }
    local_d0 = local_148;
    if (local_128 == 0) goto LAB_180042540;
LAB_18004256c:
    iVar23 = *(int *)(param_3 + 0x230);
    if (iVar23 == 0) {
      lVar25 = param_3 + 0x238;
    }
    else {
      iVar17 = 5;
      if (iVar23 < 6) {
        iVar17 = iVar23 + -1;
      }
      lVar25 = local_128 + (longlong)iVar17 * 0x98;
    }
    local_138 = lVar25;
    if (local_128 == 0) goto LAB_1800425a8;
LAB_1800425d4:
    iVar23 = *(int *)(param_3 + 0x230);
    if (iVar23 == 0) {
      lVar24 = param_3 + 0x238;
    }
    else {
      iVar17 = 6;
      if (iVar23 < 7) {
        iVar17 = iVar23 + -1;
      }
      lVar24 = local_128 + (longlong)iVar17 * 0x98;
    }
    local_130 = lVar24;
    if (local_128 == 0) goto LAB_18004260c;
LAB_18004262c:
    iVar23 = *(int *)(param_3 + 0x230);
    if (iVar23 == 0) {
      local_128 = param_3 + 0x238;
    }
    else {
      iVar17 = 7;
      if (iVar23 < 8) {
        iVar17 = iVar23 + -1;
      }
      local_128 = local_128 + (longlong)iVar17 * 0x98;
    }
  }
  if (*(int *)(param_3 + 0xac) != 0) {
    lpMem_00 = "";
    local_90 = &DAT_1800d4ecd;
    local_a0 = &DAT_1800d4ecd;
    uStack_98 = 0;
    auVar2._4_4_ = uVar27;
    auVar2._0_4_ = uVar29;
    auVar2._8_8_ = uVar28;
    FUN_180006050(auVar2,param_2,&local_a0,0x1800d7400,param_5,param_6,param_7,param_8,param_9,
                  param_10);
    FUN_1800079f8((longlong *)(param_3 + 0x118),0x1800d7d28,9);
    *(undefined4 *)(param_3 + 0x124) = 1;
    if ((*(char **)(param_3 + 0x338) != (char *)0x0) && (**(char **)(param_3 + 0x338) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0x338),0x1800d4ecd,0);
      *(undefined4 *)(param_3 + 0x344) = 1;
    }
    local_180 = "";
    local_178 = 0;
    local_170 = &DAT_1800d4ecd;
    pvVar6 = GetProcessHeap();
    pcVar7 = (char *)HeapAlloc(pvVar6,0,0x15);
    if (pcVar7 == (char *)0x0) {
      local_180 = "";
      pcVar21 = lpMem_00;
    }
    else {
      param_6 = 0x14;
      pcVar7[8] = '\0';
      pcVar7[9] = '\0';
      pcVar7[10] = '\0';
      pcVar7[0xb] = '\0';
      pcVar7[0xc] = '\0';
      pcVar7[0xd] = '\0';
      pcVar7[0xe] = '\0';
      pcVar7[0xf] = '\0';
      pcVar7[0] = '\0';
      pcVar7[1] = '\0';
      pcVar7[2] = '\0';
      pcVar7[3] = '\0';
      pcVar7[4] = '\0';
      pcVar7[5] = '\0';
      pcVar7[6] = '\0';
      pcVar7[7] = '\0';
      pcVar7[0x10] = '\0';
      pcVar7[0x11] = '\0';
      pcVar7[0x12] = '\0';
      pcVar7[0x13] = '\0';
      pcVar7[0x14] = '\0';
      local_180 = pcVar7;
      FUN_180099d78(pcVar7,0x15,0x1800d6c40,0x14);
      local_178 = 0x100000001;
      pcVar21 = pcVar7;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_180);
    if ((pcVar7 != (char *)0x0) && (pcVar21 != (char *)0x0)) {
      pvVar6 = GetProcessHeap();
      HeapFree(pvVar6,0,pcVar21);
    }
    local_180 = "";
    local_178 = 0;
    local_170 = &DAT_1800d4ecd;
    pvVar6 = GetProcessHeap();
    pcVar7 = (char *)HeapAlloc(pvVar6,0,0x8d);
    if (pcVar7 == (char *)0x0) {
      local_180 = "";
      pcVar21 = lpMem_00;
    }
    else {
      param_6 = 0x8c;
      pcVar7[8] = '\0';
      pcVar7[9] = '\0';
      pcVar7[10] = '\0';
      pcVar7[0xb] = '\0';
      pcVar7[0xc] = '\0';
      pcVar7[0xd] = '\0';
      pcVar7[0xe] = '\0';
      pcVar7[0xf] = '\0';
      pcVar7[0] = '\0';
      pcVar7[1] = '\0';
      pcVar7[2] = '\0';
      pcVar7[3] = '\0';
      pcVar7[4] = '\0';
      pcVar7[5] = '\0';
      pcVar7[6] = '\0';
      pcVar7[7] = '\0';
      pcVar7[0x18] = '\0';
      pcVar7[0x19] = '\0';
      pcVar7[0x1a] = '\0';
      pcVar7[0x1b] = '\0';
      pcVar7[0x1c] = '\0';
      pcVar7[0x1d] = '\0';
      pcVar7[0x1e] = '\0';
      pcVar7[0x1f] = '\0';
      pcVar7[0x10] = '\0';
      pcVar7[0x11] = '\0';
      pcVar7[0x12] = '\0';
      pcVar7[0x13] = '\0';
      pcVar7[0x14] = '\0';
      pcVar7[0x15] = '\0';
      pcVar7[0x16] = '\0';
      pcVar7[0x17] = '\0';
      pcVar7[0x28] = '\0';
      pcVar7[0x29] = '\0';
      pcVar7[0x2a] = '\0';
      pcVar7[0x2b] = '\0';
      pcVar7[0x2c] = '\0';
      pcVar7[0x2d] = '\0';
      pcVar7[0x2e] = '\0';
      pcVar7[0x2f] = '\0';
      pcVar7[0x20] = '\0';
      pcVar7[0x21] = '\0';
      pcVar7[0x22] = '\0';
      pcVar7[0x23] = '\0';
      pcVar7[0x24] = '\0';
      pcVar7[0x25] = '\0';
      pcVar7[0x26] = '\0';
      pcVar7[0x27] = '\0';
      pcVar7[0x38] = '\0';
      pcVar7[0x39] = '\0';
      pcVar7[0x3a] = '\0';
      pcVar7[0x3b] = '\0';
      pcVar7[0x3c] = '\0';
      pcVar7[0x3d] = '\0';
      pcVar7[0x3e] = '\0';
      pcVar7[0x3f] = '\0';
      pcVar7[0x30] = '\0';
      pcVar7[0x31] = '\0';
      pcVar7[0x32] = '\0';
      pcVar7[0x33] = '\0';
      pcVar7[0x34] = '\0';
      pcVar7[0x35] = '\0';
      pcVar7[0x36] = '\0';
      pcVar7[0x37] = '\0';
      pcVar7[0x48] = '\0';
      pcVar7[0x49] = '\0';
      pcVar7[0x4a] = '\0';
      pcVar7[0x4b] = '\0';
      pcVar7[0x4c] = '\0';
      pcVar7[0x4d] = '\0';
      pcVar7[0x4e] = '\0';
      pcVar7[0x4f] = '\0';
      pcVar7[0x40] = '\0';
      pcVar7[0x41] = '\0';
      pcVar7[0x42] = '\0';
      pcVar7[0x43] = '\0';
      pcVar7[0x44] = '\0';
      pcVar7[0x45] = '\0';
      pcVar7[0x46] = '\0';
      pcVar7[0x47] = '\0';
      pcVar7[0x58] = '\0';
      pcVar7[0x59] = '\0';
      pcVar7[0x5a] = '\0';
      pcVar7[0x5b] = '\0';
      pcVar7[0x5c] = '\0';
      pcVar7[0x5d] = '\0';
      pcVar7[0x5e] = '\0';
      pcVar7[0x5f] = '\0';
      pcVar7[0x50] = '\0';
      pcVar7[0x51] = '\0';
      pcVar7[0x52] = '\0';
      pcVar7[0x53] = '\0';
      pcVar7[0x54] = '\0';
      pcVar7[0x55] = '\0';
      pcVar7[0x56] = '\0';
      pcVar7[0x57] = '\0';
      pcVar7[0x68] = '\0';
      pcVar7[0x69] = '\0';
      pcVar7[0x6a] = '\0';
      pcVar7[0x6b] = '\0';
      pcVar7[0x6c] = '\0';
      pcVar7[0x6d] = '\0';
      pcVar7[0x6e] = '\0';
      pcVar7[0x6f] = '\0';
      pcVar7[0x60] = '\0';
      pcVar7[0x61] = '\0';
      pcVar7[0x62] = '\0';
      pcVar7[99] = '\0';
      pcVar7[100] = '\0';
      pcVar7[0x65] = '\0';
      pcVar7[0x66] = '\0';
      pcVar7[0x67] = '\0';
      pcVar7[0x78] = '\0';
      pcVar7[0x79] = '\0';
      pcVar7[0x7a] = '\0';
      pcVar7[0x7b] = '\0';
      pcVar7[0x7c] = '\0';
      pcVar7[0x7d] = '\0';
      pcVar7[0x7e] = '\0';
      pcVar7[0x7f] = '\0';
      pcVar7[0x70] = '\0';
      pcVar7[0x71] = '\0';
      pcVar7[0x72] = '\0';
      pcVar7[0x73] = '\0';
      pcVar7[0x74] = '\0';
      pcVar7[0x75] = '\0';
      pcVar7[0x76] = '\0';
      pcVar7[0x77] = '\0';
      pcVar7[0x80] = '\0';
      pcVar7[0x81] = '\0';
      pcVar7[0x82] = '\0';
      pcVar7[0x83] = '\0';
      pcVar7[0x84] = '\0';
      pcVar7[0x85] = '\0';
      pcVar7[0x86] = '\0';
      pcVar7[0x87] = '\0';
      pcVar7[0x88] = '\0';
      pcVar7[0x89] = '\0';
      pcVar7[0x8a] = '\0';
      pcVar7[0x8b] = '\0';
      pcVar7[0x8c] = '\0';
      local_180 = pcVar7;
      FUN_180099d78(pcVar7,0x8d,0x1800d6bb0,0x8c);
      local_178 = 0x100000001;
      pcVar21 = pcVar7;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_180);
    uVar29 = extraout_s0_17;
    uVar27 = extraout_var_17;
    uVar8 = extraout_var_37;
    if ((pcVar7 != (char *)0x0) && (pcVar21 != (char *)0x0)) {
      pvVar6 = GetProcessHeap();
      HeapFree(pvVar6,0,pcVar21);
      local_180 = (char *)0x0;
      local_178 = 0;
      uVar29 = extraout_s0_18;
      uVar27 = extraout_var_18;
      uVar8 = extraout_var_38;
    }
    pcVar7 = *(char **)(param_3 + 0x118);
    if (*(char **)(param_3 + 0x118) == (char *)0x0) {
      pcVar7 = lpMem_00;
    }
    auVar3._4_4_ = uVar27;
    auVar3._0_4_ = uVar29;
    auVar3._8_8_ = uVar8;
    FUN_180026368(auVar3,param_2,(undefined8 *)(param_3 + 0x338),0x1800d6c60,pcVar7,param_6,param_7,
                  param_8,param_9,param_10);
    local_180 = "";
    local_178 = 0;
    local_170 = &DAT_1800d4ecd;
    pvVar6 = GetProcessHeap();
    pcVar7 = (char *)HeapAlloc(pvVar6,0,7);
    if (pcVar7 == (char *)0x0) {
      local_180 = "";
    }
    else {
      pcVar7[0] = '\0';
      pcVar7[1] = '\0';
      pcVar7[2] = '\0';
      pcVar7[3] = '\0';
      pcVar7[4] = '\0';
      pcVar7[5] = '\0';
      pcVar7[6] = '\0';
      local_180 = pcVar7;
      FUN_180099d78(pcVar7,7,0x1800d6c58,6);
      local_178 = 0x100000001;
      lpMem_00 = pcVar7;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_180);
    if ((pcVar7 != (char *)0x0) && (lpMem_00 != (char *)0x0)) {
      pvVar6 = GetProcessHeap();
      HeapFree(pvVar6,0,lpMem_00);
      local_180 = (char *)0x0;
      local_178 = 0;
    }
    *(undefined4 *)(param_3 + 4) = 0;
    *(undefined4 *)(param_3 + 0x10) = 0;
    *(undefined4 *)(param_3 + 0xcb8) = 0;
    *(undefined4 *)(param_3 + 0x37c) = 1;
    FUN_1800079f8(local_168,0x1800d7d10,0x11);
    *(undefined4 *)((longlong)local_f8 + 0xc) = 1;
    *(undefined2 *)((longlong)local_f8 + 0x24) = 0x23;
    *(undefined2 *)(local_f8 + 5) = 10;
    *(undefined4 *)(local_f8 + 3) = 0xd200;
    FUN_1800079f8(local_140,0x1800d7d48,0x14);
    *(undefined4 *)((longlong)local_f0 + 0xc) = 1;
    *(undefined4 *)(local_f0 + 3) = 0xc0c0c0;
    *(undefined2 *)((longlong)local_f0 + 0x24) = 0x23;
    *(undefined2 *)(local_f0 + 5) = 10;
    *(undefined8 *)(local_120 + 0x18) = 0xffff00ff00ff;
    *(undefined2 *)(local_120 + 0x24) = 5;
    *(undefined2 *)(local_120 + 0x28) = 1;
    *(undefined4 *)(local_120 + 0x20) = 1;
    *(undefined2 *)(local_110 + 0x24) = 5;
    *(undefined2 *)(local_110 + 0x28) = 1;
    *(undefined8 *)(local_110 + 0x18) = 0xff00ff0000ffff;
    *(undefined4 *)(local_110 + 0x20) = 1;
    *(undefined2 *)(lVar19 + 0x24) = 5;
    *(undefined2 *)(lVar19 + 0x28) = 1;
    *(undefined8 *)(lVar19 + 0x18) = 0xff00ff0000ffff;
    *(undefined4 *)(lVar19 + 0x20) = 1;
    FUN_1800079f8(local_160,0x1800d7d38,10);
    lpMem = local_a0;
    *(undefined4 *)((longlong)local_e8 + 0xc) = 1;
    *(undefined4 *)((longlong)local_e8 + 0x1c) = 3;
    *(undefined1 *)(local_e8 + 3) = 1;
    *(undefined4 *)(local_e0 + 0x1c) = 0xc;
    *(undefined1 *)(local_e0 + 0x18) = 0xb;
    *(undefined4 *)(lVar14 + 0x1c) = 0x1a;
    *(undefined1 *)(lVar14 + 0x18) = 0xb;
    *(undefined4 *)(local_d8 + 0x1c) = 9;
    *(undefined1 *)(local_d8 + 0x18) = 0xb;
    *(undefined1 *)(local_d0 + 0x18) = 0xb;
    *(undefined4 *)(local_d0 + 0x1c) = 10;
    *(undefined4 *)(lVar25 + 0x1c) = 0x28;
    *(undefined1 *)(lVar25 + 0x18) = 0xb;
    *(undefined1 *)(lVar24 + 0x18) = 5;
    *(undefined4 *)(lVar24 + 0x1c) = 1;
    *(undefined1 *)(local_128 + 0x18) = 0xb;
    *(undefined4 *)(local_128 + 0x1c) = 5;
    *(undefined4 *)(local_128 + 0x2c) = 0;
    *(undefined4 *)(local_128 + 0x3c) = 1000;
    if ((int)uStack_98 == 0) {
      return;
    }
    if (local_a0 == (undefined1 *)0x0) {
      return;
    }
    pvVar6 = GetProcessHeap();
    HeapFree(pvVar6,0,lpMem);
    return;
  }
  if (*(int *)(param_3 + 900) == 0) {
    auVar1._4_4_ = uVar27;
    auVar1._0_4_ = uVar29;
    auVar1._8_8_ = uVar28;
    uVar8 = FUN_1800254e8(auVar1,param_2,param_3,uVar8,param_5,param_6,param_7,param_8,param_9,
                          param_10);
    *local_c8 = (int)uVar8;
  }
  if (*local_c8 != 0) {
    return;
  }
  if ((*(longlong *)(param_3 + 0x160) == 0) && (*(code **)(param_3 + 0x170) != (code *)0x0)) {
    (**(code **)(param_3 + 0x170))(*(undefined4 *)(param_3 + 0x178));
  }
  uVar9 = FUN_180026708(local_150);
  (**(code **)(param_3 + 0x390))
            (param_3 + 0xab0,local_198 + 0x30,*(undefined4 *)(param_3 + 900),uVar9 & 0xffffffff);
  uVar9 = FUN_180026708(lVar14);
  (**(code **)(param_3 + 0x390))
            (param_3 + 0xab0,local_158 + 0x30,*(undefined4 *)(param_3 + 900),uVar9 & 0xffffffff);
  uVar18 = *(uint *)(param_3 + 900);
  lVar14 = *(longlong *)(local_198 + 0x30);
  uVar5 = uVar18;
  if (lVar14 == 0) {
    if (*(code **)(local_198 + 0x40) != (code *)0x0) {
      (**(code **)(local_198 + 0x40))(*(undefined4 *)(local_198 + 0x48));
      uVar5 = *(uint *)(param_3 + 900);
    }
    lVar14 = *(longlong *)(local_198 + 0x30);
    if (lVar14 != 0) goto LAB_180042b3c;
    pfVar11 = (float *)(local_198 + 0x54);
  }
  else {
LAB_180042b3c:
    iVar23 = *(int *)(local_198 + 0x50);
    if (iVar23 == 0) {
      pfVar11 = (float *)(local_198 + 0x54);
    }
    else {
      uVar18 = uVar18 & ((int)uVar18 >> 0x1f ^ 0xffffffffU);
      if (iVar23 <= (int)uVar18) {
        uVar18 = iVar23 - 1;
      }
      pfVar11 = (float *)(lVar14 + (longlong)(int)uVar18 * 4);
    }
  }
  lVar14 = *(longlong *)(local_158 + 0x30);
  uVar18 = uVar5;
  if (lVar14 == 0) {
    if (*(code **)(local_158 + 0x40) != (code *)0x0) {
      (**(code **)(local_158 + 0x40))(*(undefined4 *)(local_158 + 0x48));
      uVar18 = *(uint *)(param_3 + 900);
    }
    lVar14 = *(longlong *)(local_158 + 0x30);
    if (lVar14 != 0) goto LAB_180042ba0;
    pfVar16 = (float *)(local_158 + 0x54);
  }
  else {
LAB_180042ba0:
    iVar23 = *(int *)(local_158 + 0x50);
    if (iVar23 == 0) {
      pfVar16 = (float *)(local_158 + 0x54);
    }
    else {
      uVar5 = uVar5 & ((int)uVar5 >> 0x1f ^ 0xffffffffU);
      if (iVar23 <= (int)uVar5) {
        uVar5 = iVar23 - 1;
      }
      pfVar16 = (float *)(lVar14 + (longlong)(int)uVar5 * 4);
    }
  }
  fVar26 = *pfVar16;
  fVar30 = *pfVar11;
  lVar14 = *(longlong *)(local_188 + 0x30);
  if (lVar14 == 0) {
    if (*(code **)(local_188 + 0x40) != (code *)0x0) {
      (**(code **)(local_188 + 0x40))(*(undefined4 *)(local_188 + 0x48));
    }
    lVar14 = *(longlong *)(local_188 + 0x30);
    if (lVar14 != 0) goto LAB_180042c04;
    pfVar11 = (float *)(local_188 + 0x54);
  }
  else {
LAB_180042c04:
    iVar23 = *(int *)(local_188 + 0x50);
    if (iVar23 == 0) {
      pfVar11 = (float *)(local_188 + 0x54);
    }
    else {
      uVar18 = uVar18 & ((int)uVar18 >> 0x1f ^ 0xffffffffU);
      if (iVar23 <= (int)uVar18) {
        uVar18 = iVar23 - 1;
      }
      pfVar11 = (float *)(lVar14 + (longlong)(int)uVar18 * 4);
    }
  }
  *pfVar11 = fVar30 - fVar26;
  uVar9 = FUN_180026708(lVar22);
  (**(code **)(param_3 + 0x390))
            (local_188 + 0x30,local_108 + 0x30,*(undefined4 *)(param_3 + 900),uVar9 & 0xffffffff);
  uVar18 = *(uint *)(param_3 + 900);
  lVar14 = *(longlong *)(local_198 + 0x30);
  if (lVar14 == 0) {
    if (*(code **)(local_198 + 0x40) != (code *)0x0) {
      (**(code **)(local_198 + 0x40))(*(undefined4 *)(local_198 + 0x48));
      uVar18 = *(uint *)(param_3 + 900);
    }
    lVar14 = *(longlong *)(local_198 + 0x30);
    lVar22 = local_c0;
    if (lVar14 != 0) goto LAB_180042ca8;
LAB_180042ce4:
    if (*(code **)(lVar22 + 0x40) != (code *)0x0) {
      (**(code **)(lVar22 + 0x40))(*(undefined4 *)(lVar22 + 0x48));
      uVar18 = *(uint *)(param_3 + 900);
    }
  }
  else {
LAB_180042ca8:
    lVar22 = local_198;
    if (lVar14 == 0) goto LAB_180042ce4;
  }
  uVar9 = FUN_180026708(local_150);
  fVar26 = FUN_1800be8d8(extraout_s18 / (float)(int)uVar9);
  lVar14 = *(longlong *)(local_1a8 + 0x30);
  if (lVar14 == 0) {
    if (*(code **)(local_1a8 + 0x40) != (code *)0x0) {
      (**(code **)(local_1a8 + 0x40))(*(undefined4 *)(local_1a8 + 0x48));
    }
    lVar14 = *(longlong *)(local_1a8 + 0x30);
    if (lVar14 != 0) goto LAB_180042d94;
    pfVar11 = (float *)(local_1a8 + 0x54);
  }
  else {
LAB_180042d94:
    iVar23 = *(int *)(local_1a8 + 0x50);
    if (iVar23 == 0) {
      pfVar11 = (float *)(local_1a8 + 0x54);
    }
    else {
      uVar18 = uVar18 & ((int)uVar18 >> 0x1f ^ 0xffffffffU);
      if (iVar23 <= (int)uVar18) {
        uVar18 = iVar23 - 1;
      }
      pfVar11 = (float *)(lVar14 + (longlong)(int)uVar18 * 4);
    }
  }
  *pfVar11 = (fVar26 * 180.0) / 3.1415927;
  uVar9 = FUN_180026708(local_148);
  uVar10 = FUN_180026708(extraout_x11);
  lVar14 = *(longlong *)(lVar15 + 0x80);
  if (lVar14 == 0) {
    if (*(code **)(lVar15 + 0x90) != (code *)0x0) {
      (**(code **)(lVar15 + 0x90))(*(undefined4 *)(lVar15 + 0x98));
    }
    lVar14 = *(longlong *)(lVar15 + 0x80);
    if (lVar14 != 0) goto LAB_180042e10;
    lVar22 = lVar15 + 0xa8;
LAB_180042e3c:
    if (*(code **)(lVar15 + 0x90) != (code *)0x0) {
      (**(code **)(lVar15 + 0x90))(*(undefined4 *)(lVar15 + 0x98));
    }
    lVar14 = *(longlong *)(lVar15 + 0x80);
    if (lVar14 != 0) goto LAB_180042e64;
    lVar25 = lVar15 + 0xa8;
LAB_180042e94:
    if (*(code **)(lVar15 + 0x90) != (code *)0x0) {
      (**(code **)(lVar15 + 0x90))(*(undefined4 *)(lVar15 + 0x98));
    }
    lVar14 = *(longlong *)(lVar15 + 0x80);
    if (lVar14 != 0) goto LAB_180042ebc;
    lVar24 = lVar15 + 0xa8;
LAB_180042eec:
    if (*(code **)(lVar15 + 0x90) != (code *)0x0) {
      (**(code **)(lVar15 + 0x90))(*(undefined4 *)(lVar15 + 0x98));
    }
    lVar14 = *(longlong *)(lVar15 + 0x80);
    if (lVar14 != 0) goto LAB_180042f0c;
    lVar14 = lVar15 + 0xa8;
  }
  else {
LAB_180042e10:
    iVar23 = *(int *)(lVar15 + 0xa0);
    if (iVar23 == 0) {
      lVar22 = lVar15 + 0xa8;
    }
    else {
      iVar17 = 0;
      if (iVar23 < 1) {
        iVar17 = iVar23 + -1;
      }
      lVar22 = lVar14 + (longlong)iVar17 * 0x28;
    }
    if (lVar14 == 0) goto LAB_180042e3c;
LAB_180042e64:
    iVar23 = *(int *)(lVar15 + 0xa0);
    if (iVar23 == 0) {
      lVar25 = lVar15 + 0xa8;
    }
    else {
      iVar17 = 1;
      if (iVar23 < 2) {
        iVar17 = iVar23 + -1;
      }
      lVar25 = lVar14 + (longlong)iVar17 * 0x28;
    }
    if (lVar14 == 0) goto LAB_180042e94;
LAB_180042ebc:
    iVar23 = *(int *)(lVar15 + 0xa0);
    if (iVar23 == 0) {
      lVar24 = lVar15 + 0xa8;
    }
    else {
      iVar17 = 2;
      if (iVar23 < 3) {
        iVar17 = iVar23 + -1;
      }
      lVar24 = lVar14 + (longlong)iVar17 * 0x28;
    }
    if (lVar14 == 0) goto LAB_180042eec;
LAB_180042f0c:
    iVar23 = *(int *)(lVar15 + 0xa0);
    if (iVar23 == 0) {
      lVar14 = lVar15 + 0xa8;
    }
    else {
      iVar17 = 3;
      if (iVar23 < 4) {
        iVar17 = iVar23 + -1;
      }
      lVar14 = lVar14 + (longlong)iVar17 * 0x28;
    }
  }
  (**(code **)(param_3 + 0x4b8))
            (param_3 + 400,*(undefined4 *)(param_3 + 900),uVar10 & 0xffffffff,uVar9 & 0xffffffff,
             lVar15 + 0x30,lVar22,lVar25,lVar24,lVar14);
  uVar18 = *(uint *)(param_3 + 900);
  lVar14 = *(longlong *)(lVar15 + 0x30);
  if (lVar14 == 0) {
    if (*(code **)(lVar15 + 0x40) != (code *)0x0) {
      (**(code **)(lVar15 + 0x40))(*(undefined4 *)(lVar15 + 0x48));
      uVar18 = *(uint *)(param_3 + 900);
    }
    lVar14 = *(longlong *)(lVar15 + 0x30);
    if (lVar14 != 0) goto LAB_180042fa4;
LAB_180042fdc:
    if (*(code **)(lVar15 + 0x40) != (code *)0x0) {
      (**(code **)(lVar15 + 0x40))(*(undefined4 *)(lVar15 + 0x48));
      uVar18 = *(uint *)(param_3 + 900);
    }
  }
  else {
LAB_180042fa4:
    if (lVar14 == 0) goto LAB_180042fdc;
  }
  uVar9 = FUN_180026708(local_148);
  fVar26 = FUN_1800be8d8(extraout_s18_00 / (float)(int)uVar9);
  lVar14 = *(longlong *)(lVar19 + 0x30);
  if (lVar14 == 0) {
    if (*(code **)(lVar19 + 0x40) != (code *)0x0) {
      (**(code **)(lVar19 + 0x40))(*(undefined4 *)(lVar19 + 0x48));
    }
    lVar14 = *(longlong *)(lVar19 + 0x30);
    if (lVar14 != 0) goto LAB_180043084;
    pfVar11 = (float *)(lVar19 + 0x54);
  }
  else {
LAB_180043084:
    iVar23 = *(int *)(lVar19 + 0x50);
    if (iVar23 == 0) {
      pfVar11 = (float *)(lVar19 + 0x54);
    }
    else {
      uVar18 = uVar18 & ((int)uVar18 >> 0x1f ^ 0xffffffffU);
      if (iVar23 <= (int)uVar18) {
        uVar18 = iVar23 - 1;
      }
      pfVar11 = (float *)(lVar14 + (longlong)(int)uVar18 * 4);
    }
  }
  *pfVar11 = (fVar26 * 180.0) / 3.1415927;
  if ((*(longlong *)(lVar15 + 0x30) == 0) && (*(code **)(lVar15 + 0x40) != (code *)0x0)) {
    (**(code **)(lVar15 + 0x40))(*(undefined4 *)(lVar15 + 0x48));
  }
  uVar9 = FUN_180026708(local_138);
  if (*extraout_x11_00 <= (float)(int)uVar9) {
LAB_180043230:
    bVar4 = false;
  }
  else {
    uVar18 = *(uint *)(param_3 + 900);
    lVar14 = *(longlong *)(lVar19 + 0x30);
    uVar5 = uVar18;
    if (lVar14 == 0) {
      if (*(code **)(lVar19 + 0x40) != (code *)0x0) {
        (**(code **)(lVar19 + 0x40))(*(undefined4 *)(lVar19 + 0x48));
        uVar5 = *(uint *)(param_3 + 900);
      }
      lVar14 = *(longlong *)(lVar19 + 0x30);
      if (lVar14 != 0) goto LAB_180043164;
      pfVar11 = (float *)(lVar19 + 0x54);
      iVar23 = uVar5 - 1;
LAB_1800431a0:
      if (*(code **)(lVar19 + 0x40) != (code *)0x0) {
        (**(code **)(lVar19 + 0x40))(*(undefined4 *)(lVar19 + 0x48));
        uVar5 = *(uint *)(param_3 + 900);
      }
      lVar14 = *(longlong *)(lVar19 + 0x30);
      if (lVar14 != 0) goto LAB_1800431c8;
      pfVar16 = (float *)(lVar19 + 0x54);
    }
    else {
LAB_180043164:
      iVar23 = *(int *)(lVar19 + 0x50);
      if (iVar23 == 0) {
        pfVar11 = (float *)(lVar19 + 0x54);
      }
      else {
        uVar18 = uVar18 & ((int)uVar18 >> 0x1f ^ 0xffffffffU);
        if (iVar23 <= (int)uVar18) {
          uVar18 = iVar23 - 1;
        }
        pfVar11 = (float *)(lVar14 + (longlong)(int)uVar18 * 4);
      }
      iVar23 = uVar5 - 1;
      if (lVar14 == 0) goto LAB_1800431a0;
LAB_1800431c8:
      iVar17 = *(int *)(lVar19 + 0x50);
      if (iVar17 == 0) {
        pfVar16 = (float *)(lVar19 + 0x54);
      }
      else {
        if (iVar23 < 0) {
          iVar23 = 0;
        }
        if (iVar17 <= iVar23) {
          iVar23 = iVar17 + -1;
        }
        pfVar16 = (float *)(lVar14 + (longlong)iVar23 * 4);
      }
    }
    if (*pfVar16 <= *pfVar11) goto LAB_180043230;
    pfVar11 = (float *)FUN_18000f448(local_1a8,uVar5);
    fVar26 = *pfVar11;
    pfVar11 = (float *)FUN_18000f448(local_1a8,*(int *)(param_3 + 900) + -1);
    bVar4 = true;
    if (fVar26 <= *pfVar11) goto LAB_180043230;
  }
  uVar18 = *(uint *)(param_3 + 900);
  lVar14 = *(longlong *)(lVar15 + 0x30);
  uVar5 = uVar18;
  if (lVar14 == 0) {
    if (*(code **)(lVar15 + 0x40) != (code *)0x0) {
      (**(code **)(lVar15 + 0x40))(*(undefined4 *)(lVar15 + 0x48));
      uVar5 = *(uint *)(param_3 + 900);
    }
    lVar14 = *(longlong *)(lVar15 + 0x30);
    if (lVar14 != 0) goto LAB_180043278;
    pfVar11 = (float *)(lVar15 + 0x54);
    iVar23 = uVar5 - 1;
LAB_1800432ac:
    if (*(code **)(lVar15 + 0x40) != (code *)0x0) {
      (**(code **)(lVar15 + 0x40))(*(undefined4 *)(lVar15 + 0x48));
      uVar5 = *(uint *)(param_3 + 900);
    }
    lVar14 = *(longlong *)(lVar15 + 0x30);
    if (lVar14 != 0) goto LAB_1800432d4;
    pfVar16 = (float *)(lVar15 + 0x54);
  }
  else {
LAB_180043278:
    iVar23 = *(int *)(lVar15 + 0x50);
    if (iVar23 == 0) {
      pfVar11 = (float *)(lVar15 + 0x54);
    }
    else {
      uVar18 = uVar18 & ((int)uVar18 >> 0x1f ^ 0xffffffffU);
      if (iVar23 <= (int)uVar18) {
        uVar18 = iVar23 - 1;
      }
      pfVar11 = (float *)(lVar14 + (longlong)(int)uVar18 * 4);
    }
    iVar23 = uVar5 - 1;
    if (lVar14 == 0) goto LAB_1800432ac;
LAB_1800432d4:
    iVar17 = *(int *)(lVar15 + 0x50);
    if (iVar17 == 0) {
      pfVar16 = (float *)(lVar15 + 0x54);
    }
    else {
      if (iVar23 < 0) {
        iVar23 = 0;
      }
      if (iVar17 <= iVar23) {
        iVar23 = iVar17 + -1;
      }
      pfVar16 = (float *)(lVar14 + (longlong)iVar23 * 4);
    }
  }
  fVar26 = *pfVar11;
  fVar30 = *pfVar16;
  if (bVar4) {
    lVar14 = *(longlong *)(local_188 + 0x30);
    if (lVar14 == 0) {
      if (*(code **)(local_188 + 0x40) != (code *)0x0) {
        (**(code **)(local_188 + 0x40))(*(undefined4 *)(local_188 + 0x48));
      }
      lVar14 = *(longlong *)(local_188 + 0x30);
      if (lVar14 != 0) goto LAB_18004334c;
      pfVar11 = (float *)(local_188 + 0x54);
    }
    else {
LAB_18004334c:
      iVar23 = *(int *)(local_188 + 0x50);
      if (iVar23 == 0) {
        pfVar11 = (float *)(local_188 + 0x54);
      }
      else {
        uVar5 = uVar5 & ((int)uVar5 >> 0x1f ^ 0xffffffffU);
        if (iVar23 <= (int)uVar5) {
          uVar5 = iVar23 - 1;
        }
        pfVar11 = (float *)(lVar14 + (longlong)(int)uVar5 * 4);
      }
    }
    if (*pfVar11 < 0.0) {
      if (fVar30 <= fVar26) {
        uVar5 = FUN_180026550((longlong)local_160);
        uVar18 = 0x3b;
        if (uVar5 < 0x3c) {
          uVar18 = uVar5;
        }
        plVar12 = FUN_1800076d0((longlong *)(param_3 + 0x448),uVar18);
        puVar13 = (undefined4 *)FUN_180005d08(plVar12,*(int *)(param_3 + 900));
        uVar29 = *puVar13;
        plVar12 = local_140;
      }
      else {
        bVar4 = FUN_180026690(local_130);
        if (bVar4) {
          uVar18 = extraout_w11 - 1;
          uVar9 = FUN_180026708(local_128);
          plVar12 = local_168;
          bVar4 = false;
          iVar23 = uVar18 - (int)uVar9;
          if (iVar23 < (int)uVar18) {
            do {
              lVar14 = plVar12[6];
              if (lVar14 == 0) {
                if ((code *)plVar12[8] != (code *)0x0) {
                  (*(code *)plVar12[8])((int)plVar12[9]);
                }
                lVar14 = plVar12[6];
                if (lVar14 != 0) goto LAB_1800433d8;
                pfVar11 = (float *)((longlong)plVar12 + 0x54);
              }
              else {
LAB_1800433d8:
                iVar17 = (int)plVar12[10];
                if (iVar17 == 0) {
                  pfVar11 = (float *)((longlong)plVar12 + 0x54);
                }
                else {
                  uVar5 = uVar18 & ((int)uVar18 >> 0x1f ^ 0xffffffffU);
                  if (iVar17 <= (int)uVar5) {
                    uVar5 = iVar17 - 1;
                  }
                  pfVar11 = (float *)(lVar14 + (longlong)(int)uVar5 * 4);
                }
              }
              uVar18 = uVar18 - 1;
              if (*pfVar11 != 0.0) {
                bVar4 = true;
              }
            } while (iVar23 < (int)uVar18);
            if (bVar4) goto LAB_180043480;
          }
        }
        uVar5 = FUN_180026550((longlong)local_160);
        uVar18 = 0x3b;
        if (uVar5 < 0x3c) {
          uVar18 = uVar5;
        }
        plVar12 = FUN_1800076d0((longlong *)(param_3 + 0x448),uVar18);
        puVar13 = (undefined4 *)FUN_180005d08(plVar12,*(int *)(param_3 + 900));
        uVar29 = *puVar13;
        plVar12 = local_168;
      }
      puVar13 = (undefined4 *)FUN_18000f448((longlong)plVar12,*(int *)(param_3 + 900));
      *puVar13 = uVar29;
    }
  }
LAB_180043480:
  uVar18 = *(uint *)(param_3 + 900);
  lVar14 = *(longlong *)(local_1a8 + 0x30);
  uVar5 = uVar18;
  uVar20 = uVar18;
  if (lVar14 == 0) {
    if (*(code **)(local_1a8 + 0x40) != (code *)0x0) {
      (**(code **)(local_1a8 + 0x40))(*(undefined4 *)(local_1a8 + 0x48));
      uVar20 = *(uint *)(param_3 + 900);
    }
    lVar14 = *(longlong *)(local_1a8 + 0x30);
    if (lVar14 != 0) {
      uVar5 = *(uint *)(local_118 + 900);
      goto LAB_1800434e4;
    }
    pfVar11 = (float *)(local_1a8 + 0x54);
    iVar23 = uVar20 - 1;
    uVar5 = *(uint *)(local_118 + 900);
    plVar12 = (longlong *)(local_b8 + 0x30);
LAB_180043524:
    if ((code *)plVar12[2] != (code *)0x0) {
      (*(code *)plVar12[2])((int)plVar12[3]);
      uVar5 = *(uint *)(local_118 + 900);
    }
    lVar14 = *plVar12;
    if (lVar14 != 0) goto LAB_18004354c;
    pfVar16 = (float *)((longlong)plVar12 + 0x24);
  }
  else {
LAB_1800434e4:
    iVar23 = *(int *)(local_1a8 + 0x50);
    if (iVar23 == 0) {
      pfVar11 = (float *)(local_1a8 + 0x54);
    }
    else {
      uVar18 = uVar18 & ((int)uVar18 >> 0x1f ^ 0xffffffffU);
      if (iVar23 <= (int)uVar18) {
        uVar18 = iVar23 - 1;
      }
      pfVar11 = (float *)(lVar14 + (longlong)(int)uVar18 * 4);
    }
    iVar23 = uVar20 - 1;
    plVar12 = (longlong *)(local_1a8 + 0x30);
    if (lVar14 == 0) goto LAB_180043524;
LAB_18004354c:
    iVar17 = (int)plVar12[4];
    if (iVar17 == 0) {
      pfVar16 = (float *)((longlong)plVar12 + 0x24);
    }
    else {
      if (iVar23 < 0) {
        iVar23 = 0;
      }
      if (iVar17 <= iVar23) {
        iVar23 = iVar17 + -1;
      }
      pfVar16 = (float *)(lVar14 + (longlong)iVar23 * 4);
    }
  }
  if (*pfVar11 <= *pfVar16) {
    uVar29 = *(undefined4 *)(local_120 + 0x1c);
    lVar14 = *(longlong *)(local_1a8 + 0x58);
    if (lVar14 == 0) {
      if (*(code **)(local_1a8 + 0x68) != (code *)0x0) {
        (**(code **)(local_1a8 + 0x68))(*(undefined4 *)(local_1a8 + 0x70));
      }
      lVar14 = *(longlong *)(local_1a8 + 0x58);
      if (lVar14 != 0) goto LAB_18004362c;
      puVar13 = (undefined4 *)(local_1a8 + 0x7c);
    }
    else {
LAB_18004362c:
      iVar23 = *(int *)(local_1a8 + 0x78);
      if (iVar23 == 0) {
        puVar13 = (undefined4 *)(local_1a8 + 0x7c);
      }
      else {
        uVar5 = uVar5 & ((int)uVar5 >> 0x1f ^ 0xffffffffU);
        if (iVar23 <= (int)uVar5) {
          uVar5 = iVar23 - 1;
        }
        puVar13 = (undefined4 *)(lVar14 + (longlong)(int)uVar5 * 4);
      }
    }
    *puVar13 = uVar29;
  }
  else {
    uVar29 = *(undefined4 *)(local_120 + 0x18);
    uVar18 = *(uint *)(local_100 + 900);
    lVar14 = *(longlong *)(local_1a8 + 0x58);
    if (lVar14 == 0) {
      if (*(code **)(local_1a8 + 0x68) != (code *)0x0) {
        (**(code **)(local_1a8 + 0x68))(*(undefined4 *)(local_1a8 + 0x70));
      }
      lVar14 = *(longlong *)(local_1a8 + 0x58);
      if (lVar14 == 0) {
        *(undefined4 *)(local_1a8 + 0x7c) = uVar29;
        goto LAB_180043658;
      }
    }
    iVar23 = *(int *)(local_1a8 + 0x78);
    if (iVar23 == 0) {
      *(undefined4 *)(local_1a8 + 0x7c) = uVar29;
    }
    else {
      uVar18 = uVar18 & ((int)uVar18 >> 0x1f ^ 0xffffffffU);
      if (iVar23 <= (int)uVar18) {
        uVar18 = iVar23 - 1;
      }
      *(undefined4 *)(lVar14 + (longlong)(int)uVar18 * 4) = uVar29;
    }
  }
LAB_180043658:
  uVar18 = *(uint *)(param_3 + 900);
  if ((*(longlong *)(lVar15 + 0x30) == 0) && (*(code **)(lVar15 + 0x40) != (code *)0x0)) {
    (**(code **)(lVar15 + 0x40))(*(undefined4 *)(lVar15 + 0x48));
    uVar18 = *(uint *)(local_118 + 900);
  }
  uVar9 = FUN_180026708(local_138);
  if (*extraout_x11_01 <= (float)(int)uVar9) {
    uVar29 = *(undefined4 *)(local_110 + 0x1c);
    lVar14 = *(longlong *)(lVar15 + 0x58);
    if (lVar14 == 0) {
      if (*(code **)(lVar15 + 0x68) != (code *)0x0) {
        (**(code **)(lVar15 + 0x68))(*(undefined4 *)(lVar15 + 0x70));
      }
      lVar14 = *(longlong *)(lVar15 + 0x58);
      if (lVar14 == 0) {
        puVar13 = (undefined4 *)(lVar15 + 0x7c);
        goto LAB_18004375c;
      }
    }
    iVar23 = *(int *)(lVar15 + 0x78);
    if (iVar23 != 0) goto LAB_180043748;
    puVar13 = (undefined4 *)(lVar15 + 0x7c);
  }
  else {
    uVar29 = *(undefined4 *)(local_110 + 0x18);
    lVar14 = *(longlong *)(lVar15 + 0x58);
    if (lVar14 == 0) {
      if (*(code **)(lVar15 + 0x68) != (code *)0x0) {
        (**(code **)(lVar15 + 0x68))(*(undefined4 *)(lVar15 + 0x70));
      }
      lVar14 = *(longlong *)(lVar15 + 0x58);
      if (lVar14 == 0) {
        puVar13 = (undefined4 *)(lVar15 + 0x7c);
        goto LAB_18004375c;
      }
    }
    iVar23 = *(int *)(lVar15 + 0x78);
    if (iVar23 == 0) {
      puVar13 = (undefined4 *)(lVar15 + 0x7c);
    }
    else {
LAB_180043748:
      uVar18 = uVar18 & ((int)uVar18 >> 0x1f ^ 0xffffffffU);
      if (iVar23 <= (int)uVar18) {
        uVar18 = iVar23 - 1;
      }
      puVar13 = (undefined4 *)(lVar14 + (longlong)(int)uVar18 * 4);
    }
  }
LAB_18004375c:
  *puVar13 = uVar29;
  uVar18 = *(uint *)(param_3 + 900);
  lVar15 = *(longlong *)(lVar19 + 0x30);
  uVar5 = uVar18;
  if (lVar15 == 0) {
    if (*(code **)(lVar19 + 0x40) != (code *)0x0) {
      (**(code **)(lVar19 + 0x40))(*(undefined4 *)(lVar19 + 0x48));
      uVar5 = *(uint *)(param_3 + 900);
    }
    lVar15 = *(longlong *)(lVar19 + 0x30);
    if (lVar15 != 0) goto LAB_1800437ac;
    pfVar11 = (float *)(lVar19 + 0x54);
    iVar23 = uVar5 - 1;
LAB_1800437e8:
    if (*(code **)(lVar19 + 0x40) != (code *)0x0) {
      (**(code **)(lVar19 + 0x40))(*(undefined4 *)(lVar19 + 0x48));
    }
    lVar15 = *(longlong *)(lVar19 + 0x30);
    if (lVar15 != 0) goto LAB_180043808;
    pfVar16 = (float *)(lVar19 + 0x54);
  }
  else {
LAB_1800437ac:
    iVar23 = *(int *)(lVar19 + 0x50);
    if (iVar23 == 0) {
      pfVar11 = (float *)(lVar19 + 0x54);
    }
    else {
      uVar18 = uVar18 & ((int)uVar18 >> 0x1f ^ 0xffffffffU);
      if (iVar23 <= (int)uVar18) {
        uVar18 = iVar23 - 1;
      }
      pfVar11 = (float *)(lVar15 + (longlong)(int)uVar18 * 4);
    }
    iVar23 = uVar5 - 1;
    if (lVar15 == 0) goto LAB_1800437e8;
LAB_180043808:
    iVar17 = *(int *)(lVar19 + 0x50);
    if (iVar17 == 0) {
      pfVar16 = (float *)(lVar19 + 0x54);
    }
    else {
      if (iVar23 < 0) {
        iVar23 = 0;
      }
      if (iVar17 <= iVar23) {
        iVar23 = iVar17 + -1;
      }
      pfVar16 = (float *)(lVar15 + (longlong)iVar23 * 4);
    }
  }
  uVar18 = *(uint *)(local_100 + 900);
  lVar15 = *(longlong *)(lVar19 + 0x58);
  if (*pfVar16 <= *pfVar11) {
    uVar29 = *(undefined4 *)(lVar19 + 0x1c);
    if (lVar15 == 0) {
      if (*(code **)(lVar19 + 0x68) != (code *)0x0) {
        (**(code **)(lVar19 + 0x68))(*(undefined4 *)(lVar19 + 0x70));
      }
      lVar15 = *(longlong *)(lVar19 + 0x58);
      if (lVar15 != 0) goto LAB_1800438bc;
    }
    else {
LAB_1800438bc:
      iVar23 = *(int *)(lVar19 + 0x78);
      if (iVar23 != 0) goto LAB_1800438c8;
    }
    puVar13 = (undefined4 *)(lVar19 + 0x7c);
  }
  else {
    uVar29 = *(undefined4 *)(lVar19 + 0x18);
    if (lVar15 == 0) {
      if (*(code **)(lVar19 + 0x68) != (code *)0x0) {
        (**(code **)(lVar19 + 0x68))(*(undefined4 *)(lVar19 + 0x70));
      }
      lVar15 = *(longlong *)(lVar19 + 0x58);
      if (lVar15 == 0) {
        puVar13 = (undefined4 *)(lVar19 + 0x7c);
        goto LAB_1800438dc;
      }
    }
    iVar23 = *(int *)(lVar19 + 0x78);
    if (iVar23 == 0) {
      puVar13 = (undefined4 *)(lVar19 + 0x7c);
      goto LAB_1800438dc;
    }
LAB_1800438c8:
    uVar18 = uVar18 & ((int)uVar18 >> 0x1f ^ 0xffffffffU);
    if (iVar23 <= (int)uVar18) {
      uVar18 = iVar23 - 1;
    }
    puVar13 = (undefined4 *)(lVar15 + (longlong)(int)uVar18 * 4);
  }
LAB_1800438dc:
  *puVar13 = uVar29;
  return;
}

