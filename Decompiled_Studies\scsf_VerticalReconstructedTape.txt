
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_VerticalReconstructedTape
               (undefined1 param_1 [16],undefined8 param_2,longlong param_3,undefined8 param_4,
               undefined8 param_5,undefined8 param_6,undefined8 param_7,undefined8 param_8,
               undefined8 param_9,undefined8 param_10)

{
  int iVar1;
  char cVar2;
  undefined1 auVar3 [16];
  undefined1 auVar4 [16];
  undefined1 auVar5 [16];
  longlong **pplVar6;
  bool bVar7;
  uint uVar8;
  undefined4 uVar9;
  undefined4 uVar10;
  int *piVar11;
  char *pcVar12;
  HANDLE pvVar13;
  undefined8 uVar14;
  longlong *plVar15;
  float *pfVar16;
  ulonglong uVar17;
  undefined4 *puVar18;
  float *pfVar19;
  int extraout_w1;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  undefined8 extraout_x1_10;
  undefined8 extraout_x1_11;
  undefined8 extraout_x1_12;
  undefined8 extraout_x1_13;
  undefined8 extraout_x1_14;
  undefined8 extraout_x1_15;
  undefined8 extraout_x1_16;
  undefined8 extraout_x1_17;
  undefined8 extraout_x1_18;
  undefined8 extraout_x1_19;
  undefined8 extraout_x1_20;
  undefined8 extraout_x1_21;
  undefined8 extraout_x1_22;
  undefined8 extraout_x1_23;
  undefined8 extraout_x1_24;
  undefined8 extraout_x1_25;
  undefined8 extraout_x1_26;
  undefined8 extraout_x1_27;
  undefined8 extraout_x1_28;
  undefined8 extraout_x1_29;
  undefined8 extraout_x1_30;
  undefined8 extraout_x1_31;
  undefined8 extraout_x1_32;
  undefined8 extraout_x1_33;
  undefined8 extraout_x1_34;
  undefined8 extraout_x1_35;
  undefined8 extraout_x1_36;
  undefined8 extraout_x1_37;
  undefined8 extraout_x1_38;
  undefined8 extraout_x1_39;
  undefined8 extraout_x1_40;
  undefined8 extraout_x1_41;
  undefined8 extraout_x1_42;
  undefined8 extraout_x1_43;
  undefined8 extraout_x1_44;
  int iVar20;
  longlong lVar21;
  longlong *plVar22;
  longlong *plVar23;
  longlong lVar24;
  uint uVar25;
  uint uVar26;
  longlong lVar27;
  longlong *plVar28;
  longlong *plVar29;
  longlong *plVar30;
  longlong *plVar31;
  longlong lVar32;
  longlong *plVar33;
  longlong *plVar34;
  longlong *plVar35;
  longlong *plVar36;
  int iVar37;
  int extraout_w11;
  longlong extraout_x11;
  int iVar38;
  longlong *plVar39;
  undefined2 uVar40;
  int iVar41;
  longlong *plVar42;
  char *pcVar43;
  char *pcVar44;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  undefined4 extraout_s0_02;
  undefined4 extraout_s0_03;
  undefined4 extraout_s0_04;
  undefined4 extraout_s0_05;
  undefined4 extraout_s0_06;
  undefined4 extraout_s0_07;
  undefined4 extraout_s0_08;
  undefined4 extraout_s0_09;
  undefined4 extraout_s0_10;
  undefined4 extraout_s0_11;
  undefined4 extraout_s0_12;
  undefined4 extraout_s0_13;
  undefined4 extraout_s0_14;
  undefined4 extraout_s0_15;
  undefined4 extraout_s0_16;
  undefined4 extraout_s0_17;
  undefined4 extraout_s0_18;
  undefined4 extraout_s0_19;
  undefined4 extraout_s0_20;
  undefined4 extraout_s0_21;
  undefined4 extraout_s0_22;
  undefined4 extraout_s0_23;
  undefined4 extraout_s0_24;
  undefined4 extraout_s0_25;
  undefined4 extraout_s0_26;
  undefined4 extraout_s0_27;
  undefined4 extraout_s0_28;
  undefined4 extraout_s0_29;
  undefined4 extraout_s0_30;
  undefined4 extraout_s0_31;
  undefined4 extraout_s0_32;
  undefined4 extraout_s0_33;
  undefined4 extraout_s0_34;
  undefined4 extraout_s0_35;
  undefined4 extraout_s0_36;
  undefined4 extraout_s0_37;
  undefined4 extraout_s0_38;
  undefined4 extraout_s0_39;
  undefined4 extraout_s0_40;
  undefined4 extraout_s0_41;
  undefined4 extraout_s0_42;
  undefined4 extraout_s0_43;
  undefined4 extraout_s0_44;
  undefined4 extraout_s0_45;
  undefined4 extraout_s0_46;
  undefined4 extraout_s0_47;
  undefined4 extraout_s0_48;
  undefined4 extraout_s0_49;
  undefined4 extraout_s0_50;
  undefined4 extraout_s0_51;
  undefined4 extraout_s0_52;
  undefined4 extraout_s0_53;
  undefined4 extraout_s0_54;
  undefined4 extraout_s0_55;
  undefined4 extraout_s0_56;
  undefined4 extraout_s0_57;
  undefined4 extraout_s0_58;
  undefined4 extraout_s0_59;
  undefined4 extraout_var;
  undefined4 extraout_var_00;
  undefined4 extraout_var_01;
  undefined4 extraout_var_02;
  undefined4 extraout_var_03;
  undefined4 extraout_var_04;
  undefined4 extraout_var_05;
  undefined4 extraout_var_06;
  undefined4 extraout_var_07;
  undefined4 extraout_var_08;
  undefined4 extraout_var_09;
  undefined4 extraout_var_10;
  undefined4 extraout_var_11;
  undefined4 extraout_var_12;
  undefined4 extraout_var_13;
  undefined4 extraout_var_14;
  undefined4 extraout_var_15;
  undefined4 extraout_var_16;
  undefined4 extraout_var_17;
  undefined4 extraout_var_18;
  undefined4 extraout_var_19;
  undefined4 extraout_var_20;
  undefined4 extraout_var_21;
  undefined4 extraout_var_22;
  undefined4 extraout_var_23;
  undefined4 extraout_var_24;
  undefined4 extraout_var_25;
  undefined4 extraout_var_26;
  undefined4 extraout_var_27;
  undefined4 extraout_var_28;
  undefined4 extraout_var_29;
  undefined4 extraout_var_30;
  undefined4 extraout_var_31;
  undefined4 extraout_var_32;
  undefined4 extraout_var_33;
  undefined4 extraout_var_34;
  undefined4 extraout_var_35;
  undefined4 extraout_var_36;
  undefined4 extraout_var_37;
  undefined4 extraout_var_38;
  undefined4 extraout_var_39;
  undefined4 extraout_var_40;
  undefined4 extraout_var_41;
  undefined4 extraout_var_42;
  undefined4 extraout_var_43;
  undefined4 extraout_var_44;
  undefined4 extraout_var_45;
  undefined4 extraout_var_46;
  undefined4 extraout_var_47;
  undefined4 extraout_var_48;
  undefined4 extraout_var_49;
  undefined4 extraout_var_50;
  undefined4 extraout_var_51;
  undefined4 extraout_var_52;
  undefined4 extraout_var_53;
  undefined4 extraout_var_54;
  undefined4 extraout_var_55;
  undefined4 extraout_var_56;
  undefined4 extraout_var_57;
  undefined4 extraout_var_58;
  undefined4 extraout_var_59;
  undefined8 extraout_var_60;
  undefined8 extraout_var_61;
  undefined8 extraout_var_62;
  undefined8 extraout_var_63;
  undefined8 extraout_var_64;
  undefined8 extraout_var_65;
  undefined8 extraout_var_66;
  undefined8 extraout_var_67;
  undefined8 extraout_var_68;
  undefined8 extraout_var_69;
  undefined8 extraout_var_70;
  undefined8 extraout_var_71;
  undefined8 extraout_var_72;
  undefined8 extraout_var_73;
  undefined8 extraout_var_74;
  undefined8 extraout_var_75;
  undefined8 extraout_var_76;
  undefined8 extraout_var_77;
  undefined8 extraout_var_78;
  undefined8 extraout_var_79;
  undefined8 extraout_var_80;
  undefined8 extraout_var_81;
  undefined8 extraout_var_82;
  undefined8 extraout_var_83;
  undefined8 extraout_var_84;
  undefined8 extraout_var_85;
  undefined8 extraout_var_86;
  undefined8 extraout_var_87;
  undefined8 extraout_var_88;
  undefined8 extraout_var_89;
  undefined8 extraout_var_90;
  undefined8 extraout_var_91;
  undefined8 extraout_var_92;
  undefined8 extraout_var_93;
  undefined8 extraout_var_94;
  undefined8 extraout_var_95;
  undefined8 extraout_var_96;
  undefined8 extraout_var_97;
  undefined8 extraout_var_98;
  undefined8 extraout_var_99;
  undefined8 extraout_var_x00100;
  undefined8 extraout_var_x00101;
  undefined8 extraout_var_x00102;
  undefined8 extraout_var_x00103;
  undefined8 extraout_var_x00104;
  undefined8 extraout_var_x00105;
  undefined8 uVar45;
  undefined8 extraout_var_x00106;
  undefined8 extraout_var_x00107;
  undefined8 extraout_var_x00108;
  undefined8 extraout_var_x00109;
  undefined8 extraout_var_x00110;
  undefined8 extraout_var_x00111;
  undefined8 extraout_var_x00112;
  undefined8 extraout_var_x00113;
  undefined8 extraout_var_x00114;
  undefined8 extraout_var_x00115;
  undefined8 extraout_var_x00116;
  undefined8 extraout_var_x00117;
  undefined8 extraout_var_x00118;
  undefined8 extraout_var_x00119;
  undefined8 extraout_var_x00120;
  float fVar46;
  float fVar47;
  float fVar48;
  float fVar49;
  float fVar50;
  float extraout_s18;
  char **local_228;
  longlong *local_220;
  longlong *local_218;
  longlong *local_210;
  longlong *local_200;
  longlong *local_1e8;
  longlong *local_1e0;
  longlong *local_1d8;
  longlong *local_1d0;
  longlong *local_1c8;
  longlong *local_1b8;
  longlong *local_190;
  longlong *local_180;
  longlong *local_178;
  longlong *local_168;
  longlong *local_128;
  longlong *local_120;
  longlong *local_118;
  longlong *local_110;
  undefined4 *local_108;
  undefined8 local_100;
  char *local_f0;
  undefined8 uStack_e8;
  undefined1 *local_e0;
  char *local_d0;
  undefined8 uStack_c8;
  undefined1 *local_c0;
  
                    /* 0x90140  27  scsf_VerticalReconstructedTape */
  local_100 = 0xfffffffffffffffe;
  piVar11 = (int *)(**(code **)(param_3 + 0x18b8))(param_1._0_4_,0);
  local_108 = (undefined4 *)(**(code **)(param_3 + 0x18b8))(1);
  (**(code **)(param_3 + 0x18c0))(2);
  lVar21 = *(longlong *)(param_3 + 0x500);
  uVar14 = extraout_x1;
  uVar9 = extraout_s0;
  uVar10 = extraout_var;
  uVar45 = extraout_var_60;
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar14 = extraout_x1_00;
      uVar9 = extraout_s0_00;
      uVar10 = extraout_var_00;
      uVar45 = extraout_var_61;
    }
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 != 0) goto LAB_1800901e0;
    lVar24 = param_3 + 0x528;
  }
  else {
LAB_1800901e0:
    iVar38 = *(int *)(param_3 + 0x520);
    if (iVar38 == 0) {
      lVar24 = param_3 + 0x528;
    }
    else {
      iVar41 = 0;
      if (iVar38 < 1) {
        iVar41 = iVar38 + -1;
      }
      lVar24 = lVar21 + (longlong)iVar41 * 0x170;
    }
  }
  lVar27 = *(longlong *)(lVar24 + 0x80);
  if (lVar27 == 0) {
    if (*(code **)(lVar24 + 0x90) != (code *)0x0) {
      (**(code **)(lVar24 + 0x90))(*(undefined4 *)(lVar24 + 0x98));
      lVar21 = *(longlong *)(param_3 + 0x500);
      uVar14 = extraout_x1_01;
      uVar9 = extraout_s0_01;
      uVar10 = extraout_var_01;
      uVar45 = extraout_var_62;
    }
    lVar27 = *(longlong *)(lVar24 + 0x80);
    if (lVar27 != 0) goto LAB_180090230;
    plVar28 = (longlong *)(lVar24 + 0xa8);
  }
  else {
LAB_180090230:
    iVar38 = *(int *)(lVar24 + 0xa0);
    if (iVar38 == 0) {
      plVar28 = (longlong *)(lVar24 + 0xa8);
    }
    else {
      iVar41 = 0;
      if (iVar38 < 1) {
        iVar41 = iVar38 + -1;
      }
      plVar28 = (longlong *)(lVar27 + (longlong)iVar41 * 0x28);
    }
  }
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar14 = extraout_x1_02;
      uVar9 = extraout_s0_02;
      uVar10 = extraout_var_02;
      uVar45 = extraout_var_63;
    }
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 != 0) goto LAB_18009027c;
    lVar24 = param_3 + 0x528;
  }
  else {
LAB_18009027c:
    iVar38 = *(int *)(param_3 + 0x520);
    if (iVar38 == 0) {
      lVar24 = param_3 + 0x528;
    }
    else {
      iVar41 = 0;
      if (iVar38 < 1) {
        iVar41 = iVar38 + -1;
      }
      lVar24 = lVar21 + (longlong)iVar41 * 0x170;
    }
  }
  lVar27 = *(longlong *)(lVar24 + 0x80);
  if (lVar27 == 0) {
    if (*(code **)(lVar24 + 0x90) != (code *)0x0) {
      (**(code **)(lVar24 + 0x90))(*(undefined4 *)(lVar24 + 0x98));
      lVar21 = *(longlong *)(param_3 + 0x500);
      uVar14 = extraout_x1_03;
      uVar9 = extraout_s0_03;
      uVar10 = extraout_var_03;
      uVar45 = extraout_var_64;
    }
    lVar27 = *(longlong *)(lVar24 + 0x80);
    if (lVar27 != 0) goto LAB_1800902cc;
    plVar42 = (longlong *)(lVar24 + 0xa8);
  }
  else {
LAB_1800902cc:
    iVar38 = *(int *)(lVar24 + 0xa0);
    if (iVar38 == 0) {
      plVar42 = (longlong *)(lVar24 + 0xa8);
    }
    else {
      iVar41 = 1;
      if (iVar38 < 2) {
        iVar41 = iVar38 + -1;
      }
      plVar42 = (longlong *)(lVar27 + (longlong)iVar41 * 0x28);
    }
  }
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar14 = extraout_x1_04;
      uVar9 = extraout_s0_04;
      uVar10 = extraout_var_04;
      uVar45 = extraout_var_65;
    }
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 != 0) goto LAB_18009031c;
    lVar21 = param_3 + 0x528;
  }
  else {
LAB_18009031c:
    iVar38 = *(int *)(param_3 + 0x520);
    if (iVar38 == 0) {
      lVar21 = param_3 + 0x528;
    }
    else {
      iVar41 = 0;
      if (iVar38 < 1) {
        iVar41 = iVar38 + -1;
      }
      lVar21 = lVar21 + (longlong)iVar41 * 0x170;
    }
  }
  if ((*(longlong *)(lVar21 + 0x80) == 0) && (*(code **)(lVar21 + 0x90) != (code *)0x0)) {
    (**(code **)(lVar21 + 0x90))(*(undefined4 *)(lVar21 + 0x98));
    uVar14 = extraout_x1_05;
    uVar9 = extraout_s0_05;
    uVar10 = extraout_var_05;
    uVar45 = extraout_var_66;
  }
  lVar21 = *(longlong *)(param_3 + 0x500);
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar14 = extraout_x1_06;
      uVar9 = extraout_s0_06;
      uVar10 = extraout_var_06;
      uVar45 = extraout_var_67;
    }
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 != 0) goto LAB_180090380;
    lVar24 = param_3 + 0x528;
  }
  else {
LAB_180090380:
    iVar38 = *(int *)(param_3 + 0x520);
    if (iVar38 == 0) {
      lVar24 = param_3 + 0x528;
    }
    else {
      iVar41 = 0;
      if (iVar38 < 1) {
        iVar41 = iVar38 + -1;
      }
      lVar24 = lVar21 + (longlong)iVar41 * 0x170;
    }
  }
  lVar27 = *(longlong *)(lVar24 + 0x80);
  if (lVar27 == 0) {
    if (*(code **)(lVar24 + 0x90) != (code *)0x0) {
      (**(code **)(lVar24 + 0x90))(*(undefined4 *)(lVar24 + 0x98));
      lVar21 = *(longlong *)(param_3 + 0x500);
      uVar14 = extraout_x1_07;
      uVar9 = extraout_s0_07;
      uVar10 = extraout_var_07;
      uVar45 = extraout_var_68;
    }
    lVar27 = *(longlong *)(lVar24 + 0x80);
    if (lVar27 != 0) goto LAB_1800903d0;
    plVar29 = (longlong *)(lVar24 + 0xa8);
  }
  else {
LAB_1800903d0:
    iVar38 = *(int *)(lVar24 + 0xa0);
    if (iVar38 == 0) {
      plVar29 = (longlong *)(lVar24 + 0xa8);
    }
    else {
      iVar41 = 3;
      if (iVar38 < 4) {
        iVar41 = iVar38 + -1;
      }
      plVar29 = (longlong *)(lVar27 + (longlong)iVar41 * 0x28);
    }
  }
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar14 = extraout_x1_08;
      uVar9 = extraout_s0_08;
      uVar10 = extraout_var_08;
      uVar45 = extraout_var_69;
    }
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 != 0) goto LAB_180090420;
    lVar24 = param_3 + 0x528;
  }
  else {
LAB_180090420:
    iVar38 = *(int *)(param_3 + 0x520);
    if (iVar38 == 0) {
      lVar24 = param_3 + 0x528;
    }
    else {
      iVar41 = 0;
      if (iVar38 < 1) {
        iVar41 = iVar38 + -1;
      }
      lVar24 = lVar21 + (longlong)iVar41 * 0x170;
    }
  }
  lVar27 = *(longlong *)(lVar24 + 0x80);
  if (lVar27 == 0) {
    if (*(code **)(lVar24 + 0x90) != (code *)0x0) {
      (**(code **)(lVar24 + 0x90))(*(undefined4 *)(lVar24 + 0x98));
      lVar21 = *(longlong *)(param_3 + 0x500);
      uVar14 = extraout_x1_09;
      uVar9 = extraout_s0_09;
      uVar10 = extraout_var_09;
      uVar45 = extraout_var_70;
    }
    lVar27 = *(longlong *)(lVar24 + 0x80);
    if (lVar27 != 0) goto LAB_180090470;
    plVar39 = (longlong *)(lVar24 + 0xa8);
  }
  else {
LAB_180090470:
    iVar38 = *(int *)(lVar24 + 0xa0);
    if (iVar38 == 0) {
      plVar39 = (longlong *)(lVar24 + 0xa8);
    }
    else {
      iVar41 = 4;
      if (iVar38 < 5) {
        iVar41 = iVar38 + -1;
      }
      plVar39 = (longlong *)(lVar27 + (longlong)iVar41 * 0x28);
    }
  }
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar14 = extraout_x1_10;
      uVar9 = extraout_s0_10;
      uVar10 = extraout_var_10;
      uVar45 = extraout_var_71;
    }
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 != 0) goto LAB_1800904c0;
    lVar24 = param_3 + 0x528;
  }
  else {
LAB_1800904c0:
    iVar38 = *(int *)(param_3 + 0x520);
    if (iVar38 == 0) {
      lVar24 = param_3 + 0x528;
    }
    else {
      iVar41 = 0;
      if (iVar38 < 1) {
        iVar41 = iVar38 + -1;
      }
      lVar24 = lVar21 + (longlong)iVar41 * 0x170;
    }
  }
  lVar27 = *(longlong *)(lVar24 + 0x80);
  if (lVar27 == 0) {
    if (*(code **)(lVar24 + 0x90) != (code *)0x0) {
      (**(code **)(lVar24 + 0x90))(*(undefined4 *)(lVar24 + 0x98));
      lVar21 = *(longlong *)(param_3 + 0x500);
      uVar14 = extraout_x1_11;
      uVar9 = extraout_s0_11;
      uVar10 = extraout_var_11;
      uVar45 = extraout_var_72;
    }
    lVar27 = *(longlong *)(lVar24 + 0x80);
    if (lVar27 != 0) goto LAB_180090510;
    plVar30 = (longlong *)(lVar24 + 0xa8);
  }
  else {
LAB_180090510:
    iVar38 = *(int *)(lVar24 + 0xa0);
    if (iVar38 == 0) {
      plVar30 = (longlong *)(lVar24 + 0xa8);
    }
    else {
      iVar41 = 5;
      if (iVar38 < 6) {
        iVar41 = iVar38 + -1;
      }
      plVar30 = (longlong *)(lVar27 + (longlong)iVar41 * 0x28);
    }
  }
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar14 = extraout_x1_12;
      uVar9 = extraout_s0_12;
      uVar10 = extraout_var_12;
      uVar45 = extraout_var_73;
    }
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 != 0) goto LAB_180090560;
    lVar24 = param_3 + 0x528;
  }
  else {
LAB_180090560:
    iVar38 = *(int *)(param_3 + 0x520);
    if (iVar38 == 0) {
      lVar24 = param_3 + 0x528;
    }
    else {
      iVar41 = 0;
      if (iVar38 < 1) {
        iVar41 = iVar38 + -1;
      }
      lVar24 = lVar21 + (longlong)iVar41 * 0x170;
    }
  }
  lVar27 = *(longlong *)(lVar24 + 0x80);
  if (lVar27 == 0) {
    if (*(code **)(lVar24 + 0x90) != (code *)0x0) {
      (**(code **)(lVar24 + 0x90))(*(undefined4 *)(lVar24 + 0x98));
      lVar21 = *(longlong *)(param_3 + 0x500);
      uVar14 = extraout_x1_13;
      uVar9 = extraout_s0_13;
      uVar10 = extraout_var_13;
      uVar45 = extraout_var_74;
    }
    lVar27 = *(longlong *)(lVar24 + 0x80);
    if (lVar27 != 0) goto LAB_1800905b0;
    plVar31 = (longlong *)(lVar24 + 0xa8);
  }
  else {
LAB_1800905b0:
    iVar38 = *(int *)(lVar24 + 0xa0);
    if (iVar38 == 0) {
      plVar31 = (longlong *)(lVar24 + 0xa8);
    }
    else {
      iVar41 = 6;
      if (iVar38 < 7) {
        iVar41 = iVar38 + -1;
      }
      plVar31 = (longlong *)(lVar27 + (longlong)iVar41 * 0x28);
    }
  }
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar14 = extraout_x1_14;
      uVar9 = extraout_s0_14;
      uVar10 = extraout_var_14;
      uVar45 = extraout_var_75;
    }
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 != 0) goto LAB_180090600;
    lVar24 = param_3 + 0x528;
  }
  else {
LAB_180090600:
    iVar38 = *(int *)(param_3 + 0x520);
    if (iVar38 == 0) {
      lVar24 = param_3 + 0x528;
    }
    else {
      iVar41 = 0;
      if (iVar38 < 1) {
        iVar41 = iVar38 + -1;
      }
      lVar24 = lVar21 + (longlong)iVar41 * 0x170;
    }
  }
  lVar27 = *(longlong *)(lVar24 + 0x80);
  if (lVar27 == 0) {
    if (*(code **)(lVar24 + 0x90) != (code *)0x0) {
      (**(code **)(lVar24 + 0x90))(*(undefined4 *)(lVar24 + 0x98));
      lVar21 = *(longlong *)(param_3 + 0x500);
      uVar14 = extraout_x1_15;
      uVar9 = extraout_s0_15;
      uVar10 = extraout_var_15;
      uVar45 = extraout_var_76;
    }
    lVar27 = *(longlong *)(lVar24 + 0x80);
    if (lVar27 != 0) goto LAB_180090650;
    lVar24 = lVar24 + 0xa8;
  }
  else {
LAB_180090650:
    iVar38 = *(int *)(lVar24 + 0xa0);
    if (iVar38 == 0) {
      lVar24 = lVar24 + 0xa8;
    }
    else {
      iVar41 = 7;
      if (iVar38 < 8) {
        iVar41 = iVar38 + -1;
      }
      lVar24 = lVar27 + (longlong)iVar41 * 0x28;
    }
  }
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar14 = extraout_x1_16;
      uVar9 = extraout_s0_16;
      uVar10 = extraout_var_16;
      uVar45 = extraout_var_77;
    }
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 != 0) goto LAB_1800906a0;
    lVar21 = param_3 + 0x528;
  }
  else {
LAB_1800906a0:
    iVar38 = *(int *)(param_3 + 0x520);
    if (iVar38 == 0) {
      lVar21 = param_3 + 0x528;
    }
    else {
      iVar41 = 0;
      if (iVar38 < 1) {
        iVar41 = iVar38 + -1;
      }
      lVar21 = lVar21 + (longlong)iVar41 * 0x170;
    }
  }
  if ((*(longlong *)(lVar21 + 0x80) == 0) && (*(code **)(lVar21 + 0x90) != (code *)0x0)) {
    (**(code **)(lVar21 + 0x90))(*(undefined4 *)(lVar21 + 0x98));
    uVar14 = extraout_x1_17;
    uVar9 = extraout_s0_17;
    uVar10 = extraout_var_17;
    uVar45 = extraout_var_78;
  }
  lVar21 = *(longlong *)(param_3 + 0x500);
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar14 = extraout_x1_18;
      uVar9 = extraout_s0_18;
      uVar10 = extraout_var_18;
      uVar45 = extraout_var_79;
    }
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 != 0) goto LAB_180090704;
    lVar27 = param_3 + 0x528;
  }
  else {
LAB_180090704:
    iVar38 = *(int *)(param_3 + 0x520);
    if (iVar38 == 0) {
      lVar27 = param_3 + 0x528;
    }
    else {
      iVar41 = 0;
      if (iVar38 < 1) {
        iVar41 = iVar38 + -1;
      }
      lVar27 = lVar21 + (longlong)iVar41 * 0x170;
    }
  }
  lVar32 = *(longlong *)(lVar27 + 0x80);
  if (lVar32 == 0) {
    if (*(code **)(lVar27 + 0x90) != (code *)0x0) {
      (**(code **)(lVar27 + 0x90))(*(undefined4 *)(lVar27 + 0x98));
      lVar21 = *(longlong *)(param_3 + 0x500);
      uVar14 = extraout_x1_19;
      uVar9 = extraout_s0_19;
      uVar10 = extraout_var_19;
      uVar45 = extraout_var_80;
    }
    lVar32 = *(longlong *)(lVar27 + 0x80);
    if (lVar32 != 0) goto LAB_180090754;
    plVar33 = (longlong *)(lVar27 + 0xa8);
  }
  else {
LAB_180090754:
    iVar38 = *(int *)(lVar27 + 0xa0);
    if (iVar38 == 0) {
      plVar33 = (longlong *)(lVar27 + 0xa8);
    }
    else {
      iVar41 = 9;
      if (iVar38 < 10) {
        iVar41 = iVar38 + -1;
      }
      plVar33 = (longlong *)(lVar32 + (longlong)iVar41 * 0x28);
    }
  }
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar14 = extraout_x1_20;
      uVar9 = extraout_s0_20;
      uVar10 = extraout_var_20;
      uVar45 = extraout_var_81;
    }
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 != 0) goto LAB_1800907a4;
    lVar27 = param_3 + 0x528;
  }
  else {
LAB_1800907a4:
    iVar38 = *(int *)(param_3 + 0x520);
    if (iVar38 == 0) {
      lVar27 = param_3 + 0x528;
    }
    else {
      iVar41 = 0;
      if (iVar38 < 1) {
        iVar41 = iVar38 + -1;
      }
      lVar27 = lVar21 + (longlong)iVar41 * 0x170;
    }
  }
  lVar32 = *(longlong *)(lVar27 + 0x80);
  if (lVar32 == 0) {
    if (*(code **)(lVar27 + 0x90) != (code *)0x0) {
      (**(code **)(lVar27 + 0x90))(*(undefined4 *)(lVar27 + 0x98));
      lVar21 = *(longlong *)(param_3 + 0x500);
      uVar14 = extraout_x1_21;
      uVar9 = extraout_s0_21;
      uVar10 = extraout_var_21;
      uVar45 = extraout_var_82;
    }
    lVar32 = *(longlong *)(lVar27 + 0x80);
    if (lVar32 != 0) goto LAB_1800907f4;
    plVar34 = (longlong *)(lVar27 + 0xa8);
  }
  else {
LAB_1800907f4:
    iVar38 = *(int *)(lVar27 + 0xa0);
    if (iVar38 == 0) {
      plVar34 = (longlong *)(lVar27 + 0xa8);
    }
    else {
      iVar41 = 10;
      if (iVar38 < 0xb) {
        iVar41 = iVar38 + -1;
      }
      plVar34 = (longlong *)(lVar32 + (longlong)iVar41 * 0x28);
    }
  }
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar14 = extraout_x1_22;
      uVar9 = extraout_s0_22;
      uVar10 = extraout_var_22;
      uVar45 = extraout_var_83;
    }
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 != 0) goto LAB_180090844;
    lVar27 = param_3 + 0x528;
  }
  else {
LAB_180090844:
    iVar38 = *(int *)(param_3 + 0x520);
    if (iVar38 == 0) {
      lVar27 = param_3 + 0x528;
    }
    else {
      iVar41 = 0;
      if (iVar38 < 1) {
        iVar41 = iVar38 + -1;
      }
      lVar27 = lVar21 + (longlong)iVar41 * 0x170;
    }
  }
  lVar32 = *(longlong *)(lVar27 + 0x80);
  if (lVar32 == 0) {
    if (*(code **)(lVar27 + 0x90) != (code *)0x0) {
      (**(code **)(lVar27 + 0x90))(*(undefined4 *)(lVar27 + 0x98));
      lVar21 = *(longlong *)(param_3 + 0x500);
      uVar14 = extraout_x1_23;
      uVar9 = extraout_s0_23;
      uVar10 = extraout_var_23;
      uVar45 = extraout_var_84;
    }
    lVar32 = *(longlong *)(lVar27 + 0x80);
    if (lVar32 != 0) goto LAB_180090894;
    plVar35 = (longlong *)(lVar27 + 0xa8);
  }
  else {
LAB_180090894:
    iVar38 = *(int *)(lVar27 + 0xa0);
    if (iVar38 == 0) {
      plVar35 = (longlong *)(lVar27 + 0xa8);
    }
    else {
      iVar41 = 0xb;
      if (iVar38 < 0xc) {
        iVar41 = iVar38 + -1;
      }
      plVar35 = (longlong *)(lVar32 + (longlong)iVar41 * 0x28);
    }
  }
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar14 = extraout_x1_24;
      uVar9 = extraout_s0_24;
      uVar10 = extraout_var_24;
      uVar45 = extraout_var_85;
    }
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 != 0) goto LAB_1800908e4;
    lVar27 = param_3 + 0x528;
  }
  else {
LAB_1800908e4:
    iVar38 = *(int *)(param_3 + 0x520);
    if (iVar38 == 0) {
      lVar27 = param_3 + 0x528;
    }
    else {
      iVar41 = 1;
      if (iVar38 < 2) {
        iVar41 = iVar38 + -1;
      }
      lVar27 = lVar21 + (longlong)iVar41 * 0x170;
    }
  }
  lVar32 = *(longlong *)(lVar27 + 0x80);
  if (lVar32 == 0) {
    if (*(code **)(lVar27 + 0x90) != (code *)0x0) {
      (**(code **)(lVar27 + 0x90))(*(undefined4 *)(lVar27 + 0x98));
      lVar21 = *(longlong *)(param_3 + 0x500);
      uVar14 = extraout_x1_25;
      uVar9 = extraout_s0_25;
      uVar10 = extraout_var_25;
      uVar45 = extraout_var_86;
    }
    lVar32 = *(longlong *)(lVar27 + 0x80);
    if (lVar32 != 0) goto LAB_180090938;
    plVar36 = (longlong *)(lVar27 + 0xa8);
  }
  else {
LAB_180090938:
    iVar38 = *(int *)(lVar27 + 0xa0);
    if (iVar38 == 0) {
      plVar36 = (longlong *)(lVar27 + 0xa8);
    }
    else {
      iVar41 = 0;
      if (iVar38 < 1) {
        iVar41 = iVar38 + -1;
      }
      plVar36 = (longlong *)(lVar32 + (longlong)iVar41 * 0x28);
    }
  }
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar14 = extraout_x1_26;
      uVar9 = extraout_s0_26;
      uVar10 = extraout_var_26;
      uVar45 = extraout_var_87;
    }
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 != 0) goto LAB_180090984;
    lVar21 = param_3 + 0x528;
  }
  else {
LAB_180090984:
    iVar38 = *(int *)(param_3 + 0x520);
    if (iVar38 == 0) {
      lVar21 = param_3 + 0x528;
    }
    else {
      iVar41 = 1;
      if (iVar38 < 2) {
        iVar41 = iVar38 + -1;
      }
      lVar21 = lVar21 + (longlong)iVar41 * 0x170;
    }
  }
  lVar27 = *(longlong *)(lVar21 + 0x80);
  if (lVar27 == 0) {
    if (*(code **)(lVar21 + 0x90) != (code *)0x0) {
      (**(code **)(lVar21 + 0x90))(*(undefined4 *)(lVar21 + 0x98));
      uVar14 = extraout_x1_27;
      uVar9 = extraout_s0_27;
      uVar10 = extraout_var_27;
      uVar45 = extraout_var_88;
    }
    lVar27 = *(longlong *)(lVar21 + 0x80);
    if (lVar27 != 0) goto LAB_1800909d4;
    plVar22 = (longlong *)(lVar21 + 0xa8);
  }
  else {
LAB_1800909d4:
    iVar38 = *(int *)(lVar21 + 0xa0);
    if (iVar38 == 0) {
      plVar22 = (longlong *)(lVar21 + 0xa8);
    }
    else {
      iVar41 = 1;
      if (iVar38 < 2) {
        iVar41 = iVar38 + -1;
      }
      plVar22 = (longlong *)(lVar27 + (longlong)iVar41 * 0x28);
    }
  }
  lVar21 = *(longlong *)(param_3 + 0x210);
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_28;
      uVar9 = extraout_s0_28;
      uVar10 = extraout_var_28;
      uVar45 = extraout_var_89;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180090a3c;
    local_1b8 = (longlong *)(param_3 + 0x238);
LAB_180090a70:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_29;
      uVar9 = extraout_s0_29;
      uVar10 = extraout_var_29;
      uVar45 = extraout_var_90;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180090aa0;
    local_220 = (longlong *)(param_3 + 0x238);
LAB_180090ad8:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_30;
      uVar9 = extraout_s0_30;
      uVar10 = extraout_var_30;
      uVar45 = extraout_var_91;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180090b08;
    local_218 = (longlong *)(param_3 + 0x238);
LAB_180090b40:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_31;
      uVar9 = extraout_s0_31;
      uVar10 = extraout_var_31;
      uVar45 = extraout_var_92;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180090b70;
    local_210 = (longlong *)(param_3 + 0x238);
LAB_180090ba8:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_32;
      uVar9 = extraout_s0_32;
      uVar10 = extraout_var_32;
      uVar45 = extraout_var_93;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180090bdc;
    local_128 = (longlong *)(param_3 + 0x238);
LAB_180090c18:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_33;
      uVar9 = extraout_s0_33;
      uVar10 = extraout_var_33;
      uVar45 = extraout_var_94;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180090c4c;
    local_118 = (longlong *)(param_3 + 0x238);
LAB_180090c88:
    local_200 = local_118;
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_34;
      uVar9 = extraout_s0_34;
      uVar10 = extraout_var_34;
      uVar45 = extraout_var_95;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180090cbc;
    local_168 = (longlong *)(param_3 + 0x238);
LAB_180090cf8:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_35;
      uVar9 = extraout_s0_35;
      uVar10 = extraout_var_35;
      uVar45 = extraout_var_96;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180090d2c;
    local_110 = (longlong *)(param_3 + 0x238);
LAB_180090d68:
    local_180 = local_110;
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_36;
      uVar9 = extraout_s0_36;
      uVar10 = extraout_var_36;
      uVar45 = extraout_var_97;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180090d9c;
    local_120 = (longlong *)(param_3 + 0x238);
LAB_180090dd8:
    local_178 = local_120;
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_37;
      uVar9 = extraout_s0_37;
      uVar10 = extraout_var_37;
      uVar45 = extraout_var_98;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180090e04;
    local_1e8 = (longlong *)(param_3 + 0x238);
LAB_180090e38:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_38;
      uVar9 = extraout_s0_38;
      uVar10 = extraout_var_38;
      uVar45 = extraout_var_99;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180090e68;
    local_1e0 = (longlong *)(param_3 + 0x238);
LAB_180090ea0:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_39;
      uVar9 = extraout_s0_39;
      uVar10 = extraout_var_39;
      uVar45 = extraout_var_x00100;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180090ecc;
    plVar15 = (longlong *)(param_3 + 0x238);
LAB_180090f00:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_40;
      uVar9 = extraout_s0_40;
      uVar10 = extraout_var_40;
      uVar45 = extraout_var_x00101;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180090f2c;
    local_190 = (longlong *)(param_3 + 0x238);
LAB_180090f60:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_41;
      uVar9 = extraout_s0_41;
      uVar10 = extraout_var_41;
      uVar45 = extraout_var_x00102;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180090f90;
    local_1d8 = (longlong *)(param_3 + 0x238);
LAB_180090fc8:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_42;
      uVar9 = extraout_s0_42;
      uVar10 = extraout_var_42;
      uVar45 = extraout_var_x00103;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180090ff8;
    local_1d0 = (longlong *)(param_3 + 0x238);
LAB_180091030:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_43;
      uVar9 = extraout_s0_43;
      uVar10 = extraout_var_43;
      uVar45 = extraout_var_x00104;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_180091060;
    local_1c8 = (longlong *)(param_3 + 0x238);
LAB_180091098:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar14 = extraout_x1_44;
      uVar9 = extraout_s0_44;
      uVar10 = extraout_var_44;
      uVar45 = extraout_var_x00105;
    }
    lVar21 = *(longlong *)(param_3 + 0x210);
    if (lVar21 != 0) goto LAB_1800910b8;
    plVar23 = (longlong *)(param_3 + 0x238);
  }
  else {
LAB_180090a3c:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      local_1b8 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 0;
      if (iVar38 < 1) {
        iVar41 = iVar38 + -1;
      }
      local_1b8 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    if (lVar21 == 0) goto LAB_180090a70;
LAB_180090aa0:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      local_220 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 1;
      if (iVar38 < 2) {
        iVar41 = iVar38 + -1;
      }
      local_220 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    if (lVar21 == 0) goto LAB_180090ad8;
LAB_180090b08:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      local_218 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 2;
      if (iVar38 < 3) {
        iVar41 = iVar38 + -1;
      }
      local_218 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    if (lVar21 == 0) goto LAB_180090b40;
LAB_180090b70:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      local_210 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 3;
      if (iVar38 < 4) {
        iVar41 = iVar38 + -1;
      }
      local_210 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    if (lVar21 == 0) goto LAB_180090ba8;
LAB_180090bdc:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      local_128 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 4;
      if (iVar38 < 5) {
        iVar41 = iVar38 + -1;
      }
      local_128 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    if (lVar21 == 0) goto LAB_180090c18;
LAB_180090c4c:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      local_200 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 5;
      if (iVar38 < 6) {
        iVar41 = iVar38 + -1;
      }
      local_200 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    local_118 = local_200;
    if (lVar21 == 0) goto LAB_180090c88;
LAB_180090cbc:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      local_168 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 6;
      if (iVar38 < 7) {
        iVar41 = iVar38 + -1;
      }
      local_168 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    if (lVar21 == 0) goto LAB_180090cf8;
LAB_180090d2c:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      local_180 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 7;
      if (iVar38 < 8) {
        iVar41 = iVar38 + -1;
      }
      local_180 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    local_110 = local_180;
    if (lVar21 == 0) goto LAB_180090d68;
LAB_180090d9c:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      local_178 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 8;
      if (iVar38 < 9) {
        iVar41 = iVar38 + -1;
      }
      local_178 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    local_120 = local_178;
    if (lVar21 == 0) goto LAB_180090dd8;
LAB_180090e04:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      local_1e8 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 9;
      if (iVar38 < 10) {
        iVar41 = iVar38 + -1;
      }
      local_1e8 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    if (lVar21 == 0) goto LAB_180090e38;
LAB_180090e68:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      local_1e0 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 10;
      if (iVar38 < 0xb) {
        iVar41 = iVar38 + -1;
      }
      local_1e0 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    if (lVar21 == 0) goto LAB_180090ea0;
LAB_180090ecc:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      plVar15 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 0xb;
      if (iVar38 < 0xc) {
        iVar41 = iVar38 + -1;
      }
      plVar15 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    if (lVar21 == 0) goto LAB_180090f00;
LAB_180090f2c:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      local_190 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 0xc;
      if (iVar38 < 0xd) {
        iVar41 = iVar38 + -1;
      }
      local_190 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    if (lVar21 == 0) goto LAB_180090f60;
LAB_180090f90:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      local_1d8 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 0xd;
      if (iVar38 < 0xe) {
        iVar41 = iVar38 + -1;
      }
      local_1d8 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    if (lVar21 == 0) goto LAB_180090fc8;
LAB_180090ff8:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      local_1d0 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 0xe;
      if (iVar38 < 0xf) {
        iVar41 = iVar38 + -1;
      }
      local_1d0 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    if (lVar21 == 0) goto LAB_180091030;
LAB_180091060:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      local_1c8 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 0xf;
      if (iVar38 < 0x10) {
        iVar41 = iVar38 + -1;
      }
      local_1c8 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
    if (lVar21 == 0) goto LAB_180091098;
LAB_1800910b8:
    iVar38 = *(int *)(param_3 + 0x230);
    if (iVar38 == 0) {
      plVar23 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar41 = 0x10;
      if (iVar38 < 0x11) {
        iVar41 = iVar38 + -1;
      }
      plVar23 = (longlong *)(lVar21 + (longlong)iVar41 * 0x98);
    }
  }
  if (*(int *)(param_3 + 0xac) != 0) {
    FUN_1800079f8((longlong *)(param_3 + 0x118),0x1800db9c8,0x1b);
    pcVar44 = "";
    *(undefined4 *)(param_3 + 0x124) = 1;
    if ((*(char **)(param_3 + 0x338) != (char *)0x0) && (**(char **)(param_3 + 0x338) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0x338),0x1800d4ecd,0);
      *(undefined4 *)(param_3 + 0x344) = 1;
    }
    local_e0 = &DAT_1800d4ecd;
    local_f0 = "";
    uStack_e8 = 0;
    pcVar12 = FUN_180004620(0x15);
    if (pcVar12 == (char *)0x0) {
      local_f0 = "";
      pcVar43 = pcVar44;
    }
    else {
      param_6 = 0x14;
      local_f0 = pcVar12;
      FUN_180099d78(pcVar12,0x15,0x1800d6c40,0x14);
      uStack_e8 = 0x100000001;
      pcVar43 = pcVar12;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_f0);
    if ((pcVar12 != (char *)0x0) && (pcVar43 != (char *)0x0)) {
      pvVar13 = GetProcessHeap();
      HeapFree(pvVar13,0,pcVar43);
    }
    local_e0 = &DAT_1800d4ecd;
    local_f0 = "";
    uStack_e8 = 0;
    pcVar12 = FUN_180004620(0x8d);
    if (pcVar12 == (char *)0x0) {
      local_f0 = "";
      pcVar43 = pcVar44;
    }
    else {
      param_6 = 0x8c;
      local_f0 = pcVar12;
      FUN_180099d78(pcVar12,0x8d,0x1800d6bb0,0x8c);
      uStack_e8 = 0x100000001;
      pcVar43 = pcVar12;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_f0);
    uVar9 = extraout_s0_45;
    uVar10 = extraout_var_45;
    uVar14 = extraout_var_x00106;
    if ((pcVar12 != (char *)0x0) && (pcVar43 != (char *)0x0)) {
      pvVar13 = GetProcessHeap();
      HeapFree(pvVar13,0,pcVar43);
      local_f0 = (char *)0x0;
      uStack_e8 = 0;
      uVar9 = extraout_s0_46;
      uVar10 = extraout_var_46;
      uVar14 = extraout_var_x00107;
    }
    pcVar12 = *(char **)(param_3 + 0x118);
    if (*(char **)(param_3 + 0x118) == (char *)0x0) {
      pcVar12 = pcVar44;
    }
    auVar4._4_4_ = uVar10;
    auVar4._0_4_ = uVar9;
    auVar4._8_8_ = uVar14;
    FUN_180026368(auVar4,param_2,(undefined8 *)(param_3 + 0x338),0x1800d6c60,pcVar12,param_6,param_7
                  ,param_8,param_9,param_10);
    local_e0 = &DAT_1800d4ecd;
    local_f0 = "";
    uStack_e8 = 0;
    pcVar12 = FUN_180004620(7);
    if (pcVar12 == (char *)0x0) {
      local_f0 = "";
    }
    else {
      param_6 = 6;
      local_f0 = pcVar12;
      FUN_180099d78(pcVar12,7,0x1800d6c58,6);
      uStack_e8 = 0x100000001;
      pcVar44 = pcVar12;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_f0);
    uVar9 = extraout_s0_47;
    uVar10 = extraout_var_47;
    uVar14 = extraout_var_x00108;
    if ((pcVar12 != (char *)0x0) && (pcVar44 != (char *)0x0)) {
      pvVar13 = GetProcessHeap();
      HeapFree(pvVar13,0,pcVar44);
      local_f0 = (char *)0x0;
      uStack_e8 = 0;
      uVar9 = extraout_s0_48;
      uVar10 = extraout_var_48;
      uVar14 = extraout_var_x00109;
    }
    *(undefined4 *)(param_3 + 0x37c) = 1;
    *(undefined4 *)(param_3 + 0x38c) = 1;
    *(undefined4 *)(param_3 + 0xd28) = 1;
    *(undefined4 *)(param_3 + 0x330) = 0;
    *(undefined4 *)(param_3 + 0x10) = 0;
    *(undefined4 *)(param_3 + 4) = 0;
    iVar38 = 0;
    do {
      local_c0 = &DAT_1800d4ecd;
      local_d0 = "";
      uStack_c8 = 0;
      auVar5._4_4_ = uVar10;
      auVar5._0_4_ = uVar9;
      auVar5._8_8_ = uVar14;
      FUN_180006050(auVar5,param_2,&local_d0,0x1800db9b8,(ulonglong)(iVar38 + 100),param_6,param_7,
                    param_8,param_9,param_10);
      lVar21 = *(longlong *)(param_3 + 0x500);
      uVar9 = extraout_s0_49;
      uVar10 = extraout_var_49;
      uVar14 = extraout_var_x00110;
      if (lVar21 == 0) {
        if (*(code **)(param_3 + 0x510) != (code *)0x0) {
          (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
          uVar9 = extraout_s0_50;
          uVar10 = extraout_var_50;
          uVar14 = extraout_var_x00111;
        }
        lVar21 = *(longlong *)(param_3 + 0x500);
        if (lVar21 != 0) goto LAB_180091368;
        local_228 = (char **)(param_3 + 0x528);
      }
      else {
LAB_180091368:
        iVar41 = *(int *)(param_3 + 0x520);
        if (iVar41 == 0) {
          local_228 = (char **)(param_3 + 0x528);
        }
        else {
          iVar20 = iVar38;
          if (iVar41 <= iVar38) {
            iVar20 = iVar41 + -1;
          }
          local_228 = (char **)(lVar21 + (longlong)iVar20 * 0x170);
        }
      }
      pcVar44 = local_d0;
      if ((&local_d0 != local_228) &&
         (((local_d0 != (char *)0x0 && (*local_d0 != '\0')) ||
          ((*local_228 != (char *)0x0 && (**local_228 != '\0')))))) {
        if (local_d0 == (char *)0x0) {
          if ((*(int *)(local_228 + 1) != 0) && (pcVar12 = *local_228, pcVar12 != (char *)0x0)) {
            pvVar13 = GetProcessHeap();
            HeapFree(pvVar13,0,pcVar12);
            uVar9 = extraout_s0_52;
            uVar10 = extraout_var_52;
            uVar14 = extraout_var_x00113;
          }
          *(undefined4 *)(local_228 + 1) = 0;
          *local_228 = local_228[2];
        }
        else {
          cVar2 = *local_d0;
          pcVar12 = local_d0;
          while (cVar2 != '\0') {
            pcVar12 = pcVar12 + 1;
            cVar2 = *pcVar12;
          }
          iVar41 = (int)((longlong)pcVar12 - (longlong)local_d0);
          if (0x7ffffffe < (ulonglong)((longlong)pcVar12 - (longlong)local_d0)) {
            iVar41 = 0x7ffffffe;
          }
          FUN_1800079f8((longlong *)local_228,(longlong)local_d0,iVar41);
          uVar9 = extraout_s0_51;
          uVar10 = extraout_var_51;
          uVar14 = extraout_var_x00112;
        }
        *(undefined4 *)((longlong)local_228 + 0xc) = 1;
      }
      lVar21 = *(longlong *)(param_3 + 0x500);
      if (lVar21 == 0) {
        if (*(code **)(param_3 + 0x510) != (code *)0x0) {
          (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
          uVar9 = extraout_s0_53;
          uVar10 = extraout_var_53;
          uVar14 = extraout_var_x00114;
        }
        lVar21 = *(longlong *)(param_3 + 0x500);
        if (lVar21 != 0) goto LAB_180091550;
        lVar21 = param_3 + 0x528;
      }
      else {
LAB_180091550:
        iVar41 = *(int *)(param_3 + 0x520);
        if (iVar41 == 0) {
          lVar21 = param_3 + 0x528;
        }
        else {
          iVar20 = iVar38;
          if (iVar41 <= iVar38) {
            iVar20 = iVar41 + -1;
          }
          lVar21 = lVar21 + (longlong)iVar20 * 0x170;
        }
      }
      *(undefined2 *)(lVar21 + 0x24) = 0x49;
      lVar21 = *(longlong *)(param_3 + 0x500);
      if (lVar21 == 0) {
        if (*(code **)(param_3 + 0x510) != (code *)0x0) {
          (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
          uVar9 = extraout_s0_54;
          uVar10 = extraout_var_54;
          uVar14 = extraout_var_x00115;
        }
        lVar21 = *(longlong *)(param_3 + 0x500);
        if (lVar21 != 0) goto LAB_180091670;
        lVar21 = param_3 + 0x528;
      }
      else {
LAB_180091670:
        iVar41 = *(int *)(param_3 + 0x520);
        if (iVar41 == 0) {
          lVar21 = param_3 + 0x528;
        }
        else {
          iVar20 = iVar38;
          if (iVar41 <= iVar38) {
            iVar20 = iVar41 + -1;
          }
          lVar21 = lVar21 + (longlong)iVar20 * 0x170;
        }
      }
      *(undefined4 *)(lVar21 + 0x18) = 0;
      lVar21 = *(longlong *)(param_3 + 0x500);
      if (lVar21 == 0) {
        if (*(code **)(param_3 + 0x510) != (code *)0x0) {
          (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
          uVar9 = extraout_s0_55;
          uVar10 = extraout_var_55;
          uVar14 = extraout_var_x00116;
        }
        lVar21 = *(longlong *)(param_3 + 0x500);
        if (lVar21 != 0) goto LAB_18009178c;
        lVar21 = param_3 + 0x528;
      }
      else {
LAB_18009178c:
        iVar41 = *(int *)(param_3 + 0x520);
        if (iVar41 == 0) {
          lVar21 = param_3 + 0x528;
        }
        else {
          iVar20 = iVar38;
          if (iVar41 <= iVar38) {
            iVar20 = iVar41 + -1;
          }
          lVar21 = lVar21 + (longlong)iVar20 * 0x170;
        }
      }
      *(undefined4 *)(lVar21 + 0x1c) = 0;
      lVar21 = *(longlong *)(param_3 + 0x500);
      if (lVar21 == 0) {
        if (*(code **)(param_3 + 0x510) != (code *)0x0) {
          (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
          uVar9 = extraout_s0_56;
          uVar10 = extraout_var_56;
          uVar14 = extraout_var_x00117;
        }
        lVar21 = *(longlong *)(param_3 + 0x500);
        if (lVar21 != 0) goto LAB_1800918a8;
        lVar21 = param_3 + 0x528;
      }
      else {
LAB_1800918a8:
        iVar41 = *(int *)(param_3 + 0x520);
        if (iVar41 == 0) {
          lVar21 = param_3 + 0x528;
        }
        else {
          iVar20 = iVar38;
          if (iVar41 <= iVar38) {
            iVar20 = iVar41 + -1;
          }
          lVar21 = lVar21 + (longlong)iVar20 * 0x170;
        }
      }
      *(undefined4 *)(lVar21 + 0x20) = 1;
      lVar21 = *(longlong *)(param_3 + 0x500);
      if (lVar21 == 0) {
        if (*(code **)(param_3 + 0x510) != (code *)0x0) {
          (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
          uVar9 = extraout_s0_57;
          uVar10 = extraout_var_57;
          uVar14 = extraout_var_x00118;
        }
        lVar21 = *(longlong *)(param_3 + 0x500);
        if (lVar21 != 0) goto LAB_1800919c8;
        lVar21 = param_3 + 0x528;
      }
      else {
LAB_1800919c8:
        iVar41 = *(int *)(param_3 + 0x520);
        if (iVar41 == 0) {
          lVar21 = param_3 + 0x528;
        }
        else {
          iVar20 = iVar38;
          if (iVar41 <= iVar38) {
            iVar20 = iVar41 + -1;
          }
          lVar21 = lVar21 + (longlong)iVar20 * 0x170;
        }
      }
      *(undefined2 *)(lVar21 + 0x28) = 1;
      lVar21 = *(longlong *)(param_3 + 0x500);
      if (lVar21 == 0) {
        if (*(code **)(param_3 + 0x510) != (code *)0x0) {
          (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
          uVar9 = extraout_s0_58;
          uVar10 = extraout_var_58;
          uVar14 = extraout_var_x00119;
        }
        lVar21 = *(longlong *)(param_3 + 0x500);
        if (lVar21 != 0) goto LAB_180091ae8;
        lVar21 = param_3 + 0x528;
      }
      else {
LAB_180091ae8:
        iVar41 = *(int *)(param_3 + 0x520);
        if (iVar41 == 0) {
          lVar21 = param_3 + 0x528;
        }
        else {
          iVar20 = iVar38;
          if (iVar41 <= iVar38) {
            iVar20 = iVar41 + -1;
          }
          lVar21 = lVar21 + (longlong)iVar20 * 0x170;
        }
      }
      *(undefined4 *)(lVar21 + 0xd8) = 0;
      if (((int)uStack_c8 != 0) && (pcVar44 != (char *)0x0)) {
        pvVar13 = GetProcessHeap();
        HeapFree(pvVar13,0,pcVar44);
        local_d0 = (char *)0x0;
        uStack_c8 = 0;
        uVar9 = extraout_s0_59;
        uVar10 = extraout_var_59;
        uVar14 = extraout_var_x00120;
      }
      iVar38 = iVar38 + 1;
      if (0x3b < iVar38) {
        FUN_1800079f8(local_1b8,0x1800db640,8);
        *(undefined4 *)((longlong)local_1b8 + 0xc) = 1;
        *(undefined4 *)((longlong)local_1b8 + 0x1c) = 0x32;
        *(undefined1 *)(local_1b8 + 3) = 0xb;
        FUN_1800079f8(local_220,0x1800db650,0x10);
        *(undefined4 *)((longlong)local_220 + 0xc) = 1;
        *(undefined1 *)(local_220 + 3) = 0xb;
        *(undefined4 *)((longlong)local_220 + 0x1c) = 0xb;
        FUN_1800079f8(local_218,0x1800db6a0,0x10);
        *(undefined4 *)((longlong)local_218 + 0xc) = 1;
        *(undefined1 *)(local_218 + 3) = 0xb;
        *(undefined4 *)((longlong)local_218 + 0x1c) = 1;
        FUN_1800079f8(local_210,0x1800db9c4,3);
        *(undefined4 *)((longlong)local_210 + 0xc) = 1;
        *(undefined1 *)(local_210 + 3) = 0xe;
        *(undefined4 *)((longlong)local_210 + 0x1c) = 0x216a2;
        FUN_1800079f8(local_128,0x1800db6b8,0xe);
        *(undefined4 *)((longlong)local_128 + 0xc) = 1;
        *(undefined1 *)(local_128 + 3) = 0xe;
        *(undefined4 *)((longlong)local_128 + 0x1c) = 0x919efd;
        FUN_1800079f8(local_200,0x1800db680,0xe);
        *(undefined4 *)((longlong)local_200 + 0xc) = 1;
        *(undefined1 *)(local_200 + 3) = 0xe;
        *(undefined4 *)((longlong)local_200 + 0x1c) = 0x455cfe;
        FUN_1800079f8(local_168,0x1800db690,0xe);
        *(undefined4 *)((longlong)local_168 + 0xc) = 1;
        *(undefined1 *)(local_168 + 3) = 0xe;
        *(undefined4 *)((longlong)local_168 + 0x1c) = 0x21dd9;
        FUN_1800079f8(local_180,0x1800db6d8,0xe);
        *(undefined4 *)((longlong)local_180 + 0xc) = 1;
        *(undefined1 *)(local_180 + 3) = 0xe;
        *(undefined4 *)((longlong)local_180 + 0x1c) = 0xca8f09;
        FUN_1800079f8(local_178,0x1800db6e8,0xe);
        *(undefined4 *)((longlong)local_178 + 0xc) = 1;
        *(undefined1 *)(local_178 + 3) = 0xe;
        *(undefined4 *)((longlong)local_178 + 0x1c) = 0xf8be47;
        FUN_1800079f8(local_1e8,0x1800db6c8,0xe);
        *(undefined4 *)((longlong)local_1e8 + 0xc) = 1;
        *(undefined1 *)(local_1e8 + 3) = 0xe;
        *(undefined4 *)((longlong)local_1e8 + 0x1c) = 0xf9d68c;
        FUN_1800079f8(local_1e0,0x1800dba6c,3);
        *(undefined4 *)((longlong)local_1e0 + 0xc) = 1;
        *(undefined1 *)(local_1e0 + 3) = 0xe;
        *(undefined4 *)((longlong)local_1e0 + 0x1c) = 0x9b6b09;
        FUN_1800079f8(plVar15,0x1800dba70,10);
        *(undefined4 *)((longlong)plVar15 + 0xc) = 1;
        if ((code *)plVar15[10] != (code *)0x0) {
          (*(code *)plVar15[10])
                    (*(undefined4 *)((longlong)plVar15 + 0x4c),
                     "Point Variable Size;Transparent Circle Variable Size;Circle Hollow Variable Si ze;User-Defined"
                    );
          *(undefined1 *)(plVar15 + 3) = 0x16;
        }
        *(undefined1 *)(plVar15 + 3) = 0x16;
        *(undefined4 *)((longlong)plVar15 + 0x1c) = 0;
        FUN_1800079f8(local_190,0x1800dba50,0x19);
        *(undefined4 *)((longlong)local_190 + 0xc) = 1;
        *(undefined1 *)(local_190 + 3) = 5;
        *(undefined4 *)((longlong)local_190 + 0x1c) = 1;
        FUN_1800079f8(local_1d8,0x1800dbaa0,0x11);
        *(undefined4 *)((longlong)local_1d8 + 0xc) = 1;
        if ((code *)local_1d8[10] != (code *)0x0) {
          (*(code *)local_1d8[10])
                    (*(undefined4 *)((longlong)local_1d8 + 0x4c),"OFL Std;Fibonacci;OFL Modified");
          *(undefined1 *)(local_1d8 + 3) = 0x16;
        }
        FUN_1800079f8(local_1d0,0x1800dba80,9);
        *(undefined4 *)((longlong)local_1d0 + 0xc) = 1;
        *(undefined1 *)(local_1d0 + 3) = 0xe;
        *(undefined4 *)((longlong)local_1d0 + 0x1c) = 0xff80ff;
        FUN_1800079f8(local_1c8,0x1800dba90,9);
        *(undefined4 *)((longlong)local_1c8 + 0xc) = 1;
        *(undefined1 *)(local_1c8 + 3) = 0xe;
        *(undefined4 *)((longlong)local_1c8 + 0x1c) = 0xff00;
        FUN_1800079f8(plVar23,0x1800dbad8,0x15);
        *(undefined4 *)((longlong)plVar23 + 0xc) = 1;
        *(undefined1 *)(plVar23 + 3) = 2;
        *(undefined4 *)((longlong)plVar23 + 0x1c) = 0x3f800000;
        *(undefined4 *)((longlong)plVar23 + 0x2c) = 0x3f800000;
        *(undefined4 *)((longlong)plVar23 + 0x3c) = 0x41200000;
        return;
      }
    } while( true );
  }
  if (*(int *)(param_3 + 900) == 0) {
    auVar3._4_4_ = uVar10;
    auVar3._0_4_ = uVar9;
    auVar3._8_8_ = uVar45;
    uVar14 = FUN_1800254e8(auVar3,param_2,param_3,uVar14,param_5,param_6,param_7,param_8,param_9,
                           param_10);
    *piVar11 = (int)uVar14;
  }
  if (*piVar11 != 0) {
    return;
  }
  if ((*(int *)(param_3 + 0x11f8) != 0) && (*(int *)(param_3 + 900) == 0)) {
    *local_108 = 0;
    uVar8 = FUN_180026550((longlong)plVar15);
    if (uVar8 == 0) {
      uVar40 = 0x49;
LAB_180091ffc:
      iVar38 = 0;
      do {
        plVar15 = FUN_18000f880((longlong *)(param_3 + 0x500),iVar38);
        *(undefined2 *)((longlong)plVar15 + 0x24) = uVar40;
        plVar15 = FUN_18000f880((longlong *)(param_3 + 0x500),iVar38);
        iVar38 = iVar38 + 1;
        *(undefined2 *)(plVar15 + 5) = 1;
      } while (iVar38 < 0x3c);
    }
    else {
      uVar8 = FUN_180026550((longlong)plVar15);
      if (uVar8 == 1) {
        uVar40 = 0x47;
        goto LAB_180091ffc;
      }
      uVar8 = FUN_180026550((longlong)plVar15);
      if (uVar8 == 2) {
        uVar40 = 0x48;
        goto LAB_180091ffc;
      }
    }
    uVar9 = FUN_180026888((longlong)local_1e0);
    uVar10 = FUN_180026888((longlong)local_210);
    iVar38 = 0;
    do {
      plVar15 = FUN_18000f880((longlong *)(param_3 + 0x500),iVar38);
      *(undefined4 *)(plVar15 + 3) = uVar9;
      plVar15 = FUN_18000f880((longlong *)(param_3 + 0x500),iVar38);
      iVar38 = iVar38 + 1;
      *(undefined4 *)((longlong)plVar15 + 0x1c) = uVar10;
    } while (iVar38 < 0x3c);
  }
  uVar8 = *(uint *)(param_3 + 900);
  lVar21 = *(longlong *)(param_3 + 0xab0);
  uVar25 = uVar8;
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0xac0) != (code *)0x0) {
      (**(code **)(param_3 + 0xac0))(*(undefined4 *)(param_3 + 0xac8));
      uVar25 = *(uint *)(param_3 + 900);
    }
    lVar21 = *(longlong *)(param_3 + 0xab0);
    if (lVar21 != 0) goto LAB_1800920b0;
    puVar18 = (undefined4 *)(param_3 + 0xad4);
  }
  else {
LAB_1800920b0:
    iVar38 = *(int *)(param_3 + 0xad0);
    if (iVar38 == 0) {
      puVar18 = (undefined4 *)(param_3 + 0xad4);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar8) {
        uVar8 = iVar38 - 1;
      }
      puVar18 = (undefined4 *)(lVar21 + (longlong)(int)uVar8 * 4);
    }
  }
  uVar9 = *puVar18;
  lVar21 = *plVar28;
  if (lVar21 == 0) {
    if ((code *)plVar28[2] != (code *)0x0) {
      (*(code *)plVar28[2])((int)plVar28[3]);
    }
    lVar21 = *plVar28;
    if (lVar21 != 0) goto LAB_180092108;
    puVar18 = (undefined4 *)((longlong)plVar28 + 0x24);
  }
  else {
LAB_180092108:
    iVar38 = (int)plVar28[4];
    if (iVar38 == 0) {
      puVar18 = (undefined4 *)((longlong)plVar28 + 0x24);
    }
    else {
      uVar25 = uVar25 & ((int)uVar25 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar25) {
        uVar25 = iVar38 - 1;
      }
      puVar18 = (undefined4 *)(lVar21 + (longlong)(int)uVar25 * 4);
    }
  }
  *puVar18 = uVar9;
  uVar8 = *(uint *)(param_3 + 900);
  lVar21 = *(longlong *)(param_3 + 0xbf0);
  uVar25 = uVar8;
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0xc00) != (code *)0x0) {
      (**(code **)(param_3 + 0xc00))(*(undefined4 *)(param_3 + 0xc08));
      uVar25 = *(uint *)(param_3 + 900);
    }
    lVar21 = *(longlong *)(param_3 + 0xbf0);
    if (lVar21 != 0) goto LAB_18009216c;
    pfVar16 = (float *)(param_3 + 0xc14);
  }
  else {
LAB_18009216c:
    iVar38 = *(int *)(param_3 + 0xc10);
    if (iVar38 == 0) {
      pfVar16 = (float *)(param_3 + 0xc14);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar8) {
        uVar8 = iVar38 - 1;
      }
      pfVar16 = (float *)(lVar21 + (longlong)(int)uVar8 * 4);
    }
  }
  fVar48 = *pfVar16;
  lVar21 = *(longlong *)(param_3 + 0xbc8);
  uVar8 = uVar25;
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0xbd8) != (code *)0x0) {
      (**(code **)(param_3 + 0xbd8))(*(undefined4 *)(param_3 + 0xbe0));
      uVar8 = *(uint *)(param_3 + 900);
    }
    lVar21 = *(longlong *)(param_3 + 0xbc8);
    if (lVar21 != 0) goto LAB_1800921c8;
    pfVar16 = (float *)(param_3 + 0xbec);
  }
  else {
LAB_1800921c8:
    iVar38 = *(int *)(param_3 + 0xbe8);
    if (iVar38 == 0) {
      pfVar16 = (float *)(param_3 + 0xbec);
    }
    else {
      uVar25 = uVar25 & ((int)uVar25 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar25) {
        uVar25 = iVar38 - 1;
      }
      pfVar16 = (float *)(lVar21 + (longlong)(int)uVar25 * 4);
    }
  }
  fVar47 = *pfVar16;
  fVar46 = fVar47 + fVar48;
  (**(code **)(param_3 + 0x3a8))(param_3 + 0xad8,plVar33,uVar8,0x32);
  (**(code **)(param_3 + 0x3c0))(param_3 + 0xad8,plVar34,*(undefined4 *)(param_3 + 900),0x32);
  uVar8 = *(uint *)(param_3 + 900);
  lVar21 = *plVar34;
  uVar25 = uVar8;
  if (lVar21 == 0) {
    if ((code *)plVar34[2] != (code *)0x0) {
      (*(code *)plVar34[2])((int)plVar34[3]);
      uVar25 = *(uint *)(param_3 + 900);
    }
    lVar21 = *plVar34;
    if (lVar21 != 0) goto LAB_180092260;
    pfVar16 = (float *)((longlong)plVar34 + 0x24);
  }
  else {
LAB_180092260:
    iVar38 = (int)plVar34[4];
    if (iVar38 == 0) {
      pfVar16 = (float *)((longlong)plVar34 + 0x24);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar8) {
        uVar8 = iVar38 - 1;
      }
      pfVar16 = (float *)(lVar21 + (longlong)(int)uVar8 * 4);
    }
  }
  lVar21 = *plVar33;
  uVar8 = uVar25;
  if (lVar21 == 0) {
    if ((code *)plVar33[2] != (code *)0x0) {
      (*(code *)plVar33[2])((int)plVar33[3]);
      uVar8 = *(uint *)(param_3 + 900);
    }
    lVar21 = *plVar33;
    if (lVar21 != 0) goto LAB_1800922c4;
    pfVar19 = (float *)((longlong)plVar33 + 0x24);
  }
  else {
LAB_1800922c4:
    iVar38 = (int)plVar33[4];
    if (iVar38 == 0) {
      pfVar19 = (float *)((longlong)plVar33 + 0x24);
    }
    else {
      uVar25 = uVar25 & ((int)uVar25 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar25) {
        uVar25 = iVar38 - 1;
      }
      pfVar19 = (float *)(lVar21 + (longlong)(int)uVar25 * 4);
    }
  }
  fVar50 = *pfVar19;
  fVar49 = *pfVar16;
  lVar21 = *plVar35;
  if (lVar21 == 0) {
    if ((code *)plVar35[2] != (code *)0x0) {
      (*(code *)plVar35[2])((int)plVar35[3]);
    }
    lVar21 = *plVar35;
    if (lVar21 != 0) goto LAB_180092328;
    pfVar16 = (float *)((longlong)plVar35 + 0x24);
  }
  else {
LAB_180092328:
    iVar38 = (int)plVar35[4];
    if (iVar38 == 0) {
      pfVar16 = (float *)((longlong)plVar35 + 0x24);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar8) {
        uVar8 = iVar38 - 1;
      }
      pfVar16 = (float *)(lVar21 + (longlong)(int)uVar8 * 4);
    }
  }
  *pfVar16 = fVar50 + fVar49;
  uVar8 = *(uint *)(param_3 + 900);
  lVar21 = *plVar34;
  uVar25 = uVar8;
  if (lVar21 == 0) {
    if ((code *)plVar34[2] != (code *)0x0) {
      (*(code *)plVar34[2])((int)plVar34[3]);
      uVar25 = *(uint *)(param_3 + 900);
    }
    lVar21 = *plVar34;
    if (lVar21 != 0) goto LAB_180092394;
    pfVar16 = (float *)((longlong)plVar34 + 0x24);
  }
  else {
LAB_180092394:
    iVar38 = (int)plVar34[4];
    if (iVar38 == 0) {
      pfVar16 = (float *)((longlong)plVar34 + 0x24);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar8) {
        uVar8 = iVar38 - 1;
      }
      pfVar16 = (float *)(lVar21 + (longlong)(int)uVar8 * 4);
    }
  }
  lVar21 = *plVar33;
  uVar8 = uVar25;
  if (lVar21 == 0) {
    if ((code *)plVar33[2] != (code *)0x0) {
      (*(code *)plVar33[2])((int)plVar33[3]);
      uVar8 = *(uint *)(param_3 + 900);
    }
    lVar21 = *plVar33;
    if (lVar21 != 0) goto LAB_1800923f8;
    pfVar19 = (float *)((longlong)plVar33 + 0x24);
  }
  else {
LAB_1800923f8:
    iVar38 = (int)plVar33[4];
    if (iVar38 == 0) {
      pfVar19 = (float *)((longlong)plVar33 + 0x24);
    }
    else {
      uVar25 = uVar25 & ((int)uVar25 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar25) {
        uVar25 = iVar38 - 1;
      }
      pfVar19 = (float *)(lVar21 + (longlong)(int)uVar25 * 4);
    }
  }
  fVar49 = *pfVar16;
  fVar50 = *pfVar19;
  lVar21 = *plVar36;
  if (lVar21 == 0) {
    if ((code *)plVar36[2] != (code *)0x0) {
      (*(code *)plVar36[2])((int)plVar36[3]);
    }
    lVar21 = *plVar36;
    if (lVar21 != 0) goto LAB_180092464;
    pfVar16 = (float *)((longlong)plVar36 + 0x24);
  }
  else {
LAB_180092464:
    iVar38 = (int)plVar36[4];
    if (iVar38 == 0) {
      pfVar16 = (float *)((longlong)plVar36 + 0x24);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar8) {
        uVar8 = iVar38 - 1;
      }
      pfVar16 = (float *)(lVar21 + (longlong)(int)uVar8 * 4);
    }
  }
  *pfVar16 = fVar49 * 3.0 + fVar50;
  uVar8 = *(uint *)(param_3 + 900);
  lVar21 = *plVar34;
  uVar25 = uVar8;
  if (lVar21 == 0) {
    if ((code *)plVar34[2] != (code *)0x0) {
      (*(code *)plVar34[2])((int)plVar34[3]);
      uVar25 = *(uint *)(param_3 + 900);
    }
    lVar21 = *plVar34;
    if (lVar21 != 0) goto LAB_1800924d0;
    pfVar16 = (float *)((longlong)plVar34 + 0x24);
  }
  else {
LAB_1800924d0:
    iVar38 = (int)plVar34[4];
    if (iVar38 == 0) {
      pfVar16 = (float *)((longlong)plVar34 + 0x24);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar8) {
        uVar8 = iVar38 - 1;
      }
      pfVar16 = (float *)(lVar21 + (longlong)(int)uVar8 * 4);
    }
  }
  lVar21 = *plVar33;
  uVar8 = uVar25;
  if (lVar21 == 0) {
    if ((code *)plVar33[2] != (code *)0x0) {
      (*(code *)plVar33[2])((int)plVar33[3]);
      uVar8 = *(uint *)(param_3 + 900);
    }
    lVar21 = *plVar33;
    if (lVar21 != 0) goto LAB_180092534;
    pfVar19 = (float *)((longlong)plVar33 + 0x24);
  }
  else {
LAB_180092534:
    iVar38 = (int)plVar33[4];
    if (iVar38 == 0) {
      pfVar19 = (float *)((longlong)plVar33 + 0x24);
    }
    else {
      uVar25 = uVar25 & ((int)uVar25 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar25) {
        uVar25 = iVar38 - 1;
      }
      pfVar19 = (float *)(lVar21 + (longlong)(int)uVar25 * 4);
    }
  }
  fVar49 = *pfVar16;
  fVar50 = *pfVar19;
  lVar21 = *plVar22;
  if (lVar21 == 0) {
    if ((code *)plVar22[2] != (code *)0x0) {
      (*(code *)plVar22[2])((int)plVar22[3]);
    }
    lVar21 = *plVar22;
    if (lVar21 != 0) goto LAB_1800925a0;
    pfVar16 = (float *)((longlong)plVar22 + 0x24);
  }
  else {
LAB_1800925a0:
    iVar38 = (int)plVar22[4];
    if (iVar38 == 0) {
      pfVar16 = (float *)((longlong)plVar22 + 0x24);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar8) {
        uVar8 = iVar38 - 1;
      }
      pfVar16 = (float *)(lVar21 + (longlong)(int)uVar8 * 4);
    }
  }
  *pfVar16 = fVar49 * 5.0 + fVar50;
  uVar8 = *(uint *)(param_3 + 900);
  lVar21 = *plVar35;
  uVar25 = uVar8;
  if (lVar21 == 0) {
    if ((code *)plVar35[2] != (code *)0x0) {
      (*(code *)plVar35[2])((int)plVar35[3]);
      uVar25 = *(uint *)(param_3 + 900);
    }
    lVar21 = *plVar35;
    if (lVar21 != 0) goto LAB_18009260c;
    pfVar16 = (float *)((longlong)plVar35 + 0x24);
  }
  else {
LAB_18009260c:
    iVar38 = (int)plVar35[4];
    if (iVar38 == 0) {
      pfVar16 = (float *)((longlong)plVar35 + 0x24);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar8) {
        uVar8 = iVar38 - 1;
      }
      pfVar16 = (float *)(lVar21 + (longlong)(int)uVar8 * 4);
    }
  }
  if (*pfVar16 <= fVar46) {
    pfVar16 = (float *)FUN_180005d08(plVar35,uVar25);
    fVar46 = *pfVar16;
  }
  uVar8 = *(uint *)(param_3 + 900);
  lVar21 = *plVar30;
  if (lVar21 == 0) {
    if ((code *)plVar30[2] != (code *)0x0) {
      (*(code *)plVar30[2])((int)plVar30[3]);
    }
    lVar21 = *plVar30;
    if (lVar21 != 0) goto LAB_180092680;
    pfVar16 = (float *)((longlong)plVar30 + 0x24);
  }
  else {
LAB_180092680:
    iVar38 = (int)plVar30[4];
    if (iVar38 == 0) {
      pfVar16 = (float *)((longlong)plVar30 + 0x24);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar8) {
        uVar8 = iVar38 - 1;
      }
      pfVar16 = (float *)(lVar21 + (longlong)(int)uVar8 * 4);
    }
  }
  *pfVar16 = fVar46;
  if (fVar48 < fVar47) {
    local_1e0 = local_210;
  }
  uVar8 = FUN_180026888((longlong)local_1e0);
  pfVar16 = (float *)FUN_180005d08(plVar29,*(int *)(param_3 + 900));
  *pfVar16 = (float)uVar8;
  uVar17 = FUN_180026708((longlong)local_1b8);
  (**(code **)(param_3 + 0x3d0))(plVar30,plVar31,*(undefined4 *)(param_3 + 900),uVar17 & 0xffffffff)
  ;
  uVar17 = FUN_180026708((longlong)local_1b8);
  (**(code **)(param_3 + 0x3d8))(plVar30,lVar24,*(undefined4 *)(param_3 + 900),uVar17 & 0xffffffff);
  FUN_180026708((longlong)local_218);
  uVar17 = FUN_180026708((longlong)local_220);
  iVar38 = (int)uVar17 - extraout_w11;
  if ((*plVar31 == 0) && ((code *)plVar31[2] != (code *)0x0)) {
    (*(code *)plVar31[2])((int)plVar31[3]);
  }
  if ((*plVar30 == 0) && ((code *)plVar30[2] != (code *)0x0)) {
    (*(code *)plVar30[2])((int)plVar30[3]);
  }
  uVar17 = FUN_180026708((longlong)local_218);
  fVar46 = (float)(int)uVar17 + extraout_s18;
  iVar41 = (int)fVar46;
  bVar7 = 0.0 <= fVar46;
  if (fVar46 <= 0.0) {
LAB_180092848:
    if ((!bVar7) && (fVar46 - (float)iVar41 <= -0.5)) {
      iVar41 = iVar41 + -1;
    }
  }
  else {
    if (fVar46 - (float)iVar41 < 0.5) {
      bVar7 = true;
      if (!NAN(fVar46)) {
        bVar7 = 0.0 <= fVar46;
      }
      goto LAB_180092848;
    }
    iVar41 = iVar41 + 1;
  }
  local_e0 = &DAT_1800d4ecd;
  local_f0 = "";
  uStack_e8 = 0;
  uVar8 = *(uint *)(param_3 + 900);
  lVar21 = *plVar22;
  uVar25 = uVar8;
  if (lVar21 == 0) {
    if ((code *)plVar22[2] != (code *)0x0) {
      (*(code *)plVar22[2])((int)plVar22[3]);
      uVar25 = *(uint *)(param_3 + 900);
    }
    lVar21 = *plVar22;
    if (lVar21 != 0) goto LAB_1800928bc;
    pfVar16 = (float *)((longlong)plVar22 + 0x24);
  }
  else {
LAB_1800928bc:
    iVar20 = (int)plVar22[4];
    if (iVar20 == 0) {
      pfVar16 = (float *)((longlong)plVar22 + 0x24);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar8) {
        uVar8 = iVar20 - 1;
      }
      pfVar16 = (float *)(lVar21 + (longlong)(int)uVar8 * 4);
    }
  }
  lVar21 = *(longlong *)(param_3 + 0xad8);
  uVar8 = uVar25;
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0xae8) != (code *)0x0) {
      (**(code **)(param_3 + 0xae8))(*(undefined4 *)(param_3 + 0xaf0));
      uVar8 = *(uint *)(param_3 + 900);
    }
    lVar21 = *(longlong *)(param_3 + 0xad8);
    if (lVar21 != 0) goto LAB_180092918;
    pfVar19 = (float *)(param_3 + 0xafc);
  }
  else {
LAB_180092918:
    iVar20 = *(int *)(param_3 + 0xaf8);
    if (iVar20 == 0) {
      pfVar19 = (float *)(param_3 + 0xafc);
    }
    else {
      uVar25 = uVar25 & ((int)uVar25 >> 0x1f ^ 0xffffffffU);
      if (iVar20 <= (int)uVar25) {
        uVar25 = iVar20 - 1;
      }
      pfVar19 = (float *)(lVar21 + (longlong)(int)uVar25 * 4);
    }
  }
  if (*pfVar19 <= *pfVar16) {
    pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),uVar8);
    pfVar19 = (float *)FUN_180005d08(plVar36,*(int *)(param_3 + 900));
    if (*pfVar19 < *pfVar16) {
      iVar41 = iVar41 * 3;
      pplVar6 = &local_120;
      if (fVar48 < fVar47) {
        pplVar6 = &local_118;
      }
      uVar8 = FUN_180026888((longlong)*pplVar6);
      pfVar16 = (float *)FUN_180005d08(plVar29,*(int *)(param_3 + 900));
      *pfVar16 = (float)uVar8;
      puVar18 = (undefined4 *)FUN_180005d08(plVar39,*(int *)(param_3 + 900));
      uVar9 = 0x40000000;
      if (fVar48 < fVar47) {
        uVar9 = 0xc0000000;
      }
      goto LAB_180092a7c;
    }
    pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),*(int *)(param_3 + 900));
    pfVar19 = (float *)FUN_180005d08(plVar35,*(int *)(param_3 + 900));
    if (*pfVar19 < *pfVar16) {
      iVar41 = iVar41 << 1;
      plVar30 = local_110;
      if (fVar48 < fVar47) {
        plVar30 = local_168;
      }
      uVar8 = FUN_180026888((longlong)plVar30);
      pfVar16 = (float *)FUN_180005d08(plVar29,*(int *)(param_3 + 900));
      *pfVar16 = (float)uVar8;
      puVar18 = (undefined4 *)FUN_180005d08(plVar39,*(int *)(param_3 + 900));
      uVar9 = 0x3f800000;
      if (fVar48 < fVar47) {
        uVar9 = 0xbf800000;
      }
      goto LAB_180092a7c;
    }
  }
  else {
    iVar41 = iVar41 << 2;
    if (fVar48 < fVar47) {
      local_1e8 = local_128;
    }
    uVar8 = FUN_180026888((longlong)local_1e8);
    pfVar16 = (float *)FUN_180005d08(plVar29,extraout_w1);
    *pfVar16 = (float)uVar8;
    puVar18 = (undefined4 *)FUN_180005d08(plVar39,*(int *)(param_3 + 900));
    uVar9 = 0x40400000;
    if (fVar48 < fVar47) {
      uVar9 = 0xc0400000;
    }
LAB_180092a7c:
    *puVar18 = uVar9;
  }
  uVar8 = FUN_180026550((longlong)local_1d8);
  if (uVar8 == 1) {
    pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),*(int *)(param_3 + 900));
    fVar48 = (float)iVar38;
    if (*pfVar16 < 89.0) {
      pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),*(int *)(param_3 + 900));
      if (*pfVar16 < 55.0) {
        pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),*(int *)(param_3 + 900));
        if (*pfVar16 < 34.0) {
          pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),*(int *)(param_3 + 900));
          if (*pfVar16 < 21.0) {
            pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),*(int *)(param_3 + 900));
            if (*pfVar16 < 13.0) {
              pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),*(int *)(param_3 + 900)
                                              );
              if (*pfVar16 < 8.0) {
                pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),
                                                 *(int *)(param_3 + 900));
                if (*pfVar16 < 5.0) {
LAB_180092be8:
                  fVar48 = (float)iVar38;
                  pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),
                                                   *(int *)(param_3 + 900));
                  if (*pfVar16 < 3.0) {
                    pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),
                                                     *(int *)(param_3 + 900));
                    if (*pfVar16 < 2.0) {
                      uVar17 = FUN_180026708((longlong)local_218);
                      iVar38 = (int)uVar17;
                      fVar48 = fVar48 * 0.1;
                    }
                    else {
                      uVar17 = FUN_180026708((longlong)local_218);
                      iVar38 = (int)uVar17;
                      fVar48 = fVar48 * 0.2;
                    }
                  }
                  else {
                    uVar17 = FUN_180026708((longlong)local_218);
                    iVar38 = (int)uVar17;
                    fVar48 = fVar48 * 0.3;
                  }
                }
                else {
                  uVar17 = FUN_180026708((longlong)local_218);
                  iVar38 = (int)uVar17;
                  fVar48 = fVar48 * 0.4;
                }
              }
              else {
                uVar17 = FUN_180026708((longlong)local_218);
                iVar38 = (int)uVar17;
                fVar48 = fVar48 * 0.5;
              }
            }
            else {
              uVar17 = FUN_180026708((longlong)local_218);
              iVar38 = (int)uVar17;
              fVar48 = fVar48 * 0.6;
            }
          }
          else {
            uVar17 = FUN_180026708((longlong)local_218);
            iVar38 = (int)uVar17;
            fVar48 = fVar48 * 0.7;
          }
        }
        else {
          uVar17 = FUN_180026708((longlong)local_218);
          iVar38 = (int)uVar17;
          fVar48 = fVar48 * 0.8;
        }
      }
      else {
        uVar17 = FUN_180026708((longlong)local_218);
        iVar38 = (int)uVar17;
        fVar48 = fVar48 * 0.9;
      }
LAB_180092da0:
      fVar48 = (float)iVar38 + fVar48;
    }
    else {
      uVar17 = FUN_180026708((longlong)local_218);
      fVar48 = (float)(int)uVar17 + fVar48;
    }
LAB_180092da8:
    iVar41 = FUN_18000bc78(fVar48);
  }
  else {
    uVar8 = FUN_180026550(extraout_x11);
    if (uVar8 == 2) {
      pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),*(int *)(param_3 + 900));
      fVar48 = (float)iVar38;
      if (*pfVar16 < 50.0) {
        pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),*(int *)(param_3 + 900));
        if (*pfVar16 < 35.0) {
          pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),*(int *)(param_3 + 900));
          if (*pfVar16 < 20.0) {
            pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),*(int *)(param_3 + 900));
            if (*pfVar16 < 15.0) {
              pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),*(int *)(param_3 + 900)
                                              );
              if (*pfVar16 < 10.0) {
                pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),
                                                 *(int *)(param_3 + 900));
                if (*pfVar16 < 5.0) {
                  pfVar16 = (float *)FUN_180005d08((longlong *)(param_3 + 0xad8),
                                                   *(int *)(param_3 + 900));
                  if (*pfVar16 < 4.0) goto LAB_180092be8;
                  uVar17 = FUN_180026708((longlong)local_218);
                  iVar38 = (int)uVar17;
                  fVar48 = fVar48 * 0.4;
                }
                else {
                  uVar17 = FUN_180026708((longlong)local_218);
                  iVar38 = (int)uVar17;
                  fVar48 = fVar48 * 0.5;
                }
              }
              else {
                uVar17 = FUN_180026708((longlong)local_218);
                iVar38 = (int)uVar17;
                fVar48 = fVar48 * 0.6;
              }
            }
            else {
              uVar17 = FUN_180026708((longlong)local_218);
              iVar38 = (int)uVar17;
              fVar48 = fVar48 * 0.7;
            }
          }
          else {
            uVar17 = FUN_180026708((longlong)local_218);
            iVar38 = (int)uVar17;
            fVar48 = fVar48 * 0.8;
          }
        }
        else {
          uVar17 = FUN_180026708((longlong)local_218);
          iVar38 = (int)uVar17;
          fVar48 = fVar48 * 0.9;
        }
        goto LAB_180092da0;
      }
      uVar17 = FUN_180026708((longlong)local_218);
      fVar48 = (float)(int)uVar17 + fVar48;
      goto LAB_180092da8;
    }
  }
  uVar8 = *(uint *)(param_3 + 900);
  lVar21 = *plVar28;
  if (lVar21 == 0) {
    if ((code *)plVar28[2] != (code *)0x0) {
      (*(code *)plVar28[2])((int)plVar28[3]);
    }
    lVar21 = *plVar28;
    if (lVar21 != 0) goto LAB_180092de4;
    pfVar16 = (float *)((longlong)plVar28 + 0x24);
  }
  else {
LAB_180092de4:
    iVar38 = (int)plVar28[4];
    if (iVar38 == 0) {
      pfVar16 = (float *)((longlong)plVar28 + 0x24);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar8) {
        uVar8 = iVar38 - 1;
      }
      pfVar16 = (float *)(lVar21 + (longlong)(int)uVar8 * 4);
    }
  }
  *pfVar16 = (float)iVar41;
  uVar8 = *(uint *)(param_3 + 900);
  lVar21 = *(longlong *)(param_3 + 0xab0);
  uVar25 = uVar8;
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0xac0) != (code *)0x0) {
      (**(code **)(param_3 + 0xac0))(*(undefined4 *)(param_3 + 0xac8));
      uVar25 = *(uint *)(param_3 + 900);
    }
    lVar21 = *(longlong *)(param_3 + 0xab0);
    if (lVar21 != 0) goto LAB_180092e4c;
    puVar18 = (undefined4 *)(param_3 + 0xad4);
  }
  else {
LAB_180092e4c:
    iVar38 = *(int *)(param_3 + 0xad0);
    if (iVar38 == 0) {
      puVar18 = (undefined4 *)(param_3 + 0xad4);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar8) {
        uVar8 = iVar38 - 1;
      }
      puVar18 = (undefined4 *)(lVar21 + (longlong)(int)uVar8 * 4);
    }
  }
  uVar9 = *puVar18;
  lVar21 = *plVar42;
  if (lVar21 == 0) {
    if ((code *)plVar42[2] != (code *)0x0) {
      (*(code *)plVar42[2])((int)plVar42[3]);
    }
    lVar21 = *plVar42;
    if (lVar21 != 0) goto LAB_180092ea4;
    puVar18 = (undefined4 *)((longlong)plVar42 + 0x24);
  }
  else {
LAB_180092ea4:
    iVar38 = (int)plVar42[4];
    if (iVar38 == 0) {
      puVar18 = (undefined4 *)((longlong)plVar42 + 0x24);
    }
    else {
      uVar25 = uVar25 & ((int)uVar25 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar25) {
        uVar25 = iVar38 - 1;
      }
      puVar18 = (undefined4 *)(lVar21 + (longlong)(int)uVar25 * 4);
    }
  }
  *puVar18 = uVar9;
  lVar21 = *(longlong *)(param_3 + 0x448);
  if (lVar21 == 0) {
    if (*(code **)(param_3 + 0x458) != (code *)0x0) {
      (**(code **)(param_3 + 0x458))(*(undefined4 *)(param_3 + 0x460));
    }
    lVar21 = *(longlong *)(param_3 + 0x448);
    if (lVar21 != 0) goto LAB_180092f08;
    uVar8 = *(uint *)(param_3 + 900);
    plVar42 = (longlong *)(param_3 + 0x470);
LAB_180092f44:
    uVar25 = uVar8;
    if (*(code **)(param_3 + 0x458) != (code *)0x0) {
      (**(code **)(param_3 + 0x458))(*(undefined4 *)(param_3 + 0x460));
      uVar25 = *(uint *)(param_3 + 900);
    }
    lVar21 = *(longlong *)(param_3 + 0x448);
    if (lVar21 != 0) goto LAB_180092f6c;
    plVar39 = (longlong *)(param_3 + 0x470);
  }
  else {
LAB_180092f08:
    iVar38 = *(int *)(param_3 + 0x468);
    if (iVar38 == 0) {
      plVar42 = (longlong *)(param_3 + 0x470);
    }
    else {
      iVar20 = 3;
      if (iVar38 < 4) {
        iVar20 = iVar38 + -1;
      }
      plVar42 = (longlong *)(lVar21 + (longlong)iVar20 * 0x28);
    }
    uVar8 = *(uint *)(param_3 + 900);
    uVar25 = uVar8;
    if (lVar21 == 0) goto LAB_180092f44;
LAB_180092f6c:
    iVar38 = *(int *)(param_3 + 0x468);
    if (iVar38 == 0) {
      plVar39 = (longlong *)(param_3 + 0x470);
    }
    else {
      iVar20 = 0x18;
      if (iVar38 < 0x19) {
        iVar20 = iVar38 + -1;
      }
      plVar39 = (longlong *)(lVar21 + (longlong)iVar20 * 0x28);
    }
  }
  lVar21 = *plVar42;
  if (lVar21 == 0) {
    if ((code *)plVar42[2] != (code *)0x0) {
      (*(code *)plVar42[2])((int)plVar42[3]);
    }
    lVar21 = *plVar42;
    if (lVar21 != 0) goto LAB_180092fbc;
    pfVar16 = (float *)((longlong)plVar42 + 0x24);
  }
  else {
LAB_180092fbc:
    iVar38 = (int)plVar42[4];
    if (iVar38 == 0) {
      pfVar16 = (float *)((longlong)plVar42 + 0x24);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar8) {
        uVar8 = iVar38 - 1;
      }
      pfVar16 = (float *)(lVar21 + (longlong)(int)uVar8 * 4);
    }
  }
  lVar21 = *plVar39;
  if (lVar21 == 0) {
    if ((code *)plVar39[2] != (code *)0x0) {
      (*(code *)plVar39[2])((int)plVar39[3]);
    }
    lVar21 = *plVar39;
    if (lVar21 != 0) goto LAB_180093008;
    pfVar19 = (float *)((longlong)plVar39 + 0x24);
  }
  else {
LAB_180093008:
    iVar38 = (int)plVar39[4];
    if (iVar38 == 0) {
      pfVar19 = (float *)((longlong)plVar39 + 0x24);
    }
    else {
      uVar25 = uVar25 & ((int)uVar25 >> 0x1f ^ 0xffffffffU);
      if (iVar38 <= (int)uVar25) {
        uVar25 = iVar38 - 1;
      }
      pfVar19 = (float *)(lVar21 + (longlong)(int)uVar25 * 4);
    }
  }
  if (*pfVar16 <= *pfVar19) {
    lVar21 = *(longlong *)(param_3 + 0x448);
    if (lVar21 == 0) {
      if (*(code **)(param_3 + 0x458) != (code *)0x0) {
        (**(code **)(param_3 + 0x458))(*(undefined4 *)(param_3 + 0x460));
      }
      lVar21 = *(longlong *)(param_3 + 0x448);
      if (lVar21 != 0) goto LAB_18009309c;
      iVar38 = *(int *)(param_3 + 900);
      plVar42 = (longlong *)(param_3 + 0x470);
LAB_1800930d8:
      iVar20 = iVar38;
      if (*(code **)(param_3 + 0x458) != (code *)0x0) {
        (**(code **)(param_3 + 0x458))(*(undefined4 *)(param_3 + 0x460));
        iVar20 = *(int *)(param_3 + 900);
      }
      lVar21 = *(longlong *)(param_3 + 0x448);
      if (lVar21 != 0) goto LAB_180093100;
      plVar39 = (longlong *)(param_3 + 0x470);
    }
    else {
LAB_18009309c:
      iVar38 = *(int *)(param_3 + 0x468);
      if (iVar38 == 0) {
        plVar42 = (longlong *)(param_3 + 0x470);
      }
      else {
        iVar20 = 3;
        if (iVar38 < 4) {
          iVar20 = iVar38 + -1;
        }
        plVar42 = (longlong *)(lVar21 + (longlong)iVar20 * 0x28);
      }
      iVar38 = *(int *)(param_3 + 900);
      iVar20 = iVar38;
      if (lVar21 == 0) goto LAB_1800930d8;
LAB_180093100:
      iVar1 = *(int *)(param_3 + 0x468);
      if (iVar1 == 0) {
        plVar39 = (longlong *)(param_3 + 0x470);
      }
      else {
        iVar37 = 0x17;
        if (iVar1 < 0x18) {
          iVar37 = iVar1 + -1;
        }
        plVar39 = (longlong *)(lVar21 + (longlong)iVar37 * 0x28);
      }
    }
    pfVar16 = (float *)FUN_180005d08(plVar42,iVar38);
    pfVar19 = (float *)FUN_180005d08(plVar39,iVar20);
    if (*pfVar19 <= *pfVar16) goto LAB_180093190;
    fVar48 = FUN_180026608((longlong)plVar23);
    pfVar16 = (float *)FUN_180005d08(plVar28,*(int *)(param_3 + 900));
    *pfVar16 = fVar48 * (float)iVar41;
    local_1c8 = local_1d0;
  }
  else {
    fVar48 = FUN_180026608((longlong)plVar23);
    pfVar16 = (float *)FUN_180005d08(plVar28,*(int *)(param_3 + 900));
    *pfVar16 = fVar48 * (float)iVar41;
  }
  uVar8 = FUN_180026888((longlong)local_1c8);
  pfVar16 = (float *)FUN_180005d08(plVar29,*(int *)(param_3 + 900));
  *pfVar16 = (float)uVar8;
LAB_180093190:
  iVar38 = 0x3b;
  do {
    fVar48 = 0.0;
    uVar25 = *(int *)(param_3 + 900) - iVar38;
    uVar8 = (int)uVar25 >> 0x1f;
    switch((char)local_190[3]) {
    default:
      goto switchD_1800931c4_caseD_0;
    case '\x01':
    case '\x03':
    case '\x04':
    case '\x05':
    case '\x06':
    case '\v':
    case '\r':
    case '\x0e':
    case '\x0f':
    case '\x10':
    case '\x11':
    case '\x13':
    case '\x16':
    case '\x18':
      bVar7 = *(int *)((longlong)local_190 + 0x1c) == 0;
      break;
    case '\x02':
      bVar7 = *(float *)((longlong)local_190 + 0x1c) == 0.0;
      break;
    case '\b':
    case '\t':
    case '\n':
    case '\x17':
    case '\x19':
      bVar7 = false;
      if (!NAN(*(double *)((longlong)local_190 + 0x1c))) {
        bVar7 = *(double *)((longlong)local_190 + 0x1c) == 0.0;
      }
    }
    if (!bVar7) {
      lVar21 = *(longlong *)(param_3 + 0xad8);
      if (lVar21 == 0) {
        if (*(code **)(param_3 + 0xae8) != (code *)0x0) {
          (**(code **)(param_3 + 0xae8))(*(undefined4 *)(param_3 + 0xaf0));
        }
        lVar21 = *(longlong *)(param_3 + 0xad8);
        if (lVar21 != 0) goto LAB_18009321c;
        pfVar16 = (float *)(param_3 + 0xafc);
      }
      else {
LAB_18009321c:
        iVar41 = *(int *)(param_3 + 0xaf8);
        if (iVar41 == 0) {
          pfVar16 = (float *)(param_3 + 0xafc);
        }
        else {
          uVar26 = uVar25 & (uVar8 ^ 0xffffffff);
          if (iVar41 <= (int)uVar26) {
            uVar26 = iVar41 - 1;
          }
          pfVar16 = (float *)(lVar21 + (longlong)(int)uVar26 * 4);
        }
      }
      fVar46 = 0.1;
      for (iVar41 = (int)*pfVar16; iVar41 != 0; iVar41 = iVar41 / 10) {
        fVar47 = (float)(iVar41 % 10) * fVar46;
        fVar46 = fVar46 / 10.0;
        fVar48 = fVar47 + fVar48;
      }
    }
switchD_1800931c4_caseD_0:
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 == 0) {
      if (*(code **)(param_3 + 0x510) != (code *)0x0) {
        (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      }
      lVar21 = *(longlong *)(param_3 + 0x500);
      if (lVar21 != 0) goto LAB_1800932b8;
      lVar21 = param_3 + 0x528;
    }
    else {
LAB_1800932b8:
      iVar41 = *(int *)(param_3 + 0x520);
      if (iVar41 == 0) {
        lVar21 = param_3 + 0x528;
      }
      else {
        iVar20 = iVar38;
        if (iVar41 <= iVar38) {
          iVar20 = iVar41 + -1;
        }
        lVar21 = lVar21 + (longlong)iVar20 * 0x170;
      }
    }
    uVar26 = *(uint *)(param_3 + 900);
    lVar24 = *(longlong *)(lVar21 + 0x30);
    if (lVar24 == 0) {
      if (*(code **)(lVar21 + 0x40) != (code *)0x0) {
        (**(code **)(lVar21 + 0x40))(*(undefined4 *)(lVar21 + 0x48));
      }
      lVar24 = *(longlong *)(lVar21 + 0x30);
      if (lVar24 != 0) goto LAB_18009330c;
      pfVar16 = (float *)(lVar21 + 0x54);
    }
    else {
LAB_18009330c:
      iVar41 = *(int *)(lVar21 + 0x50);
      if (iVar41 == 0) {
        pfVar16 = (float *)(lVar21 + 0x54);
      }
      else {
        uVar26 = uVar26 & ((int)uVar26 >> 0x1f ^ 0xffffffffU);
        if (iVar41 <= (int)uVar26) {
          uVar26 = iVar41 - 1;
        }
        pfVar16 = (float *)(lVar24 + (longlong)(int)uVar26 * 4);
      }
    }
    *pfVar16 = (-1.0 - (float)iVar38) - fVar48;
    lVar21 = *plVar28;
    if (lVar21 == 0) {
      if ((code *)plVar28[2] != (code *)0x0) {
        (*(code *)plVar28[2])((int)plVar28[3]);
      }
      lVar21 = *plVar28;
      if (lVar21 != 0) goto LAB_18009335c;
      puVar18 = (undefined4 *)((longlong)plVar28 + 0x24);
    }
    else {
LAB_18009335c:
      iVar41 = (int)plVar28[4];
      if (iVar41 == 0) {
        puVar18 = (undefined4 *)((longlong)plVar28 + 0x24);
      }
      else {
        uVar26 = uVar25 & (uVar8 ^ 0xffffffff);
        if (iVar41 <= (int)uVar26) {
          uVar26 = iVar41 - 1;
        }
        puVar18 = (undefined4 *)(lVar21 + (longlong)(int)uVar26 * 4);
      }
    }
    uVar9 = *puVar18;
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 == 0) {
      if (*(code **)(param_3 + 0x510) != (code *)0x0) {
        (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      }
      lVar21 = *(longlong *)(param_3 + 0x500);
      if (lVar21 != 0) goto LAB_1800933ac;
      lVar21 = param_3 + 0x528;
    }
    else {
LAB_1800933ac:
      iVar41 = *(int *)(param_3 + 0x520);
      if (iVar41 == 0) {
        lVar21 = param_3 + 0x528;
      }
      else {
        iVar20 = iVar38;
        if (iVar41 <= iVar38) {
          iVar20 = iVar41 + -1;
        }
        lVar21 = lVar21 + (longlong)iVar20 * 0x170;
      }
    }
    lVar24 = *(longlong *)(lVar21 + 0x80);
    if (lVar24 == 0) {
      if (*(code **)(lVar21 + 0x90) != (code *)0x0) {
        (**(code **)(lVar21 + 0x90))(*(undefined4 *)(lVar21 + 0x98));
      }
      lVar24 = *(longlong *)(lVar21 + 0x80);
      if (lVar24 != 0) goto LAB_1800933fc;
      plVar42 = (longlong *)(lVar21 + 0xa8);
    }
    else {
LAB_1800933fc:
      iVar41 = *(int *)(lVar21 + 0xa0);
      if (iVar41 == 0) {
        plVar42 = (longlong *)(lVar21 + 0xa8);
      }
      else {
        iVar20 = 0;
        if (iVar41 < 1) {
          iVar20 = iVar41 + -1;
        }
        plVar42 = (longlong *)(lVar24 + (longlong)iVar20 * 0x28);
      }
    }
    uVar26 = *(uint *)(param_3 + 900);
    lVar21 = *plVar42;
    if (lVar21 == 0) {
      if ((code *)plVar42[2] != (code *)0x0) {
        (*(code *)plVar42[2])((int)plVar42[3]);
      }
      lVar21 = *plVar42;
      if (lVar21 != 0) goto LAB_18009344c;
      puVar18 = (undefined4 *)((longlong)plVar42 + 0x24);
    }
    else {
LAB_18009344c:
      iVar41 = (int)plVar42[4];
      if (iVar41 == 0) {
        puVar18 = (undefined4 *)((longlong)plVar42 + 0x24);
      }
      else {
        uVar26 = uVar26 & ((int)uVar26 >> 0x1f ^ 0xffffffffU);
        if (iVar41 <= (int)uVar26) {
          uVar26 = iVar41 - 1;
        }
        puVar18 = (undefined4 *)(lVar21 + (longlong)(int)uVar26 * 4);
      }
    }
    *puVar18 = uVar9;
    lVar21 = *plVar29;
    if (lVar21 == 0) {
      if ((code *)plVar29[2] != (code *)0x0) {
        (*(code *)plVar29[2])((int)plVar29[3]);
      }
      lVar21 = *plVar29;
      if (lVar21 != 0) goto LAB_18009349c;
      pfVar16 = (float *)((longlong)plVar29 + 0x24);
    }
    else {
LAB_18009349c:
      iVar41 = (int)plVar29[4];
      if (iVar41 == 0) {
        pfVar16 = (float *)((longlong)plVar29 + 0x24);
      }
      else {
        uVar25 = uVar25 & (uVar8 ^ 0xffffffff);
        if (iVar41 <= (int)uVar25) {
          uVar25 = iVar41 - 1;
        }
        pfVar16 = (float *)(lVar21 + (longlong)(int)uVar25 * 4);
      }
    }
    fVar48 = *pfVar16;
    lVar21 = *(longlong *)(param_3 + 0x500);
    if (lVar21 == 0) {
      if (*(code **)(param_3 + 0x510) != (code *)0x0) {
        (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      }
      lVar21 = *(longlong *)(param_3 + 0x500);
      if (lVar21 != 0) goto LAB_1800934f0;
      lVar21 = param_3 + 0x528;
    }
    else {
LAB_1800934f0:
      iVar41 = *(int *)(param_3 + 0x520);
      if (iVar41 == 0) {
        lVar21 = param_3 + 0x528;
      }
      else {
        iVar20 = iVar38;
        if (iVar41 <= iVar38) {
          iVar20 = iVar41 + -1;
        }
        lVar21 = lVar21 + (longlong)iVar20 * 0x170;
      }
    }
    uVar8 = *(uint *)(param_3 + 900);
    lVar24 = *(longlong *)(lVar21 + 0x58);
    if (lVar24 == 0) {
      if (*(code **)(lVar21 + 0x68) != (code *)0x0) {
        (**(code **)(lVar21 + 0x68))(*(undefined4 *)(lVar21 + 0x70));
      }
      lVar24 = *(longlong *)(lVar21 + 0x58);
      if (lVar24 != 0) goto LAB_180093544;
      piVar11 = (int *)(lVar21 + 0x7c);
    }
    else {
LAB_180093544:
      iVar41 = *(int *)(lVar21 + 0x78);
      if (iVar41 == 0) {
        piVar11 = (int *)(lVar21 + 0x7c);
      }
      else {
        uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
        if (iVar41 <= (int)uVar8) {
          uVar8 = iVar41 - 1;
        }
        piVar11 = (int *)(lVar24 + (longlong)(int)uVar8 * 4);
      }
    }
    *piVar11 = (int)fVar48;
    iVar38 = iVar38 + -1;
    if (iVar38 < 0) {
      return;
    }
  } while( true );
}

