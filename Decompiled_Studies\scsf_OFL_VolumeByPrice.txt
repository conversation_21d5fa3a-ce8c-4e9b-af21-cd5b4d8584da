
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */
/* WARNING: Removing unreachable block (ram,0x0001800817c8) */
/* WARNING: Removing unreachable block (ram,0x0001800817d0) */
/* WARNING: Type propagation algorithm not settling */

void scsf_OFL_VolumeByPrice
               (undefined1 param_1 [16],undefined1 param_2 [16],int *param_3,undefined8 param_4,
               undefined8 param_5,undefined8 param_6,ulonglong param_7,ulonglong param_8,
               ulonglong param_9,undefined8 param_10)

{
  longlong *plVar1;
  undefined1 *puVar2;
  undefined8 *******pppppppuVar3;
  undefined1 uVar4;
  undefined1 auVar5 [16];
  undefined1 auVar6 [16];
  undefined1 auVar7 [16];
  undefined1 auVar8 [16];
  undefined1 auVar9 [16];
  undefined1 auVar10 [16];
  undefined1 auVar11 [16];
  undefined1 auVar12 [16];
  undefined1 auVar13 [16];
  uint *puVar14;
  bool bVar15;
  byte bVar16;
  char cVar17;
  undefined4 uVar18;
  uint uVar19;
  uint uVar20;
  int iVar21;
  uint uVar22;
  undefined4 uVar23;
  uint uVar24;
  uint uVar25;
  undefined8 *puVar26;
  undefined8 *puVar27;
  char *pcVar28;
  HANDLE pvVar29;
  undefined8 uVar30;
  longlong *plVar31;
  char *pcVar32;
  undefined8 extraout_x0;
  undefined8 extraout_x0_00;
  longlong *plVar33;
  undefined4 *puVar34;
  float *pfVar35;
  int *piVar36;
  ulonglong uVar37;
  ulonglong uVar38;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  undefined8 extraout_x1_10;
  undefined8 extraout_x1_11;
  undefined8 extraout_x1_12;
  undefined8 extraout_x1_13;
  undefined8 extraout_x1_14;
  undefined8 extraout_x1_15;
  undefined8 extraout_x1_16;
  undefined8 extraout_x1_17;
  undefined8 extraout_x1_18;
  undefined8 extraout_x1_19;
  undefined8 extraout_x1_20;
  undefined8 extraout_x1_21;
  undefined8 extraout_x1_22;
  undefined8 extraout_x1_23;
  undefined8 extraout_x1_24;
  undefined8 extraout_x1_25;
  undefined8 extraout_x1_26;
  undefined8 extraout_x1_27;
  undefined8 extraout_x1_28;
  undefined8 extraout_x1_29;
  undefined8 extraout_x1_30;
  undefined8 extraout_x1_31;
  undefined8 extraout_x1_32;
  undefined8 extraout_x1_33;
  undefined8 extraout_x1_34;
  undefined8 extraout_x1_35;
  undefined8 extraout_x1_36;
  undefined8 extraout_x1_37;
  undefined8 extraout_x1_38;
  undefined8 extraout_x1_39;
  undefined8 extraout_x1_40;
  undefined8 extraout_x1_41;
  undefined8 extraout_x1_42;
  undefined8 extraout_x1_43;
  undefined8 extraout_x1_44;
  undefined8 extraout_x1_45;
  undefined8 extraout_x1_46;
  undefined8 extraout_x1_47;
  undefined8 extraout_x1_48;
  undefined8 extraout_x1_49;
  undefined8 extraout_x1_50;
  undefined8 extraout_x1_51;
  undefined8 extraout_x1_52;
  undefined8 extraout_x1_53;
  undefined8 extraout_x1_54;
  undefined8 extraout_x1_55;
  undefined8 extraout_x1_56;
  undefined8 extraout_x1_57;
  undefined8 extraout_x1_58;
  undefined8 extraout_x1_59;
  undefined8 extraout_x1_60;
  undefined8 extraout_x1_61;
  undefined8 extraout_x1_62;
  undefined8 extraout_x1_63;
  undefined8 extraout_x1_64;
  undefined8 extraout_x1_65;
  undefined8 extraout_x1_66;
  undefined8 extraout_x1_67;
  undefined8 extraout_x1_68;
  undefined8 extraout_x1_69;
  undefined8 extraout_x1_70;
  undefined8 extraout_x1_71;
  undefined8 extraout_x1_72;
  undefined8 extraout_x1_73;
  undefined8 extraout_x1_74;
  TypeDescriptor *pTVar39;
  int iVar40;
  int iVar41;
  ulonglong *puVar42;
  longlong lVar43;
  code *extraout_x9;
  longlong lVar44;
  undefined8 *extraout_x9_00;
  longlong extraout_x10;
  uint extraout_w11;
  uint extraout_w11_00;
  uint extraout_w11_01;
  int extraout_w11_02;
  int extraout_w11_03;
  int extraout_w11_04;
  longlong extraout_x11;
  ulonglong extraout_x11_00;
  ulonglong extraout_x11_01;
  longlong *extraout_x11_02;
  uint extraout_w12;
  int extraout_w12_00;
  longlong *extraout_x12;
  longlong extraout_x13;
  longlong extraout_x13_00;
  longlong *extraout_x13_01;
  int iVar45;
  int iVar46;
  code *pcVar47;
  ulonglong uVar48;
  ulonglong *puVar49;
  undefined8 uVar50;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  undefined4 extraout_s0_02;
  undefined4 extraout_s0_03;
  undefined4 extraout_s0_04;
  undefined4 extraout_s0_05;
  undefined4 extraout_s0_06;
  undefined4 extraout_s0_07;
  undefined4 extraout_s0_08;
  undefined4 extraout_s0_09;
  undefined4 extraout_s0_10;
  undefined4 extraout_s0_11;
  undefined4 extraout_s0_12;
  undefined4 extraout_s0_13;
  undefined4 extraout_s0_14;
  undefined4 extraout_s0_15;
  undefined4 extraout_s0_16;
  undefined4 extraout_s0_17;
  undefined4 extraout_s0_18;
  undefined4 extraout_s0_19;
  undefined4 extraout_s0_20;
  undefined4 extraout_s0_21;
  undefined4 extraout_s0_22;
  undefined4 extraout_s0_23;
  undefined4 extraout_s0_24;
  undefined4 extraout_s0_25;
  undefined4 extraout_s0_26;
  undefined4 extraout_s0_27;
  undefined4 extraout_s0_28;
  undefined4 extraout_s0_29;
  undefined4 extraout_s0_30;
  undefined4 extraout_s0_31;
  undefined4 extraout_s0_32;
  undefined4 extraout_s0_33;
  undefined4 extraout_s0_34;
  undefined4 extraout_s0_35;
  undefined4 extraout_s0_36;
  undefined4 extraout_s0_37;
  undefined4 extraout_s0_38;
  undefined4 extraout_s0_39;
  undefined4 extraout_s0_40;
  undefined4 extraout_s0_41;
  undefined4 extraout_s0_42;
  undefined4 extraout_s0_43;
  undefined4 extraout_s0_44;
  undefined4 extraout_s0_45;
  undefined4 extraout_s0_46;
  undefined4 extraout_s0_47;
  undefined4 extraout_s0_48;
  undefined4 extraout_s0_49;
  undefined4 extraout_s0_50;
  undefined4 extraout_s0_51;
  undefined4 extraout_s0_52;
  undefined4 extraout_s0_53;
  undefined4 extraout_s0_54;
  undefined4 extraout_s0_55;
  undefined4 extraout_s0_56;
  undefined4 extraout_s0_57;
  undefined4 extraout_s0_58;
  undefined4 extraout_s0_59;
  undefined4 extraout_s0_60;
  undefined4 extraout_s0_61;
  undefined4 extraout_s0_62;
  undefined4 extraout_s0_63;
  undefined4 extraout_s0_64;
  undefined4 extraout_s0_65;
  undefined4 extraout_s0_66;
  undefined4 extraout_s0_67;
  undefined4 extraout_s0_68;
  undefined4 extraout_s0_69;
  undefined4 extraout_s0_70;
  undefined4 extraout_s0_71;
  undefined4 extraout_s0_72;
  undefined4 extraout_s0_73;
  undefined4 extraout_s0_74;
  undefined4 extraout_s0_75;
  undefined4 extraout_s0_76;
  undefined4 uVar51;
  undefined4 extraout_s0_77;
  undefined4 extraout_s0_78;
  undefined4 extraout_s0_79;
  undefined4 extraout_s0_80;
  undefined4 extraout_s0_81;
  undefined4 extraout_s0_82;
  float fVar52;
  undefined4 extraout_var;
  undefined4 extraout_var_00;
  undefined4 extraout_var_01;
  undefined4 extraout_var_02;
  undefined4 extraout_var_03;
  undefined4 extraout_var_04;
  undefined4 extraout_var_05;
  undefined4 extraout_var_06;
  undefined4 extraout_var_07;
  undefined4 extraout_var_08;
  undefined4 extraout_var_09;
  undefined4 extraout_var_10;
  undefined4 extraout_var_11;
  undefined4 extraout_var_12;
  undefined4 extraout_var_13;
  undefined4 extraout_var_14;
  undefined4 extraout_var_15;
  undefined4 extraout_var_16;
  undefined4 extraout_var_17;
  undefined4 extraout_var_18;
  undefined4 extraout_var_19;
  undefined4 extraout_var_20;
  undefined4 extraout_var_21;
  undefined4 extraout_var_22;
  undefined4 extraout_var_23;
  undefined4 extraout_var_24;
  undefined4 extraout_var_25;
  undefined4 extraout_var_26;
  undefined4 extraout_var_27;
  undefined4 extraout_var_28;
  undefined4 extraout_var_29;
  undefined4 extraout_var_30;
  undefined4 extraout_var_31;
  undefined4 extraout_var_32;
  undefined4 extraout_var_33;
  undefined4 extraout_var_34;
  undefined4 extraout_var_35;
  undefined4 extraout_var_36;
  undefined4 extraout_var_37;
  undefined4 extraout_var_38;
  undefined4 extraout_var_39;
  undefined4 extraout_var_40;
  undefined4 extraout_var_41;
  undefined4 extraout_var_42;
  undefined4 extraout_var_43;
  undefined4 extraout_var_44;
  undefined4 extraout_var_45;
  undefined4 extraout_var_46;
  undefined4 extraout_var_47;
  undefined4 extraout_var_48;
  undefined4 extraout_var_49;
  undefined4 extraout_var_50;
  undefined4 extraout_var_51;
  undefined4 extraout_var_52;
  undefined4 extraout_var_53;
  undefined4 extraout_var_54;
  undefined4 extraout_var_55;
  undefined4 extraout_var_56;
  undefined4 extraout_var_57;
  undefined4 extraout_var_58;
  undefined4 extraout_var_59;
  undefined4 extraout_var_60;
  undefined4 extraout_var_61;
  undefined4 extraout_var_62;
  undefined4 extraout_var_63;
  undefined4 extraout_var_64;
  undefined4 extraout_var_65;
  undefined4 extraout_var_66;
  undefined4 extraout_var_67;
  undefined4 extraout_var_68;
  undefined4 extraout_var_69;
  undefined4 extraout_var_70;
  undefined4 extraout_var_71;
  undefined4 extraout_var_72;
  undefined4 extraout_var_73;
  undefined4 extraout_var_74;
  undefined4 extraout_var_75;
  undefined4 extraout_var_76;
  undefined4 uVar53;
  undefined4 extraout_var_77;
  undefined4 extraout_var_78;
  undefined4 uVar54;
  undefined4 extraout_var_79;
  undefined4 extraout_var_80;
  undefined4 extraout_var_81;
  undefined4 extraout_var_82;
  undefined8 extraout_var_83;
  undefined8 extraout_var_84;
  undefined8 extraout_var_85;
  undefined8 extraout_var_86;
  undefined8 extraout_var_87;
  undefined8 extraout_var_88;
  undefined8 extraout_var_89;
  undefined8 extraout_var_90;
  undefined8 extraout_var_91;
  undefined8 extraout_var_92;
  undefined8 extraout_var_93;
  undefined8 extraout_var_94;
  undefined8 extraout_var_95;
  undefined8 extraout_var_96;
  undefined8 extraout_var_97;
  undefined8 extraout_var_98;
  undefined8 extraout_var_99;
  undefined8 extraout_var_x00100;
  undefined8 extraout_var_x00101;
  undefined8 extraout_var_x00102;
  undefined8 extraout_var_x00103;
  undefined8 extraout_var_x00104;
  undefined8 extraout_var_x00105;
  undefined8 extraout_var_x00106;
  undefined8 extraout_var_x00107;
  undefined8 extraout_var_x00108;
  undefined8 extraout_var_x00109;
  undefined8 extraout_var_x00110;
  undefined8 extraout_var_x00111;
  undefined8 extraout_var_x00112;
  undefined8 extraout_var_x00113;
  undefined8 extraout_var_x00114;
  undefined8 extraout_var_x00115;
  undefined8 extraout_var_x00116;
  undefined8 extraout_var_x00117;
  undefined8 extraout_var_x00118;
  undefined8 extraout_var_x00119;
  undefined8 extraout_var_x00120;
  undefined8 extraout_var_x00121;
  undefined8 extraout_var_x00122;
  undefined8 extraout_var_x00123;
  undefined8 extraout_var_x00124;
  undefined8 extraout_var_x00125;
  undefined8 extraout_var_x00126;
  undefined8 extraout_var_x00127;
  undefined8 extraout_var_x00128;
  undefined8 extraout_var_x00129;
  undefined8 extraout_var_x00130;
  undefined8 extraout_var_x00131;
  undefined8 extraout_var_x00132;
  undefined8 extraout_var_x00133;
  undefined8 extraout_var_x00134;
  undefined8 extraout_var_x00135;
  undefined8 extraout_var_x00136;
  undefined8 extraout_var_x00137;
  undefined8 extraout_var_x00138;
  undefined8 extraout_var_x00139;
  undefined8 extraout_var_x00140;
  undefined8 extraout_var_x00141;
  undefined8 extraout_var_x00142;
  undefined8 extraout_var_x00143;
  undefined8 extraout_var_x00144;
  undefined8 extraout_var_x00145;
  undefined8 extraout_var_x00146;
  undefined8 extraout_var_x00147;
  undefined8 extraout_var_x00148;
  undefined8 extraout_var_x00149;
  undefined8 extraout_var_x00150;
  undefined8 extraout_var_x00151;
  undefined8 extraout_var_x00152;
  undefined8 extraout_var_x00153;
  undefined8 extraout_var_x00154;
  undefined8 extraout_var_x00155;
  undefined8 extraout_var_x00156;
  undefined8 extraout_var_x00157;
  undefined8 extraout_var_x00158;
  undefined8 extraout_var_x00159;
  undefined8 extraout_var_x00160;
  undefined8 extraout_var_x00161;
  undefined8 extraout_var_x00162;
  undefined8 extraout_var_x00163;
  undefined8 extraout_var_x00164;
  undefined8 extraout_var_x00165;
  undefined8 extraout_var_x00166;
  undefined4 uVar55;
  float fVar56;
  float fVar57;
  float fVar58;
  float extraout_s18;
  float extraout_s18_00;
  float extraout_s19;
  float extraout_s20;
  longlong *local_b00;
  longlong *local_af0;
  longlong *local_ae8;
  longlong *local_ae0;
  longlong *local_ad8;
  int *local_ad0;
  int *local_ac8;
  longlong *local_ac0;
  longlong *local_ab0;
  longlong *local_aa8;
  longlong *local_aa0;
  longlong *local_a98;
  int *local_a90;
  longlong *local_a88;
  longlong *local_a80;
  longlong *local_a78;
  longlong *local_a70;
  longlong *local_a68;
  longlong *local_a60;
  int *local_a58;
  longlong *local_a50;
  int *local_a48;
  longlong *local_a40;
  longlong *local_a38;
  longlong *local_a30;
  int *local_a28;
  int *local_a20;
  int *local_a18;
  longlong *local_a10;
  longlong *local_a08;
  undefined8 local_a00;
  longlong *local_9f8;
  longlong *local_9f0;
  longlong *local_9e8;
  longlong *local_9e0;
  longlong *local_9d8;
  longlong *local_9d0;
  longlong *local_9c8;
  longlong *local_9c0;
  longlong *local_9b8;
  longlong *local_9b0;
  longlong *local_9a8;
  longlong *local_9a0;
  longlong *local_998;
  longlong *local_990;
  longlong *local_988;
  longlong *local_980;
  longlong *local_978;
  longlong *local_970;
  longlong *local_968;
  longlong *local_960;
  longlong *local_958;
  longlong *local_950;
  longlong *local_948;
  longlong *local_940;
  longlong *local_938;
  longlong *local_930;
  int *local_928;
  longlong *local_920;
  int *local_918;
  int *local_910;
  int *local_908;
  int *local_900;
  int *local_8f8;
  int *local_8f0;
  int *local_8e8;
  int *local_8e0;
  int *local_8d8;
  int *local_8d0;
  int *local_8c8;
  longlong *local_8c0;
  longlong *local_8b8;
  longlong *local_8b0;
  longlong *local_8a8;
  longlong *local_8a0;
  longlong *local_898;
  longlong *local_890;
  longlong *local_888;
  longlong *local_880;
  longlong *local_878;
  longlong *local_870;
  longlong *local_868;
  longlong *local_860;
  longlong *local_858;
  int *local_850;
  longlong *local_848;
  longlong *local_840;
  longlong *local_838;
  int *local_830;
  uint *local_828;
  longlong *local_820;
  longlong *local_818;
  longlong *local_810;
  longlong *local_808;
  longlong *local_800;
  longlong *local_7f8;
  longlong *local_7f0;
  longlong *local_7e8;
  int *local_7e0;
  int *local_7d8;
  longlong *local_7d0;
  longlong *local_7c8;
  longlong *local_7c0;
  longlong *local_7b8;
  longlong *local_7b0;
  longlong *local_7a8;
  int *local_7a0;
  undefined8 *******local_790;
  undefined8 uStack_788;
  undefined8 local_780;
  ulonglong local_778;
  undefined8 *******local_770;
  undefined8 uStack_768;
  undefined8 local_760;
  ulonglong local_758;
  undefined8 *******local_750;
  undefined8 uStack_748;
  undefined8 local_740;
  ulonglong local_738;
  undefined8 *******local_730;
  undefined8 uStack_728;
  undefined8 local_720;
  ulonglong local_718;
  int *local_710;
  int *local_708;
  int *local_700;
  longlong *local_6f8;
  longlong *local_6f0;
  int *local_6e8;
  longlong *local_6e0;
  longlong *local_6d8;
  longlong *local_6d0;
  longlong *local_6c8;
  longlong *local_6c0;
  longlong *local_6b8;
  longlong *local_6b0;
  longlong *local_6a8;
  longlong *local_6a0;
  undefined8 *local_698;
  longlong *local_690;
  longlong *local_688;
  longlong *local_680;
  longlong *local_678;
  longlong *local_670;
  longlong *local_668;
  longlong *local_660;
  longlong *local_658;
  longlong *local_650;
  int *local_648;
  longlong *local_640;
  int *local_638;
  int *local_630;
  longlong *local_628;
  longlong *local_620;
  longlong local_618;
  longlong *local_610;
  longlong *local_608;
  longlong *local_600;
  uint *local_5f8;
  uint *local_5f0;
  uint *local_5e8;
  undefined8 *******local_5e0;
  undefined8 uStack_5d8;
  undefined8 local_5d0;
  ulonglong local_5c8;
  undefined8 *******local_5c0;
  undefined8 uStack_5b8;
  undefined8 local_5b0;
  ulonglong local_5a8;
  int *local_5a0;
  longlong *local_598;
  int *local_590;
  int *local_588;
  int *local_580;
  int *local_578;
  int *local_570;
  int *local_568;
  int *local_560;
  int *local_558;
  uint *local_550;
  longlong *local_548;
  int *local_540;
  undefined4 *local_538;
  int *local_530;
  int *local_528;
  longlong lStack_520;
  undefined1 local_518;
  undefined7 uStack_517;
  undefined8 local_508;
  ulonglong local_500;
  undefined8 local_4f8;
  longlong lStack_4f0;
  undefined8 auStack_4e8 [3];
  char *local_4d0;
  undefined8 uStack_4c8;
  undefined1 *local_4c0;
  uint *local_4b0;
  longlong *local_4a8;
  undefined **local_4a0;
  undefined **local_498;
  undefined1 *local_490;
  undefined8 uStack_488;
  undefined1 *local_480;
  undefined **local_470;
  undefined **local_468;
  undefined **local_460;
  undefined **local_458;
  ulonglong *local_450;
  ulonglong *local_448;
  undefined8 local_440;
  undefined **local_438;
  undefined8 local_430;
  undefined8 uStack_428;
  longlong lStack_420;
  undefined8 uStack_418;
  undefined8 local_410;
  undefined1 auStack_408 [5];
  bool bStack_403;
  bool bStack_402;
  undefined1 uStack_401;
  undefined8 local_400;
  uint local_3f8;
  undefined4 local_3f4;
  uint local_3f0;
  undefined4 uStack_3ec;
  float fStack_3e8;
  undefined4 uStack_3e4;
  undefined8 local_3e0;
  undefined8 local_3d8;
  undefined8 local_3d0;
  longlong lStack_3c8;
  longlong local_3c0;
  undefined8 local_3b8;
  undefined8 local_3b0;
  undefined8 uStack_3a8;
  ulonglong local_3a0;
  undefined8 local_398;
  uint local_390;
  uint uStack_38c;
  undefined4 uStack_388;
  uint uStack_384;
  undefined4 local_380;
  undefined4 local_37c;
  undefined4 local_378;
  undefined1 local_374;
  undefined1 local_373;
  undefined1 local_372;
  undefined1 local_371;
  undefined4 local_370;
  undefined4 uStack_36c;
  undefined1 uStack_368;
  undefined1 uStack_367;
  undefined2 uStack_366;
  undefined4 uStack_364;
  undefined4 local_360;
  undefined4 uStack_35c;
  undefined4 local_358;
  uint local_354;
  int local_350;
  uint uStack_34c;
  undefined8 uStack_348;
  undefined8 uStack_340;
  undefined8 local_338;
  undefined8 local_330;
  undefined8 uStack_328;
  undefined8 local_320;
  undefined8 uStack_318;
  undefined8 local_310;
  undefined8 uStack_308;
  longlong local_300;
  longlong lStack_2f8;
  longlong local_2e8;
  longlong lStack_2e0;
  longlong local_2d0;
  longlong lStack_2c8;
  longlong local_2b8;
  longlong lStack_2b0;
  longlong local_2a0;
  longlong lStack_298;
  undefined1 *local_280;
  undefined8 uStack_278;
  undefined1 *local_270;
  longlong local_260;
  undefined8 uStack_258;
  undefined8 uStack_250;
  undefined8 uStack_248;
  undefined8 local_240;
  undefined8 uStack_238;
  undefined8 uStack_230;
  undefined8 uStack_228;
  undefined8 local_220;
  undefined8 uStack_218;
  undefined8 uStack_210;
  undefined8 uStack_208;
  undefined8 local_200;
  undefined8 uStack_1f8;
  undefined8 uStack_1f0;
  undefined8 uStack_1e8;
  undefined8 local_1e0;
  undefined8 local_1d0;
  undefined8 uStack_1c8;
  undefined8 uStack_1c0;
  undefined8 uStack_1b8;
  undefined8 local_1b0;
  undefined8 uStack_1a8;
  undefined8 uStack_1a0;
  undefined8 uStack_198;
  undefined8 local_190;
  undefined8 uStack_188;
  undefined8 uStack_180;
  undefined8 uStack_178;
  undefined8 local_170;
  undefined8 uStack_168;
  undefined8 uStack_160;
  undefined8 uStack_158;
  undefined8 local_150;
  undefined8 uStack_148;
  undefined8 uStack_140;
  undefined8 uStack_138;
  undefined8 local_130;
  undefined8 uStack_128;
  undefined8 uStack_120;
  undefined8 uStack_118;
  undefined8 local_110;
  undefined8 uStack_108;
  undefined8 uStack_100;
  undefined8 uStack_f8;
  undefined8 local_f0;
  undefined8 uStack_e8;
  undefined8 uStack_e0;
  undefined8 uStack_d8;
  undefined8 local_d0;
  undefined8 uStack_c8;
  undefined8 uStack_c0;
  undefined8 uStack_b8;
  undefined8 local_b0;
  undefined8 uStack_a8;
  
  uVar55 = param_2._4_4_;
                    /* 0x7f6a8  20  scsf_OFL_VolumeByPrice */
  uVar23 = param_2._0_4_;
  local_4f8 = 0xfffffffffffffffe;
  local_270 = &DAT_1800d4ecd;
  local_280 = &DAT_1800d4ecd;
  uStack_278 = 0;
  (**(code **)(param_3 + 0x478))(param_1._0_4_,0x20241105);
  local_4a8 = (longlong *)(**(code **)(param_3 + 0x62e))(0);
  local_5f0 = (uint *)(**(code **)(param_3 + 0x62e))(2);
  local_5f8 = (uint *)(**(code **)(param_3 + 0x62e))(3);
  local_538 = (undefined4 *)(**(code **)(param_3 + 0x62e))(7);
  local_828 = (uint *)(**(code **)(param_3 + 0x62e))(8);
  local_550 = (uint *)(**(code **)(param_3 + 0x62e))(9);
  local_5e8 = (uint *)(**(code **)(param_3 + 0x62e))(10);
  local_4b0 = (uint *)(**(code **)(param_3 + 0x62e))(0xb);
  local_540 = (int *)(**(code **)(param_3 + 0x62e))(0xc);
  local_698 = (undefined8 *)(**(code **)(param_3 + 0x62e))(0xd);
  puVar26 = (undefined8 *)(**(code **)(param_3 + 0x634))(0);
  puVar27 = (undefined8 *)(**(code **)(param_3 + 0x446))(4);
  lVar43 = *(longlong *)(param_3 + 0x140);
  local_618 = 0;
  local_b00 = (longlong *)*puVar27;
  if (lVar43 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007f820;
    local_530 = param_3 + 0x14a;
LAB_18007f85c:
    local_ac8 = local_530;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007f888;
    local_528 = param_3 + 0x14a;
LAB_18007f8bc:
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007f8ec;
    local_928 = param_3 + 0x14a;
LAB_18007f928:
    local_ad0 = local_928;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007f958;
    local_5a0 = param_3 + 0x14a;
LAB_18007f994:
    local_a18 = local_5a0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007f9b4;
    local_830 = param_3 + 0x14a;
  }
  else {
LAB_18007f820:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_ac8 = param_3 + 0x14a;
    }
    else {
      iVar21 = 0;
      if (iVar40 < 1) {
        iVar21 = iVar40 + -1;
      }
      local_ac8 = (int *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_530 = local_ac8;
    if (lVar43 == 0) goto LAB_18007f85c;
LAB_18007f888:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_528 = param_3 + 0x14a;
    }
    else {
      iVar21 = 1;
      if (iVar40 + -1 == 0 || iVar40 < 1) {
        iVar21 = iVar40 + -1;
      }
      local_528 = (int *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    if (lVar43 == 0) goto LAB_18007f8bc;
LAB_18007f8ec:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_ad0 = param_3 + 0x14a;
    }
    else {
      iVar21 = 2;
      if (iVar40 < 3) {
        iVar21 = iVar40 + -1;
      }
      local_ad0 = (int *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_928 = local_ad0;
    if (lVar43 == 0) goto LAB_18007f928;
LAB_18007f958:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_a18 = param_3 + 0x14a;
    }
    else {
      iVar21 = 3;
      if (iVar40 < 4) {
        iVar21 = iVar40 + -1;
      }
      local_a18 = (int *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_5a0 = local_a18;
    if (lVar43 == 0) goto LAB_18007f994;
LAB_18007f9b4:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_830 = param_3 + 0x14a;
    }
    else {
      iVar21 = 4;
      if (iVar40 < 5) {
        iVar21 = iVar40 + -1;
      }
      local_830 = (int *)(lVar43 + (longlong)iVar21 * 0x170);
    }
  }
  local_608 = FUN_18000f880((longlong *)(param_3 + 0x140),5);
  local_610 = FUN_18000f880((longlong *)(param_3 + 0x140),6);
  local_600 = FUN_18000f880((longlong *)(param_3 + 0x140),7);
  local_548 = FUN_18000f880((longlong *)(param_3 + 0x140),8);
  lVar43 = *(longlong *)(param_3 + 0x140);
  iVar40 = 9;
  uVar30 = extraout_x1;
  uVar18 = extraout_s0;
  uVar54 = extraout_var;
  uVar50 = extraout_var_83;
  if (lVar43 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_00;
      uVar18 = extraout_s0_00;
      uVar54 = extraout_var_00;
      uVar50 = extraout_var_84;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007fa5c;
    local_7a8 = (longlong *)(param_3 + 0x14a);
LAB_18007fa94:
    local_898 = local_7a8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_01;
      uVar18 = extraout_s0_01;
      uVar54 = extraout_var_01;
      uVar50 = extraout_var_85;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007fac4;
    local_820 = (longlong *)(param_3 + 0x14a);
LAB_18007fb00:
    local_890 = local_820;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_02;
      uVar18 = extraout_s0_02;
      uVar54 = extraout_var_02;
      uVar50 = extraout_var_86;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007fb30;
    local_818 = (longlong *)(param_3 + 0x14a);
LAB_18007fb6c:
    local_878 = local_818;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_03;
      uVar18 = extraout_s0_03;
      uVar54 = extraout_var_03;
      uVar50 = extraout_var_87;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007fb9c;
    local_810 = (longlong *)(param_3 + 0x14a);
LAB_18007fbd8:
    local_888 = local_810;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_04;
      uVar18 = extraout_s0_04;
      uVar54 = extraout_var_04;
      uVar50 = extraout_var_88;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007fc08;
    local_808 = (longlong *)(param_3 + 0x14a);
LAB_18007fc44:
    local_880 = local_808;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_05;
      uVar18 = extraout_s0_05;
      uVar54 = extraout_var_05;
      uVar50 = extraout_var_89;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007fc74;
    local_800 = (longlong *)(param_3 + 0x14a);
LAB_18007fcb0:
    local_858 = local_800;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_06;
      uVar18 = extraout_s0_06;
      uVar54 = extraout_var_06;
      uVar50 = extraout_var_90;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007fce0;
    local_7f8 = (longlong *)(param_3 + 0x14a);
LAB_18007fd1c:
    local_870 = local_7f8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_07;
      uVar18 = extraout_s0_07;
      uVar54 = extraout_var_07;
      uVar50 = extraout_var_91;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007fd4c;
    local_7f0 = (longlong *)(param_3 + 0x14a);
LAB_18007fd88:
    local_868 = local_7f0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_08;
      uVar18 = extraout_s0_08;
      uVar54 = extraout_var_08;
      uVar50 = extraout_var_92;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007fdb8;
    local_598 = (longlong *)(param_3 + 0x14a);
LAB_18007fdf4:
    local_a70 = local_598;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_09;
      uVar18 = extraout_s0_09;
      uVar54 = extraout_var_09;
      uVar50 = extraout_var_93;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007fe24;
    local_8b8 = (longlong *)(param_3 + 0x14a);
LAB_18007fe60:
    local_ac0 = local_8b8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_10;
      uVar18 = extraout_s0_10;
      uVar54 = extraout_var_10;
      uVar50 = extraout_var_94;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007fe90;
    local_8b0 = (longlong *)(param_3 + 0x14a);
LAB_18007fecc:
    local_ae0 = local_8b0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_11;
      uVar18 = extraout_s0_11;
      uVar54 = extraout_var_11;
      uVar50 = extraout_var_95;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007fefc;
    local_8a8 = (longlong *)(param_3 + 0x14a);
LAB_18007ff38:
    local_aa0 = local_8a8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_12;
      uVar18 = extraout_s0_12;
      uVar54 = extraout_var_12;
      uVar50 = extraout_var_96;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007ff68;
    local_8a0 = (longlong *)(param_3 + 0x14a);
LAB_18007ffa4:
    local_a98 = local_8a0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_13;
      uVar18 = extraout_s0_13;
      uVar54 = extraout_var_13;
      uVar50 = extraout_var_97;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18007ffd4;
    local_7e8 = (longlong *)(param_3 + 0x14a);
LAB_180080010:
    local_860 = local_7e8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_14;
      uVar18 = extraout_s0_14;
      uVar54 = extraout_var_14;
      uVar50 = extraout_var_98;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_180080040;
    local_920 = (longlong *)(param_3 + 0x14a);
LAB_18008007c:
    local_a10 = local_920;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar30 = extraout_x1_15;
      uVar18 = extraout_s0_15;
      uVar54 = extraout_var_15;
      uVar50 = extraout_var_99;
    }
    lVar43 = *(longlong *)(param_3 + 0x140);
    if (lVar43 != 0) goto LAB_18008009c;
    local_7b0 = (longlong *)(param_3 + 0x14a);
  }
  else {
LAB_18007fa5c:
    iVar21 = param_3[0x148];
    if (iVar21 == 0) {
      local_898 = (longlong *)(param_3 + 0x14a);
    }
    else {
      if (iVar21 < 10) {
        iVar40 = iVar21 + -1;
      }
      local_898 = (longlong *)(lVar43 + (longlong)iVar40 * 0x170);
    }
    local_7a8 = local_898;
    if (lVar43 == 0) goto LAB_18007fa94;
LAB_18007fac4:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_890 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar21 = 10;
      if (iVar40 < 0xb) {
        iVar21 = iVar40 + -1;
      }
      local_890 = (longlong *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_820 = local_890;
    if (lVar43 == 0) goto LAB_18007fb00;
LAB_18007fb30:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_878 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar21 = 0xb;
      if (iVar40 < 0xc) {
        iVar21 = iVar40 + -1;
      }
      local_878 = (longlong *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_818 = local_878;
    if (lVar43 == 0) goto LAB_18007fb6c;
LAB_18007fb9c:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_888 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar21 = 0xc;
      if (iVar40 < 0xd) {
        iVar21 = iVar40 + -1;
      }
      local_888 = (longlong *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_810 = local_888;
    if (lVar43 == 0) goto LAB_18007fbd8;
LAB_18007fc08:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_880 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar21 = 0xd;
      if (iVar40 < 0xe) {
        iVar21 = iVar40 + -1;
      }
      local_880 = (longlong *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_808 = local_880;
    if (lVar43 == 0) goto LAB_18007fc44;
LAB_18007fc74:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_858 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar21 = 0xe;
      if (iVar40 < 0xf) {
        iVar21 = iVar40 + -1;
      }
      local_858 = (longlong *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_800 = local_858;
    if (lVar43 == 0) goto LAB_18007fcb0;
LAB_18007fce0:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_870 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar21 = 0xf;
      if (iVar40 < 0x10) {
        iVar21 = iVar40 + -1;
      }
      local_870 = (longlong *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_7f8 = local_870;
    if (lVar43 == 0) goto LAB_18007fd1c;
LAB_18007fd4c:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_868 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar21 = 0x10;
      if (iVar40 < 0x11) {
        iVar21 = iVar40 + -1;
      }
      local_868 = (longlong *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_7f0 = local_868;
    if (lVar43 == 0) goto LAB_18007fd88;
LAB_18007fdb8:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_a70 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar21 = 0x11;
      if (iVar40 < 0x12) {
        iVar21 = iVar40 + -1;
      }
      local_a70 = (longlong *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_598 = local_a70;
    if (lVar43 == 0) goto LAB_18007fdf4;
LAB_18007fe24:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_ac0 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar21 = 0x12;
      if (iVar40 < 0x13) {
        iVar21 = iVar40 + -1;
      }
      local_ac0 = (longlong *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_8b8 = local_ac0;
    if (lVar43 == 0) goto LAB_18007fe60;
LAB_18007fe90:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_ae0 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar21 = 0x13;
      if (iVar40 < 0x14) {
        iVar21 = iVar40 + -1;
      }
      local_ae0 = (longlong *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_8b0 = local_ae0;
    if (lVar43 == 0) goto LAB_18007fecc;
LAB_18007fefc:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_aa0 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar21 = 0x14;
      if (iVar40 < 0x15) {
        iVar21 = iVar40 + -1;
      }
      local_aa0 = (longlong *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_8a8 = local_aa0;
    if (lVar43 == 0) goto LAB_18007ff38;
LAB_18007ff68:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_a98 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar21 = 0x15;
      if (iVar40 < 0x16) {
        iVar21 = iVar40 + -1;
      }
      local_a98 = (longlong *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_8a0 = local_a98;
    if (lVar43 == 0) goto LAB_18007ffa4;
LAB_18007ffd4:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_860 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar21 = 0x16;
      if (iVar40 < 0x17) {
        iVar21 = iVar40 + -1;
      }
      local_860 = (longlong *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_7e8 = local_860;
    if (lVar43 == 0) goto LAB_180080010;
LAB_180080040:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_a10 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar21 = 0x17;
      if (iVar40 < 0x18) {
        iVar21 = iVar40 + -1;
      }
      local_a10 = (longlong *)(lVar43 + (longlong)iVar21 * 0x170);
    }
    local_920 = local_a10;
    if (lVar43 == 0) goto LAB_18008007c;
LAB_18008009c:
    iVar40 = param_3[0x148];
    if (iVar40 == 0) {
      local_7b0 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar21 = 0x18;
      if (iVar40 < 0x19) {
        iVar21 = iVar40 + -1;
      }
      local_7b0 = (longlong *)(lVar43 + (longlong)iVar21 * 0x170);
    }
  }
  lVar43 = *(longlong *)(param_3 + 0x84);
  if (lVar43 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_16;
      uVar18 = extraout_s0_16;
      uVar54 = extraout_var_16;
      uVar50 = extraout_var_x00100;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080108;
    local_848 = (longlong *)(param_3 + 0x8e);
LAB_180080140:
    local_ab0 = local_848;
    local_a60 = local_848;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_17;
      uVar18 = extraout_s0_17;
      uVar54 = extraout_var_17;
      uVar50 = extraout_var_x00101;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080170;
    local_708 = param_3 + 0x8e;
LAB_1800801a4:
    local_a28 = local_708;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_18;
      uVar18 = extraout_s0_18;
      uVar54 = extraout_var_18;
      uVar50 = extraout_var_x00102;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800801d4;
    local_8c0 = (longlong *)(param_3 + 0x8e);
LAB_18008020c:
    local_af0 = local_8c0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_19;
      uVar18 = extraout_s0_19;
      uVar54 = extraout_var_19;
      uVar50 = extraout_var_x00103;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_18008023c;
    local_700 = param_3 + 0x8e;
LAB_180080274:
    local_a48 = local_700;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_20;
      uVar18 = extraout_s0_20;
      uVar54 = extraout_var_20;
      uVar50 = extraout_var_x00104;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800802a8;
    local_6f8 = (longlong *)(param_3 + 0x8e);
LAB_1800802e4:
    local_a80 = local_6f8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_21;
      uVar18 = extraout_s0_21;
      uVar54 = extraout_var_21;
      uVar50 = extraout_var_x00105;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080310;
    local_7a0 = param_3 + 0x8e;
LAB_180080344:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_22;
      uVar18 = extraout_s0_22;
      uVar54 = extraout_var_22;
      uVar50 = extraout_var_x00106;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080374;
    local_9e8 = (longlong *)(param_3 + 0x8e);
LAB_1800803ac:
    local_a50 = local_9e8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_23;
      uVar18 = extraout_s0_23;
      uVar54 = extraout_var_23;
      uVar50 = extraout_var_x00107;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800803dc;
    local_6d8 = (longlong *)(param_3 + 0x8e);
LAB_180080414:
    local_9e0 = local_6d8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_24;
      uVar18 = extraout_s0_24;
      uVar54 = extraout_var_24;
      uVar50 = extraout_var_x00108;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080444;
    local_6d0 = (longlong *)(param_3 + 0x8e);
LAB_18008047c:
    local_9d8 = local_6d0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_25;
      uVar18 = extraout_s0_25;
      uVar54 = extraout_var_25;
      uVar50 = extraout_var_x00109;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800804ac;
    local_6f0 = (longlong *)(param_3 + 0x8e);
LAB_1800804e4:
    local_aa8 = local_6f0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_26;
      uVar18 = extraout_s0_26;
      uVar54 = extraout_var_26;
      uVar50 = extraout_var_x00110;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080514;
    local_a40 = (longlong *)(param_3 + 0x8e);
LAB_18008054c:
    local_ae8 = local_a40;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_27;
      uVar18 = extraout_s0_27;
      uVar54 = extraout_var_27;
      uVar50 = extraout_var_x00111;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_18008057c;
    local_7e0 = param_3 + 0x8e;
LAB_1800805b4:
    local_a20 = local_7e0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_28;
      uVar18 = extraout_s0_28;
      uVar54 = extraout_var_28;
      uVar50 = extraout_var_x00112;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800805e4;
    local_7d0 = (longlong *)(param_3 + 0x8e);
LAB_18008061c:
    local_a68 = local_7d0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_29;
      uVar18 = extraout_s0_29;
      uVar54 = extraout_var_29;
      uVar50 = extraout_var_x00113;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_18008064c;
    local_950 = (longlong *)(param_3 + 0x8e);
LAB_180080684:
    local_a00 = local_950;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_30;
      uVar18 = extraout_s0_30;
      uVar54 = extraout_var_30;
      uVar50 = extraout_var_x00114;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800806b4;
    local_840 = (longlong *)(param_3 + 0x8e);
LAB_1800806ec:
    local_a08 = local_840;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_31;
      uVar18 = extraout_s0_31;
      uVar54 = extraout_var_31;
      uVar50 = extraout_var_x00115;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_18008071c;
    local_6b8 = (longlong *)(param_3 + 0x8e);
LAB_180080754:
    local_9c8 = local_6b8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_32;
      uVar18 = extraout_s0_32;
      uVar54 = extraout_var_32;
      uVar50 = extraout_var_x00116;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080784;
    local_6b0 = (longlong *)(param_3 + 0x8e);
LAB_1800807bc:
    local_9a0 = local_6b0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_33;
      uVar18 = extraout_s0_33;
      uVar54 = extraout_var_33;
      uVar50 = extraout_var_x00117;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800807ec;
    local_7c8 = (longlong *)(param_3 + 0x8e);
LAB_180080824:
    local_9b8 = local_7c8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_34;
      uVar18 = extraout_s0_34;
      uVar54 = extraout_var_34;
      uVar50 = extraout_var_x00118;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080854;
    local_6a8 = (longlong *)(param_3 + 0x8e);
LAB_18008088c:
    local_9b0 = local_6a8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_35;
      uVar18 = extraout_s0_35;
      uVar54 = extraout_var_35;
      uVar50 = extraout_var_x00119;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800808bc;
    local_6a0 = (longlong *)(param_3 + 0x8e);
LAB_1800808f4:
    local_9a8 = local_6a0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_36;
      uVar18 = extraout_s0_36;
      uVar54 = extraout_var_36;
      uVar50 = extraout_var_x00120;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080924;
    local_7c0 = (longlong *)(param_3 + 0x8e);
LAB_18008095c:
    local_998 = local_7c0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_37;
      uVar18 = extraout_s0_37;
      uVar54 = extraout_var_37;
      uVar50 = extraout_var_x00121;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_18008098c;
    local_6e8 = param_3 + 0x8e;
LAB_1800809c4:
    local_918 = local_6e8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_38;
      uVar18 = extraout_s0_38;
      uVar54 = extraout_var_38;
      uVar50 = extraout_var_x00122;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800809f4;
    local_690 = (longlong *)(param_3 + 0x8e);
LAB_180080a2c:
    local_990 = local_690;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_39;
      uVar18 = extraout_s0_39;
      uVar54 = extraout_var_39;
      uVar50 = extraout_var_x00123;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080a5c;
    local_980 = (longlong *)(param_3 + 0x8e);
LAB_180080a94:
    local_ad8 = local_980;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_40;
      uVar18 = extraout_s0_40;
      uVar54 = extraout_var_40;
      uVar50 = extraout_var_x00124;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080ac4;
    local_680 = (longlong *)(param_3 + 0x8e);
LAB_180080afc:
    local_970 = local_680;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_41;
      uVar18 = extraout_s0_41;
      uVar54 = extraout_var_41;
      uVar50 = extraout_var_x00125;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080b2c;
    local_678 = (longlong *)(param_3 + 0x8e);
LAB_180080b64:
    local_968 = local_678;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_42;
      uVar18 = extraout_s0_42;
      uVar54 = extraout_var_42;
      uVar50 = extraout_var_x00126;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080b94;
    local_7b8 = (longlong *)(param_3 + 0x8e);
LAB_180080bcc:
    local_978 = local_7b8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_43;
      uVar18 = extraout_s0_43;
      uVar54 = extraout_var_43;
      uVar50 = extraout_var_x00127;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080bfc;
    local_670 = (longlong *)(param_3 + 0x8e);
LAB_180080c34:
    local_960 = local_670;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_44;
      uVar18 = extraout_s0_44;
      uVar54 = extraout_var_44;
      uVar50 = extraout_var_x00128;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080c64;
    local_668 = (longlong *)(param_3 + 0x8e);
LAB_180080c9c:
    local_958 = local_668;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_45;
      uVar18 = extraout_s0_45;
      uVar54 = extraout_var_45;
      uVar50 = extraout_var_x00129;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080ccc;
    local_660 = (longlong *)(param_3 + 0x8e);
LAB_180080d04:
    local_a78 = local_660;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_46;
      uVar18 = extraout_s0_46;
      uVar54 = extraout_var_46;
      uVar50 = extraout_var_x00130;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080d34;
    local_658 = (longlong *)(param_3 + 0x8e);
LAB_180080d6c:
    local_9c0 = local_658;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_47;
      uVar18 = extraout_s0_47;
      uVar54 = extraout_var_47;
      uVar50 = extraout_var_x00131;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080d9c;
    local_838 = (longlong *)(param_3 + 0x8e);
LAB_180080dd4:
    local_9f8 = local_838;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_48;
      uVar18 = extraout_s0_48;
      uVar54 = extraout_var_48;
      uVar50 = extraout_var_x00132;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080e04;
    local_688 = (longlong *)(param_3 + 0x8e);
LAB_180080e3c:
    local_988 = local_688;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_49;
      uVar18 = extraout_s0_49;
      uVar54 = extraout_var_49;
      uVar50 = extraout_var_x00133;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080e6c;
    local_648 = param_3 + 0x8e;
LAB_180080ea4:
    local_a90 = local_648;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_50;
      uVar18 = extraout_s0_50;
      uVar54 = extraout_var_50;
      uVar50 = extraout_var_x00134;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080ed4;
    local_640 = (longlong *)(param_3 + 0x8e);
LAB_180080f0c:
    local_940 = local_640;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_51;
      uVar18 = extraout_s0_51;
      uVar54 = extraout_var_51;
      uVar50 = extraout_var_x00135;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080f3c;
    local_638 = param_3 + 0x8e;
LAB_180080f74:
    local_910 = local_638;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_52;
      uVar18 = extraout_s0_52;
      uVar54 = extraout_var_52;
      uVar50 = extraout_var_x00136;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180080fa4;
    local_6c8 = (longlong *)(param_3 + 0x8e);
LAB_180080fdc:
    local_938 = local_6c8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_53;
      uVar18 = extraout_s0_53;
      uVar54 = extraout_var_53;
      uVar50 = extraout_var_x00137;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_18008100c;
    local_710 = param_3 + 0x8e;
LAB_180081044:
    local_908 = local_710;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_54;
      uVar18 = extraout_s0_54;
      uVar54 = extraout_var_54;
      uVar50 = extraout_var_x00138;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180081074;
    local_590 = param_3 + 0x8e;
LAB_1800810ac:
    local_900 = local_590;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_55;
      uVar18 = extraout_s0_55;
      uVar54 = extraout_var_55;
      uVar50 = extraout_var_x00139;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800810dc;
    local_588 = param_3 + 0x8e;
LAB_180081114:
    local_8f8 = local_588;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_56;
      uVar18 = extraout_s0_56;
      uVar54 = extraout_var_56;
      uVar50 = extraout_var_x00140;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180081144;
    local_580 = param_3 + 0x8e;
LAB_18008117c:
    local_8f0 = local_580;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_57;
      uVar18 = extraout_s0_57;
      uVar54 = extraout_var_57;
      uVar50 = extraout_var_x00141;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800811ac;
    local_578 = param_3 + 0x8e;
LAB_1800811e4:
    local_8e8 = local_578;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_58;
      uVar18 = extraout_s0_58;
      uVar54 = extraout_var_58;
      uVar50 = extraout_var_x00142;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180081214;
    local_570 = param_3 + 0x8e;
LAB_18008124c:
    local_8e0 = local_570;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_59;
      uVar18 = extraout_s0_59;
      uVar54 = extraout_var_59;
      uVar50 = extraout_var_x00143;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_18008127c;
    local_568 = param_3 + 0x8e;
LAB_1800812b4:
    local_8c8 = local_568;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_60;
      uVar18 = extraout_s0_60;
      uVar54 = extraout_var_60;
      uVar50 = extraout_var_x00144;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800812e4;
    local_630 = param_3 + 0x8e;
LAB_18008131c:
    local_8d8 = local_630;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_61;
      uVar18 = extraout_s0_61;
      uVar54 = extraout_var_61;
      uVar50 = extraout_var_x00145;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_18008134c;
    local_650 = (longlong *)(param_3 + 0x8e);
LAB_180081384:
    local_948 = local_650;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_62;
      uVar18 = extraout_s0_62;
      uVar54 = extraout_var_62;
      uVar50 = extraout_var_x00146;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800813b4;
    local_628 = (longlong *)(param_3 + 0x8e);
LAB_1800813ec:
    local_a38 = local_628;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_63;
      uVar18 = extraout_s0_63;
      uVar54 = extraout_var_63;
      uVar50 = extraout_var_x00147;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_18008141c;
    local_620 = (longlong *)(param_3 + 0x8e);
LAB_180081454:
    local_a30 = local_620;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_64;
      uVar18 = extraout_s0_64;
      uVar54 = extraout_var_64;
      uVar50 = extraout_var_x00148;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180081480;
    piVar36 = param_3 + 0x8e;
LAB_1800814b4:
    local_7d8 = piVar36;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_65;
      uVar18 = extraout_s0_65;
      uVar54 = extraout_var_65;
      uVar50 = extraout_var_x00149;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800814e4;
    local_6e0 = (longlong *)(param_3 + 0x8e);
LAB_18008151c:
    local_9f0 = local_6e0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_66;
      uVar18 = extraout_s0_66;
      uVar54 = extraout_var_66;
      uVar50 = extraout_var_x00150;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_18008154c;
    local_558 = param_3 + 0x8e;
LAB_180081584:
    local_a58 = local_558;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_67;
      uVar18 = extraout_s0_67;
      uVar54 = extraout_var_67;
      uVar50 = extraout_var_x00151;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800815b4;
    local_560 = param_3 + 0x8e;
LAB_1800815ec:
    local_8d0 = local_560;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_68;
      uVar18 = extraout_s0_68;
      uVar54 = extraout_var_68;
      uVar50 = extraout_var_x00152;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_180081620;
    local_9d0 = (longlong *)(param_3 + 0x8e);
LAB_18008165c:
    local_a88 = local_9d0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_69;
      uVar18 = extraout_s0_69;
      uVar54 = extraout_var_69;
      uVar50 = extraout_var_x00153;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_18008168c;
    local_6c0 = (longlong *)(param_3 + 0x8e);
LAB_1800816c4:
    local_930 = local_6c0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar30 = extraout_x1_70;
      uVar18 = extraout_s0_70;
      uVar54 = extraout_var_70;
      uVar50 = extraout_var_x00154;
    }
    lVar43 = *(longlong *)(param_3 + 0x84);
    if (lVar43 != 0) goto LAB_1800816e4;
    local_850 = param_3 + 0x8e;
  }
  else {
LAB_180080108:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_ab0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0;
      if (iVar40 < 1) {
        iVar21 = iVar40 + -1;
      }
      local_ab0 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_a60 = local_ab0;
    local_848 = local_ab0;
    if (lVar43 == 0) goto LAB_180080140;
LAB_180080170:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_a28 = param_3 + 0x8e;
    }
    else {
      iVar21 = 1;
      if (iVar40 + -1 == 0 || iVar40 < 1) {
        iVar21 = iVar40 + -1;
      }
      local_a28 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_708 = local_a28;
    if (lVar43 == 0) goto LAB_1800801a4;
LAB_1800801d4:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_af0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 2;
      if (iVar40 < 3) {
        iVar21 = iVar40 + -1;
      }
      local_af0 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_8c0 = local_af0;
    if (lVar43 == 0) goto LAB_18008020c;
LAB_18008023c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_a48 = param_3 + 0x8e;
    }
    else {
      iVar21 = 3;
      if (iVar40 < 4) {
        iVar21 = iVar40 + -1;
      }
      local_a48 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_700 = local_a48;
    if (lVar43 == 0) goto LAB_180080274;
LAB_1800802a8:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_a80 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 4;
      if (iVar40 < 5) {
        iVar21 = iVar40 + -1;
      }
      local_a80 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_6f8 = local_a80;
    if (lVar43 == 0) goto LAB_1800802e4;
LAB_180080310:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_7a0 = param_3 + 0x8e;
    }
    else {
      iVar21 = 5;
      if (iVar40 < 6) {
        iVar21 = iVar40 + -1;
      }
      local_7a0 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    if (lVar43 == 0) goto LAB_180080344;
LAB_180080374:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_a50 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 6;
      if (iVar40 < 7) {
        iVar21 = iVar40 + -1;
      }
      local_a50 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_9e8 = local_a50;
    if (lVar43 == 0) goto LAB_1800803ac;
LAB_1800803dc:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_9e0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 7;
      if (iVar40 < 8) {
        iVar21 = iVar40 + -1;
      }
      local_9e0 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_6d8 = local_9e0;
    if (lVar43 == 0) goto LAB_180080414;
LAB_180080444:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_9d8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 8;
      if (iVar40 < 9) {
        iVar21 = iVar40 + -1;
      }
      local_9d8 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_6d0 = local_9d8;
    if (lVar43 == 0) goto LAB_18008047c;
LAB_1800804ac:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_aa8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 9;
      if (iVar40 < 10) {
        iVar21 = iVar40 + -1;
      }
      local_aa8 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_6f0 = local_aa8;
    if (lVar43 == 0) goto LAB_1800804e4;
LAB_180080514:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_ae8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 10;
      if (iVar40 < 0xb) {
        iVar21 = iVar40 + -1;
      }
      local_ae8 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_a40 = local_ae8;
    if (lVar43 == 0) goto LAB_18008054c;
LAB_18008057c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_a20 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0xb;
      if (iVar40 < 0xc) {
        iVar21 = iVar40 + -1;
      }
      local_a20 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_7e0 = local_a20;
    if (lVar43 == 0) goto LAB_1800805b4;
LAB_1800805e4:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_a68 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0xc;
      if (iVar40 < 0xd) {
        iVar21 = iVar40 + -1;
      }
      local_a68 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_7d0 = local_a68;
    if (lVar43 == 0) goto LAB_18008061c;
LAB_18008064c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_a00 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0xd;
      if (iVar40 < 0xe) {
        iVar21 = iVar40 + -1;
      }
      local_a00 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_950 = local_a00;
    if (lVar43 == 0) goto LAB_180080684;
LAB_1800806b4:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_a08 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0xe;
      if (iVar40 < 0xf) {
        iVar21 = iVar40 + -1;
      }
      local_a08 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_840 = local_a08;
    if (lVar43 == 0) goto LAB_1800806ec;
LAB_18008071c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_9c8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0xf;
      if (iVar40 < 0x10) {
        iVar21 = iVar40 + -1;
      }
      local_9c8 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_6b8 = local_9c8;
    if (lVar43 == 0) goto LAB_180080754;
LAB_180080784:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_9a0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x10;
      if (iVar40 < 0x11) {
        iVar21 = iVar40 + -1;
      }
      local_9a0 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_6b0 = local_9a0;
    if (lVar43 == 0) goto LAB_1800807bc;
LAB_1800807ec:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_9b8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x11;
      if (iVar40 < 0x12) {
        iVar21 = iVar40 + -1;
      }
      local_9b8 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_7c8 = local_9b8;
    if (lVar43 == 0) goto LAB_180080824;
LAB_180080854:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_9b0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x12;
      if (iVar40 < 0x13) {
        iVar21 = iVar40 + -1;
      }
      local_9b0 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_6a8 = local_9b0;
    if (lVar43 == 0) goto LAB_18008088c;
LAB_1800808bc:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_9a8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x13;
      if (iVar40 < 0x14) {
        iVar21 = iVar40 + -1;
      }
      local_9a8 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_6a0 = local_9a8;
    if (lVar43 == 0) goto LAB_1800808f4;
LAB_180080924:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_998 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x14;
      if (iVar40 < 0x15) {
        iVar21 = iVar40 + -1;
      }
      local_998 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_7c0 = local_998;
    if (lVar43 == 0) goto LAB_18008095c;
LAB_18008098c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_918 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0x15;
      if (iVar40 < 0x16) {
        iVar21 = iVar40 + -1;
      }
      local_918 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_6e8 = local_918;
    if (lVar43 == 0) goto LAB_1800809c4;
LAB_1800809f4:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_990 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x16;
      if (iVar40 < 0x17) {
        iVar21 = iVar40 + -1;
      }
      local_990 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_690 = local_990;
    if (lVar43 == 0) goto LAB_180080a2c;
LAB_180080a5c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_ad8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x17;
      if (iVar40 < 0x18) {
        iVar21 = iVar40 + -1;
      }
      local_ad8 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_980 = local_ad8;
    if (lVar43 == 0) goto LAB_180080a94;
LAB_180080ac4:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_970 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x18;
      if (iVar40 < 0x19) {
        iVar21 = iVar40 + -1;
      }
      local_970 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_680 = local_970;
    if (lVar43 == 0) goto LAB_180080afc;
LAB_180080b2c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_968 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x19;
      if (iVar40 < 0x1a) {
        iVar21 = iVar40 + -1;
      }
      local_968 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_678 = local_968;
    if (lVar43 == 0) goto LAB_180080b64;
LAB_180080b94:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_978 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x1a;
      if (iVar40 < 0x1b) {
        iVar21 = iVar40 + -1;
      }
      local_978 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_7b8 = local_978;
    if (lVar43 == 0) goto LAB_180080bcc;
LAB_180080bfc:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_960 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x1b;
      if (iVar40 < 0x1c) {
        iVar21 = iVar40 + -1;
      }
      local_960 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_670 = local_960;
    if (lVar43 == 0) goto LAB_180080c34;
LAB_180080c64:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_958 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x1c;
      if (iVar40 < 0x1d) {
        iVar21 = iVar40 + -1;
      }
      local_958 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_668 = local_958;
    if (lVar43 == 0) goto LAB_180080c9c;
LAB_180080ccc:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_a78 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x1d;
      if (iVar40 < 0x1e) {
        iVar21 = iVar40 + -1;
      }
      local_a78 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_660 = local_a78;
    if (lVar43 == 0) goto LAB_180080d04;
LAB_180080d34:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_9c0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x1e;
      if (iVar40 < 0x1f) {
        iVar21 = iVar40 + -1;
      }
      local_9c0 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_658 = local_9c0;
    if (lVar43 == 0) goto LAB_180080d6c;
LAB_180080d9c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_9f8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x1f;
      if (iVar40 < 0x20) {
        iVar21 = iVar40 + -1;
      }
      local_9f8 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_838 = local_9f8;
    if (lVar43 == 0) goto LAB_180080dd4;
LAB_180080e04:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_988 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x20;
      if (iVar40 < 0x21) {
        iVar21 = iVar40 + -1;
      }
      local_988 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_688 = local_988;
    if (lVar43 == 0) goto LAB_180080e3c;
LAB_180080e6c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_a90 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0x21;
      if (iVar40 < 0x22) {
        iVar21 = iVar40 + -1;
      }
      local_a90 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_648 = local_a90;
    if (lVar43 == 0) goto LAB_180080ea4;
LAB_180080ed4:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_940 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x22;
      if (iVar40 < 0x23) {
        iVar21 = iVar40 + -1;
      }
      local_940 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_640 = local_940;
    if (lVar43 == 0) goto LAB_180080f0c;
LAB_180080f3c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_910 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0x23;
      if (iVar40 < 0x24) {
        iVar21 = iVar40 + -1;
      }
      local_910 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_638 = local_910;
    if (lVar43 == 0) goto LAB_180080f74;
LAB_180080fa4:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_938 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x24;
      if (iVar40 < 0x25) {
        iVar21 = iVar40 + -1;
      }
      local_938 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_6c8 = local_938;
    if (lVar43 == 0) goto LAB_180080fdc;
LAB_18008100c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_908 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0x25;
      if (iVar40 < 0x26) {
        iVar21 = iVar40 + -1;
      }
      local_908 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_710 = local_908;
    if (lVar43 == 0) goto LAB_180081044;
LAB_180081074:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_900 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0x26;
      if (iVar40 < 0x27) {
        iVar21 = iVar40 + -1;
      }
      local_900 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_590 = local_900;
    if (lVar43 == 0) goto LAB_1800810ac;
LAB_1800810dc:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_8f8 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0x27;
      if (iVar40 < 0x28) {
        iVar21 = iVar40 + -1;
      }
      local_8f8 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_588 = local_8f8;
    if (lVar43 == 0) goto LAB_180081114;
LAB_180081144:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_8f0 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0x28;
      if (iVar40 < 0x29) {
        iVar21 = iVar40 + -1;
      }
      local_8f0 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_580 = local_8f0;
    if (lVar43 == 0) goto LAB_18008117c;
LAB_1800811ac:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_8e8 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0x29;
      if (iVar40 < 0x2a) {
        iVar21 = iVar40 + -1;
      }
      local_8e8 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_578 = local_8e8;
    if (lVar43 == 0) goto LAB_1800811e4;
LAB_180081214:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_8e0 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0x2a;
      if (iVar40 < 0x2b) {
        iVar21 = iVar40 + -1;
      }
      local_8e0 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_570 = local_8e0;
    if (lVar43 == 0) goto LAB_18008124c;
LAB_18008127c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_8c8 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0x2b;
      if (iVar40 < 0x2c) {
        iVar21 = iVar40 + -1;
      }
      local_8c8 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_568 = local_8c8;
    if (lVar43 == 0) goto LAB_1800812b4;
LAB_1800812e4:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_8d8 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0x2c;
      if (iVar40 < 0x2d) {
        iVar21 = iVar40 + -1;
      }
      local_8d8 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_630 = local_8d8;
    if (lVar43 == 0) goto LAB_18008131c;
LAB_18008134c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_948 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x2d;
      if (iVar40 < 0x2e) {
        iVar21 = iVar40 + -1;
      }
      local_948 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_650 = local_948;
    if (lVar43 == 0) goto LAB_180081384;
LAB_1800813b4:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_a38 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x2e;
      if (iVar40 < 0x2f) {
        iVar21 = iVar40 + -1;
      }
      local_a38 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_628 = local_a38;
    if (lVar43 == 0) goto LAB_1800813ec;
LAB_18008141c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_a30 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x2f;
      if (iVar40 < 0x30) {
        iVar21 = iVar40 + -1;
      }
      local_a30 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_620 = local_a30;
    if (lVar43 == 0) goto LAB_180081454;
LAB_180081480:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      piVar36 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0x30;
      if (iVar40 < 0x31) {
        iVar21 = iVar40 + -1;
      }
      piVar36 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_7d8 = piVar36;
    if (lVar43 == 0) goto LAB_1800814b4;
LAB_1800814e4:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_9f0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x31;
      if (iVar40 < 0x32) {
        iVar21 = iVar40 + -1;
      }
      local_9f0 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_6e0 = local_9f0;
    if (lVar43 == 0) goto LAB_18008151c;
LAB_18008154c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_a58 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0x32;
      if (iVar40 < 0x33) {
        iVar21 = iVar40 + -1;
      }
      local_a58 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_558 = local_a58;
    if (lVar43 == 0) goto LAB_180081584;
LAB_1800815b4:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_8d0 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0x33;
      if (iVar40 < 0x34) {
        iVar21 = iVar40 + -1;
      }
      local_8d0 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_560 = local_8d0;
    if (lVar43 == 0) goto LAB_1800815ec;
LAB_180081620:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_a88 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x34;
      if (iVar40 < 0x35) {
        iVar21 = iVar40 + -1;
      }
      local_a88 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_9d0 = local_a88;
    if (lVar43 == 0) goto LAB_18008165c;
LAB_18008168c:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_930 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar21 = 0x35;
      if (iVar40 < 0x36) {
        iVar21 = iVar40 + -1;
      }
      local_930 = (longlong *)(lVar43 + (longlong)iVar21 * 0x98);
    }
    local_6c0 = local_930;
    if (lVar43 == 0) goto LAB_1800816c4;
LAB_1800816e4:
    iVar40 = param_3[0x8c];
    if (iVar40 == 0) {
      local_850 = param_3 + 0x8e;
    }
    else {
      iVar21 = 0x36;
      if (iVar40 < 0x37) {
        iVar21 = iVar40 + -1;
      }
      local_850 = (int *)(lVar43 + (longlong)iVar21 * 0x98);
    }
  }
  local_438 = PitSessions::vftable;
  local_470 = VbP_PricePositions::vftable;
  local_468 = RollingLookbackModes::vftable;
  local_460 = SwingModes::vftable;
  local_458 = TimeBasedSwings::vftable;
  local_4a0 = VbP_AnchorPositions::vftable;
  local_498 = VbP_CalculationMethods::vftable;
  if (param_3[0x2b] != 0) {
    FUN_1800079f8((longlong *)(param_3 + 0x46),0x1800d5290,0x13);
    param_3[0x49] = 1;
    if ((*(char **)(param_3 + 0xce) != (char *)0x0) && (**(char **)(param_3 + 0xce) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0xce),0x1800d4ecd,0);
      param_3[0xd1] = 1;
    }
    pcVar32 = "";
    local_4c0 = &DAT_1800d4ecd;
    local_4d0 = "";
    uStack_4c8 = 0;
    pcVar28 = FUN_180004620(0x15);
    if (pcVar28 == (char *)0x0) {
      local_4d0 = "";
    }
    else {
      param_6 = 0x14;
      local_4d0 = pcVar28;
      FUN_180099d78(pcVar28,0x15,0x1800d6c40,0x14);
      uStack_4c8 = 0x100000001;
      pcVar32 = pcVar28;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_4d0);
    if ((pcVar28 != (char *)0x0) && (pcVar32 != (char *)0x0)) {
      pvVar29 = GetProcessHeap();
      HeapFree(pvVar29,0,pcVar32);
      local_4d0 = (char *)0x0;
      uStack_4c8 = 0;
    }
    FUN_180004498(&local_4d0,
                  "<a href=\"https://imgur.com/zL2LDnw\"><img src=\"https://i.imgur.com/zL2LDnw.png\ " title=\"OrderFlow Labs, orderflowlabs.com\" height=\"100\" /></a>"
                 );
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_4d0);
    pcVar32 = local_4d0;
    uVar18 = extraout_s0_71;
    uVar54 = extraout_var_71;
    uVar30 = extraout_var_x00155;
    if (((int)uStack_4c8 != 0) && (local_4d0 != (char *)0x0)) {
      pvVar29 = GetProcessHeap();
      HeapFree(pvVar29,0,pcVar32);
      local_4d0 = (char *)0x0;
      uStack_4c8 = 0;
      uVar18 = extraout_s0_72;
      uVar54 = extraout_var_72;
      uVar30 = extraout_var_x00156;
    }
    puVar2 = *(undefined1 **)(param_3 + 0x46);
    if (*(undefined1 **)(param_3 + 0x46) == (undefined1 *)0x0) {
      puVar2 = &DAT_1800d4ecd;
    }
    auVar13._4_4_ = uVar54;
    auVar13._0_4_ = uVar18;
    auVar13._8_8_ = uVar30;
    FUN_180026368(auVar13,CONCAT44(uVar55,uVar23),(undefined8 *)(param_3 + 0xce),0x1800d6c60,puVar2,
                  param_6,param_7,param_8,param_9,param_10);
    FUN_180004498(&local_4d0,"</div>");
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_4d0);
    pcVar32 = local_4d0;
    if (((int)uStack_4c8 != 0) && (local_4d0 != (char *)0x0)) {
      pvVar29 = GetProcessHeap();
      HeapFree(pvVar29,0,pcVar32);
      local_4d0 = (char *)0x0;
      uStack_4c8 = 0;
    }
    param_3[0xdf] = 0;
    param_3[4] = 0;
    param_3[0x32e] = 1;
    param_3[0x40a] = 0;
    param_3[0x276] = 1;
    param_3[0x2c] = 1;
    param_3[0x3d4] = 2;
    param_3[0x51a] = 1;
    param_3[0x51b] = 1;
    *(undefined2 *)(param_3 + 0x52e) = 1;
    *(undefined2 *)(local_530 + 9) = 5;
    local_530[6] = 0xffff;
    *(undefined2 *)(local_530 + 10) = 1;
    *(undefined2 *)(local_528 + 9) = 5;
    local_528[6] = 0xff;
    *(undefined2 *)(local_528 + 10) = 5;
    *(undefined2 *)(local_928 + 9) = 5;
    local_928[6] = 0x221f0;
    local_928[7] = 0xf0a60d;
    local_928[8] = 1;
    *(undefined2 *)(local_928 + 10) = 5;
    *(undefined2 *)(local_830 + 9) = 5;
    local_830[6] = 0xffff;
    *(undefined2 *)(local_830 + 10) = 1;
    *(undefined2 *)(local_5a0 + 9) = 5;
    local_5a0[6] = 0x8000;
    *(undefined2 *)(local_5a0 + 10) = 1;
    *(undefined2 *)((longlong)local_608 + 0x24) = 0x1c;
    *(undefined2 *)(local_608 + 5) = 1;
    *(undefined4 *)(local_608 + 3) = 0x4bc8ff;
    *(undefined2 *)((longlong)local_610 + 0x24) = 0x1b;
    *(undefined2 *)(local_610 + 5) = 1;
    *(undefined4 *)(local_610 + 3) = 0x4bc8ff;
    *(undefined2 *)((longlong)local_600 + 0x24) = 3;
    *(undefined2 *)(local_600 + 5) = 1;
    *(undefined4 *)(local_600 + 3) = 0xa2a2;
    *(undefined2 *)((longlong)local_548 + 0x24) = 5;
    *(undefined4 *)(local_548 + 3) = 0xffff;
    *(undefined2 *)(local_548 + 5) = 1;
    FUN_1800079f8(local_7a8,0x1800dad98,0x15);
    *(int *)((longlong)local_898 + 0xc) = 1;
    *(undefined2 *)((longlong)local_898 + 0x24) = 0x35;
    *(undefined2 *)(local_898 + 5) = 1;
    local_898[3] = 0x241a1a00453732;
    *(int *)(local_898 + 4) = 1;
    FUN_1800079f8(local_820,0x1800dae00,0x16);
    *(int *)((longlong)local_890 + 0xc) = 1;
    *(undefined2 *)((longlong)local_890 + 0x24) = 0x35;
    *(undefined2 *)(local_890 + 5) = 1;
    local_890[3] = 0x41343000735b53;
    *(int *)(local_890 + 4) = 1;
    FUN_1800079f8(local_818,0x1800dae18,7);
    *(int *)((longlong)local_878 + 0xc) = 1;
    *(undefined2 *)((longlong)local_878 + 0x24) = 0x35;
    *(undefined2 *)(local_878 + 5) = 1;
    *(int *)(local_878 + 3) = 0xff9e3e;
    FUN_1800079f8(local_810,0x1800dadd0,0x11);
    *(int *)((longlong)local_888 + 0xc) = 1;
    *(undefined2 *)((longlong)local_888 + 0x24) = 0x35;
    local_888[3] = 0x60310200cc6600;
    *(undefined2 *)(local_888 + 5) = 1;
    *(int *)(local_888 + 4) = 1;
    FUN_1800079f8(local_808,0x1800dade8,0x11);
    *(int *)((longlong)local_880 + 0xc) = 1;
    *(undefined2 *)((longlong)local_880 + 0x24) = 0x35;
    *(undefined2 *)(local_880 + 5) = 1;
    local_880[3] = 0x40000a1085;
    *(int *)(local_880 + 4) = 1;
    FUN_1800079f8(local_800,0x1800dae40,0x20);
    *(int *)((longlong)local_858 + 0xc) = 1;
    *(undefined2 *)((longlong)local_858 + 0x24) = 0x35;
    *(undefined2 *)(local_858 + 5) = 1;
    local_858[3] = 0x332c2800332c28;
    *(int *)(local_858 + 4) = 1;
    FUN_1800079f8(local_7f8,0x1800dae68,0x11);
    *(int *)((longlong)local_870 + 0xc) = 1;
    *(undefined2 *)((longlong)local_870 + 0x24) = 0x35;
    *(undefined2 *)(local_870 + 5) = 1;
    local_870[3] = 0xeccaa800eccaa8;
    *(int *)(local_870 + 4) = 1;
    FUN_1800079f8(local_7f0,0x1800dae20,0x11);
    *(int *)((longlong)local_868 + 0xc) = 1;
    *(undefined2 *)((longlong)local_868 + 0x24) = 0x35;
    *(undefined2 *)(local_868 + 5) = 1;
    local_868[3] = 0x5537ff005537ff;
    *(int *)(local_868 + 4) = 1;
    FUN_1800079f8(local_a70,0x1800dae34,4);
    *(int *)((longlong)local_598 + 0xc) = 1;
    *(int *)((longlong)local_598 + 0x24) = 0x20000;
    *(undefined2 *)(local_598 + 5) = 2;
    *(int *)(local_598 + 3) = 0x8b7367;
    FUN_1800079f8(local_ac0,0x1800daea0,0x15);
    *(int *)((longlong)local_8b8 + 0xc) = 1;
    *(int *)(local_8b8 + 0x1c) = 0x21;
    *(int *)((longlong)local_8b8 + 0x24) = 0x20005;
    *(undefined2 *)(local_8b8 + 5) = 1;
    *(int *)(local_8b8 + 3) = 0x8b7367;
    FUN_1800079f8(local_ac0 + 0x21,0x1800daeb8,2);
    *(undefined4 *)((longlong)local_ac0 + 0x114) = 1;
    FUN_1800079f8(local_ae0,0x1800dae80,0x15);
    *(int *)((longlong)local_8b0 + 0xc) = 1;
    *(int *)(local_8b0 + 0x1c) = 0x21;
    *(int *)((longlong)local_8b0 + 0x24) = 0x20005;
    *(undefined2 *)(local_8b0 + 5) = 1;
    *(int *)(local_8b0 + 3) = 0x8b7367;
    FUN_1800079f8(local_ae0 + 0x21,0x1800dae98,2);
    *(undefined4 *)((longlong)local_ae0 + 0x114) = 1;
    FUN_1800079f8(local_aa0,0x1800daee0,0x15);
    *(int *)((longlong)local_8a8 + 0xc) = 1;
    *(int *)(local_8a8 + 0x1c) = 0x21;
    *(int *)((longlong)local_8a8 + 0x24) = 0x20005;
    *(undefined2 *)(local_8a8 + 5) = 1;
    *(int *)(local_8a8 + 3) = 0x8b7367;
    FUN_1800079f8(local_aa0 + 0x21,0x1800daef8,2);
    *(undefined4 *)((longlong)local_aa0 + 0x114) = 1;
    FUN_1800079f8(local_a98,0x1800daec0,0x15);
    *(int *)((longlong)local_8a0 + 0xc) = 1;
    *(int *)(local_8a0 + 0x1c) = 0x21;
    *(int *)((longlong)local_8a0 + 0x24) = 0x20005;
    *(undefined2 *)(local_8a0 + 5) = 1;
    *(int *)(local_8a0 + 3) = 0x8b7367;
    FUN_1800079f8(local_a98 + 0x21,0x1800daed8,2);
    *(undefined4 *)((longlong)local_a98 + 0x114) = 1;
    FUN_1800079f8(local_7e8,0x1800daf30,0xb);
    *(int *)((longlong)local_860 + 0xc) = 1;
    *(int *)((longlong)local_860 + 0x24) = 0x20003;
    *(undefined2 *)(local_860 + 5) = 1;
    *(int *)(local_860 + 3) = 0x5e4e46;
    FUN_1800079f8(local_a10,0x1800daf40,0xb);
    plVar33 = local_7b0;
    *(int *)((longlong)local_920 + 0xc) = 1;
    *(undefined2 *)((longlong)local_920 + 0x24) = 0x35;
    *(undefined2 *)(local_920 + 5) = 8;
    local_920[3] = 0x14141400c0c0c0;
    *(int *)(local_920 + 4) = 1;
    FUN_1800079f8(local_7b0,0x1800daf00,0x14);
    *(undefined4 *)((longlong)plVar33 + 0xc) = 1;
    *(undefined4 *)(plVar33 + 3) = 0x202020;
    *(undefined2 *)((longlong)plVar33 + 0x24) = 0x35;
    *(undefined2 *)(plVar33 + 5) = 1;
    FUN_1800079f8(local_ab0,0x1800d6cb0,0x11);
    *(int *)((longlong)local_a60 + 0xc) = 1;
    puVar26 = FUN_180029680((longlong *)&local_438,(undefined8 *)&local_518);
    plVar33 = local_a60;
    if (0xf < (ulonglong)puVar26[3]) {
      puVar26 = (undefined8 *)*puVar26;
    }
    if ((code *)local_a60[10] != (code *)0x0) {
      (*(code *)local_a60[10])(*(int *)((longlong)local_a60 + 0x4c),puVar26);
      *(undefined1 *)(plVar33 + 3) = 0x16;
    }
    if (0xf < local_500) {
      FUN_1800966b8((LPVOID)CONCAT71(uStack_517,local_518));
    }
    local_518 = 0;
    local_508 = 0;
    local_500 = 0xf;
    *(undefined1 *)(local_a60 + 3) = 0x16;
    *(int *)((longlong)local_a60 + 0x1c) = 0;
    *(undefined1 *)(local_708 + 6) = 5;
    local_708[7] = 0;
    FUN_1800079f8(local_af0,0x1800daf18,0x16);
    *(int *)((longlong)local_8c0 + 0xc) = 1;
    local_720 = 0;
    local_718 = 0xf;
    uStack_728 = 0;
    local_730 = (undefined8 *******)0x0;
    (*(code *)local_498[1])(&local_498,&local_450);
    uVar48 = 0;
    if ((longlong)local_448 - (longlong)local_450 >> 5 != 0) {
      lVar43 = 0;
      do {
        puVar26 = (undefined8 *)((longlong)local_450 + lVar43);
        puVar49 = puVar26 + 2;
        if (0xf < (ulonglong)puVar26[3]) {
          puVar26 = (undefined8 *)*puVar26;
        }
        FUN_180019068(&local_730,puVar26,*puVar49);
        if (uVar48 != ((longlong)local_448 - (longlong)local_450 >> 5) - 1U) {
          FUN_180019068(&local_730,(undefined8 *)&DAT_1800d6d5c,1);
        }
        uVar48 = uVar48 + 1;
        lVar43 = lVar43 + 0x20;
      } while (uVar48 < (ulonglong)((longlong)local_448 - (longlong)local_450 >> 5));
    }
    if (local_450 != (ulonglong *)0x0) {
      if (local_450 != local_448) {
        puVar49 = local_450 + 3;
        do {
          if (0xf < *puVar49) {
            FUN_1800966b8((LPVOID)puVar49[-3]);
          }
          puVar49[-1] = 0;
          *puVar49 = 0xf;
          *(undefined1 *)(puVar49 + -3) = 0;
          puVar42 = puVar49 + 1;
          puVar49 = puVar49 + 4;
        } while (puVar42 != local_448);
      }
      FUN_1800966b8(local_450);
      local_450 = (ulonglong *)0x0;
      local_448 = (ulonglong *)0x0;
      local_440 = 0;
    }
    plVar33 = local_8c0;
    pppppppuVar3 = local_730;
    if (local_718 < 0x10) {
      pppppppuVar3 = &local_730;
    }
    if ((code *)local_8c0[10] != (code *)0x0) {
      (*(code *)local_8c0[10])(*(int *)((longlong)local_8c0 + 0x4c),pppppppuVar3);
      *(undefined1 *)(plVar33 + 3) = 0x16;
    }
    if (0xf < local_718) {
      FUN_1800966b8(local_730);
    }
    local_730 = (undefined8 *******)((ulonglong)local_730 & 0xffffffffffffff00);
    local_720 = 0;
    local_718 = 0xf;
    *(undefined1 *)(local_8c0 + 3) = 0x16;
    *(int *)((longlong)local_8c0 + 0x1c) = 2;
    *(undefined1 *)(local_700 + 6) = 0xb;
    local_700[7] = 0;
    FUN_1800079f8(local_a80,0x1800dafa0,0x1f);
    *(int *)((longlong)local_6f8 + 0xc) = 1;
    *(undefined1 *)(local_6f8 + 3) = 2;
    *(int *)((longlong)local_6f8 + 0x1c) = 0x41200000;
    FUN_1800079f8(local_aa8,0x1800dafc0,0x17);
    *(int *)((longlong)local_6f0 + 0xc) = 1;
    *(undefined1 *)(local_6f0 + 3) = 5;
    *(int *)((longlong)local_6f0 + 0x1c) = 0;
    FUN_1800079f8(local_ae8,0x1800daf50,0x23);
    *(int *)((longlong)local_a40 + 0xc) = 1;
    *(undefined1 *)(local_a40 + 3) = 0x15;
    ((int *)((longlong)local_a40 + 0x1c))[0] = 1;
    ((int *)((longlong)local_a40 + 0x1c))[1] = 1;
    if (*(code **)(local_7e0 + 0x14) != (code *)0x0) {
      (**(code **)(local_7e0 + 0x14))(local_7e0[0x13],"Average;Mode");
    }
    *(undefined1 *)(local_7e0 + 6) = 0x16;
    local_7e0[7] = 0;
    *(undefined1 *)(local_7a0 + 6) = 0xb;
    local_7a0[7] = 0x14;
    local_7a0[0xb] = 1;
    local_7a0[0xf] = 0xffff;
    FUN_1800079f8(local_a50,0x1800daf78,0x20);
    *(int *)((longlong)local_9e8 + 0xc) = 1;
    *(undefined1 *)(local_9e8 + 3) = 0x15;
    ((int *)((longlong)local_9e8 + 0x1c))[0] = 1;
    ((int *)((longlong)local_9e8 + 0x1c))[1] = 1;
    FUN_1800079f8(local_838,0x1800db028,0x17);
    *(int *)((longlong)local_9f8 + 0xc) = 1;
    local_740 = 0;
    local_738 = 0xf;
    uStack_748 = 0;
    local_750 = (undefined8 *******)0x0;
    (*(code *)local_458[1])(&local_458,&local_300);
    uVar48 = 0;
    if (lStack_2f8 - local_300 >> 5 != 0) {
      lVar43 = 0;
      do {
        puVar26 = (undefined8 *)(lVar43 + local_300);
        puVar49 = puVar26 + 2;
        if (0xf < (ulonglong)puVar26[3]) {
          puVar26 = (undefined8 *)*puVar26;
        }
        FUN_180019068(&local_750,puVar26,*puVar49);
        if (uVar48 != (lStack_2f8 - local_300 >> 5) - 1U) {
          FUN_180019068(&local_750,(undefined8 *)&DAT_1800d6d5c,1);
        }
        uVar48 = uVar48 + 1;
        lVar43 = lVar43 + 0x20;
      } while (uVar48 < (ulonglong)(lStack_2f8 - local_300 >> 5));
    }
    FUN_1800297f8(&local_300);
    plVar33 = local_9f8;
    pppppppuVar3 = local_750;
    if (local_738 < 0x10) {
      pppppppuVar3 = &local_750;
    }
    if ((code *)local_9f8[10] != (code *)0x0) {
      (*(code *)local_9f8[10])(*(int *)((longlong)local_9f8 + 0x4c),pppppppuVar3);
      *(undefined1 *)(plVar33 + 3) = 0x16;
    }
    if (0xf < local_738) {
      FUN_1800966b8(local_750);
    }
    local_750 = (undefined8 *******)((ulonglong)local_750 & 0xffffffffffffff00);
    local_740 = 0;
    local_738 = 0xf;
    *(undefined1 *)(local_9f8 + 3) = 0x16;
    *(int *)((longlong)local_9f8 + 0x1c) = 1;
    uStack_768 = 0;
    local_760 = 0;
    local_758 = 0xf;
    local_770 = (undefined8 *******)0x0;
    (*(code *)local_468[1])(&local_468,&local_2e8);
    uVar48 = 0;
    if (lStack_2e0 - local_2e8 >> 5 != 0) {
      lVar43 = 0;
      do {
        puVar26 = (undefined8 *)(lVar43 + local_2e8);
        puVar49 = puVar26 + 2;
        if (0xf < (ulonglong)puVar26[3]) {
          puVar26 = (undefined8 *)*puVar26;
        }
        FUN_180019068(&local_770,puVar26,*puVar49);
        if (uVar48 != (lStack_2e0 - local_2e8 >> 5) - 1U) {
          FUN_180019068(&local_770,(undefined8 *)&DAT_1800d6d5c,1);
        }
        uVar48 = uVar48 + 1;
        lVar43 = lVar43 + 0x20;
      } while (uVar48 < (ulonglong)(lStack_2e0 - local_2e8 >> 5));
    }
    FUN_1800297f8(&local_2e8);
    piVar36 = local_7d8;
    pppppppuVar3 = local_770;
    if (local_758 < 0x10) {
      pppppppuVar3 = &local_770;
    }
    if (*(code **)(local_7d8 + 0x14) != (code *)0x0) {
      (**(code **)(local_7d8 + 0x14))(local_7d8[0x13],pppppppuVar3);
      *(undefined1 *)(piVar36 + 6) = 0x16;
    }
    if (0xf < local_758) {
      FUN_1800966b8(local_770);
    }
    local_770 = (undefined8 *******)((ulonglong)local_770 & 0xffffffffffffff00);
    local_760 = 0;
    local_758 = 0xf;
    *(undefined1 *)(local_7d8 + 6) = 0x16;
    local_7d8[7] = 0;
    FUN_1800079f8(local_9f0,0x1800db040,0x21);
    *(int *)((longlong)local_6e0 + 0xc) = 1;
    *(undefined1 *)(local_6e0 + 3) = 0xb;
    *(int *)((longlong)local_6e0 + 0x1c) = 5;
    *(int *)((longlong)local_6e0 + 0x2c) = 1;
    *(int *)((longlong)local_6e0 + 0x3c) = 0xffff;
    FUN_1800079f8(local_9e0,0x1800dafd8,0x22);
    *(int *)((longlong)local_6d8 + 0xc) = 1;
    *(undefined1 *)(local_6d8 + 3) = 0x12;
    ((int *)((longlong)local_6d8 + 0x1c))[0] = 1;
    ((int *)((longlong)local_6d8 + 0x1c))[1] = 1;
    *(int *)((longlong)local_6d8 + 0x24) = 0;
    FUN_1800079f8(local_9d8,0x1800db000,0x21);
    *(int *)((longlong)local_6d0 + 0xc) = 1;
    *(undefined1 *)(local_6d0 + 3) = 0x12;
    ((int *)((longlong)local_6d0 + 0x1c))[0] = 1;
    ((int *)((longlong)local_6d0 + 0x1c))[1] = 1;
    *(int *)((longlong)local_6d0 + 0x24) = 1;
    FUN_1800079f8(local_a88,0x1800db098,0x1c);
    *(int *)((longlong)local_9d0 + 0xc) = 1;
    *(undefined1 *)(local_9d0 + 3) = 0x15;
    ((int *)((longlong)local_9d0 + 0x1c))[0] = 1;
    ((int *)((longlong)local_9d0 + 0x1c))[1] = 1;
    FUN_1800079f8(local_938,0x1800db0b8,0x19);
    *(int *)((longlong)local_6c8 + 0xc) = 1;
    *(undefined1 *)(local_6c8 + 3) = 0xb;
    *(int *)((longlong)local_6c8 + 0x1c) = 1;
    *(int *)((longlong)local_6c8 + 0x2c) = 1;
    *(int *)((longlong)local_6c8 + 0x3c) = 0x7fffffff;
    FUN_1800079f8(local_930,0x1800db068,0x21);
    *(int *)((longlong)local_6c0 + 0xc) = 1;
    *(undefined1 *)(local_6c0 + 3) = 0xb;
    *(int *)((longlong)local_6c0 + 0x1c) = 0;
    *(int *)((longlong)local_6c0 + 0x2c) = 0;
    *(int *)((longlong)local_6c0 + 0x3c) = 0x7fffffff;
    FUN_18004fdd0((longlong)local_a58,"OFL VbP");
    FUN_1800079f8(local_a68,0x1800db108,0x14);
    *(int *)((longlong)local_7d0 + 0xc) = 1;
    if ((code *)local_7d0[10] != (code *)0x0) {
      (*(code *)local_7d0[10])(*(int *)((longlong)local_7d0 + 0x4c),"Off;Volume;Delta");
    }
    *(undefined1 *)(local_7d0 + 3) = 0x16;
    *(int *)((longlong)local_7d0 + 0x1c) = 1;
    FUN_1800079f8(local_950,0x1800db0d8,0x10);
    *(int *)((longlong)local_a00 + 0xc) = 1;
    local_780 = 0;
    local_778 = 0xf;
    uStack_788 = 0;
    local_790 = (undefined8 *******)0x0;
    (*(code *)local_470[1])(&local_470,&local_2d0);
    uVar48 = 0;
    if (lStack_2c8 - local_2d0 >> 5 != 0) {
      lVar43 = 0;
      do {
        puVar26 = (undefined8 *)(lVar43 + local_2d0);
        puVar49 = puVar26 + 2;
        if (0xf < (ulonglong)puVar26[3]) {
          puVar26 = (undefined8 *)*puVar26;
        }
        FUN_180019068(&local_790,puVar26,*puVar49);
        if (uVar48 != (lStack_2c8 - local_2d0 >> 5) - 1U) {
          FUN_180019068(&local_790,(undefined8 *)&DAT_1800d6d5c,1);
        }
        uVar48 = uVar48 + 1;
        lVar43 = lVar43 + 0x20;
      } while (uVar48 < (ulonglong)(lStack_2c8 - local_2d0 >> 5));
    }
    FUN_1800297f8(&local_2d0);
    plVar33 = local_a00;
    pppppppuVar3 = local_790;
    if (local_778 < 0x10) {
      pppppppuVar3 = &local_790;
    }
    if ((code *)local_a00[10] != (code *)0x0) {
      (*(code *)local_a00[10])(*(int *)((longlong)local_a00 + 0x4c),pppppppuVar3);
      *(undefined1 *)(plVar33 + 3) = 0x16;
    }
    if (0xf < local_778) {
      FUN_1800966b8(local_790);
    }
    local_790 = (undefined8 *******)((ulonglong)local_790 & 0xffffffffffffff00);
    local_780 = 0;
    local_778 = 0xf;
    *(undefined1 *)(local_a00 + 3) = 0x16;
    *(int *)((longlong)local_a00 + 0x1c) = 1;
    FUN_1800079f8(local_840,0x1800db0f0,0x11);
    *(int *)((longlong)local_a08 + 0xc) = 1;
    uStack_5d8 = 0;
    local_5d0 = 0;
    local_5c8 = 0xf;
    local_5e0 = (undefined8 *******)0x0;
    (*(code *)local_460[1])(&local_460,&local_2b8);
    uVar48 = 0;
    if (lStack_2b0 - local_2b8 >> 5 != 0) {
      lVar43 = 0;
      do {
        puVar26 = (undefined8 *)(lVar43 + local_2b8);
        puVar49 = puVar26 + 2;
        if (0xf < (ulonglong)puVar26[3]) {
          puVar26 = (undefined8 *)*puVar26;
        }
        FUN_180019068(&local_5e0,puVar26,*puVar49);
        if (uVar48 != (lStack_2b0 - local_2b8 >> 5) - 1U) {
          FUN_180019068(&local_5e0,(undefined8 *)&DAT_1800d6d5c,1);
        }
        uVar48 = uVar48 + 1;
        lVar43 = lVar43 + 0x20;
      } while (uVar48 < (ulonglong)(lStack_2b0 - local_2b8 >> 5));
    }
    FUN_1800297f8(&local_2b8);
    plVar33 = local_a08;
    pppppppuVar3 = local_5e0;
    if (local_5c8 < 0x10) {
      pppppppuVar3 = &local_5e0;
    }
    if ((code *)local_a08[10] != (code *)0x0) {
      (*(code *)local_a08[10])(*(int *)((longlong)local_a08 + 0x4c),pppppppuVar3);
      *(undefined1 *)(plVar33 + 3) = 0x16;
    }
    if (0xf < local_5c8) {
      FUN_1800966b8(local_5e0);
    }
    local_5e0 = (undefined8 *******)((ulonglong)local_5e0 & 0xffffffffffffff00);
    local_5d0 = 0;
    local_5c8 = 0xf;
    *(undefined1 *)(local_a08 + 3) = 0x16;
    *(int *)((longlong)local_a08 + 0x1c) = 0;
    FUN_1800079f8(local_9c8,0x1800db160,0x14);
    *(int *)((longlong)local_6b8 + 0xc) = 1;
    *(undefined1 *)(local_6b8 + 3) = 0xb;
    *(int *)((longlong)local_6b8 + 0x1c) = 1;
    *(int *)((longlong)local_6b8 + 0x2c) = 1;
    *(int *)((longlong)local_6b8 + 0x3c) = 0xffff;
    FUN_1800079f8(local_9a0,0x1800db178,0x12);
    *(int *)((longlong)local_6b0 + 0xc) = 1;
    *(undefined1 *)(local_6b0 + 3) = 5;
    *(int *)((longlong)local_6b0 + 0x1c) = 0;
    if (*(code **)(local_850 + 0x14) != (code *)0x0) {
      (**(code **)(local_850 + 0x14))(local_850[0x13],"Linear;SemiLog;Log");
    }
    *(undefined1 *)(local_850 + 6) = 0x16;
    local_850[7] = 0;
    FUN_1800079f8(local_9b8,0x1800db150,0xb);
    *(int *)((longlong)local_7c8 + 0xc) = 1;
    if ((code *)local_7c8[10] != (code *)0x0) {
      (*(code *)local_7c8[10])
                (*(int *)((longlong)local_7c8 + 0x4c),"Arial;Consolas;Segoe UI;Tahoma");
    }
    *(undefined1 *)(local_7c8 + 3) = 0x16;
    *(int *)((longlong)local_7c8 + 0x1c) = 0;
    FUN_1800079f8(local_9b0,0x1800db1c0,0x10);
    *(int *)((longlong)local_6a8 + 0xc) = 1;
    *(undefined1 *)(local_6a8 + 3) = 0xb;
    *(int *)((longlong)local_6a8 + 0x1c) = 8;
    *(int *)((longlong)local_6a8 + 0x2c) = 1;
    *(int *)((longlong)local_6a8 + 0x3c) = 100;
    FUN_1800079f8(local_9a8,0x1800db1d8,0x14);
    *(int *)((longlong)local_6a0 + 0xc) = 1;
    *(undefined1 *)(local_6a0 + 3) = 5;
    *(int *)((longlong)local_6a0 + 0x1c) = 0;
    FUN_1800079f8(local_998,0x1800db190,0x15);
    *(int *)((longlong)local_7c0 + 0xc) = 1;
    if ((code *)local_7c0[10] != (code *)0x0) {
      (*(code *)local_7c0[10])(*(int *)((longlong)local_7c0 + 0x4c),"Base;End;Outside");
    }
    *(undefined1 *)(local_7c0 + 3) = 0x16;
    *(int *)((longlong)local_7c0 + 0x1c) = 0;
    *(undefined1 *)(local_6e8 + 6) = 5;
    local_6e8[7] = 0;
    FUN_1800079f8(local_990,0x1800db240,0x22);
    *(int *)((longlong)local_690 + 0xc) = 1;
    *(undefined1 *)(local_690 + 3) = 0xb;
    *(int *)((longlong)local_690 + 0x1c) = 0;
    *(int *)((longlong)local_690 + 0x2c) = 0;
    *(int *)((longlong)local_690 + 0x3c) = 0xffff;
    FUN_1800079f8(local_980,0x1800db268,0x27);
    *(int *)((longlong)local_ad8 + 0xc) = 1;
    local_5b0 = 0;
    local_5a8 = 0xf;
    uStack_5b8 = 0;
    local_5c0 = (undefined8 *******)0x0;
    (*(code *)local_4a0[1])(&local_4a0,&local_2a0);
    uVar48 = 0;
    if (lStack_298 - local_2a0 >> 5 != 0) {
      lVar43 = 0;
      do {
        puVar26 = (undefined8 *)(lVar43 + local_2a0);
        puVar49 = puVar26 + 2;
        if (0xf < (ulonglong)puVar26[3]) {
          puVar26 = (undefined8 *)*puVar26;
        }
        FUN_180019068(&local_5c0,puVar26,*puVar49);
        if (uVar48 != (lStack_298 - local_2a0 >> 5) - 1U) {
          FUN_180019068(&local_5c0,(undefined8 *)&DAT_1800d6d5c,1);
        }
        uVar48 = uVar48 + 1;
        lVar43 = lVar43 + 0x20;
      } while (uVar48 < (ulonglong)(lStack_298 - local_2a0 >> 5));
    }
    FUN_1800297f8(&local_2a0);
    pppppppuVar3 = local_5c0;
    if (local_5a8 < 0x10) {
      pppppppuVar3 = &local_5c0;
    }
    if ((code *)local_ad8[10] != (code *)0x0) {
      (*(code *)local_ad8[10])(*(int *)((longlong)local_ad8 + 0x4c),pppppppuVar3);
      *(undefined1 *)(local_ad8 + 3) = 0x16;
    }
    if (0xf < local_5a8) {
      FUN_1800966b8(local_5c0);
    }
    local_5c0 = (undefined8 *******)((ulonglong)local_5c0 & 0xffffffffffffff00);
    local_5b0 = 0;
    local_5a8 = 0xf;
    *(undefined1 *)(local_ad8 + 3) = 0x16;
    *(int *)((longlong)local_ad8 + 0x1c) = 0;
    FUN_1800079f8(local_970,0x1800db1f0,0x1f);
    *(int *)((longlong)local_680 + 0xc) = 1;
    *(undefined1 *)(local_680 + 3) = 0xb;
    *(int *)((longlong)local_680 + 0x1c) = 0;
    *(int *)((longlong)local_680 + 0x2c) = -0x8000;
    *(int *)((longlong)local_680 + 0x3c) = 0x7fff;
    FUN_1800079f8(local_968,0x1800db210,0x28);
    *(int *)((longlong)local_678 + 0xc) = 1;
    *(undefined1 *)(local_678 + 3) = 2;
    *(int *)((longlong)local_678 + 0x1c) = 0x42c80000;
    *(int *)((longlong)local_678 + 0x2c) = 0;
    *(int *)((longlong)local_678 + 0x3c) = 0x42c80000;
    FUN_1800079f8(local_978,0x1800db2c8,0x11);
    *(int *)((longlong)local_7b8 + 0xc) = 1;
    if ((code *)local_7b8[10] != (code *)0x0) {
      (*(code *)local_7b8[10])(*(int *)((longlong)local_7b8 + 0x4c),"Percentage;Pixels");
    }
    *(undefined1 *)(local_7b8 + 3) = 0x16;
    *(int *)((longlong)local_7b8 + 0x1c) = 1;
    FUN_1800079f8(local_960,0x1800db290,0x15);
    *(int *)((longlong)local_670 + 0xc) = 1;
    *(undefined1 *)(local_670 + 3) = 0xb;
    *(int *)((longlong)local_670 + 0x1c) = 200;
    *(int *)((longlong)local_670 + 0x2c) = 0;
    *(int *)((longlong)local_670 + 0x3c) = 0xffff;
    FUN_1800079f8(local_958,0x1800db2a8,0x1e);
    *(int *)((longlong)local_668 + 0xc) = 1;
    *(undefined1 *)(local_668 + 3) = 2;
    *(int *)((longlong)local_668 + 0x1c) = 0x42c80000;
    *(int *)((longlong)local_668 + 0x2c) = 0;
    *(int *)((longlong)local_668 + 0x3c) = 0x42c80000;
    FUN_1800079f8(local_a78,0x1800db330,0x10);
    *(int *)((longlong)local_660 + 0xc) = 1;
    *(undefined1 *)(local_660 + 3) = 5;
    *(int *)((longlong)local_660 + 0x1c) = 0;
    FUN_1800079f8(local_9c0,0x1800db348,0x1e);
    *(int *)((longlong)local_658 + 0xc) = 1;
    *(undefined1 *)(local_658 + 3) = 5;
    *(int *)((longlong)local_658 + 0x1c) = 0;
    FUN_1800079f8(local_988,0x1800db2f8,0x1c);
    *(int *)((longlong)local_688 + 0xc) = 1;
    *(undefined1 *)(local_688 + 3) = 5;
    *(int *)((longlong)local_688 + 0x1c) = 0;
    FUN_1800079f8(local_948,0x1800db318,0x13);
    *(int *)((longlong)local_650 + 0xc) = 1;
    *(undefined1 *)(local_650 + 3) = 5;
    *(int *)((longlong)local_650 + 0x1c) = 0;
    *(undefined1 *)(local_648 + 6) = 0xb;
    local_648[7] = 0;
    local_648[0xb] = -0x80000000;
    local_648[0xf] = 0x7fffffff;
    FUN_1800079f8(local_940,0x1800db3c0,0x1c);
    *(int *)((longlong)local_640 + 0xc) = 1;
    *(undefined1 *)(local_640 + 3) = 5;
    *(int *)((longlong)local_640 + 0x1c) = 0;
    *(undefined1 *)(local_638 + 6) = 5;
    local_638[7] = 0;
    *(undefined1 *)(local_630 + 6) = 5;
    local_630[7] = 0;
    FUN_1800079f8(local_a38,0x1800db3e0,0x21);
    *(int *)((longlong)local_628 + 0xc) = 1;
    *(undefined1 *)(local_628 + 3) = 2;
    *(int *)((longlong)local_628 + 0x1c) = 0x3f800000;
    *(int *)((longlong)local_628 + 0x2c) = 0x3dcccccd;
    *(int *)((longlong)local_628 + 0x3c) = 0x42c80000;
    FUN_1800079f8(local_a30,0x1800db368,0x21);
    *(int *)((longlong)local_620 + 0xc) = 1;
    *(undefined1 *)(local_620 + 3) = 2;
    *(int *)((longlong)local_620 + 0x1c) = 0x40000000;
    *(int *)((longlong)local_620 + 0x2c) = 0x3dcccccd;
    *(int *)((longlong)local_620 + 0x3c) = 0x42c80000;
    if (*(code **)(local_710 + 0x14) != (code *)0x0) {
      (**(code **)(local_710 + 0x14))
                (local_710[0x13],"Off;Above;Below;Top of Region;Bottom of Region");
    }
    *(undefined1 *)(local_710 + 6) = 0x16;
    local_710[7] = 0;
    *(undefined1 *)(local_590 + 6) = 0xb;
    local_590[7] = 0;
    *(undefined1 *)(local_588 + 6) = 0xe;
    local_588[7] = 0xc0c0c0;
    *(undefined1 *)(local_580 + 6) = 0xb;
    local_580[7] = 7;
    local_580[0xb] = 4;
    local_580[0xf] = 100;
    *(undefined1 *)(local_578 + 6) = 5;
    local_578[7] = 1;
    *(undefined1 *)(local_570 + 6) = 5;
    local_570[7] = 1;
    *(undefined1 *)(local_568 + 6) = 5;
    local_568[7] = 1;
    *(undefined1 *)(local_560 + 6) = 5;
    local_560[7] = 0;
    *(undefined2 *)((longlong)local_a60 + 0x1a) = 1;
    *(undefined2 *)((longlong)local_708 + 0x1a) = 2;
    *(undefined2 *)((longlong)local_8c0 + 0x1a) = 3;
    *(undefined2 *)((longlong)local_700 + 0x1a) = 4;
    *(undefined2 *)((longlong)local_6f8 + 0x1a) = 5;
    *(undefined2 *)((longlong)local_6f0 + 0x1a) = 6;
    *(undefined2 *)((longlong)local_a40 + 0x1a) = 7;
    *(undefined2 *)((longlong)local_7e0 + 0x1a) = 8;
    *(undefined2 *)((longlong)local_7a0 + 0x1a) = 9;
    *(undefined2 *)((longlong)local_9e8 + 0x1a) = 10;
    *(undefined2 *)((longlong)local_9f8 + 0x1a) = 0xb;
    *(undefined2 *)((longlong)local_7d8 + 0x1a) = 0xc;
    *(undefined2 *)((longlong)local_6e0 + 0x1a) = 0xd;
    *(undefined2 *)((longlong)local_6d8 + 0x1a) = 0xe;
    *(undefined2 *)((longlong)local_6d0 + 0x1a) = 0xf;
    *(undefined2 *)((longlong)local_9d0 + 0x1a) = 0x10;
    *(undefined2 *)((longlong)local_6c8 + 0x1a) = 0x11;
    *(undefined2 *)((longlong)local_6c0 + 0x1a) = 0x12;
    *(undefined2 *)((longlong)local_558 + 0x1a) = 0x13;
    *(undefined2 *)((longlong)local_7d0 + 0x1a) = 0x14;
    *(undefined2 *)((longlong)local_a00 + 0x1a) = 0x15;
    *(undefined2 *)((longlong)local_a08 + 0x1a) = 0x16;
    *(undefined2 *)((longlong)local_6b8 + 0x1a) = 0x17;
    *(undefined2 *)((longlong)local_6b0 + 0x1a) = 0x18;
    *(undefined2 *)((longlong)local_850 + 0x1a) = 0x19;
    *(undefined2 *)((longlong)local_7c8 + 0x1a) = 0x1a;
    *(undefined2 *)((longlong)local_6a8 + 0x1a) = 0x1b;
    *(undefined2 *)((longlong)local_6a0 + 0x1a) = 0x1c;
    *(undefined2 *)((longlong)local_7c0 + 0x1a) = 0x1d;
    *(undefined2 *)((longlong)local_6e8 + 0x1a) = 0x1e;
    *(undefined2 *)((longlong)local_690 + 0x1a) = 0x1f;
    *(undefined2 *)((longlong)local_688 + 0x1a) = 0x20;
    *(undefined2 *)((longlong)local_ad8 + 0x1a) = 0x21;
    *(undefined2 *)((longlong)local_680 + 0x1a) = 0x22;
    *(undefined2 *)((longlong)local_678 + 0x1a) = 0x23;
    *(undefined2 *)((longlong)local_7b8 + 0x1a) = 0x24;
    *(undefined2 *)((longlong)local_670 + 0x1a) = 0x25;
    *(undefined2 *)((longlong)local_668 + 0x1a) = 0x26;
    *(undefined2 *)((longlong)local_660 + 0x1a) = 0x27;
    *(undefined2 *)((longlong)local_658 + 0x1a) = 0x28;
    *(undefined2 *)((longlong)local_650 + 0x1a) = 0x29;
    *(undefined2 *)((longlong)local_648 + 0x1a) = 0x2a;
    *(undefined2 *)((longlong)local_640 + 0x1a) = 0x2b;
    *(undefined2 *)((longlong)local_638 + 0x1a) = 0x2c;
    *(undefined2 *)((longlong)local_630 + 0x1a) = 0x2d;
    *(undefined2 *)((longlong)local_628 + 0x1a) = 0x2e;
    *(undefined2 *)((longlong)local_620 + 0x1a) = 0x2f;
    *(undefined2 *)((longlong)local_710 + 0x1a) = 0x30;
    *(undefined2 *)((longlong)local_590 + 0x1a) = 0x31;
    *(undefined2 *)((longlong)local_588 + 0x1a) = 0x32;
    *(undefined2 *)((longlong)local_580 + 0x1a) = 0x33;
    *(undefined2 *)((longlong)local_578 + 0x1a) = 0x34;
    *(undefined2 *)((longlong)local_570 + 0x1a) = 0x35;
    *(undefined2 *)((longlong)local_568 + 0x1a) = 0x36;
    *(undefined2 *)((longlong)local_560 + 0x1a) = 0x37;
    return;
  }
  *(code **)(param_3 + 1000) = FUN_18007f128;
  if (param_3[3] == 0) {
    auVar5._4_4_ = uVar54;
    auVar5._0_4_ = uVar18;
    auVar5._8_8_ = uVar50;
    uVar30 = FUN_1800254e8(auVar5,CONCAT44(uVar55,uVar23),(longlong)param_3,uVar30,param_5,param_6,
                           param_7,param_8,param_9,param_10);
    *(int *)local_4a8 = (int)uVar30;
  }
  if ((int)*local_4a8 != 0) {
    return;
  }
  if (param_3[0x1b] != 0) {
    if (local_b00 == (longlong *)0x0) {
      return;
    }
    FUN_180007ef0(local_b00,(longlong)param_3);
    puVar26 = (undefined8 *)local_b00[0x14];
    if (puVar26 != (undefined8 *)0x0) {
      (**(code **)*puVar26)(puVar26,1);
    }
    FUN_180085c10(local_b00 + 0x17);
    FUN_180085c90(local_b00 + 0x17);
    FUN_180007d40((longlong)local_b00);
    FUN_1800966b8(local_b00);
    (**(code **)(param_3 + 0x448))(4,0);
    return;
  }
  uVar18 = (**(code **)(param_3 + 0x3ec))(param_3[0x270]);
  local_9f8 = (longlong *)CONCAT44(local_9f8._4_4_,uVar18);
  uVar19 = FUN_180026550((longlong)local_a68);
  if (uVar19 == 0) {
    iVar40 = 0;
  }
  else if (uVar19 == 1) {
    iVar40 = 1;
  }
  else {
    iVar40 = 0;
    if (uVar19 == 2) {
      iVar40 = 3;
    }
  }
  uVar19 = FUN_180026550((longlong)local_af0);
  if (uVar19 == 1) {
    uVar22 = 0;
  }
  else {
    uVar22 = FUN_180026550((longlong)local_840);
  }
  uVar20 = FUN_180026550((longlong)local_838);
  local_a08 = (longlong *)CONCAT44(local_a08._4_4_,uVar20);
  uVar20 = FUN_180026550((longlong)piVar36);
  if ((int)local_9f8 == 0) {
    return;
  }
  if (*local_5f0 == 0) {
    local_a00 = (longlong *)((ulonglong)local_a00._4_4_ << 0x20);
LAB_180083234:
    pcVar47 = *(code **)(param_3 + 0x60c);
    FUN_180004498(auStack_4e8,"OFL Volume By Price");
    (*pcVar47)(0x20241105,auStack_4e8);
    FUN_180004590(auStack_4e8);
    plVar33 = local_a40;
    local_a60 = (longlong *)CONCAT44(local_a60._4_4_,uVar20);
    iVar21 = 0;
    local_8c0 = (longlong *)CONCAT44(local_8c0._4_4_,uVar22);
    *local_5f8 = 0;
    *local_5f0 = (uint)((int)local_9f8 != 0);
    do {
      uVar22 = 0;
      if (0 < *param_3) {
        do {
          plVar31 = FUN_18000f880((longlong *)(param_3 + 0x140),iVar21);
          lVar43 = plVar31[6];
          if (lVar43 == 0) {
            if ((code *)plVar31[8] != (code *)0x0) {
              (*(code *)plVar31[8])((int)plVar31[9]);
            }
            lVar43 = plVar31[6];
            if (lVar43 != 0) goto LAB_1800832ec;
            puVar34 = (undefined4 *)((longlong)plVar31 + 0x54);
          }
          else {
LAB_1800832ec:
            iVar45 = (int)plVar31[10];
            if (iVar45 == 0) {
              puVar34 = (undefined4 *)((longlong)plVar31 + 0x54);
            }
            else {
              uVar20 = uVar22 & ((int)uVar22 >> 0x1f ^ 0xffffffffU);
              if (iVar45 <= (int)uVar20) {
                uVar20 = iVar45 - 1;
              }
              puVar34 = (undefined4 *)(lVar43 + (longlong)(int)uVar20 * 4);
            }
          }
          *puVar34 = 0;
          iVar45 = 0;
          do {
            lVar43 = *(longlong *)(param_3 + 0x140);
            if (lVar43 == 0) {
              if (*(code **)(param_3 + 0x144) != (code *)0x0) {
                (**(code **)(param_3 + 0x144))(param_3[0x146]);
              }
              lVar43 = *(longlong *)(param_3 + 0x140);
              if (lVar43 != 0) goto LAB_180083344;
              piVar36 = param_3 + 0x14a;
            }
            else {
LAB_180083344:
              iVar46 = param_3[0x148];
              if (iVar46 == 0) {
                piVar36 = param_3 + 0x14a;
              }
              else {
                iVar41 = iVar21;
                if (iVar46 <= iVar21) {
                  iVar41 = iVar46 + -1;
                }
                piVar36 = (int *)(lVar43 + (longlong)iVar41 * 0x170);
              }
            }
            lVar43 = *(longlong *)(piVar36 + 0x20);
            if (lVar43 == 0) {
              if (*(code **)(piVar36 + 0x24) != (code *)0x0) {
                (**(code **)(piVar36 + 0x24))(piVar36[0x26]);
              }
              lVar43 = *(longlong *)(piVar36 + 0x20);
              if (lVar43 != 0) goto LAB_1800835cc;
              plVar31 = (longlong *)(piVar36 + 0x2a);
            }
            else {
LAB_1800835cc:
              iVar46 = piVar36[0x28];
              if (iVar46 == 0) {
                plVar31 = (longlong *)(piVar36 + 0x2a);
              }
              else {
                iVar41 = iVar45;
                if (iVar46 <= iVar45) {
                  iVar41 = iVar46 + -1;
                }
                plVar31 = (longlong *)(lVar43 + (longlong)iVar41 * 0x28);
              }
            }
            lVar43 = *plVar31;
            if (lVar43 == 0) {
              if ((code *)plVar31[2] != (code *)0x0) {
                (*(code *)plVar31[2])((int)plVar31[3]);
              }
              lVar43 = *plVar31;
              if (lVar43 != 0) goto LAB_180083854;
              piVar36 = (int *)((longlong)plVar31 + 0x24);
            }
            else {
LAB_180083854:
              iVar46 = (int)plVar31[4];
              if (iVar46 == 0) {
                piVar36 = (int *)((longlong)plVar31 + 0x24);
              }
              else {
                uVar20 = uVar22 & ((int)uVar22 >> 0x1f ^ 0xffffffffU);
                if (iVar46 <= (int)uVar20) {
                  uVar20 = iVar46 - 1;
                }
                piVar36 = (int *)(lVar43 + (longlong)(int)uVar20 * 4);
              }
            }
            iVar45 = iVar45 + 1;
            *piVar36 = 0;
          } while (iVar45 < 0xc);
          uVar22 = uVar22 + 1;
        } while ((int)uVar22 < *param_3);
      }
      iVar21 = iVar21 + 1;
    } while (iVar21 < 0x3c);
    uVar54 = local_8c0._0_4_;
    uVar18 = (int)local_a60;
    *local_538 = 0;
    *local_828 = 0;
    if ((((*(short *)((longlong)local_8b8 + 0x24) != 5 &&
           *(short *)((longlong)local_8b8 + 0x24) != 4) ||
         (*(short *)((longlong)local_8b0 + 0x24) != 5 && *(short *)((longlong)local_8b0 + 0x24) != 4
         )) || (*(short *)((longlong)local_8a8 + 0x24) != 5 &&
                *(short *)((longlong)local_8a8 + 0x24) != 4)) ||
       (iVar21 = 0,
       *(short *)((longlong)local_8a0 + 0x24) != 5 && *(short *)((longlong)local_8a0 + 0x24) != 4))
    {
      iVar21 = 1;
    }
    *local_540 = iVar21;
    *(undefined4 *)local_698 = 0xffffffff;
    if (param_3[0x1bc] == 0) {
      uVar30 = *(undefined8 *)(param_3 + 0x13c);
    }
    else {
      uVar30 = *(undefined8 *)(param_3 + 0x4ac);
    }
    *puVar26 = uVar30;
    local_a40 = plVar33;
    if (local_b00 == (longlong *)0x0) {
      local_b00 = (longlong *)FUN_180096150(200);
      local_b00[1] = 0;
      *local_b00 = 0;
      local_b00[3] = 0;
      local_b00[2] = 0;
      local_b00[5] = 0;
      local_b00[4] = 0;
      local_b00[7] = 0;
      local_b00[6] = 0;
      local_b00[9] = 0;
      local_b00[8] = 0;
      local_b00[0xb] = 0;
      local_b00[10] = 0;
      local_b00[0xd] = 0;
      local_b00[0xc] = 0;
      local_b00[0xf] = 0;
      local_b00[0xe] = 0;
      local_b00[0x11] = 0;
      local_b00[0x10] = 0;
      local_b00[0x13] = 0;
      local_b00[0x12] = 0;
      local_b00[0x15] = 0;
      local_b00[0x14] = 0;
      local_b00[0x17] = 0;
      local_b00[0x16] = 0;
      local_b00[0x18] = 0;
      *local_b00 = 0;
      local_b00[1] = 0;
      *(undefined4 *)(local_b00 + 2) = 10;
      *(undefined8 *)((longlong)local_b00 + 0x1c) = 0;
      *(undefined8 *)((longlong)local_b00 + 0x14) = 0;
      *(undefined8 *)((longlong)local_b00 + 0x2c) = 0;
      *(undefined8 *)((longlong)local_b00 + 0x24) = 0;
      *(undefined8 *)((longlong)local_b00 + 0x34) = 0;
      *(undefined4 *)((longlong)local_b00 + 0x3c) = 0;
      *(undefined1 *)(local_b00 + 8) = 0;
      *(undefined4 *)((longlong)local_b00 + 0x44) = 0;
      *(undefined1 *)(local_b00 + 9) = 0;
      local_b00[10] = 0;
      *(undefined1 *)(local_b00 + 0xb) = 0;
      *(undefined4 *)((longlong)local_b00 + 0x5c) = 0;
      *(undefined1 *)(local_b00 + 0xc) = 0;
      *(undefined8 *)((longlong)local_b00 + 100) = 0;
      *(undefined8 *)((longlong)local_b00 + 0x6c) = 0;
      *(undefined8 *)((longlong)local_b00 + 0x74) = 0;
      *(undefined8 *)((longlong)local_b00 + 0x7c) = 0;
      *(undefined4 *)((longlong)local_b00 + 0x84) = 0;
      local_b00[0x11] = 0;
      local_b00[0x12] = 0;
      lVar43 = FUN_180096150(0x20);
      *(longlong *)lVar43 = lVar43;
      *(longlong *)(lVar43 + 8) = lVar43;
      local_b00[0x11] = lVar43;
      *(undefined1 *)(local_b00 + 0x13) = 0;
      *(undefined4 *)((longlong)local_b00 + 0x9c) = 0xffffffff;
      local_b00[0x14] = 0;
      *(undefined2 *)(local_b00 + 0x15) = 0;
      *(undefined8 *)((longlong)local_b00 + 0xac) = 0;
      local_b00[0x17] = 0;
      local_b00[0x18] = 0;
      lVar43 = FUN_180096150(0x140);
      *(longlong *)lVar43 = lVar43;
      *(longlong *)(lVar43 + 8) = lVar43;
      local_b00[0x17] = lVar43;
      *local_b00 = *(longlong *)(param_3 + 0x1c);
      (**(code **)(param_3 + 0x448))(4,local_b00);
      lVar43 = *(longlong *)(param_3 + 0x1c);
      *local_b00 = lVar43;
      if (lVar43 == 0) {
        return;
      }
      if (uVar19 != 6) goto LAB_180083c2c;
      uStack_4c8 = 0;
      local_4d0 = (char *)0x0;
      local_4c0 = (undefined1 *)0x0;
      pcVar32 = FUN_18004fd98((longlong)local_a58);
      FUN_180004498(&local_4d0,pcVar32);
      if ((local_4d0 == (char *)0x0) ||
         (uVar18 = extraout_s0_73, uVar54 = extraout_var_73, uVar30 = extraout_var_x00157,
         *local_4d0 == '\0')) {
        iVar21 = FUN_1800045e8("OFL VbP");
        FUN_1800079f8((longlong *)&local_4d0,extraout_x10 + 0x90,iVar21);
        uStack_4c8 = CONCAT44(1,(int)uStack_4c8);
        uVar18 = extraout_s0_74;
        uVar54 = extraout_var_74;
        uVar30 = extraout_var_x00158;
      }
      pcVar28 = local_4d0;
      pcVar32 = local_4d0;
      if (local_4d0 == (char *)0x0) {
        pcVar32 = "";
      }
      auVar12._4_4_ = uVar54;
      auVar12._0_4_ = uVar18;
      auVar12._8_8_ = uVar30;
      FUN_180007e38(auVar12,CONCAT44(uVar55,uVar23),local_b00,(longlong)param_3,pcVar32,param_6,
                    param_7,param_8,param_9,param_10);
      uVar22 = FUN_180007cb0(0.5,extraout_x0,(uint *)(local_a10 + 3),
                             (uint *)((longlong)local_a10 + 0x1c));
      if (pcVar28 == (char *)0x0) {
        pcVar28 = "";
      }
      FUN_180007d98(local_b00,(longlong)param_3,(uint)*(ushort *)(local_920 + 5),(longlong)pcVar28,
                    *(undefined4 *)(extraout_x13 + 0x18),*(undefined4 *)(extraout_x13 + 0x1c),uVar22
                   );
      FUN_180004590(&local_4d0);
LAB_1800843ac:
      puVar26 = (undefined8 *)FUN_180096150(0x50);
      puVar26[1] = 0;
      *puVar26 = 0;
      puVar26[3] = 0;
      puVar26[2] = 0;
      puVar26[5] = 0;
      puVar26[4] = 0;
      puVar26[7] = 0;
      puVar26[6] = 0;
      puVar26[9] = 0;
      puVar26[8] = 0;
      uVar48 = FUN_180026708((longlong)local_a90);
      *puVar26 = nsSwingIndexHandler::SwingIndexHandler::vftable;
      puVar26[1] = 0;
      puVar26[2] = 0;
      lVar43 = FUN_180096150(0x20);
      *(longlong *)lVar43 = lVar43;
      *(longlong *)(lVar43 + 8) = lVar43;
      puVar26[1] = lVar43;
      puVar26[3] = 0;
      *puVar26 = nsGrabberBasedLookback::GrabberBasedLookback::vftable;
      puVar26[4] = param_3 + 0x298;
      puVar26[5] = param_3 + 0x2a2;
      puVar26[6] = local_b00;
      puVar26[8] = local_ac8;
      puVar26[9] = local_ad0;
      *(int *)((longlong)puVar26 + 0x1c) = (int)uVar48;
      *(undefined4 *)(puVar26 + 7) = 0;
      FUN_180009d88(puVar26 + 1);
LAB_180084438:
      local_b00[0x14] = (longlong)puVar26;
    }
    else {
      FUN_180085c10(local_b00 + 0x17);
      if (uVar19 == 6) {
        uStack_4c8 = 0;
        local_4d0 = (char *)0x0;
        local_4c0 = (undefined1 *)0x0;
        pcVar32 = FUN_18004fd98((longlong)local_a58);
        FUN_180004498(&local_4d0,pcVar32);
        if ((local_4d0 == (char *)0x0) ||
           (uVar51 = extraout_s0_75, uVar53 = extraout_var_75, uVar30 = extraout_var_x00159,
           *local_4d0 == '\0')) {
          iVar21 = FUN_1800045e8("OFL VbP");
          FUN_1800079f8((longlong *)&local_4d0,0x1800db090,iVar21);
          uStack_4c8 = CONCAT44(1,(int)uStack_4c8);
          uVar51 = extraout_s0_76;
          uVar53 = extraout_var_76;
          uVar30 = extraout_var_x00160;
        }
        pcVar28 = local_4d0;
        pcVar32 = local_4d0;
        if (local_4d0 == (char *)0x0) {
          pcVar32 = "";
        }
        auVar6._4_4_ = uVar53;
        auVar6._0_4_ = uVar51;
        auVar6._8_8_ = uVar30;
        FUN_180007f78(auVar6,CONCAT44(uVar55,uVar23),(longlong)local_b00,(longlong)param_3,pcVar32,
                      param_6,param_7,param_8,param_9,param_10);
        uVar22 = FUN_180007cb0(0.5,extraout_x0_00,(uint *)(local_a10 + 3),
                               (uint *)((longlong)local_a10 + 0x1c));
        param_7 = (ulonglong)*(uint *)(extraout_x13_00 + 0x18);
        param_8 = (ulonglong)*(uint *)(extraout_x13_00 + 0x1c);
        param_9 = (ulonglong)uVar22;
        if (pcVar28 == (char *)0x0) {
          pcVar28 = "";
        }
        FUN_180007d98(local_b00,(longlong)param_3,(uint)*(ushort *)(local_920 + 5),(longlong)pcVar28
                      ,*(uint *)(extraout_x13_00 + 0x18),*(uint *)(extraout_x13_00 + 0x1c),uVar22);
        FUN_180004590(&local_4d0);
      }
      if ((longlong *)local_b00[0x14] != (longlong *)0x0) {
        param_7 = 0;
        plVar33 = FUN_1800be740((longlong *)local_b00[0x14],0,0x1800fd800,0x1800fd840,0);
        if (plVar33 != (longlong *)0x0 && uVar19 != 6) {
          FUN_180007ef0(local_b00,(longlong)param_3);
          goto LAB_180083c2c;
        }
      }
      if ((longlong *)local_b00[0x14] != (longlong *)0x0) {
        param_7 = 0;
        pTVar39 = &nsGrabberBasedLookback::GrabberBasedLookback::RTTI_Type_Descriptor;
        plVar33 = FUN_1800be740((longlong *)local_b00[0x14],0,0x1800fd800,0x1800fd840,0);
        if (plVar33 == (longlong *)0x0 && uVar19 == 6) {
          uStack_4c8 = 0;
          local_4d0 = (char *)0x0;
          local_4c0 = (undefined1 *)0x0;
          pcVar32 = FUN_18004fd98((longlong)local_a58);
          FUN_180004498(&local_4d0,pcVar32);
          if ((local_4d0 == (char *)0x0) ||
             (uVar18 = extraout_s0_77, uVar54 = extraout_var_77, uVar30 = extraout_var_x00161,
             *local_4d0 == '\0')) {
            iVar21 = FUN_1800045e8("OFL VbP");
            FUN_1800079f8((longlong *)&local_4d0,0x1800db090,iVar21);
            uStack_4c8 = CONCAT44(1,(int)uStack_4c8);
            uVar18 = extraout_s0_78;
            uVar54 = extraout_var_78;
            uVar30 = extraout_var_x00162;
          }
          pcVar28 = local_4d0;
          pcVar32 = local_4d0;
          if (local_4d0 == (char *)0x0) {
            pcVar32 = "";
          }
          auVar7._4_4_ = uVar54;
          auVar7._0_4_ = uVar18;
          auVar7._8_8_ = uVar30;
          FUN_180007f78(auVar7,CONCAT44(uVar55,uVar23),(longlong)local_b00,(longlong)param_3,pcVar32
                        ,pTVar39,param_7,param_8,param_9,param_10);
          if (pcVar28 == (char *)0x0) {
            pcVar28 = "";
          }
          auVar8._4_4_ = extraout_var_79;
          auVar8._0_4_ = extraout_s0_79;
          auVar8._8_8_ = extraout_var_x00163;
          FUN_180007e38(auVar8,CONCAT44(uVar55,uVar23),local_b00,(longlong)param_3,pcVar28,pTVar39,
                        param_7,param_8,param_9,param_10);
          FUN_180004590(&local_4d0);
          goto LAB_1800843ac;
        }
      }
LAB_180083c2c:
      if (uVar19 == 0) {
        fVar52 = FUN_180026608((longlong)local_a80);
        bVar15 = FUN_180026690((longlong)local_aa8);
        if (bVar15) {
          bVar16 = *(byte *)(local_a40 + 3);
          uVar22 = (uint)bVar16;
          if (bVar16 == 0x12 || bVar16 == 0x15) {
            uVar20 = *(uint *)((longlong)local_a40 + 0x1c);
            if (bVar16 != 0x12) goto LAB_180083d64;
LAB_180083d84:
            uVar48 = (ulonglong)*(uint *)(local_a40 + 4);
          }
          else if (bVar16 == 0x13) {
            uVar20 = *(uint *)((longlong)local_a40 + 0x1c);
            uVar22 = FUN_180026550((longlong)local_ae8);
            uVar48 = (ulonglong)uVar22;
          }
          else {
            uVar48 = FUN_180026708((longlong)local_ae8);
            uVar20 = (uint)uVar48;
            uVar22 = extraout_w11;
LAB_180083d64:
            if ((uVar22 == 0x14) || (uVar22 == 0x15)) goto LAB_180083d84;
            uVar22 = FUN_180026550((longlong)local_ae8);
            uVar48 = (ulonglong)uVar22;
          }
          FUN_180004498(&local_490,"Rotation Calculator");
          auVar11._4_4_ = extraout_var_80;
          auVar11._0_4_ = extraout_s0_80;
          auVar11._8_8_ = extraout_var_x00164;
          uVar30 = FUN_1800939c8(auVar11,CONCAT44(uVar55,uVar23),(longlong)param_3,(ulonglong)uVar20
                                 ,uVar48,&local_490,param_7,param_8,param_9,param_10);
          puVar14 = local_4b0;
          *local_4b0 = (uint)uVar30 & 0xff;
          FUN_180004590(&local_490);
          if (*puVar14 == 0) {
            return;
          }
          plVar33 = (longlong *)(**(code **)(param_3 + 0x45c))(uVar20,uVar48,2);
          pfVar35 = (float *)*plVar33;
          if (pfVar35 == (float *)0x0) {
            return;
          }
          if (pfVar35[1] <= 0.1) {
            fVar52 = 1.0;
          }
          else {
            fVar52 = *pfVar35 / pfVar35[1];
          }
        }
        puVar26 = (undefined8 *)FUN_180096150(0x50);
        puVar26[1] = 0;
        *puVar26 = 0;
        puVar26[3] = 0;
        puVar26[2] = 0;
        puVar26[5] = 0;
        puVar26[4] = 0;
        puVar26[7] = 0;
        puVar26[6] = 0;
        puVar26[9] = 0;
        puVar26[8] = 0;
        uVar48 = FUN_180026708((longlong)local_a90);
        uVar37 = FUN_180026708((longlong)local_a48);
        *puVar26 = nsSwingIndexHandler::SwingIndexHandler::vftable;
        puVar26[1] = 0;
        puVar26[2] = 0;
        lVar43 = FUN_180096150(0x20);
        *(longlong *)lVar43 = lVar43;
        *(longlong *)(lVar43 + 8) = lVar43;
        puVar26[1] = lVar43;
        puVar26[3] = 0;
        *puVar26 = nsZigZag::ZigZag::vftable;
        puVar26[4] = param_3 + 0x298;
        puVar26[5] = param_3 + 0x2a2;
        puVar26[6] = local_ac8;
        *(int *)(puVar26 + 7) = (int)uVar37;
        *(float *)((longlong)puVar26 + 0x3c) = fVar52;
        puVar26[9] = local_ad0;
        *(undefined4 *)(puVar26 + 3) = uVar54;
        *(int *)((longlong)puVar26 + 0x1c) = (int)uVar48;
        *(undefined4 *)(puVar26 + 8) = 0;
        FUN_180009d88(puVar26 + 1);
        local_b00[0x14] = (longlong)puVar26;
      }
      else if (uVar19 == 3) {
        puVar26 = (undefined8 *)FUN_180096150(0x60);
        puVar26[1] = 0;
        *puVar26 = 0;
        puVar26[3] = 0;
        puVar26[2] = 0;
        puVar26[5] = 0;
        puVar26[4] = 0;
        puVar26[7] = 0;
        puVar26[6] = 0;
        puVar26[9] = 0;
        puVar26[8] = 0;
        puVar26[0xb] = 0;
        puVar26[10] = 0;
        uVar48 = FUN_180026708((longlong)local_a90);
        uVar37 = FUN_180026708((longlong)local_9f0);
        *puVar26 = nsSwingIndexHandler::SwingIndexHandler::vftable;
        puVar26[1] = 0;
        puVar26[2] = 0;
        lVar43 = FUN_180096150(0x20);
        *(longlong *)lVar43 = lVar43;
        *(longlong *)(lVar43 + 8) = lVar43;
        puVar26[1] = lVar43;
        puVar26[3] = 0;
        *puVar26 = nsRollingLookback::RollingLookback::vftable;
        puVar26[4] = param_3 + 0x298;
        puVar26[5] = param_3 + 0x2a2;
        *(undefined4 *)(puVar26 + 6) = uVar18;
        *(int *)((longlong)puVar26 + 0x34) = (int)uVar37;
        puVar26[8] = local_ac8;
        puVar26[9] = local_ad0;
        puVar26[10] = 0;
        puVar26[0xb] = 0;
        lVar43 = FUN_180096150(0x20);
        *(longlong *)lVar43 = lVar43;
        *(longlong *)(lVar43 + 8) = lVar43;
        puVar26[10] = lVar43;
        *(int *)((longlong)puVar26 + 0x1c) = (int)uVar48;
        *(undefined4 *)(puVar26 + 7) = 0;
        FUN_180009d88(puVar26 + 1);
        FUN_180009d88(puVar26 + 10);
        local_b00[0x14] = (longlong)puVar26;
      }
      else if (uVar19 == 1) {
        bVar16 = *(byte *)(local_9e8 + 3);
        uVar22 = (uint)bVar16;
        if (bVar16 == 0x12 || bVar16 == 0x15) {
          uVar20 = *(uint *)((longlong)local_9e8 + 0x1c);
          if (bVar16 != 0x12) goto LAB_180083fc8;
LAB_180083fe8:
          uVar48 = (ulonglong)*(uint *)(local_9e8 + 4);
        }
        else if (bVar16 == 0x13) {
          uVar20 = *(uint *)((longlong)local_9e8 + 0x1c);
          uVar22 = FUN_180026550((longlong)local_a50);
          uVar48 = (ulonglong)uVar22;
        }
        else {
          uVar48 = FUN_180026708((longlong)local_a50);
          uVar20 = (uint)uVar48;
          uVar22 = extraout_w11_00;
LAB_180083fc8:
          if ((uVar22 == 0x14) || (uVar22 == 0x15)) goto LAB_180083fe8;
          uVar22 = FUN_180026550((longlong)local_a50);
          uVar48 = (ulonglong)uVar22;
        }
        FUN_180004498(&local_490,"Balance Areas");
        auVar10._4_4_ = extraout_var_81;
        auVar10._0_4_ = extraout_s0_81;
        auVar10._8_8_ = extraout_var_x00165;
        uVar30 = FUN_1800939c8(auVar10,CONCAT44(uVar55,uVar23),(longlong)param_3,(ulonglong)uVar20,
                               uVar48,&local_490,param_7,param_8,param_9,param_10);
        puVar14 = local_5e8;
        *local_5e8 = (uint)uVar30 & 0xff;
        FUN_180004590(&local_490);
        if (*puVar14 == 0) {
          return;
        }
        uVar30 = *(undefined8 *)((longlong)local_a50 + 0x1c);
        iVar21 = *(int *)((longlong)local_a50 + 0x24);
        puVar26 = (undefined8 *)FUN_180096150(0x30);
        puVar26[1] = 0;
        *puVar26 = 0;
        puVar26[3] = 0;
        puVar26[2] = 0;
        puVar26[5] = 0;
        puVar26[4] = 0;
        *puVar26 = nsSwingIndexHandler::SwingIndexHandler::vftable;
        puVar26[1] = 0;
        puVar26[2] = 0;
        lVar43 = FUN_180096150(0x20);
        *(longlong *)lVar43 = lVar43;
        *(longlong *)(lVar43 + 8) = lVar43;
        puVar26[1] = lVar43;
        puVar26[3] = 0;
        *puVar26 = nsBalanceArea::BalanceArea::vftable;
        *(undefined8 *)((longlong)puVar26 + 0x24) = uVar30;
        *(int *)((longlong)puVar26 + 0x2c) = iVar21;
        *(undefined4 *)(puVar26 + 4) = 0;
        FUN_180009d88(puVar26 + 1);
        local_b00[0x14] = (longlong)puVar26;
      }
      else {
        if (uVar19 == 2) {
          *(undefined1 *)(local_b00 + 0x15) = 1;
          puVar26 = (undefined8 *)FUN_180096150(0x48);
          puVar26[1] = 0;
          *puVar26 = 0;
          puVar26[3] = 0;
          puVar26[2] = 0;
          puVar26[5] = 0;
          puVar26[4] = 0;
          puVar26[7] = 0;
          puVar26[6] = 0;
          puVar26[8] = 0;
          uVar48 = FUN_180026708((longlong)local_a90);
          uVar22 = FUN_180026550((longlong)local_ab0);
          *puVar26 = nsSwingIndexHandler::SwingIndexHandler::vftable;
          puVar26[1] = 0;
          puVar26[2] = 0;
          lVar43 = FUN_180096150(0x20);
          *(longlong *)lVar43 = lVar43;
          *(longlong *)(lVar43 + 8) = lVar43;
          puVar26[1] = lVar43;
          puVar26[3] = 0;
          *puVar26 = nsSwingTime::SwingTime::vftable;
          puVar26[4] = param_3 + 0x298;
          puVar26[5] = param_3 + 0x2a2;
          puVar26[6] = local_ac8;
          puVar26[7] = local_ad0;
          *(uint *)(puVar26 + 8) = uVar22;
          *(int *)((longlong)puVar26 + 0x44) = (int)local_a08;
          *(undefined4 *)(puVar26 + 3) = uVar54;
          *(int *)((longlong)puVar26 + 0x1c) = (int)uVar48;
          FUN_180009d88(puVar26 + 1);
          goto LAB_180084438;
        }
        if (uVar19 == 4) {
          uVar50 = *(undefined8 *)((longlong)local_9e0 + 0x1c);
          iVar21 = *(int *)((longlong)local_9e0 + 0x24);
          uVar30 = *(undefined8 *)((longlong)local_9d8 + 0x1c);
          iVar45 = *(int *)((longlong)local_9d8 + 0x24);
          puVar26 = (undefined8 *)FUN_180096150(0x98);
          puVar26[1] = 0;
          *puVar26 = 0;
          puVar26[3] = 0;
          puVar26[2] = 0;
          puVar26[5] = 0;
          puVar26[4] = 0;
          puVar26[7] = 0;
          puVar26[6] = 0;
          puVar26[9] = 0;
          puVar26[8] = 0;
          puVar26[0xb] = 0;
          puVar26[10] = 0;
          puVar26[0xd] = 0;
          puVar26[0xc] = 0;
          puVar26[0xf] = 0;
          puVar26[0xe] = 0;
          puVar26[0x11] = 0;
          puVar26[0x10] = 0;
          puVar26[0x12] = 0;
          uVar48 = FUN_180026708((longlong)local_a90);
          *puVar26 = nsSwingIndexHandler::SwingIndexHandler::vftable;
          puVar26[1] = 0;
          puVar26[2] = 0;
          lVar43 = FUN_180096150(0x20);
          *(longlong *)lVar43 = lVar43;
          *(longlong *)(lVar43 + 8) = lVar43;
          puVar26[1] = lVar43;
          puVar26[3] = 0;
          *puVar26 = nsSubgraphSwing::SubgraphSwing::vftable;
          puVar26[4] = param_3 + 0x298;
          puVar26[5] = param_3 + 0x2a2;
          puVar26[6] = local_ac8;
          puVar26[7] = local_ad0;
          puVar26[8] = uVar50;
          *(int *)(puVar26 + 9) = iVar21;
          *(undefined8 *)((longlong)puVar26 + 0x4c) = uVar30;
          *(int *)((longlong)puVar26 + 0x54) = iVar45;
          puVar26[0xd] = 0;
          puVar26[0xe] = 0;
          puVar26[0xf] = 0;
          puVar26[0x10] = 0;
          puVar26[0x11] = 0;
          puVar26[0x12] = 0;
          *(undefined4 *)(puVar26 + 3) = uVar54;
          *(int *)((longlong)puVar26 + 0x1c) = (int)uVar48;
          FUN_180009d88(puVar26 + 1);
          puVar26[0xb] = 0;
          *(undefined4 *)(puVar26 + 0xc) = 0;
          if (puVar26[0xd] != puVar26[0xe]) {
            puVar26[0xe] = puVar26[0xd];
          }
          if (puVar26[0x10] != puVar26[0x11]) {
            puVar26[0x11] = puVar26[0x10];
          }
          local_b00[0x14] = (longlong)puVar26;
          goto LAB_18008443c;
        }
        if (uVar19 != 5) {
          if (uVar19 != 6) {
            return;
          }
          goto LAB_1800843ac;
        }
        bVar16 = *(byte *)(local_9d0 + 3);
        uVar22 = (uint)bVar16;
        if (bVar16 == 0x12 || bVar16 == 0x15) {
          uVar20 = *(uint *)((longlong)local_9d0 + 0x1c);
          if (bVar16 != 0x12) goto LAB_1800842ac;
LAB_1800842cc:
          uVar48 = (ulonglong)*(uint *)(local_9d0 + 4);
        }
        else if (bVar16 == 0x13) {
          uVar20 = *(uint *)((longlong)local_9d0 + 0x1c);
          uVar22 = FUN_180026550((longlong)local_a88);
          uVar48 = (ulonglong)uVar22;
        }
        else {
          uVar48 = FUN_180026708((longlong)local_a88);
          uVar20 = (uint)uVar48;
          uVar22 = extraout_w11_01;
LAB_1800842ac:
          if ((uVar22 + 0xec & 0xff) < 2) goto LAB_1800842cc;
          uVar22 = FUN_180026550((longlong)local_a88);
          uVar48 = (ulonglong)uVar22;
        }
        FUN_180004498(&local_490,"OFL Volume By Price");
        auVar9._4_4_ = extraout_var_82;
        auVar9._0_4_ = extraout_s0_82;
        auVar9._8_8_ = extraout_var_x00166;
        uVar30 = FUN_1800939c8(auVar9,CONCAT44(uVar55,uVar23),(longlong)param_3,(ulonglong)uVar20,
                               uVar48,&local_490,param_7,param_8,param_9,param_10);
        puVar14 = local_550;
        *local_550 = (uint)uVar30 & 0xff;
        FUN_180004590(&local_490);
        if (*puVar14 == 0) {
          return;
        }
        puVar26 = (undefined8 *)FUN_180096150(0x20);
        puVar26[1] = 0;
        *puVar26 = 0;
        puVar26[3] = 0;
        puVar26[2] = 0;
        *puVar26 = nsSwingIndexHandler::SwingIndexHandler::vftable;
        puVar26[1] = 0;
        puVar26[2] = 0;
        lVar43 = FUN_180096150(0x20);
        *(longlong *)lVar43 = lVar43;
        *(longlong *)(lVar43 + 8) = lVar43;
        puVar26[1] = lVar43;
        puVar26[3] = 0;
        *puVar26 = nsOFL_VBP_Reference::OFL_VBP_Reference::vftable;
        *(undefined4 *)(puVar26 + 3) = uVar54;
        FUN_180009d88(puVar26 + 1);
        local_b00[0x14] = (longlong)puVar26;
        plVar33 = (longlong *)
                  (**(code **)(param_3 + 0x45c))
                            (*(longlong *)((longlong)local_a88 + 0x1c),
                             *(longlong *)((longlong)local_a88 + 0x1c) >> 0x20,4);
        local_618 = *plVar33;
        if (local_618 == 0) {
          local_618 = 0;
        }
      }
    }
LAB_18008443c:
    uStack_418 = 0;
    lStack_420 = 0;
    local_430 = 0x100000000;
    uStack_428 = 0;
    _auStack_408 = 0;
    local_410 = 0;
    local_3f8 = 0;
    local_3f4 = 0;
    local_400 = 0;
    fStack_3e8 = 0.0;
    uStack_3e4 = 0;
    local_3f0 = 0;
    uStack_3ec = 0;
    local_3d8 = 0;
    local_3e0 = 0;
    lStack_3c8 = 0;
    local_3d0 = 0;
    local_3b8 = 0;
    local_3c0 = 0;
    uStack_3a8 = 0;
    local_3b0 = 0;
    local_398 = 0;
    local_3a0 = 0;
    uStack_388 = 0;
    uStack_384 = 0;
    local_390 = 0;
    uStack_38c = 0;
    local_378 = 0;
    local_374 = 0;
    local_373 = 0;
    local_372 = 0;
    local_371 = 0;
    local_380 = 0;
    local_37c = 0;
    uStack_368 = 0;
    uStack_367 = 0;
    uStack_366 = 0;
    uStack_364 = 0;
    local_370 = 0;
    uStack_36c = 0;
    local_358 = 0;
    local_354 = 0;
    local_360 = 0;
    uStack_35c = 0;
    uStack_348 = 0;
    local_350 = 0;
    uStack_34c = 0;
    local_338 = 0;
    uStack_340 = 0;
    uStack_328 = 0;
    local_330 = 0;
    uStack_318 = 0;
    local_320 = 0;
    uStack_308 = 0;
    local_310 = 0;
    FUN_180004498(&lStack_420,"Arial");
    local_3f4 = 3;
    local_3f0 = 0;
    uStack_3e4 = 0x41200000;
    local_3e0 = 0x7fffffffffffffff;
    local_3d8 = 0x500000000;
    local_37c = 0xc0c0c0;
    local_378 = 7;
    _auStack_408 = CONCAT17(uStack_401,7);
    local_400 = 0;
    local_3f8 = local_3f8 & 0xffffff00;
    uStack_3ec = 0;
    fStack_3e8 = 0.0;
    local_3d0 = 0;
    lStack_3c8 = 0;
    local_3c0 = 0;
    local_3b8 = 0;
    local_3b0 = 0;
    uStack_3a8 = 0;
    local_3a0 = 0x100000000;
    local_398 = (local_398 >> 0x18 & 0xff) << 0x18;
    local_390 = local_390 & 0xffffff00;
    uStack_38c = 0;
    uStack_388 = 1;
    uStack_384 = 0;
    local_380 = 0;
    local_374 = 0;
    local_373 = 0;
    local_372 = 0;
    local_371 = 0;
    local_370 = 0;
    uStack_36c = 0;
    uStack_368 = 0;
    uStack_367 = 0;
    uStack_364 = 0;
    local_360 = 0;
    uStack_35c = 0;
    local_358 = 0;
    local_354 = 0;
    local_350 = 0;
    uStack_34c = uStack_34c & 0xffffff00;
    uStack_348 = 0;
    uStack_340 = 0;
    local_338 = 0;
    local_330 = 0xa00000002;
    local_320 = 0;
    uStack_318 = 0;
    uStack_328 = 0x400000003e4ccccd;
    local_310 = 0;
    uStack_308 = 0;
    local_430 = CONCAT44(local_430._4_4_,iVar40);
    uVar48 = FUN_180026708((longlong)local_9c8);
    local_430 = CONCAT44((int)uVar48,(undefined4)local_430);
    bVar15 = FUN_180026690((longlong)local_9c0);
    uStack_428 = CONCAT71(uStack_428._1_7_,bVar15);
    uVar22 = FUN_180026550((longlong)local_9b8);
    switch(uVar22) {
    default:
      pcVar32 = "Arial";
      break;
    case 1:
      pcVar32 = "Consolas";
      break;
    case 2:
      pcVar32 = "Segoe UI";
      break;
    case 3:
      pcVar32 = "Tahoma";
    }
    FUN_18000bbb8(&lStack_420,pcVar32);
    uVar48 = FUN_180026708((longlong)local_9b0);
    _auStack_408 = CONCAT44(stack0xfffffffffffffbfc,(int)uVar48);
    bVar15 = FUN_180026690((longlong)local_9a8);
    auStack_408 = (undefined1  [5])CONCAT14(bVar15,auStack_408._0_4_);
    bVar15 = FUN_180026690((longlong)local_9a0);
    _auStack_408 = CONCAT15(bVar15,auStack_408);
    uVar22 = FUN_180026550((longlong)local_998);
    local_400 = CONCAT44(local_400._4_4_,uVar22);
    uVar48 = FUN_180026708((longlong)local_990);
    local_400 = CONCAT44((int)uVar48,(undefined4)local_400);
    bVar15 = FUN_180026690((longlong)local_988);
    local_3f8 = CONCAT31(local_3f8._1_3_,bVar15);
    uVar22 = FUN_180026550((longlong)local_980);
    switch(uVar22) {
    default:
      local_3f4 = 0;
      break;
    case 1:
      local_3f4 = 1;
      break;
    case 2:
      local_3f4 = 2;
      break;
    case 3:
      local_3f4 = 3;
    }
    local_3f0 = FUN_180026550((longlong)local_978);
    uVar48 = FUN_180026708((longlong)local_970);
    uStack_3ec = (undefined4)uVar48;
    fStack_3e8 = FUN_180026608((longlong)local_968);
    FUN_180026708((longlong)local_960);
    FUN_180026608((longlong)local_958);
    FUN_180026550(extraout_x11);
    local_3e0 = 0x7fffffff00000000;
    uVar22 = FUN_180026550((longlong)local_950);
    local_3d8 = CONCAT44(local_3d8._4_4_,uVar22);
    if (iVar40 == 1) {
      iVar21 = (int)local_898[3];
      iVar45 = *(int *)((longlong)local_898 + 0x1c);
      plVar33 = local_890;
    }
    else {
      iVar21 = (int)local_888[3];
      iVar45 = *(int *)((longlong)local_888 + 0x1c);
      plVar33 = local_880;
    }
    local_3d0 = CONCAT44(iVar45,iVar21);
    lStack_3c8 = plVar33[3];
    local_3c0 = local_858[3];
    local_3b8 = CONCAT44((int)local_870[3],(int)local_878[3]);
    local_3b0 = CONCAT44((int)local_868[3],*(int *)((longlong)local_870 + 0x1c));
    uStack_3a8 = CONCAT44(uStack_3a8._4_4_,*(int *)((longlong)local_868 + 0x1c));
    bVar15 = FUN_180026690((longlong)local_948);
    _auStack_408 = CONCAT16(bVar15,_auStack_408);
    uStack_3a8 = CONCAT44((int)local_860[3],(undefined4)uStack_3a8);
    local_3a0 = (ulonglong)CONCAT24((short)local_860[5],(int)*(short *)((longlong)local_860 + 0x26))
    ;
    bVar15 = FUN_180026690((longlong)local_918);
    local_398 = CONCAT71(local_398._1_7_,bVar15);
    bVar15 = FUN_180026690((longlong)local_a78);
    local_398._0_2_ = CONCAT11(bVar15,(byte)local_398);
    bVar15 = FUN_180026690((longlong)local_940);
    local_398._0_3_ = CONCAT12(bVar15,(ushort)local_398);
    local_398 = CONCAT44((int)local_7b0[3],(undefined4)local_398);
    bVar15 = FUN_180026690((longlong)local_910);
    local_390 = CONCAT31(local_390._1_3_,bVar15);
    uStack_38c = FUN_180026550((longlong)local_850);
    uVar48 = FUN_180026708((longlong)local_938);
    uStack_388 = (undefined4)uVar48;
    uVar48 = FUN_180026708((longlong)local_930);
    uStack_364 = (undefined4)uVar48;
    uStack_384 = FUN_180026550((longlong)local_908);
    uVar48 = FUN_180026708((longlong)local_900);
    local_380 = (undefined4)uVar48;
    local_37c = FUN_180026888((longlong)local_8f8);
    uVar48 = FUN_180026708((longlong)local_8f0);
    local_378 = (undefined4)uVar48;
    local_374 = FUN_180026690((longlong)local_8e8);
    local_373 = FUN_180026690((longlong)local_8e0);
    local_372 = FUN_180026690((longlong)local_8c8);
    uStack_36c = 0;
    local_371 = FUN_180026690((longlong)local_8d8);
    uStack_368 = FUN_180026690((longlong)local_8d0);
    if (uVar19 == 5) {
      _auStack_408 = (uint7)_auStack_408;
      uStack_368 = 0;
      uStack_367 = 1;
      local_398._0_2_ = (ushort)(byte)local_398;
      local_358 = *(undefined4 *)((longlong)local_a88 + 0x24);
      local_360 = (undefined4)*(undefined8 *)((longlong)local_a88 + 0x1c);
      uStack_35c = (undefined4)((ulonglong)*(undefined8 *)((longlong)local_a88 + 0x1c) >> 0x20);
      local_354 = FUN_180026550((longlong)local_848);
      local_350 = (int)local_a08;
    }
    else {
      local_354 = FUN_180026550((longlong)local_ab0);
      local_350 = (int)local_a08;
      uVar4 = (undefined1)uStack_34c;
      if (extraout_w11_02 == 2 && (int)local_a08 == 0) {
        uVar4 = 1;
      }
      uStack_34c = CONCAT31(uStack_34c._1_3_,uVar4);
    }
    plVar33 = FUN_180085d68((longlong)(local_b00 + 0x17),local_b00[0x17],(undefined4 *)&local_430);
    local_a60 = (longlong *)CONCAT44(local_a60._4_4_,param_3[9]);
    if (*(char *)(*(longlong *)(local_b00[0x17] + 8) + 0xd8) != '\0') {
      local_4a8 = (longlong *)0x0;
      FUN_18001aaf8(plVar33,(longlong *)&local_4a8,(byte *)&local_a60);
      plVar31 = local_4a8;
      plVar33 = DAT_1800ffbc8;
      if (local_4a8 != DAT_1800ffbc8) {
        lVar43 = (DAT_1800ffbf0 &
                 (((((ulonglong)*(byte *)(local_4a8 + 2) ^ 0xcbf29ce484222325) * 0x100000001b3 ^
                   (ulonglong)*(byte *)((longlong)local_4a8 + 0x11)) * 0x100000001b3 ^
                  (ulonglong)*(byte *)((longlong)local_4a8 + 0x12)) * 0x100000001b3 ^
                 (ulonglong)*(byte *)((longlong)local_4a8 + 0x13)) * 0x100000001b3) * 0x10;
        if (*(longlong **)(lVar43 + DAT_1800ffbd8 + 8) == local_4a8) {
          plVar1 = (longlong *)(lVar43 + DAT_1800ffbd8 + 8);
          if (*(longlong **)(lVar43 + DAT_1800ffbd8) == local_4a8) {
            *(longlong **)(lVar43 + DAT_1800ffbd8) = DAT_1800ffbc8;
            *plVar1 = (longlong)plVar33;
          }
          else {
            *plVar1 = local_4a8[1];
          }
        }
        else if (*(longlong **)(lVar43 + DAT_1800ffbd8) == local_4a8) {
          *(longlong *)(lVar43 + DAT_1800ffbd8) = *local_4a8;
        }
        lVar43 = *local_4a8;
        DAT_1800ffbd0 = DAT_1800ffbd0 + -1;
        *(longlong *)local_4a8[1] = lVar43;
        *(longlong *)(lVar43 + 8) = local_4a8[1];
        FUN_18001c928(local_4a8 + 3);
        FUN_1800966b8(plVar31);
      }
    }
    FUN_18007f070((longlong)&local_430);
  }
  else {
    local_a00 = (longlong *)CONCAT44(local_a00._4_4_,param_3[3]);
    if (param_3[3] == 0) goto LAB_180083234;
  }
  if (local_b00 == (longlong *)0x0) {
    return;
  }
  if (local_b00[0x14] == 0) {
    return;
  }
  if (uVar19 == 5) {
    if (*local_550 == 0) {
      return;
    }
    if (local_618 == 0) {
      return;
    }
  }
  else {
    if (uVar19 == 1) {
      uVar22 = *local_5e8;
    }
    else {
      if ((uVar19 != 0) || (bVar15 = FUN_180026690((longlong)local_aa8), !bVar15))
      goto LAB_180084ba8;
      uVar22 = *local_4b0;
    }
    if (uVar22 == 0) {
      return;
    }
  }
LAB_180084ba8:
  lVar43 = *(longlong *)(param_3 + 0x1c);
  *local_b00 = lVar43;
  if (lVar43 != 0) {
    if (*(int *)(lVar43 + 0x40) == 0) {
      *(undefined4 *)(lVar43 + 0x40) = 1000;
      *(undefined4 *)(lVar43 + 0x44) = 0x1234abcd;
      *(undefined8 *)(lVar43 + 0x48) = 0;
      *(undefined8 *)(lVar43 + 0x50) = 0xffffffff00000000;
      *(undefined8 *)(lVar43 + 0x58) = 0xffffffff;
      *(undefined8 *)(lVar43 + 0x60) = 0;
      *(undefined8 *)(lVar43 + 0x68) = 0;
      *(undefined4 *)(lVar43 + 0x70) = 0;
    }
    else {
      if (*(int *)(lVar43 + 0x40) != 1000) {
        return;
      }
      if (*(int *)(lVar43 + 0x44) != 0x1234abcd) {
        return;
      }
    }
    if (uVar19 == 6) {
      lVar43 = *(longlong *)(param_3 + 0x1c);
      *local_b00 = lVar43;
      if (lVar43 != 0) {
        if ((param_3[0x36e] != 0) && (param_3[0x36e] == *(int *)((longlong)local_b00 + 0x9c))) {
          *(undefined1 *)(local_b00 + 0xc) = 1;
          *(int *)((longlong)local_b00 + 100) = param_3[0x2d];
          uVar23 = (**(code **)(param_3 + 0x3de))(param_3[0x2d]);
          *(undefined4 *)(local_b00 + 0xd) = uVar23;
          *(int *)((longlong)local_b00 + 0x6c) = param_3[0x2e];
        }
        if (param_3[0x51c] == 0x1b) {
          *(undefined1 *)(local_b00 + 0xb) = 1;
          *(undefined4 *)((longlong)local_b00 + 0x5c) = 0x1b;
        }
        else {
          *(undefined1 *)(local_b00 + 0xb) = 0;
        }
      }
      if (*(int *)(*local_b00 + 0x70) == 1) {
        iVar21 = 0;
        do {
          if (0 < *param_3) {
            iVar45 = 0;
            do {
              plVar33 = FUN_18000f880((longlong *)(param_3 + 0x140),iVar21);
              puVar34 = (undefined4 *)FUN_18000f448((longlong)plVar33,iVar45);
              iVar46 = 0;
              *puVar34 = 0;
              do {
                plVar33 = FUN_18000f880((longlong *)(param_3 + 0x140),iVar21);
                plVar33 = FUN_1800076d0(plVar33 + 0x10,iVar46);
                puVar34 = (undefined4 *)FUN_180005d08(plVar33,iVar45);
                iVar46 = iVar46 + 1;
                *puVar34 = 0;
              } while (iVar46 < 0xc);
              iVar45 = iVar45 + 1;
            } while (iVar45 < *param_3);
          }
          iVar21 = iVar21 + 1;
        } while (iVar21 < 0x3c);
      }
      else if (((char)local_b00[9] != '\0') || ((uint)local_a00 == 0)) {
        FUN_1800043e0(*(double *)(*local_b00 + 0x48),&lStack_520);
        uVar22 = (*extraout_x9)(param_3[9],&lStack_520);
        *local_828 = uVar22;
        FUN_180014738(*(longlong *)(local_b00[0x17] + 8) + 0x10,param_3,uVar22,(longlong)local_a70);
      }
    }
    uVar22 = (uint)local_a00;
    if ((int)(uint)local_a00 < *param_3) {
      do {
        uVar24 = FUN_180026550((longlong)local_ab0);
        uVar48 = (ulonglong)uVar22;
        uStack_258 = 0;
        local_260 = 0;
        uStack_248 = 0;
        uStack_250 = 0;
        uStack_238 = 0;
        local_240 = 0;
        uStack_228 = 0;
        uStack_230 = 0;
        uStack_218 = 0;
        local_220 = 0;
        uStack_208 = 0;
        uStack_210 = 0;
        uStack_1f8 = 0;
        local_200 = 0;
        uStack_1e8 = 0;
        uStack_1f0 = 0;
        local_1e0 = 0;
        FUN_180011fd0(&local_260,(longlong)param_3,uVar22,uVar24);
        uVar20 = *local_5f8;
        if (param_3[0x2b] == 0) {
          iVar21 = 2;
          if (uVar22 == *param_3 - 1U) {
            iVar21 = 3;
          }
        }
        else {
          iVar21 = 4;
        }
        if (uVar19 == 5) {
          if ((local_618 != 0) && (*(longlong *)(local_618 + 0xc0) != 0)) {
            lVar44 = *(longlong *)(*(longlong *)(local_618 + 0xb8) + 8);
            lVar43 = *(longlong *)(lVar44 + 0x138);
            if (lVar43 != 0) {
              iVar21 = *(int *)(lVar43 + 4);
              fVar52 = (float)param_3[0x1a6];
              iVar45 = *(int *)(lVar44 + 0x14);
              pfVar35 = (float *)FUN_18000f448((longlong)local_ad0,uVar22);
              *pfVar35 = fVar52 * (float)iVar21 * (float)iVar45;
              iVar21 = local_928[6];
              piVar36 = (int *)FUN_180005d08((longlong *)(local_ad0 + 0x16),uVar22);
              *piVar36 = iVar21;
            }
          }
        }
        else {
          if (((uVar20 != uVar22) && (bVar15 = FUN_180026690((longlong)local_a28), bVar15)) &&
             (uVar37 = FUN_1800125b0((longlong)&local_260), (uVar37 & 1) != 0)) {
            uVar48 = (ulonglong)uVar22;
            (**(code **)(*(longlong *)local_b00[0x14] + 8))((longlong *)local_b00[0x14],param_3);
            *local_828 = uVar22;
          }
          uVar37 = FUN_180026708((longlong)local_a48);
          fVar52 = FUN_180026608((longlong)local_a80);
          bVar15 = FUN_180026690((longlong)local_aa8);
          if (bVar15) {
            bVar16 = *(byte *)(local_a40 + 3);
            uVar25 = (uint)bVar16;
            if (bVar16 == 0x12 || bVar16 == 0x15) {
              uVar38 = (ulonglong)*(uint *)((longlong)local_a40 + 0x1c);
              if (bVar16 != 0x12) goto LAB_180084f3c;
LAB_180084f5c:
              uVar25 = *(uint *)(local_a40 + 4);
            }
            else if (bVar16 == 0x13) {
              uVar25 = FUN_180026550((longlong)local_ae8);
              uVar38 = extraout_x11_00;
            }
            else {
              uVar38 = FUN_180026708((longlong)local_ae8);
              uVar38 = uVar38 & 0xffffffff;
              uVar25 = extraout_w12;
LAB_180084f3c:
              if ((uVar25 == 0x14) || (uVar25 == 0x15)) goto LAB_180084f5c;
              uVar25 = FUN_180026550((longlong)local_ae8);
              uVar38 = extraout_x11_01;
            }
            uVar48 = 2;
            plVar33 = (longlong *)(**(code **)(param_3 + 0x45c))(uVar38 & 0xffffffff,uVar25);
            if (*plVar33 == 0) goto LAB_18008571c;
            uVar25 = FUN_180026550((longlong)local_a20);
            if (uVar25 == 0) {
              if (extraout_s19 <= 0.1) {
                fVar52 = 1.0;
              }
              else {
                fVar52 = extraout_s20 / extraout_s19;
              }
            }
            else {
              fVar52 = extraout_s18;
              if (extraout_s18 <= 0.1) {
                fVar52 = 1.0;
              }
            }
          }
          pfVar35 = (float *)FUN_18000f448((longlong)local_a18,uVar22);
          *pfVar35 = (float)(int)uVar37;
          pfVar35 = (float *)FUN_18000f448((longlong)local_548,uVar22);
          lVar43 = local_b00[0x14];
          *pfVar35 = fVar52;
          if (*(longlong *)(lVar43 + 0x10) != 0) {
            lVar43 = *(longlong *)(*(longlong *)(lVar43 + 8) + 8);
            if ((int *)(lVar43 + 0x10) != (int *)0x0) {
              fVar56 = *(float *)(lVar43 + 0x1c);
              if (*(int *)(lVar43 + 0x18) != 1) {
                fVar56 = fVar56 * -1.0;
              }
              pfVar35 = (float *)FUN_18000f448((longlong)local_ac8,*(int *)(lVar43 + 0x10));
              fVar58 = *pfVar35;
              pfVar35 = (float *)FUN_18000f448((longlong)local_830,uVar22);
              *pfVar35 = fVar56 + fVar58;
            }
          }
          puVar34 = (undefined4 *)FUN_18000f448((longlong)local_830,uVar22);
          *(undefined4 *)((longlong)local_b00 + 0xac) = *puVar34;
          fVar52 = FUN_180011838(fVar52,(longlong)param_3);
          *(float *)(local_b00 + 0x16) = fVar52;
          if ((iVar21 == 2 && uVar19 != 3) || ((uVar19 - 2 & 0xfffffffb) == 0)) {
            uVar48 = (ulonglong)uVar22;
            (**(code **)(*(longlong *)local_b00[0x14] + 0x18))((longlong *)local_b00[0x14],param_3);
            uVar30 = extraout_x1_73;
          }
          else {
            uVar30 = extraout_x1_71;
            if (uVar20 != uVar22 && uVar19 == 3) {
              uVar48 = (ulonglong)uVar22;
              (**(code **)(*(longlong *)local_b00[0x14] + 0x10))
                        ((longlong *)local_b00[0x14],param_3,uVar48,uVar24);
              uVar30 = extraout_x1_72;
            }
          }
          if (local_b00[0x18] != 0) {
            if (uVar19 == 0) {
              uVar20 = FUN_180010820(local_b00[0x14],uVar30,uVar48,1);
              iVar21 = extraout_w11_04;
            }
            else {
              uVar20 = FUN_180010820(local_b00[0x14],uVar30,uVar48,0);
              iVar21 = extraout_w11_03;
              if (extraout_w12_00 == 2) {
                if (*(int *)(local_b00[0x14] + 0x18) == 0) {
                  iVar21 = 0x7fffffff;
                }
                else {
                  iVar21 = FUN_180010820(local_b00[0x14],extraout_x1_74,uVar48,-1);
                }
                *(int *)(*(longlong *)(local_b00[0x17] + 8) + 100) = iVar21 + -1;
              }
            }
            if (iVar40 != 0) {
              if (((uVar20 != 0xffffffff && iVar21 != 0) && (local_b00[0x18] != 0)) &&
                 (*(uint *)(*(longlong *)(local_b00[0x17] + 8) + 0x60) != uVar20)) {
                if (uVar19 == 2) {
                  uStack_1c8 = 0;
                  local_1d0 = 0;
                  uStack_1b8 = 0;
                  uStack_1c0 = 0;
                  uStack_1a8 = 0;
                  local_1b0 = 0;
                  uStack_198 = 0;
                  uStack_1a0 = 0;
                  uStack_188 = 0;
                  local_190 = 0;
                  uStack_178 = 0;
                  uStack_180 = 0;
                  uStack_168 = 0;
                  local_170 = 0;
                  uStack_158 = 0;
                  uStack_160 = 0;
                  uStack_148 = 0;
                  local_150 = 0;
                  uStack_138 = 0;
                  uStack_140 = 0;
                  uStack_128 = 0;
                  local_130 = 0;
                  uStack_118 = 0;
                  uStack_120 = 0;
                  uStack_108 = 0;
                  local_110 = 0;
                  uStack_f8 = 0;
                  uStack_100 = 0;
                  uStack_e8 = 0;
                  local_f0 = 0;
                  uStack_d8 = 0;
                  uStack_e0 = 0;
                  uStack_c8 = 0;
                  local_d0 = 0;
                  uStack_b8 = 0;
                  uStack_c0 = 0;
                  uStack_a8 = 0;
                  local_b0 = 0;
                  FUN_18007ede8((undefined4 *)&local_1d0,
                                (undefined4 *)(*(longlong *)(local_b00[0x17] + 8) + 0x10));
                  uStack_180 = CONCAT44(uVar20 - 1,(undefined4)uStack_180);
                  local_4b0 = (uint *)local_b00[0x17];
                  FUN_180085e70((longlong *)&local_4b0,-1);
                  FUN_180085d68((longlong)(local_b00 + 0x17),(longlong)local_4b0,
                                (undefined4 *)&local_1d0);
                  FUN_18007f070((longlong)&local_1d0);
                }
                for (uVar24 = *local_828; (int)uVar24 <= (int)uVar20; uVar24 = uVar24 + 1) {
                  puVar34 = (undefined4 *)FUN_18000f448((longlong)local_ac0,uVar24);
                  *puVar34 = 0;
                  puVar34 = (undefined4 *)FUN_18000f448((longlong)local_ae0,uVar24);
                  *puVar34 = 0;
                  puVar34 = (undefined4 *)FUN_18000f448((longlong)local_aa0,uVar24);
                  *puVar34 = 0;
                  puVar34 = (undefined4 *)FUN_18000f448((longlong)local_a98,uVar24);
                  *puVar34 = 0;
                }
                FUN_180014738(*(longlong *)(local_b00[0x17] + 8) + 0x10,param_3,uVar20,
                              (longlong)local_a70);
                *local_828 = uVar20;
              }
              if (*local_b00 == 0) break;
              if (*(int *)(*local_b00 + 0x70) != 1) {
                if ((uVar19 == 6) && (param_3[0x47e] != 0)) goto LAB_18008571c;
                FUN_180026608((longlong)local_a38);
                fVar52 = FUN_180026608((longlong)local_a30);
                puVar26 = (undefined8 *)local_b00[0x17];
                puVar27 = (undefined8 *)*puVar26;
                if (puVar27 != puVar26) {
                  do {
                    local_698 = puVar26;
                    if (((*(int *)(puVar27 + 2) != 0) &&
                        (*(char *)((longlong)puVar27 + 0xd9) == '\0')) && (param_3[0x47e] == 0)) {
                      iVar21 = *(int *)((longlong)puVar27 + 100);
                      iVar45 = *param_3;
                      if (iVar21 < 0x7ffffffe) {
                        iVar46 = iVar21;
                        if (iVar45 + -1 <= iVar21) {
                          iVar46 = iVar45 + -1;
                        }
                      }
                      else {
                        iVar21 = iVar45 + -1;
                        iVar46 = iVar45 + -1;
                      }
                      if (*(char *)((longlong)puVar27 + 0xa9) != '\0') {
                        if (puVar27[0x26] == 0) {
                          local_4c0 = &DAT_1800d4ecd;
                          local_4d0 = "";
                          uStack_4c8 = 0;
                          local_538 = (undefined4 *)FUN_180096150(0x28);
                          puVar26 = (undefined8 *)FUN_1800043e0(0.0,&lStack_4f0);
                          extraout_x9_00[2] = 0;
                          extraout_x9_00[2] = *puVar26;
                          *(undefined1 *)(extraout_x9_00 + 3) = 0;
                          *(undefined1 *)(extraout_x9_00 + 4) = 1;
                          *extraout_x9_00 = 0;
                          extraout_x9_00[1] = 0;
                          *(undefined4 *)((longlong)extraout_x9_00 + 0x1c) = 0;
                          puVar27[0x26] = extraout_x9_00;
                          uVar20 = *(uint *)(puVar27 + 0xc);
                          if ((int)uVar20 <= iVar46) {
                            iVar45 = uVar20 - 1;
                            do {
                              bVar16 = FUN_180014838((longlong)param_3,uVar20,
                                                     (ulonglong)*(byte *)((longlong)puVar27 + 0xf4),
                                                     *(undefined4 *)((longlong)puVar27 + 0xec));
                              if ((bVar16 & 1) == 0) {
                                FUN_180021c18((undefined4 *)puVar27[0x26],param_3,uVar20,
                                              (longlong)local_a70);
                              }
                              else {
                                puVar34 = (undefined4 *)FUN_18000f448((longlong)local_a70,iVar45);
                                uVar23 = *puVar34;
                                puVar34 = (undefined4 *)FUN_18000f448((longlong)local_a70,uVar20);
                                *puVar34 = uVar23;
                                plVar33 = FUN_1800076d0(local_a70 + 0x10,0);
                                puVar34 = (undefined4 *)FUN_180005d08(plVar33,iVar45);
                                uVar23 = *puVar34;
                                plVar33 = FUN_1800076d0(local_a70 + 0x10,0);
                                puVar34 = (undefined4 *)FUN_180005d08(plVar33,uVar20);
                                *puVar34 = uVar23;
                              }
                              uVar20 = uVar20 + 1;
                              iVar45 = iVar45 + 1;
                            } while ((int)uVar20 <= iVar46);
                          }
                          FUN_180004590(&local_4d0);
                        }
                        cVar17 = FUN_180014838((longlong)param_3,uVar22,
                                               (ulonglong)*(byte *)((longlong)puVar27 + 0xf4),
                                               *(undefined4 *)((longlong)puVar27 + 0xec));
                        if (cVar17 == '\0' && (int)uVar22 <= iVar21) {
                          FUN_180021c18((undefined4 *)puVar27[0x26],param_3,uVar22,
                                        (longlong)local_a70);
                        }
                      }
                      if (puVar27[0x27] == 0) {
                        local_480 = &DAT_1800d4ecd;
                        local_490 = &DAT_1800d4ecd;
                        uStack_488 = 0;
                        local_5e8 = (uint *)FUN_180096150(0x80);
                        local_5e8[2] = 0;
                        local_5e8[3] = 0;
                        local_5e8[0] = 0;
                        local_5e8[1] = 0;
                        local_5e8[6] = 0;
                        local_5e8[7] = 0;
                        local_5e8[4] = 0;
                        local_5e8[5] = 0;
                        local_5e8[10] = 0;
                        local_5e8[0xb] = 0;
                        local_5e8[8] = 0;
                        local_5e8[9] = 0;
                        local_5e8[0xe] = 0;
                        local_5e8[0xf] = 0;
                        local_5e8[0xc] = 0;
                        local_5e8[0xd] = 0;
                        local_5e8[0x12] = 0;
                        local_5e8[0x13] = 0;
                        local_5e8[0x10] = 0;
                        local_5e8[0x11] = 0;
                        local_5e8[0x16] = 0;
                        local_5e8[0x17] = 0;
                        local_5e8[0x14] = 0;
                        local_5e8[0x15] = 0;
                        local_5e8[0x1a] = 0;
                        local_5e8[0x1b] = 0;
                        local_5e8[0x18] = 0;
                        local_5e8[0x19] = 0;
                        local_5e8[0x1e] = 0;
                        local_5e8[0x1f] = 0;
                        local_5e8[0x1c] = 0;
                        local_5e8[0x1d] = 0;
                        puVar26 = FUN_18001f8e8(0x3e4ccccd,0x40000000,(undefined8 *)local_5e8,
                                                (longlong)param_3,
                                                *(undefined4 *)((longlong)puVar27 + 0x14),0);
                        plVar1 = local_600;
                        plVar31 = local_608;
                        plVar33 = local_610;
                        puVar27[0x27] = puVar26;
                        if (puVar26 == (undefined8 *)0x0) {
                          FUN_180004590(&local_490);
                          goto LAB_1800855ec;
                        }
                        uVar20 = *(uint *)(puVar27 + 0xc);
                        if ((int)uVar20 <= iVar46) {
                          local_a60 = (longlong *)CONCAT44(local_a60._4_4_,iVar21);
                          do {
                            bVar16 = FUN_180014838((longlong)param_3,uVar20,
                                                   (ulonglong)*(byte *)((longlong)puVar27 + 0xf4),
                                                   *(undefined4 *)((longlong)puVar27 + 0xec));
                            if ((bVar16 & 1) == 0) {
                              FUN_18001fb20((int *)puVar27[0x27],param_3,uVar20,(longlong)plVar1,
                                            (longlong)plVar31,(longlong)plVar33,'\0',0,'\0',0,'\0',0
                                           );
                            }
                            uVar20 = uVar20 + 1;
                          } while ((int)uVar20 <= iVar46);
                          iVar21 = (int)local_a60;
                        }
                      }
                      cVar17 = FUN_180014838((longlong)param_3,uVar22,
                                             (ulonglong)*(byte *)((longlong)puVar27 + 0xf4),
                                             *(undefined4 *)((longlong)puVar27 + 0xec));
                      if (cVar17 == '\0' && (int)uVar22 <= iVar21) {
                        FUN_18001fb20((int *)puVar27[0x27],param_3,uVar22,(longlong)local_600,
                                      (longlong)local_608,(longlong)local_610,'\0',0,'\0',0,'\0',0);
                      }
                    }
LAB_1800855ec:
                    bVar15 = FUN_180026690((longlong)local_a78);
                    if ((bVar15) && (*local_540 != 0)) {
                      iVar21 = *(int *)(puVar27 + 0xc);
                      iVar45 = *param_3 + -1;
                      if (*(int *)((longlong)puVar27 + 100) < *param_3 + -1) {
                        iVar45 = *(int *)((longlong)puVar27 + 100);
                      }
                      for (; local_ae0 = extraout_x12, local_ac0 = extraout_x13_01,
                          local_aa0 = extraout_x11_02, iVar21 < iVar45; iVar21 = iVar21 + 1) {
                        pfVar35 = (float *)FUN_18000f448((longlong)local_a70,iVar21);
                        fVar56 = *pfVar35;
                        plVar33 = FUN_1800076d0(local_a70 + 0x10,0);
                        pfVar35 = (float *)FUN_180005d08(plVar33,iVar21);
                        fVar58 = *pfVar35;
                        if (fVar56 != 0.0) {
                          fVar57 = fVar58 * extraout_s18_00;
                          pfVar35 = (float *)FUN_18000f448((longlong)extraout_x13_01,iVar21);
                          *pfVar35 = fVar56 + fVar57;
                          pfVar35 = (float *)FUN_18000f448((longlong)extraout_x12,iVar21);
                          fVar58 = fVar58 * fVar52;
                          *pfVar35 = fVar56 - fVar57;
                          pfVar35 = (float *)FUN_18000f448((longlong)extraout_x11_02,iVar21);
                          *pfVar35 = fVar56 + fVar58;
                          pfVar35 = (float *)FUN_18000f448((longlong)local_a98,iVar21);
                          *pfVar35 = fVar56 - fVar58;
                        }
                      }
                    }
                    puVar27 = (undefined8 *)*puVar27;
                    puVar26 = local_698;
                  } while (puVar27 != local_698);
                }
              }
            }
            *local_5f8 = uVar22;
          }
        }
LAB_18008571c:
        uVar22 = uVar22 + 1;
      } while ((int)uVar22 < *param_3);
    }
    *local_5f0 = (uint)((int)local_9f8 != 0);
  }
  return;
}

