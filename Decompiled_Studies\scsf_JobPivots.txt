
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_JobPivots(undefined1 param_1 [16],undefined1 param_2 [16],int *param_3,undefined8 param_4,
                   undefined8 param_5,undefined8 param_6,undefined8 param_7,undefined8 param_8,
                   undefined8 param_9,undefined8 param_10)

{
  undefined1 auVar1 [16];
  undefined1 auVar2 [16];
  undefined1 auVar3 [16];
  undefined1 auVar4 [16];
  undefined1 auVar5 [16];
  undefined1 auVar6 [16];
  undefined1 auVar7 [16];
  undefined1 auVar8 [16];
  longlong *plVar9;
  longlong *plVar10;
  bool bVar11;
  uint uVar12;
  int iVar13;
  undefined4 uVar14;
  int iVar15;
  uint uVar16;
  int *piVar17;
  undefined8 *puVar18;
  char *pcVar19;
  HANDLE pvVar20;
  undefined8 uVar21;
  ulonglong uVar22;
  float *pfVar23;
  undefined4 *puVar24;
  longlong *plVar25;
  float *pfVar26;
  float *pfVar27;
  ulonglong uVar28;
  ulonglong uVar29;
  char *pcVar30;
  int extraout_w1;
  undefined8 extraout_x1;
  ulonglong uVar31;
  longlong *plVar32;
  int iVar33;
  int iVar34;
  int iVar35;
  longlong lVar36;
  float extraout_w11;
  int *extraout_x11;
  char *extraout_x11_00;
  char *extraout_x11_01;
  char *extraout_x11_02;
  float *extraout_x11_03;
  float *extraout_x11_04;
  longlong extraout_x11_05;
  float *extraout_x11_06;
  float *extraout_x11_07;
  float *extraout_x11_08;
  float *extraout_x11_09;
  float *extraout_x11_10;
  float *extraout_x11_11;
  float *extraout_x11_12;
  float *extraout_x11_13;
  float *extraout_x11_14;
  float *extraout_x11_15;
  float *extraout_x11_16;
  float *extraout_x11_17;
  longlong extraout_x11_18;
  longlong extraout_x11_19;
  longlong extraout_x11_20;
  int extraout_w12;
  undefined4 extraout_w12_00;
  int extraout_w12_01;
  longlong extraout_x12;
  longlong lVar37;
  int extraout_w13;
  longlong lVar38;
  float *pfVar39;
  float *pfVar40;
  char *pcVar41;
  float *pfVar42;
  uint *puVar43;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  float fVar44;
  float fVar45;
  float fVar46;
  float fVar47;
  float fVar48;
  float fVar49;
  float fVar50;
  undefined4 extraout_s0_02;
  undefined4 extraout_s0_03;
  undefined4 extraout_s0_04;
  undefined4 extraout_s0_05;
  undefined4 extraout_s0_06;
  undefined4 extraout_s0_07;
  undefined4 extraout_s0_08;
  undefined4 extraout_var;
  undefined4 extraout_var_00;
  undefined4 extraout_var_01;
  undefined4 extraout_var_02;
  undefined4 extraout_var_03;
  undefined4 extraout_var_04;
  undefined4 uVar51;
  undefined4 extraout_var_05;
  undefined8 extraout_var_06;
  undefined8 extraout_var_07;
  undefined8 extraout_var_08;
  undefined8 extraout_var_09;
  undefined8 extraout_var_10;
  undefined8 extraout_var_11;
  undefined8 extraout_var_12;
  float fVar52;
  undefined4 uVar53;
  undefined8 uVar54;
  float extraout_s18;
  float extraout_s18_00;
  float extraout_s18_01;
  float extraout_s18_02;
  float extraout_s19;
  float extraout_s19_00;
  undefined4 in_stack_fffffffffffffa18;
  undefined4 in_stack_fffffffffffffa1c;
  undefined8 in_stack_fffffffffffffa70;
  float *local_580;
  longlong *local_578;
  float *local_570;
  longlong *local_568;
  int *local_560;
  longlong *local_558;
  longlong *local_550;
  longlong *local_548;
  undefined **local_540;
  undefined **local_538;
  undefined **local_530;
  longlong *local_528;
  float *local_520;
  longlong *local_518;
  longlong *local_510;
  float *local_508;
  longlong *local_500;
  float *local_4f8;
  longlong *local_4f0;
  longlong *local_4e8;
  longlong *local_4e0;
  float *local_4d8;
  longlong *local_4d0;
  longlong *local_4c8;
  longlong *local_4c0;
  longlong *local_4b8;
  longlong *local_4b0;
  longlong *local_4a8;
  longlong *local_4a0;
  longlong *local_498;
  float *local_490;
  float *local_488;
  int *local_480;
  int *local_478;
  longlong *local_470;
  longlong *local_468;
  longlong *local_460;
  longlong *local_458;
  longlong *local_450;
  longlong *local_448;
  longlong *local_440;
  longlong *local_438;
  longlong *local_430;
  longlong *local_428;
  char *local_420;
  char *local_418;
  longlong *local_410;
  longlong *local_408;
  int *local_400;
  longlong *local_3f8;
  longlong *local_3f0;
  longlong *local_3e8;
  longlong *local_3e0;
  longlong *local_3d8;
  undefined8 local_3d0;
  undefined8 uStack_3c8;
  undefined8 local_3c0;
  ulonglong local_3b8;
  undefined4 local_3b0;
  longlong *local_3a0;
  int *local_398;
  longlong *local_390;
  longlong *local_388;
  longlong *local_380;
  longlong *local_378;
  longlong *local_370;
  longlong *local_368;
  longlong *local_360;
  longlong *local_358;
  longlong *local_350;
  longlong *local_348;
  longlong *local_340;
  longlong *local_338;
  longlong *local_330;
  longlong *local_328;
  longlong *local_320;
  longlong *local_318;
  longlong *local_310;
  longlong local_308 [2];
  int local_2f8 [4];
  longlong local_2e8;
  longlong lStack_2e0;
  float *local_2d8;
  longlong local_2d0;
  longlong lStack_2c8;
  float *local_2c0;
  longlong local_2b8;
  longlong lStack_2b0;
  float *local_2a8;
  undefined1 local_2a0;
  undefined7 uStack_29f;
  undefined8 local_290;
  ulonglong local_288;
  undefined1 local_280;
  undefined7 uStack_27f;
  undefined8 local_270;
  ulonglong local_268;
  undefined1 local_260;
  undefined7 uStack_25f;
  undefined8 local_250;
  ulonglong local_248;
  undefined1 local_240;
  undefined7 uStack_23f;
  undefined8 local_230;
  ulonglong local_228;
  undefined8 local_220;
  longlong lStack_218;
  longlong local_210;
  undefined8 uStack_208;
  undefined8 uStack_200;
  undefined8 uStack_1f8;
  undefined4 local_1f0;
  float afStack_1e0 [6];
  float afStack_1c8 [6];
  undefined **local_1b0;
  undefined **local_1a8;
  undefined **local_1a0 [2];
  char *local_190;
  undefined8 uStack_188;
  undefined1 *local_180;
  ulonglong uStack_178;
  undefined4 local_170;
  undefined8 local_160;
  undefined8 uStack_158;
  undefined4 local_150;
  undefined4 uStack_14c;
  undefined4 uStack_148;
  undefined4 uStack_144;
  undefined4 local_140;
  undefined4 uStack_13c;
  undefined4 uStack_138;
  undefined4 uStack_134;
  undefined4 local_130;
  undefined1 uStack_12c;
  undefined2 uStack_12b;
  undefined1 uStack_129;
  undefined8 local_128;
  undefined8 local_120;
  undefined8 uStack_118;
  undefined4 local_110;
  undefined4 uStack_10c;
  undefined4 uStack_108;
  undefined4 uStack_104;
  undefined8 local_100;
  undefined8 uStack_f8;
  undefined8 uStack_f0;
  undefined8 uStack_e8;
  undefined8 local_e0;
  undefined8 local_d0;
  undefined4 local_c8;
  undefined8 local_c0;
  undefined4 local_b8;
  
                    /* 0x43928  10  scsf_JobPivots */
  fVar52 = param_2._0_4_;
  uVar53 = param_2._4_4_;
  uVar54 = param_2._8_8_;
  local_220 = 0xfffffffffffffffe;
  piVar17 = (int *)(**(code **)(param_3 + 0x62e))(param_1._0_4_,0,0);
  puVar18 = (undefined8 *)(**(code **)(param_3 + 0x446))(2);
  local_560 = (int *)*puVar18;
  puVar18 = (undefined8 *)(**(code **)(param_3 + 0x446))(3);
  pfVar42 = (float *)*puVar18;
  puVar18 = (undefined8 *)(**(code **)(param_3 + 0x446))(5);
  pfVar40 = (float *)*puVar18;
  local_400 = (int *)(**(code **)(param_3 + 0x62e))(6);
  puVar18 = (undefined8 *)(**(code **)(param_3 + 0x446))(7);
  local_420 = (char *)*puVar18;
  puVar18 = (undefined8 *)(**(code **)(param_3 + 0x446))(8);
  local_418 = (char *)*puVar18;
  puVar18 = (undefined8 *)(**(code **)(param_3 + 0x446))(9);
  local_578 = (longlong *)*puVar18;
  local_478 = (int *)(**(code **)(param_3 + 0x62e))(10);
  local_480 = (int *)(**(code **)(param_3 + 0x62e))(0xb);
  puVar18 = (undefined8 *)(**(code **)(param_3 + 0x446))(0xc);
  puVar43 = (uint *)*puVar18;
  local_398 = (int *)(**(code **)(param_3 + 0x62e))(0xd);
  local_2f8[0] = 5;
  local_2f8[1] = 10;
  local_2f8[2] = 0xf;
  local_2f8[3] = 0x1e;
  local_548 = FUN_18000f880((longlong *)(param_3 + 0x140),0);
  local_518 = FUN_18000f880((longlong *)(param_3 + 0x140),1);
  local_510 = FUN_18000f880((longlong *)(param_3 + 0x140),2);
  local_4f8 = (float *)FUN_18000f880((longlong *)(param_3 + 0x140),3);
  local_4e0 = FUN_18000f880((longlong *)(param_3 + 0x140),4);
  local_4f0 = FUN_18000f880((longlong *)(param_3 + 0x140),5);
  local_4d8 = (float *)FUN_18000f880((longlong *)(param_3 + 0x140),6);
  local_4e8 = FUN_18000f880((longlong *)(param_3 + 0x140),7);
  local_4d0 = FUN_18000f880((longlong *)(param_3 + 0x140),8);
  local_4c0 = FUN_18000f880((longlong *)(param_3 + 0x140),9);
  local_4b8 = FUN_18000f880((longlong *)(param_3 + 0x140),10);
  local_4b0 = FUN_18000f880((longlong *)(param_3 + 0x140),0xb);
  local_4a8 = FUN_18000f880((longlong *)(param_3 + 0x140),0xc);
  local_4a0 = FUN_18000f880((longlong *)(param_3 + 0x140),0xd);
  local_498 = FUN_18000f880((longlong *)(param_3 + 0x140),0xe);
  local_410 = FUN_18000f880((longlong *)(param_3 + 0x140),0xf);
  local_408 = FUN_18000f880((longlong *)(param_3 + 0x140),0x10);
  local_3f0 = FUN_18000f880((longlong *)(param_3 + 0x140),0x11);
  local_490 = (float *)FUN_18000f880((longlong *)(param_3 + 0x140),0x12);
  local_520 = (float *)FUN_18000f880((longlong *)(param_3 + 0x140),0x13);
  local_570 = (float *)FUN_18000f880((longlong *)(param_3 + 0x140),0x14);
  local_380 = FUN_18000f880((longlong *)(param_3 + 0x140),0x15);
  local_500 = FUN_180029980((longlong *)(param_3 + 0x84),0);
  local_388 = FUN_180029980((longlong *)(param_3 + 0x84),1);
  local_358 = FUN_180029980((longlong *)(param_3 + 0x84),2);
  local_550 = FUN_180029980((longlong *)(param_3 + 0x84),3);
  local_450 = FUN_180029980((longlong *)(param_3 + 0x84),4);
  local_3a0 = FUN_180029980((longlong *)(param_3 + 0x84),5);
  local_328 = FUN_180029980((longlong *)(param_3 + 0x84),6);
  local_320 = FUN_180029980((longlong *)(param_3 + 0x84),7);
  local_318 = FUN_180029980((longlong *)(param_3 + 0x84),8);
  local_310 = FUN_180029980((longlong *)(param_3 + 0x84),9);
  local_390 = FUN_180029980((longlong *)(param_3 + 0x84),10);
  local_528 = FUN_180029980((longlong *)(param_3 + 0x84),0xb);
  local_3f8 = FUN_180029980((longlong *)(param_3 + 0x84),0xc);
  local_330 = FUN_180029980((longlong *)(param_3 + 0x84),0xd);
  local_338 = FUN_180029980((longlong *)(param_3 + 0x84),0xe);
  local_368 = FUN_180029980((longlong *)(param_3 + 0x84),0xf);
  local_350 = FUN_180029980((longlong *)(param_3 + 0x84),0x10);
  local_568 = FUN_180029980((longlong *)(param_3 + 0x84),0x11);
  local_340 = FUN_180029980((longlong *)(param_3 + 0x84),0x12);
  local_460 = FUN_180029980((longlong *)(param_3 + 0x84),0x13);
  local_468 = FUN_180029980((longlong *)(param_3 + 0x84),0x14);
  local_470 = FUN_180029980((longlong *)(param_3 + 0x84),0x15);
  local_370 = FUN_180029980((longlong *)(param_3 + 0x84),0x16);
  local_458 = FUN_180029980((longlong *)(param_3 + 0x84),0x17);
  local_508 = (float *)FUN_180029980((longlong *)(param_3 + 0x84),0x18);
  local_4c8 = FUN_180029980((longlong *)(param_3 + 0x84),0x19);
  local_3d8 = FUN_180029980((longlong *)(param_3 + 0x84),0x1a);
  local_428 = FUN_180029980((longlong *)(param_3 + 0x84),0x1b);
  local_448 = FUN_180029980((longlong *)(param_3 + 0x84),0x1c);
  local_440 = FUN_180029980((longlong *)(param_3 + 0x84),0x1d);
  local_360 = FUN_180029980((longlong *)(param_3 + 0x84),0x1e);
  local_348 = FUN_180029980((longlong *)(param_3 + 0x84),0x1f);
  local_438 = FUN_180029980((longlong *)(param_3 + 0x84),0x20);
  local_430 = FUN_180029980((longlong *)(param_3 + 0x84),0x21);
  local_558 = FUN_180029980((longlong *)(param_3 + 0x84),0x22);
  local_3e8 = FUN_180029980((longlong *)(param_3 + 0x84),0x23);
  local_3e0 = FUN_180029980((longlong *)(param_3 + 0x84),0x24);
  local_1b0 = LineStyles::vftable;
  local_530 = ErasureModes::vftable;
  local_538 = PitSessions::vftable;
  local_1a8 = PricePositions::vftable;
  local_540 = OverlapModes::vftable;
  local_1a0[0] = ShowZonePrices::vftable;
  if (param_3[0x2b] != 0) {
    FUN_1800079f8((longlong *)(param_3 + 0x46),0x1800d7d60,10);
    pcVar30 = "";
    param_3[0x49] = 1;
    if ((*(char **)(param_3 + 0xce) != (char *)0x0) && (**(char **)(param_3 + 0xce) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0xce),0x1800d4ecd,0);
      param_3[0xd1] = 1;
    }
    local_180 = &DAT_1800d4ecd;
    local_190 = "";
    uStack_188 = 0;
    pcVar19 = FUN_180004620(0x15);
    if (pcVar19 == (char *)0x0) {
      local_190 = "";
      pcVar41 = pcVar30;
    }
    else {
      param_6 = 0x14;
      local_190 = pcVar19;
      FUN_180099d78(pcVar19,0x15,0x1800d6c40,0x14);
      uStack_188 = 0x100000001;
      pcVar41 = pcVar19;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_190);
    if ((pcVar19 != (char *)0x0) && (pcVar41 != (char *)0x0)) {
      pvVar20 = GetProcessHeap();
      HeapFree(pvVar20,0,pcVar41);
    }
    local_180 = &DAT_1800d4ecd;
    local_190 = "";
    uStack_188 = 0;
    pcVar19 = FUN_180004620(0x8d);
    if (pcVar19 == (char *)0x0) {
      local_190 = "";
      pcVar41 = pcVar30;
    }
    else {
      param_6 = 0x8c;
      local_190 = pcVar19;
      FUN_180099d78(pcVar19,0x8d,0x1800d6bb0,0x8c);
      uStack_188 = 0x100000001;
      pcVar41 = pcVar19;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_190);
    uVar14 = extraout_s0_00;
    uVar51 = extraout_var_00;
    uVar54 = extraout_var_07;
    if ((pcVar19 != (char *)0x0) && (pcVar41 != (char *)0x0)) {
      pvVar20 = GetProcessHeap();
      HeapFree(pvVar20,0,pcVar41);
      local_190 = (char *)0x0;
      uStack_188 = 0;
      uVar14 = extraout_s0_01;
      uVar51 = extraout_var_01;
      uVar54 = extraout_var_08;
    }
    pcVar19 = *(char **)(param_3 + 0x46);
    if (*(char **)(param_3 + 0x46) == (char *)0x0) {
      pcVar19 = pcVar30;
    }
    auVar5._4_4_ = uVar51;
    auVar5._0_4_ = uVar14;
    auVar5._8_8_ = uVar54;
    FUN_180026368(auVar5,CONCAT44(uVar53,fVar52),(undefined8 *)(param_3 + 0xce),0x1800d6c60,pcVar19,
                  param_6,param_7,param_8,param_9,param_10);
    local_180 = &DAT_1800d4ecd;
    local_190 = "";
    uStack_188 = 0;
    pcVar19 = FUN_180004620(7);
    if (pcVar19 == (char *)0x0) {
      local_190 = "";
    }
    else {
      local_190 = pcVar19;
      FUN_180099d78(pcVar19,7,0x1800d6c58,6);
      uStack_188 = 0x100000001;
      pcVar30 = pcVar19;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_190);
    if ((pcVar19 != (char *)0x0) && (pcVar30 != (char *)0x0)) {
      pvVar20 = GetProcessHeap();
      HeapFree(pvVar20,0,pcVar30);
      local_190 = (char *)0x0;
      uStack_188 = 0;
    }
    plVar32 = local_548;
    param_3[0xdf] = 1;
    param_3[1] = 0;
    param_3[4] = 0;
    param_3[0x32e] = 1;
    param_3[0x276] = 1;
    FUN_1800079f8(local_548,0x1800d7d70,10);
    plVar25 = local_518;
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined4 *)(plVar32 + 0x1c) = 0x84105;
    *(undefined2 *)((longlong)plVar32 + 0x24) = 0x58;
    *(undefined4 *)(plVar32 + 3) = 0xc0c0c0;
    *(undefined4 *)(plVar32 + 0x1b) = 0;
    FUN_1800079f8(local_518,0x1800d7d88,9);
    plVar32 = local_510;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined4 *)(plVar25 + 0x1c) = 0x84105;
    *(undefined2 *)((longlong)plVar25 + 0x24) = 0x58;
    *(undefined4 *)(plVar25 + 3) = 0xc0c0c0;
    *(undefined4 *)(plVar25 + 0x1b) = 0;
    FUN_1800079f8(local_510,0x1800d7d7c,5);
    pfVar40 = local_4f8;
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined4 *)(plVar32 + 0x1c) = 0x84105;
    *(undefined4 *)((longlong)plVar32 + 0x24) = 0x20058;
    *(undefined4 *)(plVar32 + 3) = 0x51ff00;
    *(undefined4 *)(plVar32 + 0x1b) = 0;
    FUN_1800079f8((longlong *)local_4f8,0x1800d7d98,2);
    plVar25 = local_4e0;
    pfVar40[3] = 1.4013e-45;
    pfVar40[0x38] = 7.58009e-40;
    pfVar40[9] = 1.83794e-40;
    pfVar40[6] = 2.2100087e-38;
    pfVar40[0x36] = 0.0;
    FUN_1800079f8(local_4e0,0x1800d7d94,2);
    plVar32 = local_4f0;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined4 *)(plVar25 + 0x1c) = 0x84105;
    *(undefined4 *)((longlong)plVar25 + 0x24) = 0x20058;
    *(undefined4 *)(plVar25 + 3) = 0x221f0;
    *(undefined4 *)(plVar25 + 0x1b) = 0;
    FUN_1800079f8(local_4f0,0x1800d7da0,2);
    pfVar40 = local_4d8;
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined4 *)(plVar32 + 0x1c) = 0x84105;
    *(undefined4 *)((longlong)plVar32 + 0x24) = 0x20058;
    *(undefined4 *)(plVar32 + 3) = 0xf0a60d;
    *(undefined4 *)(plVar32 + 0x1b) = 0;
    FUN_1800079f8((longlong *)local_4d8,0x1800d7d9c,2);
    plVar32 = local_4e8;
    pfVar40[3] = 1.4013e-45;
    pfVar40[0x38] = 7.58009e-40;
    pfVar40[9] = 1.83794e-40;
    pfVar40[6] = 1.95845e-40;
    pfVar40[0x36] = 0.0;
    FUN_1800079f8(local_4e8,0x1800d7da8,2);
    plVar25 = local_4d0;
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined4 *)(plVar32 + 0x1c) = 0x84105;
    *(undefined4 *)((longlong)plVar32 + 0x24) = 0x20058;
    *(undefined4 *)(plVar32 + 3) = 0xf0a60d;
    *(undefined4 *)(plVar32 + 0x1b) = 0;
    FUN_1800079f8(local_4d0,0x1800d7da4,2);
    plVar32 = local_4c0;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined4 *)(plVar25 + 0x1c) = 0x84105;
    *(undefined4 *)((longlong)plVar25 + 0x24) = 0x20058;
    *(undefined4 *)(plVar25 + 3) = 0x221f0;
    *(undefined4 *)(plVar25 + 0x1b) = 0;
    FUN_1800079f8(local_4c0,0x1800d7db0,2);
    plVar25 = local_4b8;
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined4 *)(plVar32 + 0x1c) = 0x84105;
    *(undefined4 *)((longlong)plVar32 + 0x24) = 0x20058;
    *(undefined4 *)(plVar32 + 3) = 0xf0a60d;
    *(undefined4 *)(plVar32 + 0x1b) = 0;
    FUN_1800079f8(local_4b8,0x1800d7dac,2);
    plVar32 = local_4b0;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined4 *)(plVar25 + 0x1c) = 0x84105;
    *(undefined4 *)((longlong)plVar25 + 0x24) = 0x20058;
    *(undefined4 *)(plVar25 + 3) = 0x221f0;
    *(undefined4 *)(plVar25 + 0x1b) = 0;
    FUN_1800079f8(local_4b0,0x1800d7db8,2);
    plVar25 = local_4a8;
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined4 *)(plVar32 + 0x1c) = 0x84105;
    *(undefined4 *)((longlong)plVar32 + 0x24) = 0x20058;
    *(undefined4 *)(plVar32 + 3) = 0xf0a60d;
    *(undefined4 *)(plVar32 + 0x1b) = 0;
    FUN_1800079f8(local_4a8,0x1800d7db4,2);
    plVar32 = local_4a0;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined4 *)(plVar25 + 0x1c) = 0x84105;
    *(undefined4 *)((longlong)plVar25 + 0x24) = 0x20058;
    *(undefined4 *)(plVar25 + 3) = 0x221f0;
    *(undefined4 *)(plVar25 + 0x1b) = 0;
    FUN_1800079f8(local_4a0,0x1800d7dc0,2);
    plVar25 = local_498;
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined4 *)(plVar32 + 0x1c) = 0x84105;
    *(undefined4 *)((longlong)plVar32 + 0x24) = 0x20058;
    *(undefined4 *)(plVar32 + 3) = 0xf0a60d;
    *(undefined4 *)(plVar32 + 0x1b) = 0;
    FUN_1800079f8(local_498,0x1800d7dbc,2);
    pfVar40 = local_490;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined4 *)(plVar25 + 0x1c) = 0x84105;
    *(undefined4 *)((longlong)plVar25 + 0x24) = 0x20058;
    *(undefined4 *)(plVar25 + 3) = 0x221f0;
    *(undefined4 *)(plVar25 + 0x1b) = 0;
    *(undefined2 *)((longlong)local_410 + 0x24) = 2;
    *(undefined2 *)(local_410 + 5) = 2;
    *(undefined4 *)(local_410 + 3) = 0xff80;
    *(undefined2 *)((longlong)local_408 + 0x24) = 2;
    *(undefined2 *)(local_408 + 5) = 2;
    *(undefined4 *)(local_408 + 3) = 0xff80;
    *(undefined2 *)((longlong)local_3f0 + 0x24) = 2;
    *(undefined2 *)(local_3f0 + 5) = 2;
    *(undefined4 *)(local_3f0 + 3) = 0x80ffff;
    FUN_1800079f8((longlong *)local_490,0x1800d7dd8,7);
    pfVar42 = local_520;
    *(undefined4 *)((longlong)pfVar40 + 0xc) = 1;
    *(undefined2 *)((longlong)pfVar40 + 0x24) = 5;
    *(undefined2 *)((longlong)pfVar40 + 0x28) = 4;
    *(undefined4 *)((longlong)pfVar40 + 0x18) = 0xff8000;
    FUN_1800079f8((longlong *)local_520,0x1800d7dc8,8);
    pfVar40 = local_570;
    *(undefined4 *)((longlong)pfVar42 + 0xc) = 1;
    *(undefined2 *)((longlong)pfVar42 + 0x24) = 5;
    *(undefined2 *)((longlong)pfVar42 + 0x28) = 4;
    *(undefined4 *)((longlong)pfVar42 + 0x18) = 0xffff;
    FUN_1800079f8((longlong *)local_570,0x1800d7df8,8);
    plVar25 = local_380;
    *(undefined4 *)((longlong)pfVar40 + 0xc) = 1;
    *(undefined2 *)((longlong)pfVar40 + 0x24) = 5;
    *(undefined2 *)((longlong)pfVar40 + 0x28) = 4;
    *(undefined4 *)((longlong)pfVar40 + 0x18) = 0xff;
    FUN_1800079f8(local_380,0x1800d7de0,0x10);
    plVar32 = local_500;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined4 *)(plVar25 + 0x1c) = 0x84105;
    *(undefined4 *)((longlong)plVar25 + 0x24) = 0x20003;
    *(undefined4 *)(plVar25 + 3) = 0x32afff;
    *(undefined4 *)(plVar25 + 0x1b) = 0;
    FUN_1800079f8(local_500,0x1800d7e68,0xb);
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    if ((code *)plVar32[10] != (code *)0x0) {
      (*(code *)plVar32[10])
                (*(undefined4 *)((longlong)plVar32 + 0x4c),
                 "Indexes 9:30ET;Gold 8:20ET;Silver 8:25ET;Copper 8:10ET;Crude 9:00ET;Bonds 8:20ET")
      ;
    }
    plVar32 = local_550;
    *(undefined1 *)(local_500 + 3) = 0x16;
    *(undefined4 *)((longlong)local_500 + 0x1c) = 0;
    *(undefined1 *)(local_388 + 3) = 2;
    *(undefined4 *)((longlong)local_388 + 0x1c) = 0x3f800000;
    *(undefined1 *)(local_358 + 3) = 5;
    *(undefined4 *)((longlong)local_358 + 0x1c) = 1;
    FUN_1800079f8(local_550,0x1800d7ed0,0x13);
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    if ((code *)plVar32[10] != (code *)0x0) {
      (*(code *)plVar32[10])
                (*(undefined4 *)((longlong)plVar32 + 0x4c),
                 "Line at Last Bar Left to Right;Dash;Stair Step to Edge;User-Defined;Ignore");
    }
    plVar32 = local_450;
    *(undefined1 *)(local_550 + 3) = 0x16;
    *(undefined4 *)((longlong)local_550 + 0x1c) = 3;
    FUN_1800079f8(local_450,0x1800d7f08,9);
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    if ((code *)plVar32[10] != (code *)0x0) {
      (*(code *)plVar32[10])
                (*(undefined4 *)((longlong)plVar32 + 0x4c),"None;All;Current;Developing");
    }
    plVar32 = local_3a0;
    *(undefined1 *)(local_450 + 3) = 0x16;
    *(undefined4 *)((longlong)local_450 + 0x1c) = 1;
    FUN_1800079f8(local_3a0,0x1800d7b50,10);
    plVar25 = local_320;
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined1 *)(plVar32 + 3) = 0xe;
    *(undefined4 *)((longlong)plVar32 + 0x1c) = 0xffffff;
    FUN_1800079f8(local_320,0x1800d7ba8,0x19);
    plVar32 = local_328;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined1 *)(plVar25 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar25 + 0x1c) = 0x5f;
    FUN_1800079f8(local_328,0x1800d7bf0,0x1b);
    plVar25 = local_310;
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined1 *)(plVar32 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar32 + 0x1c) = 5;
    FUN_1800079f8(local_310,0x1800d7f38,0x1c);
    plVar32 = local_318;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined1 *)(plVar25 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar25 + 0x1c) = 0x50;
    FUN_1800079f8(local_318,0x1800d7f18,0x1e);
    plVar25 = local_390;
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined1 *)(plVar32 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar32 + 0x1c) = 5;
    FUN_1800079f8(local_390,0x1800d7be0,0xe);
    plVar32 = local_528;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined1 *)(plVar25 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar25 + 0x1c) = 8;
    FUN_1800079f8(local_528,0x1800d7f90,0xc);
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    if ((code *)plVar32[10] != (code *)0x0) {
      (*(code *)plVar32[10])
                (*(undefined4 *)((longlong)plVar32 + 0x4c),
                 "Magenta/Cyan;Magenta/Blue;Red/Yellow;User-Defined");
    }
    plVar32 = local_568;
    *(undefined1 *)(local_528 + 3) = 0x16;
    *(undefined4 *)((longlong)local_528 + 0x1c) = 3;
    *(undefined1 *)(local_3f8 + 3) = 5;
    *(undefined4 *)((longlong)local_3f8 + 0x1c) = 0;
    *(undefined1 *)(local_330 + 3) = 0xe;
    *(undefined4 *)((longlong)local_330 + 0x1c) = 0xc0c0c0;
    *(undefined1 *)(local_338 + 3) = 0xe;
    *(undefined4 *)((longlong)local_338 + 0x1c) = 0x808080;
    *(undefined1 *)(local_368 + 3) = 0xb;
    *(undefined4 *)((longlong)local_368 + 0x1c) = 0x5a;
    *(undefined4 *)((longlong)local_368 + 0x2c) = 0;
    *(undefined4 *)((longlong)local_368 + 0x3c) = 100;
    *(undefined1 *)(local_350 + 3) = 0xb;
    *(undefined4 *)((longlong)local_350 + 0x1c) = 1;
    FUN_1800079f8(local_568,0x1800d7fc0,0x21);
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    puVar18 = FUN_180029680((longlong *)&local_1b0,(undefined8 *)&local_2a0);
    plVar32 = local_568;
    if (0xf < (ulonglong)puVar18[3]) {
      puVar18 = (undefined8 *)*puVar18;
    }
    if ((code *)local_568[10] != (code *)0x0) {
      (*(code *)local_568[10])(*(undefined4 *)((longlong)local_568 + 0x4c),puVar18);
      *(undefined1 *)(plVar32 + 3) = 0x16;
    }
    if (0xf < local_288) {
      FUN_1800966b8((LPVOID)CONCAT71(uStack_29f,local_2a0));
    }
    plVar25 = local_340;
    local_2a0 = 0;
    local_290 = 0;
    local_288 = 0xf;
    *(undefined1 *)(local_568 + 3) = 0x16;
    *(undefined4 *)((longlong)local_568 + 0x1c) = 0;
    FUN_1800079f8(local_340,0x1800d6d30,0x14);
    plVar32 = local_460;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined1 *)(plVar25 + 3) = 5;
    *(undefined4 *)((longlong)plVar25 + 0x1c) = 0;
    FUN_1800079f8(local_460,0x1800d7fa0,0x18);
    plVar25 = local_468;
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined1 *)(plVar32 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar32 + 0x1c) = 5;
    *(undefined4 *)((longlong)plVar32 + 0x2c) = 0;
    *(undefined4 *)((longlong)plVar32 + 0x3c) = 500;
    FUN_1800079f8(local_468,0x1800d8008,0x19);
    plVar32 = local_470;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined1 *)(plVar25 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar25 + 0x1c) = 5;
    *(undefined4 *)((longlong)plVar25 + 0x2c) = 0;
    *(undefined4 *)((longlong)plVar25 + 0x3c) = 500;
    FUN_1800079f8(local_470,0x1800d7fe8,0x19);
    plVar25 = local_370;
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined1 *)(plVar32 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar32 + 0x1c) = 5;
    *(undefined4 *)((longlong)plVar32 + 0x2c) = 0;
    *(undefined4 *)((longlong)plVar32 + 0x3c) = 500;
    FUN_1800079f8(local_370,0x1800d8048,0x15);
    plVar32 = local_458;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined1 *)(plVar25 + 3) = 5;
    *(undefined4 *)((longlong)plVar25 + 0x1c) = 0;
    FUN_1800079f8(local_458,0x1800d8028,0x19);
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    if ((code *)plVar32[10] != (code *)0x0) {
      (*(code *)plVar32[10])
                (*(undefined4 *)((longlong)plVar32 + 0x4c),
                 "5 minutes;10 minutes;15 minutes;30 minutes");
    }
    plVar32 = local_4c8;
    *(undefined1 *)(local_458 + 3) = 0x16;
    *(undefined4 *)((longlong)local_458 + 0x1c) = 2;
    FUN_1800079f8(local_4c8,0x1800d8060,0x16);
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    if ((code *)plVar32[10] != (code *)0x0) {
      (*(code *)plVar32[10])
                (*(undefined4 *)((longlong)plVar32 + 0x4c),"Off;Pivot Sessions;Pivot Days");
    }
    plVar25 = local_3d8;
    *(undefined1 *)(local_4c8 + 3) = 0x16;
    *(undefined4 *)((longlong)local_4c8 + 0x1c) = 0;
    FUN_1800079f8(local_3d8,0x1800d80a8,0xd);
    plVar32 = local_428;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined1 *)(plVar25 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar25 + 0x1c) = 5;
    *(undefined4 *)((longlong)plVar25 + 0x2c) = 3;
    *(undefined4 *)((longlong)plVar25 + 0x3c) = 5000;
    FUN_1800079f8(local_428,0x1800d80e8,0x16);
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    puVar18 = FUN_180029680((longlong *)&local_1b0,(undefined8 *)&local_280);
    plVar32 = local_428;
    if (0xf < (ulonglong)puVar18[3]) {
      puVar18 = (undefined8 *)*puVar18;
    }
    if ((code *)local_428[10] != (code *)0x0) {
      (*(code *)local_428[10])(*(undefined4 *)((longlong)local_428 + 0x4c),puVar18);
      *(undefined1 *)(plVar32 + 3) = 0x16;
    }
    if (0xf < local_268) {
      FUN_1800966b8((LPVOID)CONCAT71(uStack_27f,local_280));
    }
    plVar32 = local_448;
    local_280 = 0;
    local_270 = 0;
    local_268 = 0xf;
    *(undefined1 *)(local_428 + 3) = 0x16;
    *(undefined4 *)((longlong)local_428 + 0x1c) = 2;
    FUN_1800079f8(local_448,0x1800d80d8,10);
    plVar25 = local_440;
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined1 *)(plVar32 + 3) = 0xe;
    *(undefined4 *)((longlong)plVar32 + 0x1c) = 0;
    FUN_1800079f8(local_440,0x1800d8118,0x11);
    plVar32 = local_360;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined1 *)(plVar25 + 3) = 0xe;
    *(undefined4 *)((longlong)plVar25 + 0x1c) = 0xa79e96;
    FUN_1800079f8(local_360,0x1800d8100,0x17);
    plVar25 = local_348;
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined1 *)(plVar32 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar32 + 0x1c) = 0x4e;
    *(undefined4 *)((longlong)plVar32 + 0x2c) = 0;
    *(undefined4 *)((longlong)plVar32 + 0x3c) = 100;
    FUN_1800079f8(local_348,0x1800d8148,0x11);
    plVar32 = local_438;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined1 *)(plVar25 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar25 + 0x1c) = 1;
    FUN_1800079f8(local_438,0x1800d8130,0x10);
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    puVar18 = FUN_180029680((longlong *)local_1a0,(undefined8 *)&local_260);
    plVar32 = local_438;
    if (0xf < (ulonglong)puVar18[3]) {
      puVar18 = (undefined8 *)*puVar18;
    }
    if ((code *)local_438[10] != (code *)0x0) {
      (*(code *)local_438[10])(*(undefined4 *)((longlong)local_438 + 0x4c),puVar18);
      *(undefined1 *)(plVar32 + 3) = 0x16;
    }
    if (0xf < local_248) {
      FUN_1800966b8((LPVOID)CONCAT71(uStack_25f,local_260));
    }
    plVar32 = local_430;
    local_260 = 0;
    local_250 = 0;
    local_248 = 0xf;
    *(undefined1 *)(local_438 + 3) = 0x16;
    *(undefined4 *)((longlong)local_438 + 0x1c) = 3;
    FUN_1800079f8(local_430,0x1800d8180,0x13);
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    puVar18 = FUN_180029680((longlong *)&local_1a8,(undefined8 *)&local_240);
    plVar32 = local_430;
    if (0xf < (ulonglong)puVar18[3]) {
      puVar18 = (undefined8 *)*puVar18;
    }
    if ((code *)local_430[10] != (code *)0x0) {
      (*(code *)local_430[10])(*(undefined4 *)((longlong)local_430 + 0x4c),puVar18);
      *(undefined1 *)(plVar32 + 3) = 0x16;
    }
    if (0xf < local_228) {
      FUN_1800966b8((LPVOID)CONCAT71(uStack_23f,local_240));
    }
    plVar32 = local_558;
    local_240 = 0;
    local_230 = 0;
    local_228 = 0xf;
    *(undefined1 *)(local_430 + 3) = 0x16;
    *(undefined4 *)((longlong)local_430 + 0x1c) = 1;
    FUN_1800079f8(local_558,0x1800d8160,0x1e);
    plVar25 = local_3e8;
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined1 *)(plVar32 + 3) = 5;
    *(undefined4 *)((longlong)plVar32 + 0x1c) = 1;
    FUN_1800079f8(local_3e8,0x1800d8198,0x11);
    plVar32 = local_460;
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined1 *)(plVar25 + 3) = 5;
    *(undefined4 *)((longlong)plVar25 + 0x1c) = 0;
    *(undefined1 *)(local_3e0 + 3) = 5;
    *(undefined4 *)((longlong)local_3e0 + 0x1c) = 1;
    if (((char *)*local_460 != (char *)0x0) && (*(char *)*local_460 != '\0')) {
      FUN_1800079f8(local_460,0x1800d4ecd,0);
      *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    }
    plVar32 = local_468;
    if (((char *)*local_468 != (char *)0x0) && (*(char *)*local_468 != '\0')) {
      FUN_1800079f8(local_468,0x1800d4ecd,0);
      *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    }
    plVar32 = local_470;
    if (((char *)*local_470 != (char *)0x0) && (*(char *)*local_470 != '\0')) {
      FUN_1800079f8(local_470,0x1800d4ecd,0);
      *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    }
    pfVar40 = local_508;
    if (*(char **)local_508 == (char *)0x0) {
      return;
    }
    if (**(char **)local_508 == '\0') {
      return;
    }
    FUN_1800079f8((longlong *)local_508,0x1800d4ecd,0);
    pfVar40[3] = 1.4013e-45;
    return;
  }
  if (param_3[0xe1] == 0) {
    auVar1._4_4_ = extraout_var;
    auVar1._0_4_ = extraout_s0;
    auVar1._8_8_ = extraout_var_06;
    uVar21 = FUN_1800254e8(auVar1,CONCAT44(uVar53,fVar52),(longlong)param_3,extraout_x1,param_5,
                           param_6,param_7,param_8,param_9,param_10);
    *piVar17 = (int)uVar21;
  }
  if (*piVar17 != 0) {
    return;
  }
  uStack_158 = 0;
  local_160 = 0;
  uStack_148 = 0;
  uStack_144 = 0;
  local_150 = 0;
  uStack_14c = 0;
  uStack_138 = 0;
  uStack_134 = 0;
  local_140 = 0;
  uStack_13c = 0;
  local_128 = (float *)0x0;
  local_130 = 0;
  uStack_12c = 0;
  uStack_12b = 0;
  uStack_129 = 0;
  uStack_118 = 0;
  local_120 = 0;
  uStack_108 = 0;
  uStack_104 = 0;
  local_110 = 0;
  uStack_10c = 0;
  uStack_f8 = 0;
  local_100 = 0;
  uStack_e8 = 0;
  uStack_f0 = 0;
  local_e0 = 0;
  uVar12 = FUN_180026550((longlong)local_500);
  uVar31 = (ulonglong)(uint)param_3[0xe1];
  FUN_180011fd0(&local_160,(longlong)param_3,param_3[0xe1],uVar12);
  local_570 = (float *)CONCAT17(uStack_129,CONCAT25(uStack_12b,CONCAT14(uStack_12c,local_130)));
  local_500 = (longlong *)0x15180;
  lVar36 = ((longlong)local_570 % 86400000000) / 1000000;
  iVar13 = (int)lVar36;
  if (lVar36 < 0x15180) {
    iVar34 = iVar13 % 0x3c;
    iVar35 = (iVar13 / 0x3c) % 0x3c;
    lVar37 = lVar36;
  }
  else {
    iVar34 = 0;
    iVar35 = 0;
    lVar37 = 0;
  }
  local_520 = (float *)CONCAT44(uStack_14c,local_150);
  local_580 = (float *)(((longlong)
                         (iVar34 + (iVar35 + ((((int)lVar37 / 0xe10) % 0x18 + 4) % 0x18) * 0x3c) *
                                   0x3c) * 1000000 - (longlong)local_520 % 86400000000) +
                       (longlong)local_520);
  if (lVar36 < 0x15180) {
    iVar34 = iVar13 % 0x3c;
    iVar13 = ((iVar13 / 0x3c) % 0x3c + 0x1e) % 0x3c;
    lVar36 = (longlong)local_570 % 86400000000;
    iVar35 = ((int)(lVar36 / 1000000) + (int)(lVar36 >> 0x3f)) -
             (SUB164(SEXT816(lVar36) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
  }
  else {
    iVar34 = 0;
    iVar13 = 0x1e;
    iVar35 = 0;
  }
  local_470 = (longlong *)
              (((longlong)(iVar34 + (iVar13 + (((iVar35 / 0xe10) % 0x18 + 9) % 0x18) * 0x3c) * 0x3c)
                * 1000000 - (longlong)local_520 % 86400000000) + (longlong)local_520);
  lVar36 = ((longlong)local_570 % 86400000000) / 1000000;
  if (lVar36 < 0x15180) {
    iVar13 = (int)lVar36 % 0x3c;
    lVar36 = (longlong)local_570 % 86400000000;
    iVar34 = (((((int)(lVar36 / 1000000) + (int)(lVar36 >> 0x3f)) -
               (SUB164(SEXT816(lVar36) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f)) / 0x3c) % 0x3c +
             0x1e) % 0x3c;
    lVar36 = (longlong)local_570 % 86400000000;
    iVar35 = ((int)(lVar36 / 1000000) + (int)(lVar36 >> 0x3f)) -
             (SUB164(SEXT816(lVar36) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
  }
  else {
    iVar13 = 0;
    iVar34 = 0x1e;
    iVar35 = 0;
  }
  local_488 = (float *)((longlong)
                        (iVar13 + (iVar34 + (((iVar35 / 0xe10) % 0x18 + 9) % 0x18) * 0x3c) * 0x3c) *
                        1000000 + ((longlong)local_520 / 86400000000) * 86400000000);
  lVar36 = ((longlong)local_570 % 86400000000) / 1000000;
  if (lVar36 < 0x15180) {
    iVar13 = (int)lVar36 % 0x3c;
    lVar36 = (longlong)local_570 % 86400000000;
    iVar34 = (((((int)(lVar36 / 1000000) + (int)(lVar36 >> 0x3f)) -
               (SUB164(SEXT816(lVar36) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f)) / 0x3c) % 0x3c +
             0x1e) % 0x3c;
    lVar36 = (longlong)local_570 % 86400000000;
    iVar35 = ((int)(lVar36 / 1000000) + (int)(lVar36 >> 0x3f)) -
             (SUB164(SEXT816(lVar36) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
  }
  else {
    iVar13 = 0;
    iVar34 = 0x1e;
    iVar35 = 0;
  }
  local_468 = (longlong *)
              ((longlong)
               (iVar13 + (iVar34 + (((iVar35 / 0xe10) % 0x18 + 0x12) % 0x18) * 0x3c) * 0x3c) *
               1000000 + ((longlong)local_520 / 86400000000) * 86400000000);
  lVar36 = ((longlong)local_570 % 86400000000) / 1000000;
  if (lVar36 < 0x15180) {
    iVar13 = (int)lVar36 % 0x3c;
    lVar36 = (longlong)local_570 % 86400000000;
    iVar34 = (((((int)(lVar36 / 1000000) + (int)(lVar36 >> 0x3f)) -
               (SUB164(SEXT816(lVar36) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f)) / 0x3c) % 0x3c +
             0x1e) % 0x3c;
    lVar36 = (longlong)local_570 % 86400000000;
    iVar35 = ((int)(lVar36 / 1000000) + (int)(lVar36 >> 0x3f)) -
             (SUB164(SEXT816(lVar36) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
  }
  else {
    iVar13 = 0;
    iVar34 = 0x1e;
    iVar35 = 0;
  }
  local_378 = (longlong *)
              ((longlong)
               (iVar13 + (iVar34 + (((iVar35 / 0xe10) % 0x18 + 0x12) % 0x18) * 0x3c) * 0x3c) *
               1000000 + ((longlong)local_520 / 86400000000) * 86400000000);
  local_490 = local_128;
  iVar34 = 0x3c;
  local_508 = local_128;
  iVar13 = FUN_180011e28((longlong *)&local_508);
  local_460 = (longlong *)
              ((longlong)(extraout_w13 + (extraout_w12 + iVar13 * iVar34) * iVar34) * 1000000 +
              ((longlong)local_520 / 86400000000) * 86400000000);
  if ((param_3[0x47e] != 0) && (param_3[0xe1] == 0)) {
    uVar12 = FUN_180026550((longlong)local_550);
    if ((uVar12 == 0) && (uVar12 = FUN_180026550((longlong)local_4c8), uVar12 == 0)) {
      *(undefined2 *)((longlong)local_548 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_518 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_510 + 0x24) = 0x58;
      *(undefined2 *)(local_4f8 + 9) = 0x58;
      *(undefined2 *)((longlong)local_4e0 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_4f0 + 0x24) = 0x58;
      *(undefined2 *)(local_4d8 + 9) = 0x58;
      *(undefined2 *)((longlong)local_4e8 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_4d0 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_4c0 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_4b8 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_4b0 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_4a8 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_4a0 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_498 + 0x24) = 0x58;
    }
    else {
      uVar12 = FUN_180026550((longlong)local_550);
      if ((uVar12 == 1) && (uVar12 = FUN_180026550((longlong)local_4c8), uVar12 == 0)) {
        *(undefined2 *)((longlong)local_548 + 0x24) = 3;
        *(undefined2 *)((longlong)local_518 + 0x24) = 3;
        *(undefined2 *)((longlong)local_510 + 0x24) = 3;
        *(undefined2 *)(local_4f8 + 9) = 3;
        *(undefined2 *)((longlong)local_4e0 + 0x24) = 3;
        *(undefined2 *)((longlong)local_4f0 + 0x24) = 3;
        *(undefined2 *)(local_4d8 + 9) = 3;
        *(undefined2 *)((longlong)local_4e8 + 0x24) = 3;
        *(undefined2 *)((longlong)local_4d0 + 0x24) = 3;
        *(undefined2 *)((longlong)local_4c0 + 0x24) = 3;
        *(undefined2 *)((longlong)local_4b8 + 0x24) = 3;
        *(undefined2 *)((longlong)local_4b0 + 0x24) = 3;
        *(undefined2 *)((longlong)local_4a8 + 0x24) = 3;
        *(undefined2 *)((longlong)local_4a0 + 0x24) = 3;
        *(undefined2 *)((longlong)local_498 + 0x24) = 3;
      }
      else {
        uVar12 = FUN_180026550((longlong)local_550);
        if ((uVar12 == 2) && (uVar12 = FUN_180026550((longlong)local_4c8), uVar12 == 0)) {
          *(undefined2 *)((longlong)local_548 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_518 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_510 + 0x24) = 0x66;
          *(undefined2 *)(local_4f8 + 9) = 0x66;
          *(undefined2 *)((longlong)local_4e0 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_4f0 + 0x24) = 0x66;
          *(undefined2 *)(local_4d8 + 9) = 0x66;
          *(undefined2 *)((longlong)local_4e8 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_4d0 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_4c0 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_4b8 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_4b0 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_4a8 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_4a0 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_498 + 0x24) = 0x66;
        }
        else {
          uVar12 = FUN_180026550((longlong)local_550);
          if ((uVar12 == 4) || (uVar12 = FUN_180026550((longlong)local_4c8), uVar12 != 0)) {
            *(undefined2 *)((longlong)local_548 + 0x24) = 5;
            *(undefined2 *)((longlong)local_518 + 0x24) = 5;
            *(undefined2 *)((longlong)local_510 + 0x24) = 5;
            *(undefined2 *)(local_4f8 + 9) = 5;
            *(undefined2 *)((longlong)local_4e0 + 0x24) = 5;
            *(undefined2 *)((longlong)local_4f0 + 0x24) = 5;
            *(undefined2 *)(local_4d8 + 9) = 5;
            *(undefined2 *)((longlong)local_4e8 + 0x24) = 5;
            *(undefined2 *)((longlong)local_4d0 + 0x24) = 5;
            *(undefined2 *)((longlong)local_4c0 + 0x24) = 5;
            *(undefined2 *)((longlong)local_4b8 + 0x24) = 5;
            *(undefined2 *)((longlong)local_4b0 + 0x24) = 5;
            *(undefined2 *)((longlong)local_4a8 + 0x24) = 5;
            *(undefined2 *)((longlong)local_4a0 + 0x24) = 5;
            *(undefined2 *)((longlong)local_498 + 0x24) = 5;
            *(undefined1 *)(local_450 + 3) = 0x16;
            *(undefined4 *)((longlong)local_450 + 0x1c) = 0;
          }
        }
      }
    }
    plVar32 = local_528;
    uVar12 = FUN_180026550((longlong)local_528);
    if ((uVar12 != 0) && (uVar12 = FUN_180026550((longlong)plVar32), uVar12 != 1)) {
      FUN_180026550((longlong)plVar32);
    }
    uVar12 = FUN_180026550((longlong)local_528);
    if (uVar12 != 3) {
      *(undefined4 *)(local_548 + 3) = extraout_w12_00;
      *(undefined4 *)(local_518 + 3) = extraout_w12_00;
      *(undefined4 *)(local_510 + 3) = extraout_w12_00;
      local_4f8[6] = extraout_w11;
      *(float *)(local_4e0 + 3) = extraout_w11;
      *(float *)(local_4f0 + 3) = extraout_w11;
      local_4d8[6] = extraout_w11;
      *(float *)(local_4e8 + 3) = extraout_w11;
      *(float *)(local_4d0 + 3) = extraout_w11;
      *(float *)(local_4c0 + 3) = extraout_w11;
      *(float *)(local_4b8 + 3) = extraout_w11;
      *(float *)(local_4b0 + 3) = extraout_w11;
      *(float *)(local_4a8 + 3) = extraout_w11;
      *(float *)(local_4a0 + 3) = extraout_w11;
      *(float *)(local_498 + 3) = extraout_w11;
    }
    if (local_560 != (int *)0x0) {
      FUN_180014090(local_560);
      (**(code **)(param_3 + 0x448))(2,0);
    }
    local_508 = (float *)FUN_180096150(0x80);
    fVar52 = 2.0;
    uVar53 = 0;
    uVar54 = 0;
    uVar31 = 1;
    local_508[2] = 0.0;
    local_508[3] = 0.0;
    local_508[0] = 0.0;
    local_508[1] = 0.0;
    local_508[6] = 0.0;
    local_508[7] = 0.0;
    local_508[4] = 0.0;
    local_508[5] = 0.0;
    local_508[10] = 0.0;
    local_508[0xb] = 0.0;
    local_508[8] = 0.0;
    local_508[9] = 0.0;
    local_508[0xe] = 0.0;
    local_508[0xf] = 0.0;
    local_508[0xc] = 0.0;
    local_508[0xd] = 0.0;
    local_508[0x12] = 0.0;
    local_508[0x13] = 0.0;
    local_508[0x10] = 0.0;
    local_508[0x11] = 0.0;
    local_508[0x16] = 0.0;
    local_508[0x17] = 0.0;
    local_508[0x14] = 0.0;
    local_508[0x15] = 0.0;
    local_508[0x1a] = 0.0;
    local_508[0x1b] = 0.0;
    local_508[0x18] = 0.0;
    local_508[0x19] = 0.0;
    local_508[0x1e] = 0.0;
    local_508[0x1f] = 0.0;
    local_508[0x1c] = 0.0;
    local_508[0x1d] = 0.0;
    local_560 = (int *)FUN_18001f8e8(0x3e4ccccd,0x40000000,(undefined8 *)local_508,(longlong)param_3
                                     ,1,1);
    if (local_560 == (int *)0x0) {
      return;
    }
    (**(code **)(param_3 + 0x448))(2,local_560);
    if (pfVar42 != (float *)0x0) {
      FUN_1800966b8(pfVar42);
      (**(code **)(param_3 + 0x448))(3,0);
    }
    pfVar42 = (float *)FUN_180096718(0x50);
    pfVar42[2] = 0.0;
    pfVar42[3] = 0.0;
    pfVar42[0] = 0.0;
    pfVar42[1] = 0.0;
    pfVar42[6] = 0.0;
    pfVar42[7] = 0.0;
    pfVar42[4] = 0.0;
    pfVar42[5] = 0.0;
    pfVar42[10] = 0.0;
    pfVar42[0xb] = 0.0;
    pfVar42[8] = 0.0;
    pfVar42[9] = 0.0;
    pfVar42[0xe] = 0.0;
    pfVar42[0xf] = 0.0;
    pfVar42[0xc] = 0.0;
    pfVar42[0xd] = 0.0;
    pfVar42[0x12] = 0.0;
    pfVar42[0x13] = 0.0;
    pfVar42[0x10] = 0.0;
    pfVar42[0x11] = 0.0;
    (**(code **)(param_3 + 0x448))(3,pfVar42);
    pfVar42[2] = 0.0;
    pfVar42[3] = 0.0;
    pfVar42[0] = 0.0;
    pfVar42[1] = 0.0;
    pfVar42[6] = 0.0;
    pfVar42[7] = 0.0;
    pfVar42[4] = 0.0;
    pfVar42[5] = 0.0;
    pfVar42[10] = 0.0;
    pfVar42[0xb] = 0.0;
    pfVar42[8] = 0.0;
    pfVar42[9] = 0.0;
    pfVar42[0xe] = 0.0;
    pfVar42[0xf] = 0.0;
    pfVar42[0xc] = 0.0;
    pfVar42[0xd] = 0.0;
    pfVar42[0x12] = 0.0;
    pfVar42[0x13] = 0.0;
    pfVar42[0x10] = 0.0;
    pfVar42[0x11] = 0.0;
    if (pfVar40 != (float *)0x0) {
      FUN_180022590((int *)pfVar40,(longlong)param_3,uVar31);
      FUN_18000bcc0(pfVar40);
      (**(code **)(param_3 + 0x448))(5,0);
    }
    uStack_188 = 0;
    local_190 = (char *)0x0;
    uStack_178 = 0;
    local_180 = (undefined1 *)0x0;
    local_170 = 0;
    uStack_3c8 = 0;
    local_3d0 = (char *)0x0;
    local_3b8 = 0;
    local_3c0 = 0;
    local_3b0 = 0;
    local_508 = (float *)FUN_180096150(0xa8);
    local_508[2] = 0.0;
    local_508[3] = 0.0;
    local_508[0] = 0.0;
    local_508[1] = 0.0;
    local_508[6] = 0.0;
    local_508[7] = 0.0;
    local_508[4] = 0.0;
    local_508[5] = 0.0;
    local_508[10] = 0.0;
    local_508[0xb] = 0.0;
    local_508[8] = 0.0;
    local_508[9] = 0.0;
    local_508[0xe] = 0.0;
    local_508[0xf] = 0.0;
    local_508[0xc] = 0.0;
    local_508[0xd] = 0.0;
    local_508[0x12] = 0.0;
    local_508[0x13] = 0.0;
    local_508[0x10] = 0.0;
    local_508[0x11] = 0.0;
    local_508[0x16] = 0.0;
    local_508[0x17] = 0.0;
    local_508[0x14] = 0.0;
    local_508[0x15] = 0.0;
    local_508[0x1a] = 0.0;
    local_508[0x1b] = 0.0;
    local_508[0x18] = 0.0;
    local_508[0x19] = 0.0;
    local_508[0x1e] = 0.0;
    local_508[0x1f] = 0.0;
    local_508[0x1c] = 0.0;
    local_508[0x1d] = 0.0;
    local_508[0x22] = 0.0;
    local_508[0x23] = 0.0;
    local_508[0x20] = 0.0;
    local_508[0x21] = 0.0;
    local_508[0x26] = 0.0;
    local_508[0x27] = 0.0;
    local_508[0x24] = 0.0;
    local_508[0x25] = 0.0;
    local_508[0x28] = 0.0;
    local_508[0x29] = 0.0;
    FUN_180026550((longlong)local_568);
    uVar22 = FUN_180026708((longlong)local_368);
    iVar13 = (int)uVar22;
    uVar22 = FUN_180026708((longlong)local_350);
    local_1f0 = local_3b0;
    uStack_208 = uStack_3c8;
    local_210 = (longlong)local_3d0;
    uStack_1f8 = local_3b8;
    uStack_200 = local_3c0;
    local_3b0 = local_170;
    uStack_3c8 = uStack_188;
    local_3d0 = local_190;
    local_3b8 = uStack_178;
    local_3c0 = local_180;
    in_stack_fffffffffffffa18 = 0;
    pfVar40 = (float *)FUN_180022430(extraout_x11,(longlong)param_3,uVar31,1,(int)uVar22,3,0,iVar13,
                                     0,0,0,0,0,0,0,0,1,&local_3d0,&local_210,0,extraout_w12_01,0,0,0
                                     ,in_stack_fffffffffffffa70,0);
    if (pfVar40 == (float *)0x0) {
      return;
    }
    (**(code **)(param_3 + 0x448))(5,pfVar40);
    pcVar30 = local_420;
    if (local_420 != (char *)0x0) {
      FUN_18000af28(local_420,(longlong)param_3);
      FUN_1800477f8(pcVar30);
      (**(code **)(param_3 + 0x448))(7,0);
    }
    local_508 = (float *)FUN_180096150(0x18);
    local_508[2] = 0.0;
    local_508[3] = 0.0;
    local_508[0] = 0.0;
    local_508[1] = 0.0;
    local_508[4] = 0.0;
    local_508[5] = 0.0;
    bVar11 = FUN_180026690((longlong)local_558);
    local_420 = FUN_18000aec8(extraout_x11_00,(longlong)param_3,uVar31,bVar11);
    if (local_420 == (char *)0x0) {
      return;
    }
    (**(code **)(param_3 + 0x448))(7,local_420);
    pcVar30 = local_418;
    if (local_418 != (char *)0x0) {
      FUN_18000af28(local_418,(longlong)param_3);
      FUN_1800477f8(pcVar30);
      (**(code **)(param_3 + 0x448))(8,0);
    }
    local_508 = (float *)FUN_180096150(0x18);
    local_508[2] = 0.0;
    local_508[3] = 0.0;
    local_508[0] = 0.0;
    local_508[1] = 0.0;
    local_508[4] = 0.0;
    local_508[5] = 0.0;
    bVar11 = FUN_180026690((longlong)local_558);
    local_418 = FUN_18000aec8(extraout_x11_01,(longlong)param_3,uVar31,bVar11);
    if (local_418 == (char *)0x0) {
      return;
    }
    (**(code **)(param_3 + 0x448))(8,local_418);
    if (local_578 != (longlong *)0x0) {
      FUN_18000af28((char *)local_578,(longlong)param_3);
      FUN_1800477f8(local_578);
      (**(code **)(param_3 + 0x448))(9,0);
    }
    local_508 = (float *)FUN_180096150(0x18);
    local_508[2] = 0.0;
    local_508[3] = 0.0;
    local_508[0] = 0.0;
    local_508[1] = 0.0;
    local_508[4] = 0.0;
    local_508[5] = 0.0;
    bVar11 = FUN_180026690((longlong)local_558);
    local_578 = (longlong *)FUN_18000aec8(extraout_x11_02,(longlong)param_3,uVar31,bVar11);
    if (local_578 == (longlong *)0x0) {
      return;
    }
    (**(code **)(param_3 + 0x448))(9,local_578);
    if (puVar43 != (uint *)0x0) {
      FUN_1800048c0((longlong)puVar43,(longlong)param_3,uVar31);
      FUN_180047878(puVar43);
      (**(code **)(param_3 + 0x448))(0xc,0);
    }
    uStack_12b = 0;
    uStack_129 = 0;
    local_128 = (float *)((ulonglong)local_128 & 0xffffffffffff);
    uStack_10c = 0;
    uStack_108 = 0;
    local_160 = CONCAT44(local_160._4_4_,5000);
    uVar22 = FUN_180026708((longlong)local_348);
    local_160 = CONCAT44((int)uVar22,(int)local_160);
    uVar22 = FUN_180026708((longlong)local_438);
    uStack_158 = CONCAT44(uStack_158._4_4_,(int)uVar22);
    uVar12 = FUN_180026550((longlong)local_430);
    uStack_158 = CONCAT44(uVar12,(undefined4)uStack_158);
    uVar22 = FUN_180026708((longlong)local_360);
    uStack_14c = 1;
    uStack_148 = 0;
    local_150 = (undefined4)uVar22;
    uStack_144 = 0;
    local_140 = 0;
    uStack_13c = 0;
    uStack_138 = 0;
    uStack_134 = 0;
    local_130 = 0;
    uStack_12c = FUN_180026690((longlong)local_558);
    uVar12 = FUN_180026550((longlong)local_428);
    local_128 = (float *)CONCAT44(local_128._4_4_,uVar12);
    bVar11 = FUN_180026690((longlong)local_3e8);
    local_128._0_5_ = CONCAT14(bVar11,(undefined4)local_128);
    uVar12 = FUN_180026550((longlong)local_4c8);
    local_120 = CONCAT44(local_120._4_4_,uVar12);
    bVar11 = FUN_180026690((longlong)local_3e0);
    local_128._0_6_ = CONCAT15(bVar11,(undefined5)local_128);
    uVar22 = FUN_180026708((longlong)local_3d8);
    local_120 = CONCAT44((int)uVar22,(undefined4)local_120);
    uStack_118 = CONCAT44(uStack_118._4_4_,2);
    uVar14 = FUN_180026888((longlong)local_448);
    uStack_118 = CONCAT44(uVar14,(undefined4)uStack_118);
    local_110 = FUN_180026888((longlong)local_440);
    local_508 = (float *)FUN_180096150(0x48);
    local_508[2] = 0.0;
    local_508[3] = 0.0;
    local_508[0] = 0.0;
    local_508[1] = 0.0;
    local_508[6] = 0.0;
    local_508[7] = 0.0;
    local_508[4] = 0.0;
    local_508[5] = 0.0;
    local_508[10] = 0.0;
    local_508[0xb] = 0.0;
    local_508[8] = 0.0;
    local_508[9] = 0.0;
    local_508[0xe] = 0.0;
    local_508[0xf] = 0.0;
    local_508[0xc] = 0.0;
    local_508[0xd] = 0.0;
    local_508[0x10] = 0.0;
    local_508[0x11] = 0.0;
    puVar43 = (uint *)FUN_180004688((int *)local_508,(longlong)param_3,uVar31,(int *)&local_160);
    if (puVar43 == (uint *)0x0) {
      return;
    }
    (**(code **)(param_3 + 0x448))(0xc,puVar43);
    *local_398 = 0;
    *local_400 = 0;
    *local_478 = 0;
    *local_480 = 0;
  }
  pcVar19 = local_418;
  pcVar30 = local_420;
  if (param_3[0x1b] != 0) {
    if (local_560 != (int *)0x0) {
      FUN_180014090(local_560);
      (**(code **)(param_3 + 0x448))(2,0);
    }
    if (pfVar42 != (float *)0x0) {
      FUN_1800966b8(pfVar42);
      (**(code **)(param_3 + 0x448))(3,0);
    }
    if (pfVar40 != (float *)0x0) {
      FUN_180022590((int *)pfVar40,(longlong)param_3,uVar31);
      FUN_18000bcc0(pfVar40);
      (**(code **)(param_3 + 0x448))(5,0);
    }
    pcVar30 = local_420;
    if (local_420 != (char *)0x0) {
      FUN_18000af28(local_420,(longlong)param_3);
      FUN_1800477f8(pcVar30);
      (**(code **)(param_3 + 0x448))(7,0);
    }
    pcVar30 = local_418;
    if (local_418 != (char *)0x0) {
      FUN_18000af28(local_418,(longlong)param_3);
      FUN_1800477f8(pcVar30);
      (**(code **)(param_3 + 0x448))(8,0);
    }
    if (local_578 != (longlong *)0x0) {
      FUN_18000af28((char *)local_578,(longlong)param_3);
      FUN_1800477f8(local_578);
      (**(code **)(param_3 + 0x448))(9,0);
    }
    if (puVar43 == (uint *)0x0) {
      return;
    }
    FUN_1800048c0((longlong)puVar43,(longlong)param_3,uVar31);
    FUN_180047878(puVar43);
    (**(code **)(param_3 + 0x448))(0xc,0);
    return;
  }
  if (local_560 == (int *)0x0) {
    return;
  }
  if (pfVar40 == (float *)0x0) {
    return;
  }
  if (local_420 == (char *)0x0) {
    return;
  }
  if (local_418 == (char *)0x0) {
    return;
  }
  if (local_578 == (longlong *)0x0) {
    return;
  }
  if (puVar43 == (uint *)0x0) {
    return;
  }
  if (pfVar42 == (float *)0x0) {
    return;
  }
  iVar13 = (**(code **)(param_3 + 0x3ec))(param_3[0x270]);
  if (iVar13 == 0) {
    FUN_180022590((int *)pfVar40,(longlong)param_3,uVar31);
    FUN_18000af28(pcVar30,(longlong)param_3);
    FUN_18000af28(pcVar19,(longlong)param_3);
    FUN_18000af28((char *)local_578,(longlong)param_3);
    FUN_1800048c0((longlong)puVar43,(longlong)param_3,uVar31);
    return;
  }
  bVar11 = FUN_180026690((longlong)local_3f8);
  if (!bVar11) {
    FUN_180022590((int *)pfVar40,(longlong)param_3,uVar31);
  }
  lVar36 = (longlong)local_378 % 86400000000;
  iVar13 = ((int)(lVar36 / 1000000) + (int)(lVar36 >> 0x3f)) -
           (SUB164(SEXT816(lVar36) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
  if (86399999999 < lVar36) {
    iVar13 = 0;
  }
  lVar36 = (longlong)local_488 % 86400000000;
  iVar34 = ((int)(lVar36 / 1000000) + (int)(lVar36 >> 0x3f)) -
           (SUB164(SEXT816(lVar36) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
  if (86399999999 < lVar36) {
    iVar34 = 0;
  }
  lVar36 = (longlong)local_580 % 86400000000;
  iVar35 = ((int)(lVar36 / 1000000) + (int)(lVar36 >> 0x3f)) -
           (SUB164(SEXT816(lVar36) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
  if (86399999999 < lVar36) {
    iVar35 = 0;
  }
  FUN_18001fb20(local_560,param_3,param_3[0xe1],(longlong)local_3f0,(longlong)local_410,
                (longlong)local_408,'\x01',iVar35,'\x01',iVar34,'\x01',iVar13);
  local_580 = local_570;
  pfVar39 = local_570;
  FUN_180011ef8((longlong *)&local_580);
  local_580 = pfVar39;
  FUN_180011e90((longlong *)&local_580);
  uVar31 = 0x3c;
  local_580 = pfVar39;
  FUN_180011e28((longlong *)&local_580);
  iVar33 = 0x18;
  pfVar27 = local_520;
  local_580 = pfVar39;
  FUN_180011ef8((longlong *)&local_580);
  local_580 = pfVar39;
  FUN_180011e90((longlong *)&local_580);
  local_580 = pfVar39;
  FUN_180011e28((longlong *)&local_580);
  local_580 = pfVar39;
  FUN_180011ef8((longlong *)&local_580);
  local_580 = pfVar39;
  FUN_180011e90((longlong *)&local_580);
  local_580 = pfVar39;
  FUN_180011e28((longlong *)&local_580);
  local_580 = local_490;
  FUN_180011ef8((longlong *)&local_580);
  FUN_180011e90((longlong *)&local_580);
  FUN_180011e28((longlong *)&local_580);
  FUN_180011ef8((longlong *)&local_580);
  FUN_180011e90((longlong *)&local_580);
  FUN_180011e28((longlong *)&local_580);
  local_580 = pfVar39;
  iVar34 = FUN_180011ef8((longlong *)&local_580);
  local_580 = pfVar39;
  iVar35 = FUN_180011e90((longlong *)&local_580);
  iVar13 = 0;
  iVar15 = (int)uVar31;
  if (iVar15 != 0) {
    iVar13 = (iVar35 + 0x1e) / iVar15;
  }
  iVar13 = (iVar35 + 0x1e) - iVar13 * iVar15;
  local_580 = pfVar39;
  iVar15 = FUN_180011e28((longlong *)&local_580);
  iVar35 = 0;
  if (iVar33 != 0) {
    iVar35 = (iVar15 + 9) / iVar33;
  }
  lVar36 = (longlong)
           (iVar34 + (iVar13 + ((iVar15 + 9) - iVar35 * iVar33) * (int)uVar31) * (int)uVar31) *
           1000000 + ((longlong)pfVar27 / 86400000000) * 86400000000;
  local_180 = &DAT_1800d4ecd;
  local_190 = "";
  uStack_188 = 0;
  plVar32 = &local_2e8;
  local_2d8 = pfVar27;
  bVar11 = FUN_180093658((longlong *)&local_2d8,&lStack_2e0,plVar32,0);
  if (bVar11) {
    iVar13 = 2;
LAB_180046230:
    plVar9 = local_408;
    local_580 = (float *)FUN_18000f448((longlong)local_408,param_3[0xe1] + -1);
    plVar25 = local_410;
    pfVar23 = (float *)FUN_18000f448((longlong)local_410,param_3[0xe1] + -1);
    fVar44 = FUN_180011838((*local_580 + *pfVar23) * 0.5,(longlong)param_3);
    local_580 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1] + -1);
    FUN_18000f448((longlong)plVar9,param_3[0xe1] + -1);
    local_578 = local_388;
    fVar45 = FUN_180026608((longlong)local_388);
    pfVar23 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1] + -1);
    fVar45 = FUN_180011838(*pfVar23 + fVar45 * (extraout_s19 - extraout_s18),(longlong)param_3);
    local_488 = (float *)FUN_18000f448((longlong)plVar9,param_3[0xe1] + -1);
    local_580 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1] + -1);
    FUN_18000f448((longlong)plVar9,param_3[0xe1] + -1);
    fVar46 = FUN_180026608((longlong)local_578);
    fVar46 = FUN_180011838(*local_488 - fVar46 * (*local_580 - *extraout_x11_03),(longlong)param_3);
    local_580 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1] + -1);
    FUN_18000f448((longlong)plVar9,param_3[0xe1] + -1);
    fVar47 = FUN_180026608((longlong)local_578);
    pfVar23 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1] + -1);
    fVar47 = FUN_180011838(*pfVar23 + fVar47 * (extraout_s18_00 + extraout_s18_00),(longlong)param_3
                          );
    local_488 = (float *)FUN_18000f448((longlong)plVar9,param_3[0xe1] + -1);
    local_580 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1] + -1);
    FUN_18000f448((longlong)plVar9,param_3[0xe1] + -1);
    fVar48 = FUN_180026608((longlong)local_578);
    fVar48 = FUN_180011838(*local_488 - fVar48 * (extraout_s18_01 + extraout_s18_01),
                           (longlong)param_3);
    local_580 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1] + -1);
    FUN_18000f448((longlong)plVar9,param_3[0xe1] + -1);
    fVar49 = FUN_180026608((longlong)local_578);
    pfVar23 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1] + -1);
    fVar49 = FUN_180011838(fVar49 * (extraout_s19_00 - extraout_s18_02) * 3.0 + *pfVar23,
                           (longlong)param_3);
    local_580 = (float *)FUN_18000f448((longlong)plVar9,param_3[0xe1] + -1);
    pfVar23 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1] + -1);
    FUN_18000f448((longlong)plVar9,param_3[0xe1] + -1);
    fVar50 = FUN_180026608((longlong)local_578);
    fVar50 = FUN_180011838(*local_580 - fVar50 * (*pfVar23 - *extraout_x11_04) * 3.0,
                           (longlong)param_3);
    if (iVar13 == 0) {
      uVar12 = FUN_180026550((longlong)local_458);
      local_568 = (longlong *)CONCAT44(local_568._4_4_,fVar44);
      local_528 = (longlong *)CONCAT44(local_528._4_4_,fVar45);
      local_570 = (float *)CONCAT44(local_570._4_4_,fVar47);
      local_550 = (longlong *)CONCAT44(local_550._4_4_,fVar49);
      local_560 = (int *)CONCAT44(local_560._4_4_,fVar46);
      local_558 = (longlong *)CONCAT44(local_558._4_4_,fVar48);
      local_578 = (longlong *)CONCAT44(local_578._4_4_,extraout_s0_04);
      lVar37 = ((longlong)local_460 + (longlong)-local_2f8[uVar12] * 60000000) % 86400000000;
      iVar13 = ((int)(lVar37 / 1000000) + (int)(lVar37 >> 0x3f)) -
               (SUB164(SEXT816(lVar37) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
      if (86399999999 < lVar37) {
        iVar13 = 0;
      }
      lVar37 = (longlong)local_520 % 86400000000;
      iVar34 = ((int)(lVar37 / 1000000) + (int)(lVar37 >> 0x3f)) -
               (SUB164(SEXT816(lVar37) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
      if (86399999999 < lVar37) {
        iVar34 = 0;
      }
      if (iVar34 == iVar13) {
        local_568 = (longlong *)CONCAT44(local_568._4_4_,fVar44);
        local_528 = (longlong *)CONCAT44(local_528._4_4_,fVar45);
        local_570 = (float *)CONCAT44(local_570._4_4_,fVar47);
        local_550 = (longlong *)CONCAT44(local_550._4_4_,fVar49);
        local_560 = (int *)CONCAT44(local_560._4_4_,fVar46);
        *local_478 = param_3[0xe1];
        *local_480 = 1;
        local_558 = (longlong *)CONCAT44(local_558._4_4_,fVar48);
        local_578 = (longlong *)CONCAT44(local_578._4_4_,extraout_s0_04);
      }
    }
    else if (iVar13 == 1) {
      uVar12 = FUN_180026550((longlong)local_458);
      lVar37 = ((longlong)local_470 + (longlong)(-0x3c - local_2f8[uVar12]) * 60000000) %
               86400000000;
      iVar13 = ((int)(lVar37 / 1000000) + (int)(lVar37 >> 0x3f)) -
               (SUB164(SEXT816(lVar37) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
      if (86399999999 < lVar37) {
        iVar13 = 0;
      }
      lVar37 = (longlong)local_520 % 86400000000;
      iVar34 = ((int)(lVar37 / 1000000) + (int)(lVar37 >> 0x3f)) -
               (SUB164(SEXT816(lVar37) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
      if (86399999999 < lVar37) {
        iVar34 = 0;
      }
      uVar14 = extraout_s0_03;
      if (iVar34 == iVar13) {
        *local_478 = param_3[0xe1];
        *local_480 = 1;
      }
LAB_180046644:
      local_568 = (longlong *)CONCAT44(local_568._4_4_,fVar44);
      local_528 = (longlong *)CONCAT44(local_528._4_4_,fVar45);
      local_570 = (float *)CONCAT44(local_570._4_4_,fVar47);
      local_550 = (longlong *)CONCAT44(local_550._4_4_,fVar49);
      local_560 = (int *)CONCAT44(local_560._4_4_,fVar46);
      local_558 = (longlong *)CONCAT44(local_558._4_4_,fVar48);
      local_578 = (longlong *)CONCAT44(local_578._4_4_,uVar14);
    }
    else {
      local_568 = (longlong *)CONCAT44(local_568._4_4_,fVar44);
      local_528 = (longlong *)CONCAT44(local_528._4_4_,fVar45);
      local_570 = (float *)CONCAT44(local_570._4_4_,fVar47);
      local_550 = (longlong *)CONCAT44(local_550._4_4_,fVar49);
      local_560 = (int *)CONCAT44(local_560._4_4_,fVar46);
      local_558 = (longlong *)CONCAT44(local_558._4_4_,fVar48);
      local_578 = (longlong *)CONCAT44(local_578._4_4_,fVar50);
      if (iVar13 == 2) {
        FUN_180026550((longlong)local_458);
        local_580 = local_520;
        iVar34 = FUN_180006750((longlong *)&local_580);
        iVar13 = ((int)(extraout_x11_05 / 1000000) + (int)(extraout_x11_05 >> 0x3f)) -
                 (SUB164(SEXT816(extraout_x11_05) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
        if (extraout_x12 <= extraout_x11_05) {
          iVar13 = 0;
        }
        uVar14 = extraout_s0_02;
        if (iVar34 != iVar13) goto LAB_180046644;
        local_568 = (longlong *)CONCAT44(local_568._4_4_,fVar44);
        local_528 = (longlong *)CONCAT44(local_528._4_4_,fVar45);
        local_570 = (float *)CONCAT44(local_570._4_4_,fVar47);
        local_550 = (longlong *)CONCAT44(local_550._4_4_,fVar49);
        local_560 = (int *)CONCAT44(local_560._4_4_,fVar46);
        *local_478 = param_3[0xe1];
        *local_480 = 1;
        local_558 = (longlong *)CONCAT44(local_558._4_4_,fVar48);
        local_578 = (longlong *)CONCAT44(local_578._4_4_,extraout_s0_02);
      }
    }
  }
  else {
    plVar32 = &local_2d0;
    local_2c0 = pfVar27;
    bVar11 = FUN_180093658((longlong *)&local_2c0,&lStack_2c8,plVar32,0);
    if (bVar11) {
      iVar13 = 0;
      goto LAB_180046230;
    }
    plVar32 = &local_2b8;
    local_2b8 = lVar36;
    local_2a8 = pfVar27;
    bVar11 = FUN_180093658((longlong *)&local_2a8,&lStack_2b0,plVar32,0);
    if (bVar11) {
      iVar13 = 1;
      goto LAB_180046230;
    }
    puVar24 = (undefined4 *)FUN_18000f448((longlong)local_548,param_3[0xe1]);
    *puVar24 = 0;
    puVar24 = (undefined4 *)FUN_18000f448((longlong)local_518,param_3[0xe1]);
    *puVar24 = 0;
    puVar24 = (undefined4 *)FUN_18000f448((longlong)local_510,param_3[0xe1]);
    local_568 = (longlong *)((ulonglong)local_568 & 0xffffffff00000000);
    *puVar24 = 0;
    local_528 = (longlong *)((ulonglong)local_528 & 0xffffffff00000000);
    local_570 = (float *)((ulonglong)local_570 & 0xffffffff00000000);
    local_550 = (longlong *)((ulonglong)local_550 & 0xffffffff00000000);
    local_560 = (int *)((ulonglong)local_560 & 0xffffffff00000000);
    local_558 = (longlong *)((ulonglong)local_558 & 0xffffffff00000000);
    local_578 = (longlong *)((ulonglong)local_578 & 0xffffffff00000000);
  }
  bVar11 = FUN_180026690((longlong)local_370);
  if ((bVar11) && (*local_480 != 0)) {
    puVar24 = (undefined4 *)FUN_18000f448((longlong)local_380,param_3[0xe1]);
    *puVar24 = local_568._0_4_;
  }
  plVar25 = local_548;
  lVar38 = (longlong)local_470 % 86400000000;
  lVar37 = ((longlong)local_520 % 86400000000) / 1000000;
  iVar34 = ((int)(lVar38 / 1000000) + (int)(lVar38 >> 0x3f)) -
           (SUB164(SEXT816(lVar38) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
  iVar13 = (int)lVar37;
  if ((longlong)local_500 <= lVar37) {
    iVar13 = 0;
  }
  if (86399999999 < lVar38) {
    iVar34 = 0;
  }
  if (iVar13 == iVar34) {
LAB_1800468d0:
    bVar11 = FUN_180026690((longlong)local_370);
    plVar25 = local_380;
    if ((bVar11) && (*local_480 != 0)) {
      *local_480 = 0;
      iVar13 = *local_478;
      if (iVar13 <= param_3[0xe1]) {
        do {
          puVar24 = (undefined4 *)FUN_18000f448((longlong)plVar25,iVar13);
          iVar13 = iVar13 + 1;
          *puVar24 = 0;
        } while (iVar13 <= param_3[0xe1]);
      }
      param_3[0x433] = *local_478;
    }
    plVar25 = local_410;
    puVar24 = (undefined4 *)FUN_18000f448((longlong)local_410,param_3[0xe1] + -1);
    uVar14 = *puVar24;
    puVar24 = (undefined4 *)FUN_18000f448((longlong)local_548,param_3[0xe1]);
    plVar9 = local_408;
    *puVar24 = uVar14;
    puVar24 = (undefined4 *)FUN_18000f448((longlong)local_408,param_3[0xe1] + -1);
    uVar14 = *puVar24;
    puVar24 = (undefined4 *)FUN_18000f448((longlong)local_518,param_3[0xe1]);
    *puVar24 = uVar14;
    pfVar23 = (float *)FUN_18000f448((longlong)plVar9,param_3[0xe1] + -1);
    pfVar26 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1] + -1);
    fVar44 = FUN_180011838((*pfVar26 + *pfVar23) * 0.5,(longlong)param_3);
    plVar25 = local_510;
  }
  else {
    lVar38 = (longlong)local_468 % 86400000000;
    iVar13 = ((int)(lVar38 / 1000000) + (int)(lVar38 >> 0x3f)) -
             (SUB164(SEXT816(lVar38) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
    if (86399999999 < lVar38) {
      iVar13 = 0;
    }
    lVar38 = 0;
    if (lVar37 < (longlong)local_500) {
      lVar38 = lVar37;
    }
    if ((int)lVar38 == iVar13) goto LAB_1800468d0;
    lVar37 = (longlong)local_460 % 86400000000;
    iVar13 = ((int)(lVar37 / 1000000) + (int)(lVar37 >> 0x3f)) -
             (SUB164(SEXT816(lVar37) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
    if (86399999999 < lVar37) {
      iVar13 = 0;
    }
    lVar37 = (longlong)local_520 % 86400000000;
    iVar34 = ((int)(lVar37 / 1000000) + (int)(lVar37 >> 0x3f)) -
             (SUB164(SEXT816(lVar37) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
    if (86399999999 < lVar37) {
      iVar34 = 0;
    }
    if (iVar34 == iVar13) goto LAB_1800468d0;
    puVar24 = (undefined4 *)FUN_18000f448((longlong)local_548,param_3[0xe1] + -1);
    uVar14 = *puVar24;
    puVar24 = (undefined4 *)FUN_18000f448((longlong)plVar25,param_3[0xe1]);
    plVar25 = local_518;
    *puVar24 = uVar14;
    puVar24 = (undefined4 *)FUN_18000f448((longlong)local_518,param_3[0xe1] + -1);
    uVar14 = *puVar24;
    puVar24 = (undefined4 *)FUN_18000f448((longlong)plVar25,param_3[0xe1]);
    plVar25 = local_510;
    *puVar24 = uVar14;
    pfVar23 = (float *)FUN_18000f448((longlong)local_510,param_3[0xe1] + -1);
    fVar44 = *pfVar23;
  }
  pfVar23 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1]);
  plVar25 = local_548;
  *pfVar23 = fVar44;
  pfVar23 = (float *)FUN_18000f448((longlong)local_548,param_3[0xe1]);
  plVar9 = local_518;
  pfVar26 = (float *)FUN_18000f448((longlong)local_518,param_3[0xe1]);
  fVar45 = *pfVar23 - *pfVar26;
  FUN_18000f448((longlong)plVar25,param_3[0xe1]);
  plVar10 = local_388;
  fVar44 = FUN_180026608((longlong)local_388);
  fVar46 = *extraout_x11_06;
  local_580 = local_4f8;
  pfVar23 = (float *)FUN_18000f448((longlong)local_4f8,param_3[0xe1]);
  *pfVar23 = fVar44 * fVar45 + fVar46;
  FUN_18000f448((longlong)plVar9,param_3[0xe1]);
  fVar44 = FUN_180026608((longlong)plVar10);
  fVar46 = *extraout_x11_07;
  local_488 = (float *)local_4e0;
  pfVar23 = (float *)FUN_18000f448((longlong)local_4e0,param_3[0xe1]);
  *pfVar23 = fVar46 - fVar44 * fVar45;
  FUN_18000f448((longlong)plVar25,param_3[0xe1]);
  fVar44 = FUN_180026608((longlong)plVar10);
  fVar46 = *extraout_x11_08;
  local_378 = local_4f0;
  pfVar23 = (float *)FUN_18000f448((longlong)local_4f0,param_3[0xe1]);
  *pfVar23 = fVar44 * (fVar45 + fVar45) + fVar46;
  FUN_18000f448((longlong)plVar9,param_3[0xe1]);
  fVar44 = FUN_180026608((longlong)plVar10);
  fVar46 = *extraout_x11_09;
  local_508 = local_4d8;
  pfVar23 = (float *)FUN_18000f448((longlong)local_4d8,param_3[0xe1]);
  *pfVar23 = fVar46 - fVar44 * (fVar45 + fVar45);
  FUN_18000f448((longlong)plVar25,param_3[0xe1]);
  fVar44 = FUN_180026608((longlong)plVar10);
  fVar46 = *extraout_x11_10;
  local_3f0 = local_4e8;
  pfVar23 = (float *)FUN_18000f448((longlong)local_4e8,param_3[0xe1]);
  *pfVar23 = fVar44 * fVar45 * 3.0 + fVar46;
  FUN_18000f448((longlong)plVar9,param_3[0xe1]);
  fVar44 = FUN_180026608((longlong)plVar10);
  fVar46 = *extraout_x11_11;
  local_440 = local_4d0;
  pfVar23 = (float *)FUN_18000f448((longlong)local_4d0,param_3[0xe1]);
  *pfVar23 = fVar46 - fVar44 * fVar45 * 3.0;
  FUN_18000f448((longlong)plVar25,param_3[0xe1]);
  fVar44 = FUN_180026608((longlong)plVar10);
  fVar46 = *extraout_x11_12;
  local_448 = local_4c0;
  pfVar23 = (float *)FUN_18000f448((longlong)local_4c0,param_3[0xe1]);
  *pfVar23 = fVar44 * fVar45 * 4.0 + fVar46;
  FUN_18000f448((longlong)plVar9,param_3[0xe1]);
  fVar44 = FUN_180026608((longlong)plVar10);
  fVar46 = *extraout_x11_13;
  local_3d8 = local_4b8;
  pfVar23 = (float *)FUN_18000f448((longlong)local_4b8,param_3[0xe1]);
  *pfVar23 = fVar46 - fVar44 * fVar45 * 4.0;
  FUN_18000f448((longlong)plVar25,param_3[0xe1]);
  fVar44 = FUN_180026608((longlong)plVar10);
  fVar46 = *extraout_x11_14;
  local_3e0 = local_4b0;
  pfVar23 = (float *)FUN_18000f448((longlong)local_4b0,param_3[0xe1]);
  *pfVar23 = fVar44 * fVar45 * 5.0 + fVar46;
  FUN_18000f448((longlong)plVar9,param_3[0xe1]);
  fVar44 = FUN_180026608((longlong)plVar10);
  fVar46 = *extraout_x11_15;
  local_3e8 = local_4a8;
  pfVar23 = (float *)FUN_18000f448((longlong)local_4a8,param_3[0xe1]);
  *pfVar23 = fVar46 - fVar44 * fVar45 * 5.0;
  FUN_18000f448((longlong)plVar25,param_3[0xe1]);
  fVar44 = FUN_180026608((longlong)plVar10);
  fVar46 = *extraout_x11_16;
  local_490 = (float *)local_4a0;
  pfVar23 = (float *)FUN_18000f448((longlong)local_4a0,param_3[0xe1]);
  *pfVar23 = fVar44 * fVar45 * 6.0 + fVar46;
  FUN_18000f448((longlong)plVar9,param_3[0xe1]);
  fVar44 = FUN_180026608((longlong)plVar10);
  plVar10 = local_498;
  fVar46 = *extraout_x11_17;
  pfVar23 = (float *)FUN_18000f448((longlong)local_498,param_3[0xe1]);
  *pfVar23 = fVar46 - fVar44 * fVar45 * 6.0;
  bVar11 = FUN_180026690((longlong)local_358);
  if (bVar11) {
    fVar52 = (float)param_3[0x1a6];
    pfVar23 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1]);
    fVar52 = FUN_18000de78(*pfVar23,fVar52);
    pfVar23 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1]);
    *pfVar23 = fVar52;
    fVar52 = (float)param_3[0x1a6];
    pfVar23 = (float *)FUN_18000f448((longlong)plVar9,param_3[0xe1]);
    fVar52 = FUN_18000de78(*pfVar23,fVar52);
    pfVar23 = (float *)FUN_18000f448((longlong)plVar9,param_3[0xe1]);
    plVar25 = local_510;
    *pfVar23 = fVar52;
    fVar52 = (float)param_3[0x1a6];
    pfVar23 = (float *)FUN_18000f448((longlong)local_510,param_3[0xe1]);
    fVar52 = FUN_18000de78(*pfVar23,fVar52);
    pfVar26 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1]);
    pfVar23 = local_580;
    *pfVar26 = fVar52;
    fVar52 = (float)param_3[0x1a6];
    pfVar26 = (float *)FUN_18000f448((longlong)local_580,param_3[0xe1]);
    fVar52 = FUN_18000de78(*pfVar26,fVar52);
    pfVar26 = (float *)FUN_18000f448((longlong)pfVar23,param_3[0xe1]);
    pfVar23 = local_488;
    *pfVar26 = fVar52;
    fVar52 = (float)param_3[0x1a6];
    pfVar26 = (float *)FUN_18000f448((longlong)local_488,param_3[0xe1]);
    fVar52 = FUN_18000de78(*pfVar26,fVar52);
    pfVar23 = (float *)FUN_18000f448((longlong)pfVar23,param_3[0xe1]);
    plVar25 = local_378;
    *pfVar23 = fVar52;
    fVar52 = (float)param_3[0x1a6];
    pfVar23 = (float *)FUN_18000f448((longlong)local_378,param_3[0xe1]);
    fVar52 = FUN_18000de78(*pfVar23,fVar52);
    pfVar26 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1]);
    pfVar23 = local_508;
    *pfVar26 = fVar52;
    fVar52 = (float)param_3[0x1a6];
    pfVar26 = (float *)FUN_18000f448((longlong)local_508,param_3[0xe1]);
    fVar52 = FUN_18000de78(*pfVar26,fVar52);
    pfVar23 = (float *)FUN_18000f448((longlong)pfVar23,param_3[0xe1]);
    plVar25 = local_3f0;
    *pfVar23 = fVar52;
    fVar52 = (float)param_3[0x1a6];
    pfVar23 = (float *)FUN_18000f448((longlong)local_3f0,param_3[0xe1]);
    fVar52 = FUN_18000de78(*pfVar23,fVar52);
    pfVar23 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1]);
    plVar25 = local_440;
    *pfVar23 = fVar52;
    fVar52 = (float)param_3[0x1a6];
    pfVar23 = (float *)FUN_18000f448((longlong)local_440,param_3[0xe1]);
    fVar52 = FUN_18000de78(*pfVar23,fVar52);
    pfVar23 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1]);
    plVar25 = local_448;
    *pfVar23 = fVar52;
    fVar52 = (float)param_3[0x1a6];
    pfVar23 = (float *)FUN_18000f448((longlong)local_448,param_3[0xe1]);
    fVar52 = FUN_18000de78(*pfVar23,fVar52);
    pfVar23 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1]);
    plVar25 = local_3d8;
    *pfVar23 = fVar52;
    fVar52 = (float)param_3[0x1a6];
    pfVar23 = (float *)FUN_18000f448((longlong)local_3d8,param_3[0xe1]);
    fVar52 = FUN_18000de78(*pfVar23,fVar52);
    pfVar23 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1]);
    plVar25 = local_3e0;
    *pfVar23 = fVar52;
    fVar52 = (float)param_3[0x1a6];
    pfVar23 = (float *)FUN_18000f448((longlong)local_3e0,param_3[0xe1]);
    fVar52 = FUN_18000de78(*pfVar23,fVar52);
    pfVar23 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1]);
    plVar25 = local_3e8;
    *pfVar23 = fVar52;
    fVar52 = (float)param_3[0x1a6];
    pfVar23 = (float *)FUN_18000f448((longlong)local_3e8,param_3[0xe1]);
    fVar52 = FUN_18000de78(*pfVar23,fVar52);
    pfVar23 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1]);
    *pfVar23 = fVar52;
    fVar52 = (float)param_3[0x1a6];
    pfVar23 = (float *)FUN_18000f448((longlong)local_490,param_3[0xe1]);
    fVar52 = FUN_18000de78(*pfVar23,fVar52);
    pfVar23 = (float *)FUN_18000f448((longlong)local_490,param_3[0xe1]);
    *pfVar23 = fVar52;
    fVar52 = (float)param_3[0x1a6];
    pfVar23 = (float *)FUN_18000f448((longlong)plVar10,param_3[0xe1]);
    uVar53 = 0;
    uVar54 = 0;
    fVar44 = FUN_18000de78(*pfVar23,fVar52);
    pfVar23 = (float *)FUN_18000f448((longlong)plVar10,param_3[0xe1]);
    *pfVar23 = fVar44;
  }
  uVar12 = FUN_180026550((longlong)local_4c8);
  plVar25 = local_548;
  if (uVar12 == 0) goto LAB_180047194;
  iVar34 = param_3[0xe1];
  lVar37 = (longlong)local_520 % 86400000000;
  iVar13 = ((int)(lVar37 / 1000000) + (int)(lVar37 >> 0x3f)) -
           (SUB164(SEXT816(lVar37) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
  if (86399999999 < lVar37) {
    iVar13 = 0;
  }
  lVar37 = (longlong)local_470 % 86400000000;
  iVar35 = ((int)(lVar37 / 1000000) + (int)(lVar37 >> 0x3f)) -
           (SUB164(SEXT816(lVar37) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
  if (86399999999 < lVar37) {
    iVar35 = 0;
  }
  uVar14 = extraout_s0_05;
  uVar51 = extraout_var_02;
  uVar21 = extraout_var_09;
  if (iVar13 == iVar35) {
LAB_1800470e8:
    if (*local_398 != iVar34) {
      local_3b8 = local_3b8 & 0xffffffff00000000;
      puVar24 = (undefined4 *)FUN_18000f448((longlong)local_548,iVar34);
      plVar32 = local_518;
      local_3d0 = (char *)CONCAT44(local_3d0._4_4_,*puVar24);
      puVar24 = (undefined4 *)FUN_18000f448((longlong)local_518,iVar34);
      local_3d0 = (char *)CONCAT44(*puVar24,(undefined4)local_3d0);
      puVar24 = (undefined4 *)FUN_18000f448((longlong)plVar25,iVar34);
      uStack_3c8 = CONCAT44(uStack_3c8._4_4_,*puVar24);
      puVar24 = (undefined4 *)FUN_18000f448((longlong)plVar32,iVar34);
      uVar12 = param_3[0xe1];
      uStack_3c8 = CONCAT44(*puVar24,(undefined4)uStack_3c8);
      local_3c0 = CONCAT44(uVar12 + 1,iVar34);
      uVar14 = extraout_s0_06;
      uVar51 = extraout_var_03;
      uVar21 = extraout_var_10;
      if (uVar12 != 0) {
        auVar2._4_4_ = extraout_var_03;
        auVar2._0_4_ = extraout_s0_06;
        auVar2._8_8_ = extraout_var_10;
        auVar6._4_4_ = uVar53;
        auVar6._0_4_ = fVar52;
        auVar6._8_8_ = uVar54;
        FUN_1800050e0(auVar2,auVar6,puVar43,(longlong)param_3,(ulonglong)uVar12,&local_3d0,pfVar39,
                      uVar31,lVar36,pfVar27);
        uVar14 = extraout_s0_07;
        uVar51 = extraout_var_04;
        uVar21 = extraout_var_11;
      }
    }
  }
  else {
    lVar37 = (longlong)local_520 % 86400000000;
    iVar13 = ((int)(lVar37 / 1000000) + (int)(lVar37 >> 0x3f)) -
             (SUB164(SEXT816(lVar37) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
    if (86399999999 < lVar37) {
      iVar13 = 0;
    }
    lVar37 = (longlong)local_468 % 86400000000;
    iVar35 = ((int)(lVar37 / 1000000) + (int)(lVar37 >> 0x3f)) -
             (SUB164(SEXT816(lVar37) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
    if (86399999999 < lVar37) {
      iVar35 = 0;
    }
    if (iVar13 == iVar35) goto LAB_1800470e8;
    lVar37 = (longlong)local_460 % 86400000000;
    iVar13 = ((int)(lVar37 / 1000000) + (int)(lVar37 >> 0x3f)) -
             (SUB164(SEXT816(lVar37) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
    if (86399999999 < lVar37) {
      iVar13 = 0;
    }
    lVar37 = (longlong)local_520 % 86400000000;
    iVar35 = ((int)(lVar37 / 1000000) + (int)(lVar37 >> 0x3f)) -
             (SUB164(SEXT816(lVar37) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
    if (86399999999 < lVar37) {
      iVar35 = 0;
    }
    if (iVar35 == iVar13) goto LAB_1800470e8;
  }
  plVar32 = (longlong *)(ulonglong)(uint)param_3[0xe1];
  if (*(longlong *)(puVar43 + 8) != 0) {
    local_d0 = 0;
    local_c8 = 0;
    auVar3._4_4_ = uVar51;
    auVar3._0_4_ = uVar14;
    auVar3._8_8_ = uVar21;
    auVar7._4_4_ = uVar53;
    auVar7._0_4_ = fVar52;
    auVar7._8_8_ = uVar54;
    FUN_180023e50(auVar3,auVar7,*(int **)(puVar43 + 8),(longlong)param_3,(ulonglong)plVar32,
                  (longlong)&local_d0,pfVar39,uVar31,lVar36,pfVar27);
  }
LAB_180047194:
  bVar11 = FUN_180026690((longlong)local_340);
  plVar25 = local_548;
  if (bVar11) {
    pfVar23 = (float *)FUN_18000f448((longlong)local_548,param_3[0xe1]);
    pfVar26 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1] + -1);
    if (*pfVar23 != *pfVar26) {
      plVar32 = (longlong *)(ulonglong)(uint)param_3[0xe1];
      iVar13 = FUN_180006790(param_3,param_3[0xe1]);
      piVar17 = local_400;
      if (iVar13 == 2) {
        iVar13 = 0;
        local_580 = pfVar40;
        do {
          iVar34 = *piVar17;
          if (iVar34 < (int)plVar32) {
            do {
              plVar32 = FUN_18000f880((longlong *)(param_3 + 0x140),iVar13);
              puVar24 = (undefined4 *)FUN_18000f448((longlong)plVar32,iVar34);
              iVar34 = iVar34 + 1;
              *puVar24 = 0;
              plVar32 = (longlong *)(ulonglong)(uint)param_3[0xe1];
            } while (iVar34 < param_3[0xe1]);
          }
          iVar13 = iVar13 + 1;
        } while (iVar13 < 0x3c);
        param_3[0x433] = *local_400;
        pfVar40 = local_580;
      }
    }
  }
  plVar25 = local_548;
  pfVar23 = (float *)FUN_18000f448((longlong)local_548,param_3[0xe1]);
  pfVar26 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1] + -1);
  if ((*pfVar23 != *pfVar26) && (iVar13 = FUN_180006790(param_3,param_3[0xe1]), iVar13 == 2)) {
    *local_400 = extraout_w1;
  }
  bVar11 = FUN_180026690((longlong)local_3f8);
  plVar25 = local_548;
  if (bVar11) {
    pfVar23 = (float *)FUN_18000f448((longlong)local_548,param_3[0xe1]);
    pfVar26 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1] + -1);
    if (*pfVar23 != *pfVar26) {
      FUN_180022590((int *)pfVar40,(longlong)param_3,plVar32);
      local_580 = (float *)local_308;
      local_308[0] = 0;
      local_308[1] = 0;
      local_308[0] = FUN_180096150(0x120);
      *(longlong *)local_308[0] = local_308[0];
      *(longlong *)(local_308[0] + 8) = local_308[0];
      pfVar27 = (float *)FUN_1800043e0(0.0,&lStack_218);
      uVar12 = FUN_180026888((longlong)local_338);
      uVar31 = (ulonglong)uVar12;
      uVar16 = FUN_180026888((longlong)local_330);
      uVar12 = param_3[0xe1];
      pfVar39 = (float *)(ulonglong)uVar16;
      pfVar23 = (float *)FUN_18000f448((longlong)local_518,uVar12);
      fVar52 = *pfVar23;
      pfVar23 = (float *)FUN_18000f448((longlong)plVar25,param_3[0xe1]);
      uVar53 = 0;
      uVar54 = 0;
      lVar36 = 1;
      FUN_180023270(*pfVar23,ZEXT416((uint)fVar52),(uint *)pfVar40,(longlong)param_3,
                    (ulonglong)(uint)param_3[0xe1],(ulonglong)uVar12,pfVar39,uVar31,1,
                    (undefined8 *)pfVar27,0,'\0',0,0,0,
                    CONCAT44(in_stack_fffffffffffffa1c,in_stack_fffffffffffffa18) &
                    0xffffffffffffff00,local_308);
    }
  }
  bVar11 = FUN_180026690((longlong)local_3f8);
  if (bVar11) {
    local_c0 = 0;
    local_b8 = 0;
    auVar4._4_4_ = extraout_var_05;
    auVar4._0_4_ = extraout_s0_08;
    auVar4._8_8_ = extraout_var_12;
    auVar8._4_4_ = uVar53;
    auVar8._0_4_ = fVar52;
    auVar8._8_8_ = uVar54;
    FUN_180023e50(auVar4,auVar8,(int *)pfVar40,(longlong)param_3,(ulonglong)(uint)param_3[0xe1],
                  (longlong)&local_c0,pfVar39,uVar31,lVar36,pfVar27);
  }
  uVar12 = FUN_180026550((longlong)local_450);
  if (uVar12 != 0) {
    uVar12 = FUN_180026550(extraout_x11_18);
    if ((uVar12 == 1) || (uVar12 = FUN_180026550(extraout_x11_19), uVar12 == 2)) {
      iVar13 = param_3[0x1bf];
      pfVar40 = (float *)FUN_18000f448((longlong)local_4d0,param_3[0xe1]);
      fVar48 = *pfVar40;
      pfVar40 = (float *)FUN_18000f448((longlong)local_4d8,param_3[0xe1]);
      fVar47 = *pfVar40;
      pfVar40 = (float *)FUN_18000f448((longlong)local_4e0,param_3[0xe1]);
      fVar46 = *pfVar40;
      pfVar40 = (float *)FUN_18000f448((longlong)local_4e8,param_3[0xe1]);
      fVar45 = *pfVar40;
      pfVar40 = (float *)FUN_18000f448((longlong)local_4f0,param_3[0xe1]);
      fVar44 = *pfVar40;
      puVar43 = (uint *)FUN_18000f448((longlong)local_4f8,param_3[0xe1]);
      uVar12 = *puVar43;
      pfVar40 = (float *)FUN_18000f448((longlong)local_510,param_3[0xe1]);
      fVar52 = *pfVar40;
      uVar31 = FUN_180026708((longlong)local_328);
      uVar22 = FUN_180026708((longlong)local_320);
      uVar28 = FUN_180026708((longlong)local_390);
      uVar29 = FUN_180026708((longlong)local_3a0);
      local_580 = afStack_1e0;
      pcVar30 = (char *)FUN_180004498((undefined8 *)afStack_1e0,"Current Pivot Levels");
      FUN_18000dec8(fVar52,ZEXT416(uVar12),fVar44,fVar45,fVar46,fVar47,fVar48,(longlong)param_3,
                    0x845fed,pcVar30,(int)uVar29,(int)uVar28,(uint)uVar22,(uint)uVar31,iVar13,
                    (undefined8 *)0x0);
    }
    uVar12 = FUN_180026550((longlong)local_450);
    if ((uVar12 == 1) || (uVar12 = FUN_180026550(extraout_x11_20), uVar12 == 3)) {
      fVar47 = (float)param_3[0x1a6];
      fVar52 = FUN_18000de78(local_568._0_4_,fVar47);
      local_568 = (longlong *)CONCAT44(local_568._4_4_,fVar52);
      fVar44 = FUN_18000de78(local_528._0_4_,fVar47);
      local_528 = (longlong *)CONCAT44(local_528._4_4_,fVar44);
      fVar45 = FUN_18000de78(local_570._0_4_,fVar47);
      local_570 = (float *)CONCAT44(local_570._4_4_,fVar45);
      fVar46 = FUN_18000de78(local_560._0_4_,fVar47);
      local_560 = (int *)CONCAT44(local_560._4_4_,fVar46);
      fVar47 = FUN_18000de78(local_558._0_4_,fVar47);
      iVar13 = param_3[0x1bf];
      local_558 = (longlong *)CONCAT44(local_558._4_4_,fVar47);
      uVar31 = FUN_180026708((longlong)local_318);
      uVar22 = FUN_180026708((longlong)local_310);
      uVar28 = FUN_180026708((longlong)local_390);
      uVar29 = FUN_180026708((longlong)local_3a0);
      local_580 = afStack_1c8;
      pcVar30 = (char *)FUN_180004498((undefined8 *)afStack_1c8,"Developing Pivot Levels");
      FUN_18000dec8(fVar52,ZEXT416((uint)fVar44),fVar45,local_550._0_4_,fVar46,fVar47,
                    local_578._0_4_,(longlong)param_3,0xe85c9,pcVar30,(int)uVar29,(int)uVar28,
                    (uint)uVar22,(uint)uVar31,iVar13,(undefined8 *)0x0);
    }
  }
  *pfVar42 = (float)param_3[0x1bf];
  pfVar42[1] = 1.0;
  pfVar42[2] = (float)param_3[0x1a6];
  pfVar40 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2ac),param_3[0xe1]);
  pfVar42[3] = *pfVar40;
  pfVar40 = (float *)FUN_18000f448((longlong)local_510,param_3[0xe1]);
  pfVar42[4] = *pfVar40;
  pfVar40 = (float *)FUN_18000f448((longlong)local_4f8,param_3[0xe1]);
  pfVar42[5] = *pfVar40;
  pfVar40 = (float *)FUN_18000f448((longlong)local_4f0,param_3[0xe1]);
  pfVar42[6] = *pfVar40;
  pfVar40 = (float *)FUN_18000f448((longlong)local_4e8,param_3[0xe1]);
  pfVar42[7] = *pfVar40;
  pfVar40 = (float *)FUN_18000f448((longlong)local_4e0,param_3[0xe1]);
  pfVar42[8] = *pfVar40;
  pfVar40 = (float *)FUN_18000f448((longlong)local_4d8,param_3[0xe1]);
  pfVar42[9] = *pfVar40;
  pfVar40 = (float *)FUN_18000f448((longlong)local_4d0,param_3[0xe1]);
  pfVar42[10] = *pfVar40;
  pfVar42[0xb] = local_568._0_4_;
  pfVar42[0xc] = local_528._0_4_;
  pfVar42[0xd] = local_570._0_4_;
  pfVar42[0xe] = local_550._0_4_;
  pfVar42[0xf] = local_560._0_4_;
  pfVar42[0x10] = local_558._0_4_;
  pfVar42[0x11] = local_578._0_4_;
  *local_398 = param_3[0xe1];
  FUN_180004590(&local_190);
  return;
}

