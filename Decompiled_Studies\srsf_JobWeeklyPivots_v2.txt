
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_JobWeeklyPivots_v2
               (undefined1 param_1 [16],undefined1 param_2 [16],int *param_3,undefined8 param_4,
               undefined8 param_5,undefined8 param_6,undefined8 param_7,undefined8 param_8,
               undefined8 param_9,undefined8 param_10)

{
  undefined1 auVar1 [16];
  undefined1 auVar2 [16];
  undefined1 *lpMem;
  longlong *plVar3;
  longlong *plVar4;
  longlong *plVar5;
  longlong *plVar6;
  longlong *plVar7;
  longlong *plVar8;
  bool bVar9;
  uint uVar10;
  uint uVar11;
  int *piVar12;
  undefined8 *puVar13;
  HANDLE pvVar14;
  char *pcVar15;
  undefined8 uVar16;
  longlong *plVar17;
  float *pfVar18;
  ulonglong uVar19;
  undefined4 *puVar20;
  float *pfVar21;
  uint *puVar22;
  ulonglong uVar23;
  ulonglong uVar24;
  ulonglong uVar25;
  char *pcVar26;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  undefined8 extraout_x1_10;
  undefined8 extraout_x1_11;
  undefined8 extraout_x1_12;
  undefined8 extraout_x1_13;
  undefined8 extraout_x1_14;
  undefined8 extraout_x1_15;
  undefined8 extraout_x1_16;
  undefined8 extraout_x1_17;
  undefined8 extraout_x1_18;
  undefined8 extraout_x1_19;
  undefined8 extraout_x1_20;
  undefined8 extraout_x1_21;
  undefined8 extraout_x1_22;
  undefined8 extraout_x1_23;
  undefined8 extraout_x1_24;
  undefined8 extraout_x1_25;
  undefined8 extraout_x1_26;
  undefined8 extraout_x1_27;
  undefined8 extraout_x1_28;
  char cVar27;
  int iVar28;
  int iVar29;
  int iVar30;
  longlong lVar31;
  longlong lVar32;
  longlong lVar33;
  int iVar34;
  int extraout_w11;
  int extraout_w11_00;
  int extraout_w11_01;
  longlong extraout_x11;
  longlong extraout_x11_00;
  longlong extraout_x11_01;
  int extraout_w12;
  longlong extraout_x12;
  longlong *plVar35;
  int *piVar36;
  uint uVar37;
  char *pcVar38;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  undefined4 extraout_s0_02;
  undefined4 extraout_s0_03;
  undefined4 extraout_s0_04;
  undefined4 extraout_s0_05;
  undefined4 extraout_s0_06;
  undefined4 extraout_s0_07;
  undefined4 extraout_s0_08;
  undefined4 extraout_s0_09;
  undefined4 extraout_s0_10;
  undefined4 extraout_s0_11;
  undefined4 extraout_s0_12;
  undefined4 extraout_s0_13;
  undefined4 extraout_s0_14;
  undefined4 extraout_s0_15;
  undefined4 extraout_s0_16;
  undefined4 extraout_s0_17;
  undefined4 extraout_s0_18;
  undefined4 extraout_s0_19;
  undefined4 extraout_s0_20;
  undefined4 extraout_s0_21;
  undefined4 extraout_s0_22;
  undefined4 extraout_s0_23;
  undefined4 extraout_s0_24;
  undefined4 extraout_s0_25;
  undefined4 extraout_s0_26;
  undefined4 extraout_s0_27;
  undefined4 extraout_s0_28;
  undefined4 extraout_s0_29;
  undefined4 extraout_var;
  undefined4 extraout_var_00;
  undefined4 extraout_var_01;
  undefined4 extraout_var_02;
  undefined4 extraout_var_03;
  undefined4 extraout_var_04;
  undefined4 extraout_var_05;
  undefined4 extraout_var_06;
  undefined4 extraout_var_07;
  undefined4 extraout_var_08;
  undefined4 extraout_var_09;
  undefined4 extraout_var_10;
  undefined4 extraout_var_11;
  undefined4 extraout_var_12;
  undefined4 extraout_var_13;
  undefined4 extraout_var_14;
  undefined4 extraout_var_15;
  undefined4 extraout_var_16;
  undefined4 extraout_var_17;
  undefined4 extraout_var_18;
  undefined4 extraout_var_19;
  undefined4 extraout_var_20;
  undefined4 extraout_var_21;
  undefined4 extraout_var_22;
  undefined4 extraout_var_23;
  undefined4 extraout_var_24;
  undefined4 extraout_var_25;
  undefined4 extraout_var_26;
  undefined4 extraout_var_27;
  undefined4 extraout_var_28;
  undefined4 extraout_var_29;
  undefined4 uVar39;
  undefined8 extraout_var_30;
  undefined8 extraout_var_31;
  undefined8 extraout_var_32;
  undefined8 extraout_var_33;
  undefined8 extraout_var_34;
  undefined8 extraout_var_35;
  undefined8 extraout_var_36;
  undefined8 extraout_var_37;
  undefined8 extraout_var_38;
  undefined8 extraout_var_39;
  undefined8 extraout_var_40;
  undefined8 extraout_var_41;
  undefined8 extraout_var_42;
  undefined8 extraout_var_43;
  undefined8 extraout_var_44;
  undefined8 extraout_var_45;
  undefined8 extraout_var_46;
  undefined8 extraout_var_47;
  undefined8 extraout_var_48;
  undefined8 extraout_var_49;
  undefined8 extraout_var_50;
  undefined8 extraout_var_51;
  undefined8 extraout_var_52;
  undefined8 extraout_var_53;
  undefined8 extraout_var_54;
  undefined8 extraout_var_55;
  undefined8 extraout_var_56;
  undefined8 extraout_var_57;
  undefined8 extraout_var_58;
  undefined8 extraout_var_59;
  undefined8 extraout_var_60;
  undefined8 uVar40;
  undefined4 uVar41;
  undefined4 uVar42;
  undefined4 uVar43;
  float fVar44;
  float fVar45;
  float fVar46;
  float fVar47;
  float fVar48;
  float fVar49;
  undefined1 auVar50 [16];
  longlong *local_328;
  longlong *local_320;
  float local_318;
  longlong *local_310;
  longlong *local_308;
  longlong *local_300;
  longlong *local_2f8;
  longlong *local_2f0;
  longlong *local_2e8;
  longlong *local_2e0;
  longlong *local_2d8;
  longlong *local_2d0;
  longlong *local_2c8;
  longlong *local_2c0;
  longlong *local_2b8;
  longlong *local_2b0;
  longlong *local_2a8;
  int *local_2a0;
  int *local_298;
  int *local_290;
  float *local_288;
  longlong *local_280;
  uint *local_278;
  longlong *local_270;
  longlong *local_268;
  longlong *local_260;
  longlong *local_258;
  longlong *local_250;
  int *local_248;
  longlong *local_240;
  int *local_238;
  longlong *local_230;
  longlong *local_228;
  int *local_220;
  longlong *local_218;
  longlong *local_210;
  longlong *local_208;
  longlong *local_200;
  longlong *local_1f8;
  int *local_1f0;
  undefined1 local_1e8;
  undefined7 uStack_1e7;
  undefined8 local_1d8;
  ulonglong local_1d0;
  undefined8 local_1c8;
  char *local_1c0;
  undefined8 uStack_1b8;
  undefined1 *local_1b0;
  longlong *local_1a0;
  undefined **local_198;
  undefined1 *local_190;
  undefined8 uStack_188;
  undefined1 *local_180;
  int *local_170 [2];
  undefined1 *local_160;
  undefined8 uStack_158;
  undefined1 *local_150;
  longlong local_140;
  undefined8 uStack_138;
  longlong *local_130;
  undefined8 uStack_128;
  undefined8 local_120;
  undefined8 uStack_118;
  longlong local_110;
  undefined8 uStack_108;
  undefined8 local_100;
  undefined8 uStack_f8;
  undefined8 uStack_f0;
  undefined8 uStack_e8;
  undefined8 local_e0;
  undefined8 uStack_d8;
  undefined8 uStack_d0;
  undefined8 uStack_c8;
  undefined8 local_c0;
  
  uVar42 = param_2._4_4_;
                    /* 0x47918  11  scsf_JobWeeklyPivots_v2 */
  uVar41 = param_2._0_4_;
  pcVar26 = "";
  local_1c8 = 0xfffffffffffffffe;
  local_150 = &DAT_1800d4ecd;
  local_160 = &DAT_1800d4ecd;
  uStack_158 = 0;
  local_238 = param_3;
  piVar12 = (int *)(**(code **)(param_3 + 0x62e))(param_1._0_4_,0);
  local_278 = (uint *)(**(code **)(param_3 + 0x62e))(1);
  puVar13 = (undefined8 *)(**(code **)(param_3 + 0x446))(2);
  local_248 = (int *)*puVar13;
  puVar13 = (undefined8 *)(**(code **)(param_3 + 0x446))(3);
  lVar32 = *(longlong *)(param_3 + 0x140);
  local_288 = (float *)*puVar13;
  uVar16 = extraout_x1;
  uVar43 = extraout_s0;
  uVar39 = extraout_var;
  uVar40 = extraout_var_30;
  if (lVar32 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar16 = extraout_x1_00;
      uVar43 = extraout_s0_00;
      uVar39 = extraout_var_00;
      uVar40 = extraout_var_31;
    }
    lVar32 = *(longlong *)(param_3 + 0x140);
    if (lVar32 != 0) goto LAB_180047a08;
    local_240 = (longlong *)(param_3 + 0x14a);
LAB_180047a44:
    local_328 = local_240;
    local_310 = local_240;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar16 = extraout_x1_01;
      uVar43 = extraout_s0_01;
      uVar39 = extraout_var_01;
      uVar40 = extraout_var_32;
    }
    lVar32 = *(longlong *)(param_3 + 0x140);
    if (lVar32 != 0) goto LAB_180047a74;
    local_308 = (longlong *)(param_3 + 0x14a);
LAB_180047aa8:
    local_320 = local_308;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar16 = extraout_x1_02;
      uVar43 = extraout_s0_02;
      uVar39 = extraout_var_02;
      uVar40 = extraout_var_33;
    }
    lVar32 = *(longlong *)(param_3 + 0x140);
    if (lVar32 != 0) goto LAB_180047ae0;
    local_2b8 = (longlong *)(param_3 + 0x14a);
LAB_180047b20:
    local_318 = 4.2039e-45;
    local_300 = local_2b8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar16 = extraout_x1_03;
      uVar43 = extraout_s0_03;
      uVar39 = extraout_var_03;
      uVar40 = extraout_var_34;
    }
    lVar32 = *(longlong *)(param_3 + 0x140);
    if (lVar32 != 0) goto LAB_180047b4c;
    local_2c8 = (longlong *)(param_3 + 0x14a);
LAB_180047b80:
    local_2d0 = local_2c8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar16 = extraout_x1_04;
      uVar43 = extraout_s0_04;
      uVar39 = extraout_var_04;
      uVar40 = extraout_var_35;
    }
    lVar32 = *(longlong *)(param_3 + 0x140);
    if (lVar32 != 0) goto LAB_180047bb0;
    local_2d8 = (longlong *)(param_3 + 0x14a);
LAB_180047be8:
    local_2f8 = local_2d8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar16 = extraout_x1_05;
      uVar43 = extraout_s0_05;
      uVar39 = extraout_var_05;
      uVar40 = extraout_var_36;
    }
    lVar32 = *(longlong *)(param_3 + 0x140);
    if (lVar32 != 0) goto LAB_180047c18;
    local_2c0 = (longlong *)(param_3 + 0x14a);
LAB_180047c50:
    local_2e0 = local_2c0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar16 = extraout_x1_06;
      uVar43 = extraout_s0_06;
      uVar39 = extraout_var_06;
      uVar40 = extraout_var_37;
    }
    lVar32 = *(longlong *)(param_3 + 0x140);
    if (lVar32 != 0) goto LAB_180047c7c;
    local_2e8 = (longlong *)(param_3 + 0x14a);
LAB_180047cb0:
    local_2f0 = local_2e8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar16 = extraout_x1_07;
      uVar43 = extraout_s0_07;
      uVar39 = extraout_var_07;
      uVar40 = extraout_var_38;
    }
    lVar32 = *(longlong *)(param_3 + 0x140);
    if (lVar32 != 0) goto LAB_180047ce0;
    local_260 = (longlong *)(param_3 + 0x14a);
LAB_180047d18:
    local_2b0 = local_260;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar16 = extraout_x1_08;
      uVar43 = extraout_s0_08;
      uVar39 = extraout_var_08;
      uVar40 = extraout_var_39;
    }
    lVar32 = *(longlong *)(param_3 + 0x140);
    if (lVar32 != 0) goto LAB_180047d48;
    local_258 = (longlong *)(param_3 + 0x14a);
LAB_180047d80:
    local_2a8 = local_258;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar16 = extraout_x1_09;
      uVar43 = extraout_s0_09;
      uVar39 = extraout_var_09;
      uVar40 = extraout_var_40;
    }
    lVar32 = *(longlong *)(param_3 + 0x140);
    if (lVar32 != 0) goto LAB_180047db0;
    local_1f0 = param_3 + 0x14a;
LAB_180047de8:
    local_2a0 = local_1f0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar16 = extraout_x1_10;
      uVar43 = extraout_s0_10;
      uVar39 = extraout_var_10;
      uVar40 = extraout_var_41;
    }
    lVar32 = *(longlong *)(param_3 + 0x140);
    if (lVar32 != 0) goto LAB_180047e18;
    local_170[0] = param_3 + 0x14a;
LAB_180047e50:
    local_298 = local_170[0];
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar16 = extraout_x1_11;
      uVar43 = extraout_s0_11;
      uVar39 = extraout_var_11;
      uVar40 = extraout_var_42;
    }
    lVar32 = *(longlong *)(param_3 + 0x140);
    if (lVar32 != 0) goto LAB_180047e80;
    local_1a0 = (longlong *)(param_3 + 0x14a);
LAB_180047eb8:
    local_250 = local_1a0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar16 = extraout_x1_12;
      uVar43 = extraout_s0_12;
      uVar39 = extraout_var_12;
      uVar40 = extraout_var_43;
    }
    lVar32 = *(longlong *)(param_3 + 0x140);
    if (lVar32 != 0) goto LAB_180047ed8;
    local_1f8 = (longlong *)(param_3 + 0x14a);
  }
  else {
LAB_180047a08:
    iVar28 = param_3[0x148];
    if (iVar28 == 0) {
      local_328 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar29 = 0;
      if (iVar28 < 1) {
        iVar29 = iVar28 + -1;
      }
      local_328 = (longlong *)(lVar32 + (longlong)iVar29 * 0x170);
    }
    local_310 = local_328;
    local_240 = local_328;
    if (lVar32 == 0) goto LAB_180047a44;
LAB_180047a74:
    iVar28 = param_3[0x148];
    if (iVar28 == 0) {
      local_320 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar29 = 1;
      if (iVar28 + -1 == 0 || iVar28 < 1) {
        iVar29 = iVar28 + -1;
      }
      local_320 = (longlong *)(lVar32 + (longlong)iVar29 * 0x170);
    }
    local_308 = local_320;
    if (lVar32 == 0) goto LAB_180047aa8;
LAB_180047ae0:
    iVar28 = param_3[0x148];
    if (iVar28 == 0) {
      local_300 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar29 = 2;
      if (iVar28 < 3) {
        iVar29 = iVar28 + -1;
      }
      local_300 = (longlong *)(lVar32 + (longlong)iVar29 * 0x170);
    }
    local_318 = 4.2039e-45;
    local_2b8 = local_300;
    if (lVar32 == 0) goto LAB_180047b20;
LAB_180047b4c:
    iVar28 = param_3[0x148];
    if (iVar28 == 0) {
      local_2d0 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar29 = 3;
      if (iVar28 < 4) {
        iVar29 = iVar28 + -1;
      }
      local_2d0 = (longlong *)(lVar32 + (longlong)iVar29 * 0x170);
    }
    local_2c8 = local_2d0;
    if (lVar32 == 0) goto LAB_180047b80;
LAB_180047bb0:
    iVar28 = param_3[0x148];
    if (iVar28 == 0) {
      local_2f8 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar29 = 4;
      if (iVar28 < 5) {
        iVar29 = iVar28 + -1;
      }
      local_2f8 = (longlong *)(lVar32 + (longlong)iVar29 * 0x170);
    }
    local_2d8 = local_2f8;
    if (lVar32 == 0) goto LAB_180047be8;
LAB_180047c18:
    iVar28 = param_3[0x148];
    if (iVar28 == 0) {
      local_2e0 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar29 = 5;
      if (iVar28 < 6) {
        iVar29 = iVar28 + -1;
      }
      local_2e0 = (longlong *)(lVar32 + (longlong)iVar29 * 0x170);
    }
    local_2c0 = local_2e0;
    if (lVar32 == 0) goto LAB_180047c50;
LAB_180047c7c:
    iVar28 = param_3[0x148];
    if (iVar28 == 0) {
      local_2f0 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar29 = 6;
      if (iVar28 < 7) {
        iVar29 = iVar28 + -1;
      }
      local_2f0 = (longlong *)(lVar32 + (longlong)iVar29 * 0x170);
    }
    local_2e8 = local_2f0;
    if (lVar32 == 0) goto LAB_180047cb0;
LAB_180047ce0:
    iVar28 = param_3[0x148];
    if (iVar28 == 0) {
      local_2b0 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar29 = 7;
      if (iVar28 < 8) {
        iVar29 = iVar28 + -1;
      }
      local_2b0 = (longlong *)(lVar32 + (longlong)iVar29 * 0x170);
    }
    local_260 = local_2b0;
    if (lVar32 == 0) goto LAB_180047d18;
LAB_180047d48:
    iVar28 = param_3[0x148];
    if (iVar28 == 0) {
      local_2a8 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar29 = 8;
      if (iVar28 < 9) {
        iVar29 = iVar28 + -1;
      }
      local_2a8 = (longlong *)(lVar32 + (longlong)iVar29 * 0x170);
    }
    local_258 = local_2a8;
    if (lVar32 == 0) goto LAB_180047d80;
LAB_180047db0:
    iVar28 = param_3[0x148];
    if (iVar28 == 0) {
      local_2a0 = param_3 + 0x14a;
    }
    else {
      iVar29 = 9;
      if (iVar28 < 10) {
        iVar29 = iVar28 + -1;
      }
      local_2a0 = (int *)(lVar32 + (longlong)iVar29 * 0x170);
    }
    local_1f0 = local_2a0;
    if (lVar32 == 0) goto LAB_180047de8;
LAB_180047e18:
    iVar28 = param_3[0x148];
    if (iVar28 == 0) {
      local_298 = param_3 + 0x14a;
    }
    else {
      iVar29 = 10;
      if (iVar28 < 0xb) {
        iVar29 = iVar28 + -1;
      }
      local_298 = (int *)(lVar32 + (longlong)iVar29 * 0x170);
    }
    local_170[0] = local_298;
    if (lVar32 == 0) goto LAB_180047e50;
LAB_180047e80:
    iVar28 = param_3[0x148];
    if (iVar28 == 0) {
      local_250 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar29 = 0xb;
      if (iVar28 < 0xc) {
        iVar29 = iVar28 + -1;
      }
      local_250 = (longlong *)(lVar32 + (longlong)iVar29 * 0x170);
    }
    local_1a0 = local_250;
    if (lVar32 == 0) goto LAB_180047eb8;
LAB_180047ed8:
    iVar28 = param_3[0x148];
    if (iVar28 == 0) {
      local_1f8 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar29 = 0xc;
      if (iVar28 < 0xd) {
        iVar29 = iVar28 + -1;
      }
      local_1f8 = (longlong *)(lVar32 + (longlong)iVar29 * 0x170);
    }
  }
  lVar32 = *(longlong *)(param_3 + 0x84);
  if (lVar32 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar16 = extraout_x1_13;
      uVar43 = extraout_s0_13;
      uVar39 = extraout_var_13;
      uVar40 = extraout_var_44;
    }
    lVar32 = *(longlong *)(param_3 + 0x84);
    if (lVar32 != 0) goto LAB_180047f2c;
    local_290 = param_3 + 0x8e;
  }
  else {
LAB_180047f2c:
    iVar28 = param_3[0x8c];
    if (iVar28 == 0) {
      local_290 = param_3 + 0x8e;
    }
    else {
      iVar29 = 0;
      if (iVar28 < 1) {
        iVar29 = iVar28 + -1;
      }
      local_290 = (int *)(lVar32 + (longlong)iVar29 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_290 + 0x1a) = 1;
  iVar28 = 1;
  lVar32 = *(longlong *)(param_3 + 0x84);
  if (lVar32 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar16 = extraout_x1_14;
      uVar43 = extraout_s0_14;
      uVar39 = extraout_var_14;
      uVar40 = extraout_var_45;
    }
    lVar32 = *(longlong *)(param_3 + 0x84);
    if (lVar32 != 0) goto LAB_180047f84;
    local_220 = param_3 + 0x8e;
  }
  else {
LAB_180047f84:
    iVar29 = param_3[0x8c];
    if (iVar29 == 0) {
      local_220 = param_3 + 0x8e;
    }
    else {
      if (iVar29 < 2) {
        iVar28 = iVar29 + -1;
      }
      local_220 = (int *)(lVar32 + (longlong)iVar28 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_220 + 0x1a) = 2;
  iVar28 = 2;
  lVar32 = *(longlong *)(param_3 + 0x84);
  if (lVar32 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar16 = extraout_x1_15;
      uVar43 = extraout_s0_15;
      uVar39 = extraout_var_15;
      uVar40 = extraout_var_46;
    }
    lVar32 = *(longlong *)(param_3 + 0x84);
    if (lVar32 != 0) goto LAB_180047fdc;
    local_268 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_180047fdc:
    iVar29 = param_3[0x8c];
    if (iVar29 == 0) {
      local_268 = (longlong *)(param_3 + 0x8e);
    }
    else {
      if (iVar29 < 3) {
        iVar28 = iVar29 + -1;
      }
      local_268 = (longlong *)(lVar32 + (longlong)iVar28 * 0x98);
    }
  }
  iVar28 = 3;
  *(undefined2 *)((longlong)local_268 + 0x1a) = local_318._0_2_;
  lVar32 = *(longlong *)(param_3 + 0x84);
  if (lVar32 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar16 = extraout_x1_16;
      uVar43 = extraout_s0_16;
      uVar39 = extraout_var_16;
      uVar40 = extraout_var_47;
    }
    lVar32 = *(longlong *)(param_3 + 0x84);
    if (lVar32 != 0) goto LAB_180048034;
    local_280 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_180048034:
    iVar29 = param_3[0x8c];
    if (iVar29 == 0) {
      local_280 = (longlong *)(param_3 + 0x8e);
    }
    else {
      if (iVar29 < 4) {
        iVar28 = iVar29 + -1;
      }
      local_280 = (longlong *)(lVar32 + (longlong)iVar28 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_280 + 0x1a) = 4;
  iVar28 = 4;
  lVar32 = *(longlong *)(param_3 + 0x84);
  if (lVar32 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar16 = extraout_x1_17;
      uVar43 = extraout_s0_17;
      uVar39 = extraout_var_17;
      uVar40 = extraout_var_48;
    }
    lVar32 = *(longlong *)(param_3 + 0x84);
    if (lVar32 != 0) goto LAB_18004808c;
    local_270 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_18004808c:
    iVar29 = param_3[0x8c];
    if (iVar29 == 0) {
      local_270 = (longlong *)(param_3 + 0x8e);
    }
    else {
      if (iVar29 < 5) {
        iVar28 = iVar29 + -1;
      }
      local_270 = (longlong *)(lVar32 + (longlong)iVar28 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_270 + 0x1a) = 5;
  iVar28 = 5;
  lVar32 = *(longlong *)(param_3 + 0x84);
  if (lVar32 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar16 = extraout_x1_18;
      uVar43 = extraout_s0_18;
      uVar39 = extraout_var_18;
      uVar40 = extraout_var_49;
    }
    lVar32 = *(longlong *)(param_3 + 0x84);
    if (lVar32 != 0) goto LAB_1800480e4;
    local_218 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_1800480e4:
    iVar29 = param_3[0x8c];
    if (iVar29 == 0) {
      local_218 = (longlong *)(param_3 + 0x8e);
    }
    else {
      if (iVar29 < 6) {
        iVar28 = iVar29 + -1;
      }
      local_218 = (longlong *)(lVar32 + (longlong)iVar28 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_218 + 0x1a) = 6;
  iVar28 = 6;
  lVar32 = *(longlong *)(param_3 + 0x84);
  if (lVar32 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar16 = extraout_x1_19;
      uVar43 = extraout_s0_19;
      uVar39 = extraout_var_19;
      uVar40 = extraout_var_50;
    }
    lVar32 = *(longlong *)(param_3 + 0x84);
    if (lVar32 != 0) goto LAB_18004813c;
    local_210 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_18004813c:
    iVar29 = param_3[0x8c];
    if (iVar29 == 0) {
      local_210 = (longlong *)(param_3 + 0x8e);
    }
    else {
      if (iVar29 < 7) {
        iVar28 = iVar29 + -1;
      }
      local_210 = (longlong *)(lVar32 + (longlong)iVar28 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_210 + 0x1a) = 7;
  iVar28 = 7;
  lVar32 = *(longlong *)(param_3 + 0x84);
  if (lVar32 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar16 = extraout_x1_20;
      uVar43 = extraout_s0_20;
      uVar39 = extraout_var_20;
      uVar40 = extraout_var_51;
    }
    lVar32 = *(longlong *)(param_3 + 0x84);
    if (lVar32 != 0) goto LAB_180048194;
    local_208 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_180048194:
    iVar29 = param_3[0x8c];
    if (iVar29 == 0) {
      local_208 = (longlong *)(param_3 + 0x8e);
    }
    else {
      if (iVar29 < 8) {
        iVar28 = iVar29 + -1;
      }
      local_208 = (longlong *)(lVar32 + (longlong)iVar28 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_208 + 0x1a) = 8;
  iVar28 = 8;
  lVar32 = *(longlong *)(param_3 + 0x84);
  if (lVar32 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar16 = extraout_x1_21;
      uVar43 = extraout_s0_21;
      uVar39 = extraout_var_21;
      uVar40 = extraout_var_52;
    }
    lVar32 = *(longlong *)(param_3 + 0x84);
    if (lVar32 != 0) goto LAB_1800481ec;
    local_200 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_1800481ec:
    iVar29 = param_3[0x8c];
    if (iVar29 == 0) {
      local_200 = (longlong *)(param_3 + 0x8e);
    }
    else {
      if (iVar29 < 9) {
        iVar28 = iVar29 + -1;
      }
      local_200 = (longlong *)(lVar32 + (longlong)iVar28 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_200 + 0x1a) = 9;
  iVar28 = 9;
  lVar32 = *(longlong *)(param_3 + 0x84);
  if (lVar32 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar16 = extraout_x1_22;
      uVar43 = extraout_s0_22;
      uVar39 = extraout_var_22;
      uVar40 = extraout_var_53;
    }
    lVar32 = *(longlong *)(param_3 + 0x84);
    if (lVar32 != 0) goto LAB_180048244;
    local_230 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_180048244:
    iVar29 = param_3[0x8c];
    if (iVar29 == 0) {
      local_230 = (longlong *)(param_3 + 0x8e);
    }
    else {
      if (iVar29 < 10) {
        iVar28 = iVar29 + -1;
      }
      local_230 = (longlong *)(lVar32 + (longlong)iVar28 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_230 + 0x1a) = 10;
  iVar28 = 10;
  lVar32 = *(longlong *)(param_3 + 0x84);
  if (lVar32 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar16 = extraout_x1_23;
      uVar43 = extraout_s0_23;
      uVar39 = extraout_var_23;
      uVar40 = extraout_var_54;
    }
    lVar32 = *(longlong *)(param_3 + 0x84);
    if (lVar32 != 0) goto LAB_18004829c;
    plVar35 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_18004829c:
    iVar29 = param_3[0x8c];
    if (iVar29 == 0) {
      plVar35 = (longlong *)(param_3 + 0x8e);
    }
    else {
      if (iVar29 < 0xb) {
        iVar28 = iVar29 + -1;
      }
      plVar35 = (longlong *)(lVar32 + (longlong)iVar28 * 0x98);
    }
  }
  *(undefined2 *)((longlong)plVar35 + 0x1a) = 0xb;
  iVar28 = 0xb;
  lVar32 = *(longlong *)(param_3 + 0x84);
  if (lVar32 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar16 = extraout_x1_24;
      uVar43 = extraout_s0_24;
      uVar39 = extraout_var_24;
      uVar40 = extraout_var_55;
    }
    lVar32 = *(longlong *)(param_3 + 0x84);
    if (lVar32 != 0) goto LAB_1800482f0;
    local_228 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_1800482f0:
    iVar29 = param_3[0x8c];
    if (iVar29 == 0) {
      local_228 = (longlong *)(param_3 + 0x8e);
    }
    else {
      if (iVar29 < 0xc) {
        iVar28 = iVar29 + -1;
      }
      local_228 = (longlong *)(lVar32 + (longlong)iVar28 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_228 + 0x1a) = 0xd;
  iVar28 = 0xc;
  lVar32 = *(longlong *)(param_3 + 0x84);
  if (lVar32 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar16 = extraout_x1_25;
      uVar43 = extraout_s0_25;
      uVar39 = extraout_var_25;
      uVar40 = extraout_var_56;
    }
    lVar32 = *(longlong *)(param_3 + 0x84);
    if (lVar32 != 0) goto LAB_180048348;
    piVar36 = param_3 + 0x8e;
  }
  else {
LAB_180048348:
    iVar29 = param_3[0x8c];
    if (iVar29 == 0) {
      piVar36 = param_3 + 0x8e;
    }
    else {
      if (iVar29 < 0xd) {
        iVar28 = iVar29 + -1;
      }
      piVar36 = (int *)(lVar32 + (longlong)iVar28 * 0x98);
    }
  }
  *(undefined2 *)((longlong)piVar36 + 0x1a) = 0xc;
  local_198 = PitSessions::vftable;
  if (param_3[0x2b] != 0) {
    FUN_1800079f8((longlong *)(param_3 + 0x46),0x1800d81d0,0x11);
    param_3[0x49] = 1;
    if ((*(char **)(param_3 + 0xce) != (char *)0x0) && (**(char **)(param_3 + 0xce) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0xce),0x1800d4ecd,0);
      param_3[0xd1] = 1;
    }
    local_1c0 = "";
    uStack_1b8 = 0;
    local_1b0 = &DAT_1800d4ecd;
    pvVar14 = GetProcessHeap();
    pcVar15 = (char *)HeapAlloc(pvVar14,0,0x15);
    if (pcVar15 == (char *)0x0) {
      local_1c0 = "";
      pcVar38 = pcVar26;
    }
    else {
      param_6 = 0x14;
      pcVar15[8] = '\0';
      pcVar15[9] = '\0';
      pcVar15[10] = '\0';
      pcVar15[0xb] = '\0';
      pcVar15[0xc] = '\0';
      pcVar15[0xd] = '\0';
      pcVar15[0xe] = '\0';
      pcVar15[0xf] = '\0';
      pcVar15[0] = '\0';
      pcVar15[1] = '\0';
      pcVar15[2] = '\0';
      pcVar15[3] = '\0';
      pcVar15[4] = '\0';
      pcVar15[5] = '\0';
      pcVar15[6] = '\0';
      pcVar15[7] = '\0';
      pcVar15[0x10] = '\0';
      pcVar15[0x11] = '\0';
      pcVar15[0x12] = '\0';
      pcVar15[0x13] = '\0';
      pcVar15[0x14] = '\0';
      local_1c0 = pcVar15;
      FUN_180099d78(pcVar15,0x15,0x1800d6c40,0x14);
      uStack_1b8 = 0x100000001;
      pcVar38 = pcVar15;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_1c0);
    if ((pcVar15 != (char *)0x0) && (pcVar38 != (char *)0x0)) {
      pvVar14 = GetProcessHeap();
      HeapFree(pvVar14,0,pcVar38);
    }
    local_1c0 = "";
    uStack_1b8 = 0;
    local_1b0 = &DAT_1800d4ecd;
    pvVar14 = GetProcessHeap();
    pcVar15 = (char *)HeapAlloc(pvVar14,0,0x8d);
    if (pcVar15 == (char *)0x0) {
      local_1c0 = "";
      pcVar38 = pcVar26;
    }
    else {
      param_6 = 0x8c;
      pcVar15[8] = '\0';
      pcVar15[9] = '\0';
      pcVar15[10] = '\0';
      pcVar15[0xb] = '\0';
      pcVar15[0xc] = '\0';
      pcVar15[0xd] = '\0';
      pcVar15[0xe] = '\0';
      pcVar15[0xf] = '\0';
      pcVar15[0] = '\0';
      pcVar15[1] = '\0';
      pcVar15[2] = '\0';
      pcVar15[3] = '\0';
      pcVar15[4] = '\0';
      pcVar15[5] = '\0';
      pcVar15[6] = '\0';
      pcVar15[7] = '\0';
      pcVar15[0x18] = '\0';
      pcVar15[0x19] = '\0';
      pcVar15[0x1a] = '\0';
      pcVar15[0x1b] = '\0';
      pcVar15[0x1c] = '\0';
      pcVar15[0x1d] = '\0';
      pcVar15[0x1e] = '\0';
      pcVar15[0x1f] = '\0';
      pcVar15[0x10] = '\0';
      pcVar15[0x11] = '\0';
      pcVar15[0x12] = '\0';
      pcVar15[0x13] = '\0';
      pcVar15[0x14] = '\0';
      pcVar15[0x15] = '\0';
      pcVar15[0x16] = '\0';
      pcVar15[0x17] = '\0';
      pcVar15[0x28] = '\0';
      pcVar15[0x29] = '\0';
      pcVar15[0x2a] = '\0';
      pcVar15[0x2b] = '\0';
      pcVar15[0x2c] = '\0';
      pcVar15[0x2d] = '\0';
      pcVar15[0x2e] = '\0';
      pcVar15[0x2f] = '\0';
      pcVar15[0x20] = '\0';
      pcVar15[0x21] = '\0';
      pcVar15[0x22] = '\0';
      pcVar15[0x23] = '\0';
      pcVar15[0x24] = '\0';
      pcVar15[0x25] = '\0';
      pcVar15[0x26] = '\0';
      pcVar15[0x27] = '\0';
      pcVar15[0x38] = '\0';
      pcVar15[0x39] = '\0';
      pcVar15[0x3a] = '\0';
      pcVar15[0x3b] = '\0';
      pcVar15[0x3c] = '\0';
      pcVar15[0x3d] = '\0';
      pcVar15[0x3e] = '\0';
      pcVar15[0x3f] = '\0';
      pcVar15[0x30] = '\0';
      pcVar15[0x31] = '\0';
      pcVar15[0x32] = '\0';
      pcVar15[0x33] = '\0';
      pcVar15[0x34] = '\0';
      pcVar15[0x35] = '\0';
      pcVar15[0x36] = '\0';
      pcVar15[0x37] = '\0';
      pcVar15[0x48] = '\0';
      pcVar15[0x49] = '\0';
      pcVar15[0x4a] = '\0';
      pcVar15[0x4b] = '\0';
      pcVar15[0x4c] = '\0';
      pcVar15[0x4d] = '\0';
      pcVar15[0x4e] = '\0';
      pcVar15[0x4f] = '\0';
      pcVar15[0x40] = '\0';
      pcVar15[0x41] = '\0';
      pcVar15[0x42] = '\0';
      pcVar15[0x43] = '\0';
      pcVar15[0x44] = '\0';
      pcVar15[0x45] = '\0';
      pcVar15[0x46] = '\0';
      pcVar15[0x47] = '\0';
      pcVar15[0x58] = '\0';
      pcVar15[0x59] = '\0';
      pcVar15[0x5a] = '\0';
      pcVar15[0x5b] = '\0';
      pcVar15[0x5c] = '\0';
      pcVar15[0x5d] = '\0';
      pcVar15[0x5e] = '\0';
      pcVar15[0x5f] = '\0';
      pcVar15[0x50] = '\0';
      pcVar15[0x51] = '\0';
      pcVar15[0x52] = '\0';
      pcVar15[0x53] = '\0';
      pcVar15[0x54] = '\0';
      pcVar15[0x55] = '\0';
      pcVar15[0x56] = '\0';
      pcVar15[0x57] = '\0';
      pcVar15[0x68] = '\0';
      pcVar15[0x69] = '\0';
      pcVar15[0x6a] = '\0';
      pcVar15[0x6b] = '\0';
      pcVar15[0x6c] = '\0';
      pcVar15[0x6d] = '\0';
      pcVar15[0x6e] = '\0';
      pcVar15[0x6f] = '\0';
      pcVar15[0x60] = '\0';
      pcVar15[0x61] = '\0';
      pcVar15[0x62] = '\0';
      pcVar15[99] = '\0';
      pcVar15[100] = '\0';
      pcVar15[0x65] = '\0';
      pcVar15[0x66] = '\0';
      pcVar15[0x67] = '\0';
      pcVar15[0x78] = '\0';
      pcVar15[0x79] = '\0';
      pcVar15[0x7a] = '\0';
      pcVar15[0x7b] = '\0';
      pcVar15[0x7c] = '\0';
      pcVar15[0x7d] = '\0';
      pcVar15[0x7e] = '\0';
      pcVar15[0x7f] = '\0';
      pcVar15[0x70] = '\0';
      pcVar15[0x71] = '\0';
      pcVar15[0x72] = '\0';
      pcVar15[0x73] = '\0';
      pcVar15[0x74] = '\0';
      pcVar15[0x75] = '\0';
      pcVar15[0x76] = '\0';
      pcVar15[0x77] = '\0';
      pcVar15[0x80] = '\0';
      pcVar15[0x81] = '\0';
      pcVar15[0x82] = '\0';
      pcVar15[0x83] = '\0';
      pcVar15[0x84] = '\0';
      pcVar15[0x85] = '\0';
      pcVar15[0x86] = '\0';
      pcVar15[0x87] = '\0';
      pcVar15[0x88] = '\0';
      pcVar15[0x89] = '\0';
      pcVar15[0x8a] = '\0';
      pcVar15[0x8b] = '\0';
      pcVar15[0x8c] = '\0';
      local_1c0 = pcVar15;
      FUN_180099d78(pcVar15,0x8d,0x1800d6bb0,0x8c);
      uStack_1b8 = 0x100000001;
      pcVar38 = pcVar15;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_1c0);
    uVar43 = extraout_s0_26;
    uVar39 = extraout_var_26;
    uVar16 = extraout_var_57;
    if ((pcVar15 != (char *)0x0) && (pcVar38 != (char *)0x0)) {
      pvVar14 = GetProcessHeap();
      HeapFree(pvVar14,0,pcVar38);
      local_1c0 = (char *)0x0;
      uStack_1b8 = 0;
      uVar43 = extraout_s0_27;
      uVar39 = extraout_var_27;
      uVar16 = extraout_var_58;
    }
    pcVar15 = *(char **)(param_3 + 0x46);
    if (*(char **)(param_3 + 0x46) == (char *)0x0) {
      pcVar15 = pcVar26;
    }
    auVar50._4_4_ = uVar39;
    auVar50._0_4_ = uVar43;
    auVar50._8_8_ = uVar16;
    FUN_180026368(auVar50,CONCAT44(uVar42,uVar41),(undefined8 *)(param_3 + 0xce),0x1800d6c60,pcVar15
                  ,param_6,param_7,param_8,param_9,param_10);
    local_1c0 = "";
    uStack_1b8 = 0;
    local_1b0 = &DAT_1800d4ecd;
    pvVar14 = GetProcessHeap();
    pcVar15 = (char *)HeapAlloc(pvVar14,0,7);
    if (pcVar15 == (char *)0x0) {
      local_1c0 = "";
    }
    else {
      pcVar15[0] = '\0';
      pcVar15[1] = '\0';
      pcVar15[2] = '\0';
      pcVar15[3] = '\0';
      pcVar15[4] = '\0';
      pcVar15[5] = '\0';
      pcVar15[6] = '\0';
      local_1c0 = pcVar15;
      FUN_180099d78(pcVar15,7,0x1800d6c58,6);
      uStack_1b8 = 0x100000001;
      pcVar26 = pcVar15;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_1c0);
    if ((pcVar15 != (char *)0x0) && (pcVar26 != (char *)0x0)) {
      pvVar14 = GetProcessHeap();
      HeapFree(pvVar14,0,pcVar26);
      local_1c0 = (char *)0x0;
      uStack_1b8 = 0;
    }
    param_3[1] = 0;
    param_3[4] = 0;
    param_3[0xdf] = 1;
    param_3[0x32e] = 1;
    param_3[0x276] = 1;
    FUN_1800079f8(local_310,0x1800d7d70,10);
    *(int *)((longlong)local_328 + 0xc) = 1;
    *(int *)(local_328 + 0x1c) = 0x84105;
    *(undefined2 *)((longlong)local_328 + 0x24) = 0x58;
    *(int *)(local_328 + 3) = 0xc0c0c0;
    *(int *)(local_328 + 0x1b) = 0;
    FUN_1800079f8(local_320,0x1800d7d88,9);
    *(int *)((longlong)local_308 + 0xc) = 1;
    *(int *)(local_308 + 0x1c) = 0x84105;
    *(undefined2 *)((longlong)local_308 + 0x24) = 0x58;
    *(int *)(local_308 + 3) = 0xc0c0c0;
    *(int *)(local_308 + 0x1b) = 0;
    FUN_1800079f8(local_2b8,0x1800d7d7c,5);
    *(int *)((longlong)local_300 + 0xc) = 1;
    *(int *)(local_300 + 3) = 0x51ff00;
    *(int *)(local_300 + 0x1c) = 0x84105;
    *(int *)((longlong)local_300 + 0x24) = 0x20058;
    *(int *)(local_300 + 0x1b) = 0;
    FUN_1800079f8(local_2c8,0x1800d7d98,2);
    *(int *)((longlong)local_2d0 + 0xc) = 1;
    *(int *)(local_2d0 + 0x1c) = 0x84105;
    *(int *)((longlong)local_2d0 + 0x24) = 0x20058;
    *(int *)(local_2d0 + 3) = 0xf0a60d;
    *(int *)(local_2d0 + 0x1b) = 0;
    FUN_1800079f8(local_2f8,0x1800d7d94,2);
    *(int *)((longlong)local_2d8 + 0xc) = 1;
    *(int *)(local_2d8 + 0x1c) = 0x84105;
    *(int *)((longlong)local_2d8 + 0x24) = 0x20058;
    *(int *)(local_2d8 + 3) = 0x221f0;
    *(int *)(local_2d8 + 0x1b) = 0;
    FUN_1800079f8(local_2c0,0x1800d7da0,2);
    *(int *)((longlong)local_2e0 + 0xc) = 1;
    *(int *)(local_2e0 + 0x1c) = 0x84105;
    *(int *)((longlong)local_2e0 + 0x24) = 0x20058;
    *(int *)(local_2e0 + 3) = 0xf0a60d;
    *(int *)(local_2e0 + 0x1b) = 0;
    FUN_1800079f8(local_2f0,0x1800d7d9c,2);
    *(int *)((longlong)local_2e8 + 0xc) = 1;
    *(int *)(local_2e8 + 0x1c) = 0x84105;
    *(int *)((longlong)local_2e8 + 0x24) = 0x20058;
    *(int *)(local_2e8 + 3) = 0x221f0;
    *(int *)(local_2e8 + 0x1b) = 0;
    FUN_1800079f8(local_2b0,0x1800d7da8,2);
    *(int *)((longlong)local_260 + 0xc) = 1;
    *(int *)(local_260 + 0x1c) = 0x84105;
    *(int *)((longlong)local_260 + 0x24) = 0x20058;
    *(int *)(local_260 + 3) = 0xf0a60d;
    *(int *)(local_260 + 0x1b) = 0;
    FUN_1800079f8(local_2a8,0x1800d7da4,2);
    *(int *)((longlong)local_258 + 0xc) = 1;
    *(int *)(local_258 + 0x1c) = 0x84105;
    *(int *)((longlong)local_258 + 0x24) = 0x20058;
    *(int *)(local_258 + 3) = 0x221f0;
    *(int *)(local_258 + 0x1b) = 0;
    local_1f0[6] = 0xff80;
    *(undefined2 *)(local_1f0 + 9) = 2;
    *(undefined2 *)(local_1f0 + 10) = 1;
    local_170[0][6] = 0xff80;
    *(undefined2 *)(local_170[0] + 9) = 2;
    *(undefined2 *)(local_170[0] + 10) = 1;
    *(int *)(local_1a0 + 3) = 0x80ffff;
    *(undefined2 *)((longlong)local_1a0 + 0x24) = 2;
    *(undefined2 *)(local_1a0 + 5) = 1;
    *(undefined2 *)((longlong)local_1f8 + 0x24) = 0x4b;
    *(int *)(local_1f8 + 3) = 0xffff;
    *(undefined2 *)(local_1f8 + 5) = 1;
    if (*(code **)(local_290 + 0x14) != (code *)0x0) {
      (**(code **)(local_290 + 0x14))(local_290[0x13],"Weekly;Monthly;Quarterly");
    }
    plVar17 = local_268;
    *(undefined1 *)(local_290 + 6) = 0x16;
    local_290[7] = 0;
    *(undefined1 *)(local_220 + 6) = 5;
    local_220[7] = 1;
    FUN_1800079f8(local_268,0x1800d7ed0,0x13);
    *(undefined4 *)((longlong)plVar17 + 0xc) = 1;
    if ((code *)plVar17[10] != (code *)0x0) {
      (*(code *)plVar17[10])
                (*(undefined4 *)((longlong)plVar17 + 0x4c),
                 "Line at Last Bar Left to Right;Dash;Stair Step to Edge;Transparent Square Offset L eft for Candlestick;User-Defined"
                );
    }
    plVar17 = local_280;
    *(undefined1 *)(local_268 + 3) = 0x16;
    *(undefined4 *)((longlong)local_268 + 0x1c) = 0;
    FUN_1800079f8(local_280,0x1800d7f08,9);
    *(undefined4 *)((longlong)plVar17 + 0xc) = 1;
    if ((code *)plVar17[10] != (code *)0x0) {
      (*(code *)plVar17[10])
                (*(undefined4 *)((longlong)plVar17 + 0x4c),"None;All;Current;Developing");
    }
    plVar17 = local_270;
    *(undefined1 *)(local_280 + 3) = 0x16;
    *(undefined4 *)((longlong)local_280 + 0x1c) = 1;
    FUN_1800079f8(local_270,0x1800d7b50,10);
    plVar3 = local_210;
    *(undefined4 *)((longlong)plVar17 + 0xc) = 1;
    *(undefined1 *)(plVar17 + 3) = 0xe;
    *(undefined4 *)((longlong)plVar17 + 0x1c) = 0xffffff;
    FUN_1800079f8(local_210,0x1800d7ba8,0x19);
    plVar17 = local_218;
    *(undefined4 *)((longlong)plVar3 + 0xc) = 1;
    *(undefined1 *)(plVar3 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar3 + 0x1c) = 0x5f;
    FUN_1800079f8(local_218,0x1800d7bf0,0x1b);
    plVar4 = local_200;
    *(undefined4 *)((longlong)plVar17 + 0xc) = 1;
    *(undefined1 *)(plVar17 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar17 + 0x1c) = 5;
    FUN_1800079f8(local_200,0x1800d7f38,0x1c);
    plVar3 = local_208;
    *(undefined4 *)((longlong)plVar4 + 0xc) = 1;
    *(undefined1 *)(plVar4 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar4 + 0x1c) = 0x50;
    FUN_1800079f8(local_208,0x1800d7f18,0x1e);
    plVar17 = local_230;
    *(undefined4 *)((longlong)plVar3 + 0xc) = 1;
    *(undefined1 *)(plVar3 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar3 + 0x1c) = 5;
    FUN_1800079f8(local_230,0x1800d7be0,0xe);
    *(undefined4 *)((longlong)plVar17 + 0xc) = 1;
    *(undefined1 *)(plVar17 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar17 + 0x1c) = 8;
    FUN_1800079f8(plVar35,0x1800d7f90,0xc);
    *(undefined4 *)((longlong)plVar35 + 0xc) = 1;
    if ((code *)plVar35[10] != (code *)0x0) {
      (*(code *)plVar35[10])
                (*(undefined4 *)((longlong)plVar35 + 0x4c),
                 "Magenta/Cyan;Magenta/Blue;Red/Yellow;User-Defined");
    }
    *(undefined1 *)(plVar35 + 3) = 0x16;
    *(float *)((longlong)plVar35 + 0x1c) = local_318;
    puVar13 = FUN_180029680((longlong *)&local_198,(undefined8 *)&local_1e8);
    if (0xf < (ulonglong)puVar13[3]) {
      puVar13 = (undefined8 *)*puVar13;
    }
    if (*(code **)(piVar36 + 0x14) != (code *)0x0) {
      (**(code **)(piVar36 + 0x14))(piVar36[0x13],puVar13);
      *(undefined1 *)(piVar36 + 6) = 0x16;
    }
    if (0xf < local_1d0) {
      FUN_1800966b8((LPVOID)CONCAT71(uStack_1e7,local_1e8));
    }
    plVar35 = local_228;
    *(undefined1 *)(piVar36 + 6) = 0x16;
    local_1d8 = 0;
    local_1d0 = 0xf;
    local_1e8 = 0;
    piVar36[7] = 0;
    FUN_1800079f8(local_228,0x1800d6d30,0x14);
    *(undefined4 *)((longlong)plVar35 + 0xc) = 1;
    *(undefined1 *)(plVar35 + 3) = 5;
    *(undefined4 *)((longlong)plVar35 + 0x1c) = 0;
    return;
  }
  if (param_3[0xe1] == 0) {
    auVar1._4_4_ = uVar39;
    auVar1._0_4_ = uVar43;
    auVar1._8_8_ = uVar40;
    uVar16 = FUN_1800254e8(auVar1,CONCAT44(uVar42,uVar41),(longlong)param_3,uVar16,param_5,param_6,
                           param_7,param_8,param_9,param_10);
    *piVar12 = (int)uVar16;
  }
  if (*piVar12 != 0) {
    return;
  }
  uStack_138 = 0;
  local_140 = 0;
  uStack_128 = 0;
  local_130 = (longlong *)0x0;
  uStack_118 = 0;
  local_120 = 0;
  uStack_108 = 0;
  local_110 = 0;
  uStack_f8 = 0;
  local_100 = 0;
  uStack_e8 = 0;
  uStack_f0 = 0;
  uStack_d8 = 0;
  local_e0 = 0;
  uStack_c8 = 0;
  uStack_d0 = 0;
  local_c0 = 0;
  uVar10 = FUN_180026550((longlong)piVar36);
  FUN_180011fd0(&local_140,(longlong)param_3,param_3[0xe1],uVar10);
  plVar17 = local_268;
  if ((param_3[0x47e] != 0) && (param_3[0xe1] == 0)) {
    uVar10 = FUN_180026550((longlong)local_268);
    plVar8 = local_2c0;
    plVar7 = local_2c8;
    plVar6 = local_2f0;
    plVar5 = local_2f8;
    plVar4 = local_310;
    plVar3 = local_320;
    if (uVar10 == 0) {
      iVar28 = 0;
      *(undefined2 *)((longlong)local_328 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_308 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_300 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_2d0 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_2d8 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_2e0 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_2e8 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_260 + 0x24) = 0x58;
      *(undefined2 *)((longlong)local_258 + 0x24) = 0x58;
      local_1a0 = plVar35;
      do {
        lVar32 = *(longlong *)(param_3 + 0x140);
        if (lVar32 == 0) {
          if (*(code **)(param_3 + 0x144) != (code *)0x0) {
            (**(code **)(param_3 + 0x144))(param_3[0x146]);
          }
          lVar32 = *(longlong *)(param_3 + 0x140);
          if (lVar32 != 0) goto LAB_180048bdc;
          piVar12 = param_3 + 0x14a;
        }
        else {
LAB_180048bdc:
          iVar29 = param_3[0x148];
          if (iVar29 == 0) {
            piVar12 = param_3 + 0x14a;
          }
          else {
            iVar34 = iVar28;
            if (iVar29 <= iVar28) {
              iVar34 = iVar29 + -1;
            }
            piVar12 = (int *)(lVar32 + (longlong)iVar34 * 0x170);
          }
        }
        *(undefined2 *)(piVar12 + 10) = 1;
        (**(code **)(param_3 + 0x70e))(param_3[9],param_3[0x270],0x4b);
        iVar28 = iVar28 + 1;
        plVar35 = local_1a0;
      } while (iVar28 < 0x3c);
    }
    else {
      uVar10 = FUN_180026550((longlong)plVar17);
      if (uVar10 == 1) {
        iVar28 = 0;
        *(undefined2 *)((longlong)local_328 + 0x24) = local_318._0_2_;
        *(undefined2 *)((longlong)local_308 + 0x24) = local_318._0_2_;
        *(undefined2 *)((longlong)local_300 + 0x24) = local_318._0_2_;
        *(undefined2 *)((longlong)local_2d0 + 0x24) = local_318._0_2_;
        *(undefined2 *)((longlong)local_2d8 + 0x24) = local_318._0_2_;
        *(undefined2 *)((longlong)local_2e0 + 0x24) = local_318._0_2_;
        *(undefined2 *)((longlong)local_2e8 + 0x24) = local_318._0_2_;
        *(undefined2 *)((longlong)local_260 + 0x24) = local_318._0_2_;
        *(undefined2 *)((longlong)local_258 + 0x24) = local_318._0_2_;
        do {
          plVar17 = FUN_18000f880((longlong *)(param_3 + 0x140),iVar28);
          *(undefined2 *)(plVar17 + 5) = 1;
          (**(code **)(param_3 + 0x70e))(param_3[9],param_3[0x270],0x4b);
          iVar28 = iVar28 + 1;
          plVar3 = local_320;
          plVar4 = local_310;
          plVar5 = local_2f8;
          plVar6 = local_2f0;
          plVar7 = local_2c8;
          plVar8 = local_2c0;
        } while (iVar28 < 0x3c);
      }
      else {
        uVar10 = FUN_180026550((longlong)plVar17);
        if (uVar10 == 2) {
          iVar28 = 0;
          *(undefined2 *)((longlong)local_328 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_308 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_300 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_2d0 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_2d8 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_2e0 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_2e8 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_260 + 0x24) = 0x66;
          *(undefined2 *)((longlong)local_258 + 0x24) = 0x66;
          do {
            plVar17 = FUN_18000f880((longlong *)(param_3 + 0x140),iVar28);
            *(undefined2 *)(plVar17 + 5) = 1;
            (**(code **)(param_3 + 0x70e))(param_3[9],param_3[0x270],0x4b);
            iVar28 = iVar28 + 1;
            plVar3 = local_320;
            plVar4 = local_310;
            plVar5 = local_2f8;
            plVar6 = local_2f0;
            plVar7 = local_2c8;
            plVar8 = local_2c0;
          } while (iVar28 < 0x3c);
        }
        else {
          uVar10 = FUN_180026550((longlong)plVar17);
          plVar3 = local_320;
          plVar4 = local_310;
          plVar5 = local_2f8;
          plVar6 = local_2f0;
          plVar7 = local_2c8;
          plVar8 = local_2c0;
          if (uVar10 == 3) {
            iVar28 = 0;
            *(undefined2 *)((longlong)local_328 + 0x24) = 0x5d;
            *(undefined2 *)((longlong)local_308 + 0x24) = 0x5d;
            *(undefined2 *)((longlong)local_300 + 0x24) = 0x5d;
            *(undefined2 *)((longlong)local_2d0 + 0x24) = 0x5d;
            *(undefined2 *)((longlong)local_2d8 + 0x24) = 0x5d;
            *(undefined2 *)((longlong)local_2e0 + 0x24) = 0x5d;
            *(undefined2 *)((longlong)local_2e8 + 0x24) = 0x5d;
            *(undefined2 *)((longlong)local_260 + 0x24) = 0x5d;
            *(undefined2 *)((longlong)local_258 + 0x24) = 0x5d;
            do {
              plVar17 = FUN_18000f880((longlong *)(param_3 + 0x140),iVar28);
              *(undefined2 *)(plVar17 + 5) = 10;
              (**(code **)(param_3 + 0x70e))(param_3[9],param_3[0x270],0x55);
              iVar28 = iVar28 + 1;
              plVar3 = local_320;
              plVar4 = local_310;
              plVar5 = local_2f8;
              plVar6 = local_2f0;
              plVar7 = local_2c8;
              plVar8 = local_2c0;
            } while (iVar28 < 0x3c);
          }
        }
      }
    }
    local_2c0 = plVar8;
    local_2c8 = plVar7;
    local_2f0 = plVar6;
    local_2f8 = plVar5;
    local_310 = plVar4;
    local_320 = plVar3;
    uVar10 = FUN_180026550((longlong)plVar35);
    if ((uVar10 != 0) && (uVar10 = FUN_180026550((longlong)plVar35), uVar10 != 1)) {
      FUN_180026550((longlong)plVar35);
    }
    uVar10 = FUN_180026550((longlong)plVar35);
    if (uVar10 != 3) {
      *(int *)(local_328 + 3) = extraout_w12;
      *(int *)(local_308 + 3) = extraout_w12;
      *(int *)(local_300 + 3) = extraout_w12;
      *(int *)(local_2d0 + 3) = extraout_w11;
      *(int *)(local_2d8 + 3) = extraout_w11;
      *(int *)(local_2e0 + 3) = extraout_w11;
      *(int *)(local_2e8 + 3) = extraout_w11;
    }
    if (local_248 != (int *)0x0) {
      FUN_18004a2d0(local_248);
      (**(code **)(param_3 + 0x448))(2,0);
    }
    auVar50 = FUN_180096150(0x80);
    local_1a0 = auVar50._0_8_;
    uVar41 = 0x40000000;
    uVar42 = 0;
    local_1a0[1] = 0;
    *local_1a0 = 0;
    local_1a0[3] = 0;
    local_1a0[2] = 0;
    local_1a0[5] = 0;
    local_1a0[4] = 0;
    local_1a0[7] = 0;
    local_1a0[6] = 0;
    local_1a0[9] = 0;
    local_1a0[8] = 0;
    local_1a0[0xb] = 0;
    local_1a0[10] = 0;
    local_1a0[0xd] = 0;
    local_1a0[0xc] = 0;
    local_1a0[0xf] = 0;
    local_1a0[0xe] = 0;
    local_248 = FUN_18001cd30(0x3e4ccccd,0x40000000,(undefined4 *)local_1a0,auVar50._8_8_,1,0);
    if (local_248 == (int *)0x0) {
      return;
    }
    (**(code **)(param_3 + 0x448))(2,local_248);
    if (local_288 != (float *)0x0) {
      FUN_1800966b8(local_288);
      (**(code **)(param_3 + 0x448))(3,0);
    }
    pfVar18 = (float *)FUN_180096718(0x50);
    pfVar18[2] = 0.0;
    pfVar18[3] = 0.0;
    pfVar18[0] = 0.0;
    pfVar18[1] = 0.0;
    pfVar18[6] = 0.0;
    pfVar18[7] = 0.0;
    pfVar18[4] = 0.0;
    pfVar18[5] = 0.0;
    pfVar18[10] = 0.0;
    pfVar18[0xb] = 0.0;
    pfVar18[8] = 0.0;
    pfVar18[9] = 0.0;
    pfVar18[0xe] = 0.0;
    pfVar18[0xf] = 0.0;
    pfVar18[0xc] = 0.0;
    pfVar18[0xd] = 0.0;
    pfVar18[0x12] = 0.0;
    pfVar18[0x13] = 0.0;
    pfVar18[0x10] = 0.0;
    pfVar18[0x11] = 0.0;
    local_288 = pfVar18;
    (**(code **)(param_3 + 0x448))(3,pfVar18);
    pfVar18[2] = 0.0;
    pfVar18[3] = 0.0;
    pfVar18[0] = 0.0;
    pfVar18[1] = 0.0;
    pfVar18[6] = 0.0;
    pfVar18[7] = 0.0;
    pfVar18[4] = 0.0;
    pfVar18[5] = 0.0;
    pfVar18[10] = 0.0;
    pfVar18[0xb] = 0.0;
    pfVar18[8] = 0.0;
    pfVar18[9] = 0.0;
    pfVar18[0xe] = 0.0;
    pfVar18[0xf] = 0.0;
    pfVar18[0xc] = 0.0;
    pfVar18[0xd] = 0.0;
    pfVar18[0x12] = 0.0;
    pfVar18[0x13] = 0.0;
    pfVar18[0x10] = 0.0;
    pfVar18[0x11] = 0.0;
    *local_278 = 0;
  }
  if (param_3[0x1b] != 0) {
    if (local_248 != (int *)0x0) {
      FUN_18004a2d0(local_248);
      (**(code **)(param_3 + 0x448))(2,0);
    }
    if (local_288 == (float *)0x0) {
      return;
    }
    FUN_1800966b8(local_288);
    (**(code **)(param_3 + 0x448))(3,0);
    return;
  }
  if (local_248 == (int *)0x0) {
    return;
  }
  if (local_288 == (float *)0x0) {
    return;
  }
  bVar9 = false;
  local_1a0 = local_130;
  plVar35 = local_130;
  FUN_180010968((longlong *)&local_1a0,(int *)&local_300,(int *)&local_308,(int *)&local_328);
  local_1a0 = plVar35;
  FUN_180010968((longlong *)&local_1a0,(int *)&local_308,(int *)&local_300,(int *)&local_328);
  lVar32 = local_140;
  cVar27 = uStack_c8._7_1_;
  if (uStack_c8._7_1_ == '\0') {
    local_1b0 = &DAT_1800d4ecd;
    local_1c0 = "";
    uStack_1b8 = 0;
    uStack_c8 = CONCAT26(0x100,(undefined6)uStack_c8);
    lVar33 = (longlong)plVar35 % 86400000000;
    iVar28 = ((int)(lVar33 / 1000000) + (int)(lVar33 >> 0x3f)) -
             (SUB164(SEXT816(lVar33) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
    if (86399999999 < lVar33) {
      iVar28 = 0;
    }
    lVar33 = local_110 % 86400000000;
    iVar34 = (int)(lVar33 >> 0x3f);
    iVar29 = ((int)(lVar33 / 1000000) + iVar34) -
             (SUB164(SEXT816(lVar33) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
    if (86399999999 < lVar33) {
      iVar29 = 0;
    }
    if (iVar28 == iVar29) {
      iVar28 = (int)uStack_138 + -1;
      if (-1 < iVar28) {
        do {
          lVar31 = *(longlong *)(lVar32 + 0x160);
          if (lVar31 == 0) {
            if (*(code **)(lVar32 + 0x170) != (code *)0x0) {
              (**(code **)(lVar32 + 0x170))(*(undefined4 *)(lVar32 + 0x178));
            }
            lVar31 = *(longlong *)(lVar32 + 0x160);
            if (lVar31 != 0) goto LAB_180049140;
            plVar35 = (longlong *)(lVar32 + 0x188);
          }
          else {
LAB_180049140:
            iVar29 = *(int *)(lVar32 + 0x180);
            if (iVar29 == 0) {
              plVar35 = (longlong *)(lVar32 + 0x188);
            }
            else {
              iVar30 = iVar28;
              if (iVar29 <= iVar28) {
                iVar30 = iVar29 + -1;
              }
              plVar35 = (longlong *)(lVar31 + (longlong)iVar30 * 8);
            }
          }
          local_170[0] = (int *)*plVar35;
          (**(code **)(lVar32 + 0x1350))
                    (&local_1a0,local_170,"PST-08PDT+01,M3.2.0/02:00,M11.1.0/02:00");
          if ((int)((longlong)local_1a0 / 86400000000) < 1) break;
          plVar35 = local_1a0;
          FUN_180010968((longlong *)&local_1a0,(int *)&local_2d8,(int *)&local_328,(int *)&local_2d0
                       );
          FUN_180010968((longlong *)&local_130,(int *)&local_2e8,(int *)&local_308,(int *)&local_2e0
                       );
          if (local_328._0_4_ != local_308._0_4_) {
            uStack_c8 = CONCAT26(0x101,(undefined6)uStack_c8);
            cVar27 = '\x01';
            goto LAB_18004920c;
          }
          lVar31 = (longlong)plVar35 % 86400000000;
          iVar29 = ((int)(lVar31 / 1000000) + (int)(lVar31 >> 0x3f)) -
                   (SUB164(SEXT816(lVar31) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
          if (86399999999 < lVar31) {
            iVar29 = 0;
          }
          iVar30 = ((int)(lVar33 / 1000000) + iVar34) -
                   (SUB164(SEXT816(lVar33) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
          if (86399999999 < lVar33) {
            iVar30 = 0;
          }
          if ((iVar29 == iVar30) || (iVar28 = iVar28 + -1, iVar28 < 0)) break;
        } while( true );
      }
    }
    cVar27 = uStack_c8._7_1_;
  }
LAB_18004920c:
  if (((cVar27 == '\0') || (uStack_c8._6_1_ == '\0')) ||
     (uVar10 = param_3[0xe1], *local_278 == uVar10)) {
    uVar10 = param_3[0xe1];
    uVar37 = *local_278;
    if (uVar37 != uVar10) goto LAB_180049264;
  }
  else {
    uVar37 = *local_278;
    bVar9 = true;
LAB_180049264:
    FUN_180012b18((longlong)&local_140);
  }
  uVar11 = FUN_180026550((longlong)local_290);
  if (((uVar11 == 0) && (uVar16 = extraout_x1_26, extraout_w11_00 != 0)) ||
     (uVar11 = FUN_180026550((longlong)local_290), uVar16 = extraout_x1_27, uVar11 == 1 && bVar9)) {
LAB_1800492d0:
    FUN_18001cec0(local_248,uVar16,0);
    uVar43 = extraout_s0_29;
    uVar39 = extraout_var_29;
    uVar40 = extraout_var_60;
  }
  else {
    uVar11 = FUN_180026550(extraout_x12);
    uVar43 = extraout_s0_28;
    uVar39 = extraout_var_28;
    uVar40 = extraout_var_59;
    if (((uVar11 == 2 && extraout_w11_01 != 0) && (uint)local_300._0_4_ < 0xb) &&
       (uVar16 = extraout_x1_28, (0x492U >> (ulonglong)((uint)local_300._0_4_ & 0x1f) & 1) != 0))
    goto LAB_1800492d0;
  }
  piVar36 = local_298;
  piVar12 = local_2a0;
  auVar2._4_4_ = uVar39;
  auVar2._0_4_ = uVar43;
  auVar2._8_8_ = uVar40;
  FUN_18001cf60(auVar2,CONCAT44(uVar42,uVar41),local_248,param_3,param_3[0xe1],(longlong)local_250,
                (longlong)local_2a0,(longlong)local_298,0,0,'\0',0);
  local_2e8 = (longlong *)((ulonglong)local_2e8 & 0xffffffff00000000);
  if ((uVar37 == uVar10) || (uVar19 = FUN_180012928((longlong)&local_140), (uVar19 & 1) == 0)) {
    plVar35 = local_310;
    if (*local_278 != param_3[0xe1]) {
      puVar20 = (undefined4 *)FUN_18000f448((longlong)local_310,param_3[0xe1] - 1);
      uVar43 = *puVar20;
      puVar20 = (undefined4 *)FUN_18000f448((longlong)plVar35,param_3[0xe1]);
      plVar35 = local_320;
      *puVar20 = uVar43;
      puVar20 = (undefined4 *)FUN_18000f448((longlong)local_320,param_3[0xe1] + -1);
      uVar43 = *puVar20;
      puVar20 = (undefined4 *)FUN_18000f448((longlong)plVar35,param_3[0xe1]);
      plVar35 = local_2b8;
      *puVar20 = uVar43;
      puVar20 = (undefined4 *)FUN_18000f448((longlong)local_2b8,param_3[0xe1] + -1);
      uVar43 = *puVar20;
      goto LAB_1800495ac;
    }
  }
  else {
    puVar20 = (undefined4 *)FUN_18000f448((longlong)piVar12,param_3[0xe1] + -1);
    uVar43 = *puVar20;
    puVar20 = (undefined4 *)FUN_18000f448((longlong)local_310,param_3[0xe1]);
    *puVar20 = uVar43;
    puVar20 = (undefined4 *)FUN_18000f448((longlong)piVar36,param_3[0xe1] + -1);
    uVar43 = *puVar20;
    puVar20 = (undefined4 *)FUN_18000f448((longlong)local_320,param_3[0xe1]);
    *puVar20 = uVar43;
    pfVar18 = (float *)FUN_18000f448((longlong)piVar36,param_3[0xe1] + -1);
    pfVar21 = (float *)FUN_18000f448((longlong)piVar12,param_3[0xe1] + -1);
    fVar49 = *pfVar18;
    fVar44 = *pfVar21;
    pfVar18 = (float *)FUN_18000f448((longlong)local_2b8,param_3[0xe1]);
    *pfVar18 = (fVar49 + fVar44) * 0.5;
    bVar9 = FUN_180026690((longlong)local_228);
    plVar5 = local_2c0;
    plVar4 = local_2f0;
    plVar3 = local_2f8;
    plVar17 = local_310;
    plVar35 = local_320;
    if (bVar9) {
      iVar28 = 0;
      do {
        uVar10 = *local_278;
        if ((int)uVar10 < param_3[0xe1]) {
          local_328 = (longlong *)CONCAT44(local_328._4_4_,iVar28);
          do {
            lVar32 = *(longlong *)(param_3 + 0x140);
            if (lVar32 == 0) {
              if (*(code **)(param_3 + 0x144) != (code *)0x0) {
                (**(code **)(param_3 + 0x144))(param_3[0x146]);
              }
              lVar32 = *(longlong *)(param_3 + 0x140);
              if (lVar32 != 0) goto LAB_180049418;
              piVar12 = param_3 + 0x14a;
            }
            else {
LAB_180049418:
              iVar28 = param_3[0x148];
              if (iVar28 == 0) {
                piVar12 = param_3 + 0x14a;
              }
              else {
                iVar29 = (int)local_328._0_4_;
                if (iVar28 <= (int)local_328._0_4_) {
                  iVar29 = iVar28 + -1;
                }
                piVar12 = (int *)(lVar32 + (longlong)iVar29 * 0x170);
              }
            }
            lVar32 = *(longlong *)(piVar12 + 0xc);
            if (lVar32 == 0) {
              if (*(code **)(piVar12 + 0x10) != (code *)0x0) {
                (**(code **)(piVar12 + 0x10))(piVar12[0x12]);
              }
              lVar32 = *(longlong *)(piVar12 + 0xc);
              if (lVar32 != 0) goto LAB_1800494a0;
              piVar12 = piVar12 + 0x15;
            }
            else {
LAB_1800494a0:
              iVar28 = piVar12[0x14];
              if (iVar28 == 0) {
                piVar12 = piVar12 + 0x15;
              }
              else {
                uVar37 = uVar10 & ((int)uVar10 >> 0x1f ^ 0xffffffffU);
                if (iVar28 <= (int)uVar37) {
                  uVar37 = iVar28 - 1;
                }
                piVar12 = (int *)(lVar32 + (longlong)(int)uVar37 * 4);
              }
            }
            *piVar12 = 0;
            uVar10 = uVar10 + 1;
          } while ((int)uVar10 < param_3[0xe1]);
          iVar28 = (int)local_328._0_4_;
        }
        iVar28 = iVar28 + 1;
      } while (iVar28 < 0x3c);
      param_3[0x433] = *local_278;
    }
    *local_278 = param_3[0xe1];
    local_320 = plVar35;
    local_310 = plVar17;
    local_2f8 = plVar3;
    local_2f0 = plVar4;
    local_2c0 = plVar5;
    puVar20 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x2ac),param_3[0xe1]);
    uVar43 = *puVar20;
    plVar35 = local_1f8;
LAB_1800495ac:
    puVar20 = (undefined4 *)FUN_18000f448((longlong)plVar35,param_3[0xe1]);
    *puVar20 = uVar43;
  }
  uVar10 = param_3[0xe1];
  lVar32 = local_320[6];
  uVar37 = uVar10;
  if (lVar32 == 0) {
    if ((code *)local_320[8] != (code *)0x0) {
      (*(code *)local_320[8])((int)local_320[9]);
      uVar37 = local_238[0xe1];
    }
    lVar32 = local_320[6];
    if (lVar32 != 0) goto LAB_1800495f8;
    pfVar18 = (float *)((longlong)local_320 + 0x54);
  }
  else {
LAB_1800495f8:
    iVar28 = (int)local_320[10];
    if (iVar28 == 0) {
      pfVar18 = (float *)((longlong)local_320 + 0x54);
    }
    else {
      uVar10 = uVar10 & ((int)uVar10 >> 0x1f ^ 0xffffffffU);
      if (iVar28 <= (int)uVar10) {
        uVar10 = iVar28 - 1;
      }
      pfVar18 = (float *)(lVar32 + (longlong)(int)uVar10 * 4);
    }
  }
  lVar32 = local_310[6];
  uVar10 = uVar37;
  if (lVar32 == 0) {
    if ((code *)local_310[8] != (code *)0x0) {
      (*(code *)local_310[8])((int)local_310[9]);
      uVar10 = local_238[0xe1];
    }
    lVar32 = local_310[6];
    if (lVar32 != 0) goto LAB_180049678;
    plVar35 = local_240 + 6;
    fVar44 = *(float *)((longlong)local_310 + 0x54) - *pfVar18;
LAB_1800496c4:
    uVar37 = uVar10;
    if ((code *)plVar35[2] != (code *)0x0) {
      (*(code *)plVar35[2])((int)plVar35[3]);
      uVar37 = local_238[0xe1];
    }
    lVar32 = *plVar35;
    if (lVar32 != 0) goto LAB_1800496ec;
    pfVar18 = (float *)((longlong)plVar35 + 0x24);
  }
  else {
LAB_180049678:
    iVar28 = (int)local_310[10];
    if (iVar28 == 0) {
      pfVar21 = (float *)((longlong)local_310 + 0x54);
    }
    else {
      uVar37 = uVar37 & ((int)uVar37 >> 0x1f ^ 0xffffffffU);
      if (iVar28 <= (int)uVar37) {
        uVar37 = iVar28 - 1;
      }
      pfVar21 = (float *)(lVar32 + (longlong)(int)uVar37 * 4);
    }
    plVar35 = local_310 + 6;
    fVar44 = *pfVar21 - *pfVar18;
    uVar37 = uVar10;
    if (lVar32 == 0) goto LAB_1800496c4;
LAB_1800496ec:
    iVar28 = (int)plVar35[4];
    if (iVar28 == 0) {
      pfVar18 = (float *)((longlong)plVar35 + 0x24);
    }
    else {
      if ((int)uVar10 < 0) {
        uVar10 = 0;
      }
      if (iVar28 <= (int)uVar10) {
        uVar10 = iVar28 - 1;
      }
      pfVar18 = (float *)(lVar32 + (longlong)(int)uVar10 * 4);
    }
  }
  fVar49 = *pfVar18;
  lVar32 = local_2c8[6];
  if (lVar32 == 0) {
    if ((code *)local_2c8[8] != (code *)0x0) {
      (*(code *)local_2c8[8])((int)local_2c8[9]);
    }
    lVar32 = local_2c8[6];
    if (lVar32 != 0) goto LAB_180049750;
    pfVar18 = (float *)((longlong)local_2c8 + 0x54);
  }
  else {
LAB_180049750:
    iVar28 = (int)local_2c8[10];
    if (iVar28 == 0) {
      pfVar18 = (float *)((longlong)local_2c8 + 0x54);
    }
    else {
      uVar37 = uVar37 & ((int)uVar37 >> 0x1f ^ 0xffffffffU);
      if (iVar28 <= (int)uVar37) {
        uVar37 = iVar28 - 1;
      }
      pfVar18 = (float *)(lVar32 + (longlong)(int)uVar37 * 4);
    }
  }
  *pfVar18 = fVar49 + fVar44;
  uVar10 = param_3[0xe1];
  lVar32 = local_320[6];
  uVar37 = uVar10;
  if (lVar32 == 0) {
    if ((code *)local_320[8] != (code *)0x0) {
      (*(code *)local_320[8])((int)local_320[9]);
      uVar37 = local_238[0xe1];
    }
    lVar32 = local_320[6];
    if (lVar32 != 0) goto LAB_1800497bc;
    pfVar18 = (float *)((longlong)local_320 + 0x54);
  }
  else {
LAB_1800497bc:
    iVar28 = (int)local_320[10];
    if (iVar28 == 0) {
      pfVar18 = (float *)((longlong)local_320 + 0x54);
    }
    else {
      uVar10 = uVar10 & ((int)uVar10 >> 0x1f ^ 0xffffffffU);
      if (iVar28 <= (int)uVar10) {
        uVar10 = iVar28 - 1;
      }
      pfVar18 = (float *)(lVar32 + (longlong)(int)uVar10 * 4);
    }
  }
  fVar49 = *pfVar18;
  lVar32 = local_2f8[6];
  if (lVar32 == 0) {
    if ((code *)local_2f8[8] != (code *)0x0) {
      (*(code *)local_2f8[8])((int)local_2f8[9]);
    }
    lVar32 = local_2f8[6];
    if (lVar32 != 0) goto LAB_18004981c;
    pfVar18 = (float *)((longlong)local_2f8 + 0x54);
  }
  else {
LAB_18004981c:
    iVar28 = (int)local_2f8[10];
    if (iVar28 == 0) {
      pfVar18 = (float *)((longlong)local_2f8 + 0x54);
    }
    else {
      uVar37 = uVar37 & ((int)uVar37 >> 0x1f ^ 0xffffffffU);
      if (iVar28 <= (int)uVar37) {
        uVar37 = iVar28 - 1;
      }
      pfVar18 = (float *)(lVar32 + (longlong)(int)uVar37 * 4);
    }
  }
  *pfVar18 = fVar49 - fVar44;
  uVar10 = param_3[0xe1];
  lVar32 = local_310[6];
  uVar37 = uVar10;
  if (lVar32 == 0) {
    if ((code *)local_310[8] != (code *)0x0) {
      (*(code *)local_310[8])((int)local_310[9]);
      uVar37 = local_238[0xe1];
    }
    lVar32 = local_310[6];
    if (lVar32 != 0) goto LAB_180049888;
    pfVar18 = (float *)((longlong)local_310 + 0x54);
  }
  else {
LAB_180049888:
    iVar28 = (int)local_310[10];
    if (iVar28 == 0) {
      pfVar18 = (float *)((longlong)local_310 + 0x54);
    }
    else {
      uVar10 = uVar10 & ((int)uVar10 >> 0x1f ^ 0xffffffffU);
      if (iVar28 <= (int)uVar10) {
        uVar10 = iVar28 - 1;
      }
      pfVar18 = (float *)(lVar32 + (longlong)(int)uVar10 * 4);
    }
  }
  fVar49 = *pfVar18;
  lVar32 = local_2c0[6];
  if (lVar32 == 0) {
    if ((code *)local_2c0[8] != (code *)0x0) {
      (*(code *)local_2c0[8])((int)local_2c0[9]);
    }
    lVar32 = local_2c0[6];
    if (lVar32 != 0) goto LAB_1800498ec;
    pfVar18 = (float *)((longlong)local_2c0 + 0x54);
  }
  else {
LAB_1800498ec:
    iVar28 = (int)local_2c0[10];
    if (iVar28 == 0) {
      pfVar18 = (float *)((longlong)local_2c0 + 0x54);
    }
    else {
      uVar37 = uVar37 & ((int)uVar37 >> 0x1f ^ 0xffffffffU);
      if (iVar28 <= (int)uVar37) {
        uVar37 = iVar28 - 1;
      }
      pfVar18 = (float *)(lVar32 + (longlong)(int)uVar37 * 4);
    }
  }
  *pfVar18 = fVar44 + fVar44 + fVar49;
  uVar10 = param_3[0xe1];
  lVar32 = local_320[6];
  uVar37 = uVar10;
  if (lVar32 == 0) {
    if ((code *)local_320[8] != (code *)0x0) {
      (*(code *)local_320[8])((int)local_320[9]);
      uVar37 = local_238[0xe1];
    }
    lVar32 = local_320[6];
    if (lVar32 != 0) goto LAB_180049958;
    pfVar18 = (float *)((longlong)local_320 + 0x54);
  }
  else {
LAB_180049958:
    iVar28 = (int)local_320[10];
    if (iVar28 == 0) {
      pfVar18 = (float *)((longlong)local_320 + 0x54);
    }
    else {
      uVar10 = uVar10 & ((int)uVar10 >> 0x1f ^ 0xffffffffU);
      if (iVar28 <= (int)uVar10) {
        uVar10 = iVar28 - 1;
      }
      pfVar18 = (float *)(lVar32 + (longlong)(int)uVar10 * 4);
    }
  }
  fVar49 = *pfVar18;
  lVar32 = local_2f0[6];
  if (lVar32 == 0) {
    if ((code *)local_2f0[8] != (code *)0x0) {
      (*(code *)local_2f0[8])((int)local_2f0[9]);
    }
    lVar32 = local_2f0[6];
    if (lVar32 != 0) goto LAB_1800499bc;
    pfVar18 = (float *)((longlong)local_2f0 + 0x54);
  }
  else {
LAB_1800499bc:
    iVar28 = (int)local_2f0[10];
    if (iVar28 == 0) {
      pfVar18 = (float *)((longlong)local_2f0 + 0x54);
    }
    else {
      uVar37 = uVar37 & ((int)uVar37 >> 0x1f ^ 0xffffffffU);
      if (iVar28 <= (int)uVar37) {
        uVar37 = iVar28 - 1;
      }
      pfVar18 = (float *)(lVar32 + (longlong)(int)uVar37 * 4);
    }
  }
  plVar17 = local_310;
  *pfVar18 = fVar49 - (fVar44 + fVar44);
  pfVar18 = (float *)FUN_18000f448((longlong)local_310,param_3[0xe1]);
  fVar49 = *pfVar18;
  pfVar18 = (float *)FUN_18000f448((longlong)local_2b0,param_3[0xe1]);
  plVar35 = local_320;
  *pfVar18 = fVar44 * 3.0 + fVar49;
  pfVar18 = (float *)FUN_18000f448((longlong)local_320,param_3[0xe1]);
  fVar49 = *pfVar18;
  pfVar18 = (float *)FUN_18000f448((longlong)local_2a8,param_3[0xe1]);
  piVar12 = local_2a0;
  *pfVar18 = fVar49 - fVar44 * 3.0;
  pfVar18 = (float *)FUN_18000f448((longlong)local_2a0,param_3[0xe1] + -1);
  piVar36 = local_298;
  pfVar21 = (float *)FUN_18000f448((longlong)local_298,param_3[0xe1] + -1);
  fVar44 = *pfVar18 - *pfVar21;
  pfVar18 = (float *)FUN_18000f448((longlong)piVar36,param_3[0xe1] + -1);
  pfVar21 = (float *)FUN_18000f448((longlong)piVar12,param_3[0xe1] + -1);
  local_318 = (*pfVar21 + *pfVar18) * 0.5;
  pfVar18 = (float *)FUN_18000f448((longlong)piVar12,param_3[0xe1] + -1);
  local_2e0 = (longlong *)CONCAT44(local_2e0._4_4_,*pfVar18 + fVar44);
  pfVar18 = (float *)FUN_18000f448((longlong)piVar36,param_3[0xe1] + -1);
  local_300 = (longlong *)CONCAT44(local_300._4_4_,*pfVar18 - fVar44);
  pfVar18 = (float *)FUN_18000f448((longlong)piVar12,param_3[0xe1] + -1);
  local_2d8 = (longlong *)CONCAT44(local_2d8._4_4_,*pfVar18 + fVar44 + fVar44);
  pfVar18 = (float *)FUN_18000f448((longlong)piVar36,param_3[0xe1] + -1);
  local_308 = (longlong *)CONCAT44(local_308._4_4_,*pfVar18 - (fVar44 + fVar44));
  pfVar18 = (float *)FUN_18000f448((longlong)piVar12,param_3[0xe1] + -1);
  local_2d0 = (longlong *)CONCAT44(local_2d0._4_4_,*pfVar18 + fVar44 * 3.0);
  pfVar18 = (float *)FUN_18000f448((longlong)piVar36,param_3[0xe1] + -1);
  local_328 = (longlong *)CONCAT44(local_328._4_4_,*pfVar18 - fVar44 * 3.0);
  bVar9 = FUN_180026690((longlong)local_220);
  if (bVar9) {
    fVar44 = (float)param_3[0x1a6];
    pfVar18 = (float *)FUN_18000f448((longlong)plVar17,param_3[0xe1]);
    fVar44 = FUN_18000de78(*pfVar18,fVar44);
    pfVar18 = (float *)FUN_18000f448((longlong)plVar17,param_3[0xe1]);
    *pfVar18 = fVar44;
    fVar44 = (float)param_3[0x1a6];
    pfVar18 = (float *)FUN_18000f448((longlong)plVar35,param_3[0xe1]);
    fVar44 = FUN_18000de78(*pfVar18,fVar44);
    pfVar18 = (float *)FUN_18000f448((longlong)plVar35,param_3[0xe1]);
    plVar35 = local_2b8;
    *pfVar18 = fVar44;
    fVar44 = (float)param_3[0x1a6];
    pfVar18 = (float *)FUN_18000f448((longlong)local_2b8,param_3[0xe1]);
    fVar44 = FUN_18000de78(*pfVar18,fVar44);
    pfVar18 = (float *)FUN_18000f448((longlong)plVar35,param_3[0xe1]);
    plVar35 = local_2c8;
    *pfVar18 = fVar44;
    fVar44 = (float)param_3[0x1a6];
    pfVar18 = (float *)FUN_18000f448((longlong)local_2c8,param_3[0xe1]);
    fVar44 = FUN_18000de78(*pfVar18,fVar44);
    pfVar18 = (float *)FUN_18000f448((longlong)plVar35,param_3[0xe1]);
    plVar35 = local_2f8;
    *pfVar18 = fVar44;
    fVar44 = (float)param_3[0x1a6];
    pfVar18 = (float *)FUN_18000f448((longlong)local_2f8,param_3[0xe1]);
    fVar44 = FUN_18000de78(*pfVar18,fVar44);
    pfVar18 = (float *)FUN_18000f448((longlong)plVar35,param_3[0xe1]);
    plVar35 = local_2c0;
    *pfVar18 = fVar44;
    fVar44 = (float)param_3[0x1a6];
    pfVar18 = (float *)FUN_18000f448((longlong)local_2c0,param_3[0xe1]);
    fVar44 = FUN_18000de78(*pfVar18,fVar44);
    pfVar18 = (float *)FUN_18000f448((longlong)plVar35,param_3[0xe1]);
    plVar35 = local_2f0;
    *pfVar18 = fVar44;
    fVar44 = (float)param_3[0x1a6];
    pfVar18 = (float *)FUN_18000f448((longlong)local_2f0,param_3[0xe1]);
    fVar44 = FUN_18000de78(*pfVar18,fVar44);
    pfVar18 = (float *)FUN_18000f448((longlong)plVar35,param_3[0xe1]);
    *pfVar18 = fVar44;
  }
  local_1b0 = &DAT_1800d4ecd;
  local_1c0 = "";
  uStack_1b8 = 0;
  local_180 = &DAT_1800d4ecd;
  local_190 = &DAT_1800d4ecd;
  uStack_188 = 0;
  uVar10 = FUN_180026550((longlong)local_290);
  if (uVar10 == 0) {
LAB_180049d8c:
    FUN_1800079f8((longlong *)&local_1c0,0x1800d7c30,0x1b);
    uStack_1b8 = CONCAT44(1,(int)uStack_1b8);
    FUN_1800079f8((longlong *)&local_190,0x1800d7ca8,0x1e);
  }
  else if (uVar10 == 1) {
    FUN_1800079f8((longlong *)&local_1c0,0x1800d7c10,0x1c);
    uStack_1b8 = CONCAT44(1,(int)uStack_1b8);
    FUN_1800079f8((longlong *)&local_190,0x1800d7c88,0x1f);
    local_2e8 = (longlong *)CONCAT44(local_2e8._4_4_,0x40800000);
  }
  else {
    if (uVar10 != 2) goto LAB_180049d8c;
    FUN_1800079f8((longlong *)&local_1c0,0x1800d7c68,0x1e);
    uStack_1b8 = CONCAT44(1,(int)uStack_1b8);
    FUN_1800079f8((longlong *)&local_190,0x1800d7ce0,0x21);
    local_2e8 = (longlong *)CONCAT44(local_2e8._4_4_,0x40a00000);
  }
  uStack_188 = CONCAT44(1,(int)uStack_188);
  uVar10 = FUN_180026550((longlong)local_280);
  if (uVar10 != 0) {
    uVar10 = FUN_180026550(extraout_x11);
    if ((uVar10 == 1) || (uVar10 = FUN_180026550(extraout_x11_00), uVar10 == 2)) {
      iVar28 = param_3[0x1bf];
      pfVar18 = (float *)FUN_18000f448((longlong)local_2a8,param_3[0xe1]);
      fVar48 = *pfVar18;
      pfVar18 = (float *)FUN_18000f448((longlong)local_2f0,param_3[0xe1]);
      fVar47 = *pfVar18;
      pfVar18 = (float *)FUN_18000f448((longlong)local_2f8,param_3[0xe1]);
      fVar46 = *pfVar18;
      pfVar18 = (float *)FUN_18000f448((longlong)local_2b0,param_3[0xe1]);
      fVar45 = *pfVar18;
      pfVar18 = (float *)FUN_18000f448((longlong)local_2c0,param_3[0xe1]);
      fVar49 = *pfVar18;
      puVar22 = (uint *)FUN_18000f448((longlong)local_2c8,param_3[0xe1]);
      uVar10 = *puVar22;
      pfVar18 = (float *)FUN_18000f448((longlong)local_2b8,param_3[0xe1]);
      fVar44 = *pfVar18;
      uVar19 = FUN_180026708((longlong)local_218);
      uVar23 = FUN_180026708((longlong)local_210);
      uVar24 = FUN_180026708((longlong)local_230);
      uVar25 = FUN_180026708((longlong)local_270);
      local_1a0 = (longlong *)&local_1e8;
      pcVar26 = (char *)FUN_180007828((undefined8 *)&local_1e8,(longlong *)&local_1c0);
      FUN_18000dec8(fVar44,ZEXT416(uVar10),fVar49,fVar45,fVar46,fVar47,fVar48,(longlong)param_3,
                    0x845fed,pcVar26,(int)uVar25,(int)uVar24,(uint)uVar23,(uint)uVar19,iVar28,
                    (undefined8 *)0x0);
    }
    uVar10 = FUN_180026550((longlong)local_280);
    if ((uVar10 == 1) || (uVar10 = FUN_180026550(extraout_x11_01), uVar10 == 3)) {
      fVar48 = (float)param_3[0x1a6];
      local_318 = FUN_18000de78(local_318,fVar48);
      fVar44 = FUN_18000de78(local_2e0._0_4_,fVar48);
      local_2e0 = (longlong *)CONCAT44(local_2e0._4_4_,fVar44);
      fVar49 = FUN_18000de78(local_2d8._0_4_,fVar48);
      local_2d8 = (longlong *)CONCAT44(local_2d8._4_4_,fVar49);
      fVar45 = FUN_18000de78(local_2d0._0_4_,fVar48);
      local_2d0 = (longlong *)CONCAT44(local_2d0._4_4_,fVar45);
      fVar46 = FUN_18000de78(local_300._0_4_,fVar48);
      local_300 = (longlong *)CONCAT44(local_300._4_4_,fVar46);
      fVar47 = FUN_18000de78(local_308._0_4_,fVar48);
      local_308 = (longlong *)CONCAT44(local_308._4_4_,fVar47);
      fVar48 = FUN_18000de78(local_328._0_4_,fVar48);
      iVar28 = param_3[0x1bf];
      local_328 = (longlong *)CONCAT44(local_328._4_4_,fVar48);
      uVar19 = FUN_180026708((longlong)local_208);
      uVar23 = FUN_180026708((longlong)local_200);
      uVar24 = FUN_180026708((longlong)local_230);
      uVar25 = FUN_180026708((longlong)local_270);
      local_1a0 = (longlong *)&local_1e8;
      pcVar26 = (char *)FUN_180007828((undefined8 *)&local_1e8,(longlong *)&local_190);
      FUN_18000dec8(local_318,ZEXT416((uint)fVar44),fVar49,fVar45,fVar46,fVar47,fVar48,
                    (longlong)param_3,0xe85c9,pcVar26,(int)uVar25,(int)uVar24,(uint)uVar23,
                    (uint)uVar19,iVar28,(undefined8 *)0x0);
    }
  }
  pfVar18 = local_288;
  *local_288 = (float)param_3[0x1bf];
  local_288[1] = local_2e8._0_4_;
  local_288[2] = (float)param_3[0x1a6];
  pfVar21 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2ac),param_3[0xe1]);
  pfVar18[3] = *pfVar21;
  pfVar21 = (float *)FUN_18000f448((longlong)local_2b8,param_3[0xe1]);
  pfVar18[4] = *pfVar21;
  pfVar21 = (float *)FUN_18000f448((longlong)local_2c8,param_3[0xe1]);
  pfVar18[5] = *pfVar21;
  pfVar21 = (float *)FUN_18000f448((longlong)local_2c0,param_3[0xe1]);
  pfVar18[6] = *pfVar21;
  uVar10 = param_3[0xe1];
  lVar32 = local_2b0[6];
  if (lVar32 == 0) {
    if ((code *)local_2b0[8] != (code *)0x0) {
      (*(code *)local_2b0[8])((int)local_2b0[9]);
    }
    lVar32 = local_2b0[6];
    if (lVar32 != 0) goto LAB_18004a0d0;
    pfVar18 = (float *)((longlong)local_2b0 + 0x54);
  }
  else {
LAB_18004a0d0:
    iVar28 = (int)local_2b0[10];
    if (iVar28 == 0) {
      pfVar18 = (float *)((longlong)local_2b0 + 0x54);
    }
    else {
      uVar10 = uVar10 & ((int)uVar10 >> 0x1f ^ 0xffffffffU);
      if (iVar28 <= (int)uVar10) {
        uVar10 = iVar28 - 1;
      }
      pfVar18 = (float *)(lVar32 + (longlong)(int)uVar10 * 4);
    }
  }
  local_288[7] = *pfVar18;
  uVar10 = param_3[0xe1];
  lVar32 = local_2f8[6];
  if (lVar32 == 0) {
    if ((code *)local_2f8[8] != (code *)0x0) {
      (*(code *)local_2f8[8])((int)local_2f8[9]);
    }
    lVar32 = local_2f8[6];
    if (lVar32 != 0) goto LAB_18004a138;
    pfVar18 = (float *)((longlong)local_2f8 + 0x54);
  }
  else {
LAB_18004a138:
    iVar28 = (int)local_2f8[10];
    if (iVar28 == 0) {
      pfVar18 = (float *)((longlong)local_2f8 + 0x54);
    }
    else {
      uVar10 = uVar10 & ((int)uVar10 >> 0x1f ^ 0xffffffffU);
      if (iVar28 <= (int)uVar10) {
        uVar10 = iVar28 - 1;
      }
      pfVar18 = (float *)(lVar32 + (longlong)(int)uVar10 * 4);
    }
  }
  local_288[8] = *pfVar18;
  uVar10 = param_3[0xe1];
  lVar32 = local_2f0[6];
  if (lVar32 == 0) {
    if ((code *)local_2f0[8] != (code *)0x0) {
      (*(code *)local_2f0[8])((int)local_2f0[9]);
    }
    lVar32 = local_2f0[6];
    if (lVar32 == 0) {
      pfVar18 = (float *)((longlong)local_2f0 + 0x54);
      goto LAB_18004a1c8;
    }
  }
  iVar28 = (int)local_2f0[10];
  if (iVar28 == 0) {
    pfVar18 = (float *)((longlong)local_2f0 + 0x54);
  }
  else {
    uVar10 = uVar10 & ((int)uVar10 >> 0x1f ^ 0xffffffffU);
    if (iVar28 <= (int)uVar10) {
      uVar10 = iVar28 - 1;
    }
    pfVar18 = (float *)(lVar32 + (longlong)(int)uVar10 * 4);
  }
LAB_18004a1c8:
  pfVar21 = local_288;
  local_288[9] = *pfVar18;
  pfVar18 = (float *)FUN_18000f448((longlong)local_2a8,param_3[0xe1]);
  lpMem = local_190;
  pfVar21[10] = *pfVar18;
  pfVar21[0xb] = local_318;
  pfVar21[0xc] = local_2e0._0_4_;
  pfVar21[0xd] = local_2d8._0_4_;
  pfVar21[0xe] = local_2d0._0_4_;
  pfVar21[0xf] = local_300._0_4_;
  pfVar21[0x10] = local_308._0_4_;
  pfVar21[0x11] = local_328._0_4_;
  if (((int)uStack_188 != 0) && (local_190 != (undefined1 *)0x0)) {
    pvVar14 = GetProcessHeap();
    HeapFree(pvVar14,0,lpMem);
    local_190 = (undefined1 *)0x0;
    uStack_188 = 0;
  }
  pcVar26 = local_1c0;
  if (((int)uStack_1b8 != 0) && (local_1c0 != (char *)0x0)) {
    pvVar14 = GetProcessHeap();
    HeapFree(pvVar14,0,pcVar26);
  }
  return;
}

