
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_MGI_Monthly(undefined1 param_1 [16],undefined1 param_2 [16],uint *param_3,
                     undefined8 param_4,undefined8 param_5,undefined8 param_6,undefined8 param_7,
                     undefined8 param_8,undefined8 param_9,undefined8 param_10)

{
  bool bVar1;
  char cVar2;
  longlong lVar3;
  longlong lVar4;
  undefined1 auVar5 [16];
  undefined1 auVar6 [16];
  undefined1 auVar7 [16];
  bool bVar8;
  bool bVar9;
  bool bVar10;
  int iVar11;
  uint uVar13;
  uint uVar12;
  int *piVar14;
  undefined8 *puVar15;
  undefined8 *puVar16;
  HANDLE pvVar17;
  undefined8 **ppuVar18;
  undefined8 uVar19;
  ulonglong uVar20;
  longlong *plVar21;
  uint *puVar22;
  undefined8 extraout_x1;
  uint *extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  undefined8 extraout_x1_10;
  undefined8 extraout_x1_11;
  undefined8 extraout_x1_12;
  undefined8 extraout_x1_13;
  undefined8 extraout_x1_14;
  undefined8 extraout_x1_15;
  undefined8 extraout_x1_16;
  undefined8 extraout_x1_17;
  undefined8 extraout_x1_18;
  undefined8 extraout_x1_19;
  undefined8 extraout_x1_20;
  undefined8 extraout_x1_21;
  undefined8 extraout_x1_22;
  undefined8 extraout_x1_23;
  undefined8 extraout_x1_24;
  undefined8 extraout_x1_25;
  undefined8 extraout_x1_26;
  undefined8 extraout_x1_27;
  undefined8 extraout_x1_28;
  undefined8 extraout_x1_29;
  undefined8 extraout_x1_30;
  undefined8 extraout_x1_31;
  undefined8 extraout_x1_32;
  undefined8 extraout_x1_33;
  undefined8 extraout_x1_34;
  undefined8 extraout_x1_35;
  undefined8 extraout_x1_36;
  undefined8 extraout_x1_37;
  undefined8 extraout_x1_38;
  undefined8 extraout_x1_39;
  undefined8 extraout_x1_40;
  undefined8 extraout_x1_41;
  undefined8 extraout_x1_42;
  undefined8 extraout_x1_43;
  undefined8 extraout_x1_44;
  undefined8 extraout_x1_45;
  undefined8 extraout_x1_46;
  undefined8 extraout_x1_47;
  undefined8 extraout_x1_48;
  undefined8 extraout_x1_49;
  undefined8 extraout_x1_50;
  undefined8 extraout_x1_51;
  ulonglong extraout_x1_52;
  ulonglong extraout_x1_53;
  ulonglong extraout_x1_54;
  ulonglong extraout_x1_55;
  ulonglong extraout_x1_56;
  ulonglong extraout_x1_57;
  ulonglong extraout_x1_58;
  ulonglong extraout_x1_59;
  ulonglong extraout_x1_60;
  ulonglong extraout_x1_61;
  ulonglong extraout_x1_62;
  ulonglong extraout_x1_63;
  ulonglong extraout_x1_64;
  ulonglong extraout_x1_65;
  ulonglong extraout_x1_66;
  undefined8 extraout_x1_67;
  ulonglong extraout_x1_68;
  undefined8 extraout_x1_69;
  undefined8 extraout_x1_70;
  undefined8 extraout_x1_71;
  undefined8 extraout_x1_72;
  ulonglong extraout_x1_73;
  char *pcVar23;
  uint *puVar24;
  int iVar25;
  undefined8 *puVar26;
  float *pfVar27;
  float *pfVar28;
  char *pcVar29;
  uint uVar30;
  longlong lVar31;
  longlong extraout_x11;
  code *extraout_x11_00;
  code *extraout_x11_01;
  code *extraout_x11_02;
  longlong extraout_x12;
  uint *puVar32;
  LPVOID pvVar33;
  uint *puVar34;
  float fVar35;
  undefined8 **ppuVar36;
  undefined8 **lpMem;
  uint *puVar37;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  float fVar38;
  undefined4 extraout_s0_02;
  undefined4 extraout_s0_03;
  undefined4 uVar39;
  float fVar40;
  undefined4 extraout_s0_04;
  undefined4 extraout_var;
  undefined4 extraout_var_00;
  undefined4 extraout_var_01;
  undefined4 extraout_var_02;
  undefined4 extraout_var_03;
  undefined4 uVar41;
  undefined4 extraout_var_04;
  undefined8 extraout_var_05;
  undefined8 extraout_var_06;
  undefined8 extraout_var_07;
  undefined8 extraout_var_08;
  undefined8 extraout_var_09;
  undefined8 extraout_var_10;
  undefined4 uVar42;
  undefined4 uVar43;
  double dVar44;
  undefined1 auVar45 [16];
  byte local_32e;
  uint *local_328;
  uint *local_320;
  uint *local_318;
  uint *local_310;
  undefined8 ***local_308;
  uint *local_2f8;
  uint *local_2f0;
  uint *local_2e0;
  uint *local_2d0;
  uint *local_2c8;
  uint *local_2c0;
  uint *local_2b8;
  uint *local_2b0;
  uint *local_2a8;
  uint *local_2a0;
  uint *local_298;
  undefined8 **local_290;
  undefined8 uStack_288;
  undefined1 *local_280;
  ulonglong local_278;
  uint *local_270;
  uint *local_268;
  float *local_260;
  uint *local_258;
  uint *local_250;
  uint *local_248;
  uint *local_240;
  uint *local_238;
  uint *local_230;
  uint *local_228;
  int *local_220;
  uint *local_218;
  uint *local_210;
  uint *local_208;
  float *local_200;
  uint *local_1f8;
  uint *local_1f0;
  int *local_1e8;
  longlong *local_1e0;
  float *local_1d8;
  float *local_1d0;
  float *local_1c8;
  longlong *local_1c0;
  float *local_1b8;
  undefined8 ***local_1b0;
  uint *local_1a8;
  float *local_1a0;
  float *local_198;
  int *local_190;
  uint *puStack_188;
  uint *local_180;
  undefined8 **local_178;
  undefined8 local_170;
  undefined8 local_168;
  undefined8 *local_160;
  longlong lStack_158;
  undefined8 *local_150;
  longlong lStack_148;
  undefined **local_140 [2];
  undefined1 *local_130;
  undefined8 uStack_128;
  undefined1 *local_120;
  longlong local_110;
  undefined8 uStack_108;
  uint *local_100;
  undefined8 uStack_f8;
  undefined8 local_f0;
  undefined8 uStack_e8;
  undefined8 uStack_e0;
  undefined8 uStack_d8;
  undefined8 local_d0;
  undefined8 uStack_c8;
  undefined8 uStack_c0;
  undefined8 uStack_b8;
  undefined8 local_b0;
  undefined8 uStack_a8;
  undefined8 uStack_a0;
  undefined8 uStack_98;
  undefined8 local_90;
  
  uVar43 = param_2._4_4_;
                    /* 0x55888  14  scsf_MGI_Monthly */
  uVar42 = param_2._0_4_;
  lpMem = (undefined8 **)&DAT_1800d4ecd;
  local_168 = 0xfffffffffffffffe;
  local_120 = &DAT_1800d4ecd;
  local_130 = &DAT_1800d4ecd;
  uStack_128 = 0;
  piVar14 = (int *)(**(code **)(param_3 + 0x62e))(param_1._0_4_,0);
  local_1c8 = (float *)(**(code **)(param_3 + 0x62e))(1);
  local_260 = (float *)(**(code **)(param_3 + 0x62e))(2);
  local_1b8 = (float *)(**(code **)(param_3 + 0x62e))(3);
  local_2a8 = (uint *)(**(code **)(param_3 + 0x62e))(5);
  local_1e8 = (int *)(**(code **)(param_3 + 0x62e))(6);
  puVar15 = (undefined8 *)(**(code **)(param_3 + 0x446))(2);
  local_220 = (int *)*puVar15;
  puVar15 = (undefined8 *)(**(code **)(param_3 + 0x446))(4);
  local_200 = (float *)*puVar15;
  puVar15 = (undefined8 *)(**(code **)(param_3 + 0x446))(5);
  local_328 = (uint *)*puVar15;
  local_1d8 = (float *)(**(code **)(param_3 + 0x630))(2);
  local_1d0 = (float *)(**(code **)(param_3 + 0x630))(3);
  local_1a0 = (float *)(**(code **)(param_3 + 0x630))(6);
  local_198 = (float *)(**(code **)(param_3 + 0x630))(7);
  local_1c0 = (longlong *)(**(code **)(param_3 + 0x634))(0);
  local_1e0 = (longlong *)(**(code **)(param_3 + 0x634))(1);
  lVar31 = *(longlong *)(param_3 + 0x140);
  if (lVar31 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 != 0) goto LAB_180055a1c;
    local_208 = param_3 + 0x14a;
LAB_180055a58:
    local_320 = local_208;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 != 0) goto LAB_180055a88;
    local_210 = param_3 + 0x14a;
LAB_180055ac0:
    local_2f0 = local_210;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 != 0) goto LAB_180055af0;
    local_218 = param_3 + 0x14a;
LAB_180055b2c:
    local_2f8 = local_218;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 != 0) goto LAB_180055b5c;
    local_2b8 = param_3 + 0x14a;
LAB_180055b98:
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 != 0) goto LAB_180055bcc;
    local_230 = param_3 + 0x14a;
LAB_180055c0c:
    local_2c0 = local_230;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 != 0) goto LAB_180055c3c;
    local_2a0 = param_3 + 0x14a;
LAB_180055c78:
    local_2c8 = local_2a0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 != 0) goto LAB_180055ca8;
    local_2d0 = param_3 + 0x14a;
LAB_180055ce4:
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 != 0) goto LAB_180055d14;
    local_250 = param_3 + 0x14a;
LAB_180055d50:
    local_298 = local_250;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 != 0) goto LAB_180055d80;
    local_258 = param_3 + 0x14a;
LAB_180055dbc:
    local_2e0 = local_258;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 != 0) goto LAB_180055dec;
    local_238 = param_3 + 0x14a;
LAB_180055e28:
    local_2b0 = local_238;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 != 0) goto LAB_180055e58;
    local_180 = param_3 + 0x14a;
LAB_180055e94:
    local_318 = local_180;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 != 0) goto LAB_180055ec4;
    local_1b0 = (undefined8 ***)(param_3 + 0x14a);
LAB_180055f00:
    local_308 = local_1b0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 != 0) goto LAB_180055f30;
    local_1a8 = param_3 + 0x14a;
LAB_180055f6c:
    local_310 = local_1a8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
    }
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 != 0) goto LAB_180055f8c;
    local_240 = param_3 + 0x14a;
  }
  else {
LAB_180055a1c:
    uVar12 = param_3[0x148];
    if (uVar12 == 0) {
      local_320 = param_3 + 0x14a;
    }
    else {
      iVar11 = 0;
      if ((int)uVar12 < 1) {
        iVar11 = uVar12 - 1;
      }
      local_320 = (uint *)(lVar31 + (longlong)iVar11 * 0x170);
    }
    local_208 = local_320;
    if (lVar31 == 0) goto LAB_180055a58;
LAB_180055a88:
    uVar12 = param_3[0x148];
    if (uVar12 == 0) {
      local_2f0 = param_3 + 0x14a;
    }
    else {
      iVar11 = 1;
      if (uVar12 - 1 == 0 || (int)uVar12 < 1) {
        iVar11 = uVar12 - 1;
      }
      local_2f0 = (uint *)(lVar31 + (longlong)iVar11 * 0x170);
    }
    local_210 = local_2f0;
    if (lVar31 == 0) goto LAB_180055ac0;
LAB_180055af0:
    uVar12 = param_3[0x148];
    if (uVar12 == 0) {
      local_2f8 = param_3 + 0x14a;
    }
    else {
      iVar11 = 2;
      if ((int)uVar12 < 3) {
        iVar11 = uVar12 - 1;
      }
      local_2f8 = (uint *)(lVar31 + (longlong)iVar11 * 0x170);
    }
    local_218 = local_2f8;
    if (lVar31 == 0) goto LAB_180055b2c;
LAB_180055b5c:
    uVar12 = param_3[0x148];
    if (uVar12 == 0) {
      local_2b8 = param_3 + 0x14a;
    }
    else {
      iVar11 = 3;
      if ((int)uVar12 < 4) {
        iVar11 = uVar12 - 1;
      }
      local_2b8 = (uint *)(lVar31 + (longlong)iVar11 * 0x170);
    }
    if (lVar31 == 0) goto LAB_180055b98;
LAB_180055bcc:
    uVar12 = param_3[0x148];
    if (uVar12 == 0) {
      local_2c0 = param_3 + 0x14a;
    }
    else {
      iVar11 = 4;
      if ((int)uVar12 < 5) {
        iVar11 = uVar12 - 1;
      }
      local_2c0 = (uint *)(lVar31 + (longlong)iVar11 * 0x170);
    }
    local_230 = local_2c0;
    if (lVar31 == 0) goto LAB_180055c0c;
LAB_180055c3c:
    uVar12 = param_3[0x148];
    if (uVar12 == 0) {
      local_2c8 = param_3 + 0x14a;
    }
    else {
      iVar11 = 5;
      if ((int)uVar12 < 6) {
        iVar11 = uVar12 - 1;
      }
      local_2c8 = (uint *)(lVar31 + (longlong)iVar11 * 0x170);
    }
    local_2a0 = local_2c8;
    if (lVar31 == 0) goto LAB_180055c78;
LAB_180055ca8:
    uVar12 = param_3[0x148];
    if (uVar12 == 0) {
      local_2d0 = param_3 + 0x14a;
    }
    else {
      iVar11 = 6;
      if ((int)uVar12 < 7) {
        iVar11 = uVar12 - 1;
      }
      local_2d0 = (uint *)(lVar31 + (longlong)iVar11 * 0x170);
    }
    if (lVar31 == 0) goto LAB_180055ce4;
LAB_180055d14:
    uVar12 = param_3[0x148];
    if (uVar12 == 0) {
      local_298 = param_3 + 0x14a;
    }
    else {
      iVar11 = 7;
      if ((int)uVar12 < 8) {
        iVar11 = uVar12 - 1;
      }
      local_298 = (uint *)(lVar31 + (longlong)iVar11 * 0x170);
    }
    local_250 = local_298;
    if (lVar31 == 0) goto LAB_180055d50;
LAB_180055d80:
    uVar12 = param_3[0x148];
    if (uVar12 == 0) {
      local_2e0 = param_3 + 0x14a;
    }
    else {
      iVar11 = 8;
      if ((int)uVar12 < 9) {
        iVar11 = uVar12 - 1;
      }
      local_2e0 = (uint *)(lVar31 + (longlong)iVar11 * 0x170);
    }
    local_258 = local_2e0;
    if (lVar31 == 0) goto LAB_180055dbc;
LAB_180055dec:
    uVar12 = param_3[0x148];
    if (uVar12 == 0) {
      local_2b0 = param_3 + 0x14a;
    }
    else {
      iVar11 = 9;
      if ((int)uVar12 < 10) {
        iVar11 = uVar12 - 1;
      }
      local_2b0 = (uint *)(lVar31 + (longlong)iVar11 * 0x170);
    }
    local_238 = local_2b0;
    if (lVar31 == 0) goto LAB_180055e28;
LAB_180055e58:
    uVar12 = param_3[0x148];
    if (uVar12 == 0) {
      local_318 = param_3 + 0x14a;
    }
    else {
      iVar11 = 10;
      if ((int)uVar12 < 0xb) {
        iVar11 = uVar12 - 1;
      }
      local_318 = (uint *)(lVar31 + (longlong)iVar11 * 0x170);
    }
    local_180 = local_318;
    if (lVar31 == 0) goto LAB_180055e94;
LAB_180055ec4:
    uVar12 = param_3[0x148];
    if (uVar12 == 0) {
      local_308 = (undefined8 ***)(param_3 + 0x14a);
    }
    else {
      iVar11 = 0xb;
      if ((int)uVar12 < 0xc) {
        iVar11 = uVar12 - 1;
      }
      local_308 = (undefined8 ***)(lVar31 + (longlong)iVar11 * 0x170);
    }
    local_1b0 = local_308;
    if (lVar31 == 0) goto LAB_180055f00;
LAB_180055f30:
    uVar12 = param_3[0x148];
    if (uVar12 == 0) {
      local_310 = param_3 + 0x14a;
    }
    else {
      iVar11 = 0xc;
      if ((int)uVar12 < 0xd) {
        iVar11 = uVar12 - 1;
      }
      local_310 = (uint *)(lVar31 + (longlong)iVar11 * 0x170);
    }
    local_1a8 = local_310;
    if (lVar31 == 0) goto LAB_180055f6c;
LAB_180055f8c:
    uVar12 = param_3[0x148];
    if (uVar12 == 0) {
      local_240 = param_3 + 0x14a;
    }
    else {
      iVar11 = 0xd;
      if ((int)uVar12 < 0xe) {
        iVar11 = uVar12 - 1;
      }
      local_240 = (uint *)(lVar31 + (longlong)iVar11 * 0x170);
    }
  }
  lVar31 = *(longlong *)(param_3 + 0x84);
  if (lVar31 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
    }
    lVar31 = *(longlong *)(param_3 + 0x84);
    if (lVar31 != 0) goto LAB_180055fe4;
    local_228 = param_3 + 0x8e;
  }
  else {
LAB_180055fe4:
    uVar12 = param_3[0x8c];
    if (uVar12 == 0) {
      local_228 = param_3 + 0x8e;
    }
    else {
      iVar11 = 0;
      if ((int)uVar12 < 1) {
        iVar11 = uVar12 - 1;
      }
      local_228 = (uint *)(lVar31 + (longlong)iVar11 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_228 + 0x1a) = 2;
  iVar11 = 1;
  lVar31 = *(longlong *)(param_3 + 0x84);
  if (lVar31 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
    }
    lVar31 = *(longlong *)(param_3 + 0x84);
    if (lVar31 != 0) goto LAB_18005603c;
    local_1f0 = param_3 + 0x8e;
  }
  else {
LAB_18005603c:
    uVar12 = param_3[0x8c];
    if (uVar12 == 0) {
      local_1f0 = param_3 + 0x8e;
    }
    else {
      if ((int)uVar12 < 2) {
        iVar11 = uVar12 - 1;
      }
      local_1f0 = (uint *)(lVar31 + (longlong)iVar11 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_1f0 + 0x1a) = 1;
  iVar11 = 2;
  lVar31 = *(longlong *)(param_3 + 0x84);
  if (lVar31 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
    }
    lVar31 = *(longlong *)(param_3 + 0x84);
    if (lVar31 != 0) goto LAB_180056094;
    puVar34 = param_3 + 0x8e;
  }
  else {
LAB_180056094:
    uVar12 = param_3[0x8c];
    if (uVar12 == 0) {
      puVar34 = param_3 + 0x8e;
    }
    else {
      if ((int)uVar12 < 3) {
        iVar11 = uVar12 - 1;
      }
      puVar34 = (uint *)(lVar31 + (longlong)iVar11 * 0x98);
    }
  }
  *(undefined2 *)((longlong)puVar34 + 0x1a) = 3;
  iVar11 = 3;
  lVar31 = *(longlong *)(param_3 + 0x84);
  if (lVar31 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
    }
    lVar31 = *(longlong *)(param_3 + 0x84);
    if (lVar31 != 0) goto LAB_1800560e8;
    puVar32 = param_3 + 0x8e;
  }
  else {
LAB_1800560e8:
    uVar12 = param_3[0x8c];
    if (uVar12 == 0) {
      puVar32 = param_3 + 0x8e;
    }
    else {
      if ((int)uVar12 < 4) {
        iVar11 = uVar12 - 1;
      }
      puVar32 = (uint *)(lVar31 + (longlong)iVar11 * 0x98);
    }
  }
  *(undefined2 *)((longlong)puVar32 + 0x1a) = 4;
  iVar11 = 4;
  lVar31 = *(longlong *)(param_3 + 0x84);
  if (lVar31 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
    }
    lVar31 = *(longlong *)(param_3 + 0x84);
    if (lVar31 != 0) goto LAB_18005613c;
    local_268 = param_3 + 0x8e;
  }
  else {
LAB_18005613c:
    uVar12 = param_3[0x8c];
    if (uVar12 == 0) {
      local_268 = param_3 + 0x8e;
    }
    else {
      if ((int)uVar12 < 5) {
        iVar11 = uVar12 - 1;
      }
      local_268 = (uint *)(lVar31 + (longlong)iVar11 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_268 + 0x1a) = 5;
  iVar11 = 5;
  lVar31 = *(longlong *)(param_3 + 0x84);
  if (lVar31 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
    }
    lVar31 = *(longlong *)(param_3 + 0x84);
    if (lVar31 != 0) goto LAB_180056194;
    local_248 = param_3 + 0x8e;
  }
  else {
LAB_180056194:
    uVar12 = param_3[0x8c];
    if (uVar12 == 0) {
      local_248 = param_3 + 0x8e;
    }
    else {
      if ((int)uVar12 < 6) {
        iVar11 = uVar12 - 1;
      }
      local_248 = (uint *)(lVar31 + (longlong)iVar11 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_248 + 0x1a) = 6;
  iVar11 = 6;
  lVar31 = *(longlong *)(param_3 + 0x84);
  if (lVar31 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
    }
    lVar31 = *(longlong *)(param_3 + 0x84);
    if (lVar31 != 0) goto LAB_1800561ec;
    local_270 = param_3 + 0x8e;
  }
  else {
LAB_1800561ec:
    uVar12 = param_3[0x8c];
    if (uVar12 == 0) {
      local_270 = param_3 + 0x8e;
    }
    else {
      if ((int)uVar12 < 7) {
        iVar11 = uVar12 - 1;
      }
      local_270 = (uint *)(lVar31 + (longlong)iVar11 * 0x98);
    }
  }
  *(undefined2 *)((longlong)local_270 + 0x1a) = 7;
  local_140[0] = PitSessions::vftable;
  local_150 = (undefined8 *)0x0;
  lStack_148 = 0;
  local_150 = (undefined8 *)FUN_180096150(0x18);
  *local_150 = local_150;
  local_150[1] = local_150;
  local_160 = (undefined8 *)0x0;
  lStack_158 = 0;
  local_160 = (undefined8 *)FUN_180096150(0x18);
  *local_160 = local_160;
  local_160[1] = local_160;
  uVar12 = 0;
  local_1f8 = puVar32;
  local_190 = piVar14;
  puStack_188 = puVar34;
  do {
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 == 0) {
      if (*(code **)(param_3 + 0x144) != (code *)0x0) {
        (**(code **)(param_3 + 0x144))(param_3[0x146]);
      }
      lVar31 = *(longlong *)(param_3 + 0x140);
      if (lVar31 != 0) goto LAB_1800562a4;
      puVar34 = param_3 + 0x14a;
    }
    else {
LAB_1800562a4:
      uVar13 = param_3[0x148];
      if (uVar13 == 0) {
        puVar34 = param_3 + 0x14a;
      }
      else {
        uVar30 = uVar12 & ((int)uVar12 >> 0x1f ^ 0xffffffffU);
        if ((int)uVar13 <= (int)uVar30) {
          uVar30 = uVar13 - 1;
        }
        puVar34 = (uint *)(lVar31 + (longlong)(int)uVar30 * 0x170);
      }
    }
    puVar15 = local_150;
    if (lStack_148 == 0xaaaaaaaaaaaaaaa) goto LAB_18005872c;
    local_178 = &local_150;
    local_170 = 0;
    puVar16 = (undefined8 *)FUN_180096150(0x18);
    puVar16[2] = puVar34;
    local_170 = 0;
    lStack_148 = lStack_148 + 1;
    puVar26 = (undefined8 *)puVar15[1];
    *puVar16 = puVar15;
    puVar16[1] = puVar26;
    puVar15[1] = puVar16;
    *puVar26 = puVar16;
    lVar31 = *(longlong *)(param_3 + 0x140);
    if (lVar31 == 0) {
      if (*(code **)(param_3 + 0x144) != (code *)0x0) {
        (**(code **)(param_3 + 0x144))(param_3[0x146]);
      }
      lVar31 = *(longlong *)(param_3 + 0x140);
      if (lVar31 != 0) goto LAB_180056338;
      puVar34 = param_3 + 0x14a;
    }
    else {
LAB_180056338:
      uVar13 = param_3[0x148];
      if (uVar13 == 0) {
        puVar34 = param_3 + 0x14a;
      }
      else {
        uVar30 = uVar12 & ((int)uVar12 >> 0x1f ^ 0xffffffffU);
        if ((int)uVar13 <= (int)uVar30) {
          uVar30 = uVar13 - 1;
        }
        puVar34 = (uint *)(lVar31 + (longlong)(int)uVar30 * 0x170);
      }
    }
    puVar15 = local_160;
    if (lStack_158 == 0xaaaaaaaaaaaaaaa) {
LAB_18005872c:
                    /* WARNING: Subroutine does not return */
      FUN_1800941a8(0x1800d5268);
    }
    local_290 = &local_160;
    uStack_288 = 0;
    puVar16 = (undefined8 *)FUN_180096150(0x18);
    puVar37 = puStack_188;
    piVar14 = local_190;
    puVar32 = local_1f8;
    puVar16[2] = puVar34;
    uStack_288 = 0;
    lStack_158 = lStack_158 + 1;
    puVar26 = (undefined8 *)puVar15[1];
    *puVar16 = puVar15;
    puVar16[1] = puVar26;
    puVar15[1] = puVar16;
    *puVar26 = puVar16;
    uVar12 = uVar12 + 1;
  } while ((int)uVar12 < 0xe);
  if (param_3[0x2b] != 0) {
    FUN_1800079f8((longlong *)(param_3 + 0x46),0x1800d8b40,0xd);
    param_3[0x49] = 1;
    if ((*(char **)(param_3 + 0xce) != (char *)0x0) && (**(char **)(param_3 + 0xce) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0xce),0x1800d4ecd,0);
      param_3[0xd1] = 1;
    }
    local_290 = (undefined8 **)&DAT_1800d4ecd;
    uStack_288 = 0;
    local_280 = &DAT_1800d4ecd;
    pvVar17 = GetProcessHeap();
    ppuVar18 = (undefined8 **)HeapAlloc(pvVar17,0,0x15);
    if (ppuVar18 == (undefined8 **)0x0) {
      local_290 = (undefined8 **)&DAT_1800d4ecd;
      ppuVar36 = lpMem;
    }
    else {
      param_6 = 0x14;
      ppuVar18[1] = (undefined8 *)0x0;
      *ppuVar18 = (undefined8 *)0x0;
      *(undefined4 *)(ppuVar18 + 2) = 0;
      *(char *)((longlong)ppuVar18 + 0x14) = '\0';
      local_290 = ppuVar18;
      FUN_180099d78((char *)ppuVar18,0x15,0x1800d6c40,0x14);
      uStack_288 = 0x100000001;
      ppuVar36 = ppuVar18;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_290);
    if ((ppuVar18 != (undefined8 **)0x0) && (ppuVar36 != (undefined8 **)0x0)) {
      pvVar17 = GetProcessHeap();
      HeapFree(pvVar17,0,ppuVar36);
    }
    local_290 = (undefined8 **)&DAT_1800d4ecd;
    uStack_288 = 0;
    local_280 = &DAT_1800d4ecd;
    pvVar17 = GetProcessHeap();
    ppuVar18 = (undefined8 **)HeapAlloc(pvVar17,0,0x8d);
    if (ppuVar18 == (undefined8 **)0x0) {
      local_290 = (undefined8 **)&DAT_1800d4ecd;
      ppuVar36 = lpMem;
    }
    else {
      param_6 = 0x8c;
      ppuVar18[1] = (undefined8 *)0x0;
      *ppuVar18 = (undefined8 *)0x0;
      ppuVar18[3] = (undefined8 *)0x0;
      ppuVar18[2] = (undefined8 *)0x0;
      ppuVar18[5] = (undefined8 *)0x0;
      ppuVar18[4] = (undefined8 *)0x0;
      ppuVar18[7] = (undefined8 *)0x0;
      ppuVar18[6] = (undefined8 *)0x0;
      ppuVar18[9] = (undefined8 *)0x0;
      ppuVar18[8] = (undefined8 *)0x0;
      ppuVar18[0xb] = (undefined8 *)0x0;
      ppuVar18[10] = (undefined8 *)0x0;
      ppuVar18[0xd] = (undefined8 *)0x0;
      ppuVar18[0xc] = (undefined8 *)0x0;
      ppuVar18[0xf] = (undefined8 *)0x0;
      ppuVar18[0xe] = (undefined8 *)0x0;
      ppuVar18[0x10] = (undefined8 *)0x0;
      *(undefined4 *)(ppuVar18 + 0x11) = 0;
      *(char *)((longlong)ppuVar18 + 0x8c) = '\0';
      local_290 = ppuVar18;
      FUN_180099d78((char *)ppuVar18,0x8d,0x1800d6bb0,0x8c);
      uStack_288 = 0x100000001;
      ppuVar36 = ppuVar18;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_290);
    uVar39 = extraout_s0_00;
    uVar41 = extraout_var_00;
    uVar19 = extraout_var_06;
    if ((ppuVar18 != (undefined8 **)0x0) && (ppuVar36 != (undefined8 **)0x0)) {
      pvVar17 = GetProcessHeap();
      HeapFree(pvVar17,0,ppuVar36);
      local_290 = (undefined8 **)0x0;
      uStack_288 = 0;
      uVar39 = extraout_s0_01;
      uVar41 = extraout_var_01;
      uVar19 = extraout_var_07;
    }
    ppuVar18 = *(undefined8 ***)(param_3 + 0x46);
    if (*(undefined8 ***)(param_3 + 0x46) == (undefined8 **)0x0) {
      ppuVar18 = lpMem;
    }
    auVar45._4_4_ = uVar41;
    auVar45._0_4_ = uVar39;
    auVar45._8_8_ = uVar19;
    FUN_180026368(auVar45,CONCAT44(uVar43,uVar42),(undefined8 *)(param_3 + 0xce),0x1800d6c60,
                  ppuVar18,param_6,param_7,param_8,param_9,param_10);
    local_290 = (undefined8 **)&DAT_1800d4ecd;
    uStack_288 = 0;
    local_280 = &DAT_1800d4ecd;
    pvVar17 = GetProcessHeap();
    ppuVar18 = (undefined8 **)HeapAlloc(pvVar17,0,7);
    if (ppuVar18 == (undefined8 **)0x0) {
      local_290 = (undefined8 **)&DAT_1800d4ecd;
    }
    else {
      *(undefined4 *)ppuVar18 = 0;
      ((char *)((longlong)ppuVar18 + 4))[0] = '\0';
      ((char *)((longlong)ppuVar18 + 4))[1] = '\0';
      *(char *)((longlong)ppuVar18 + 6) = '\0';
      local_290 = ppuVar18;
      FUN_180099d78((char *)ppuVar18,7,0x1800d6c58,6);
      uStack_288 = 0x100000001;
      lpMem = ppuVar18;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_290);
    if ((ppuVar18 != (undefined8 **)0x0) && (lpMem != (undefined8 **)0x0)) {
      pvVar17 = GetProcessHeap();
      HeapFree(pvVar17,0,lpMem);
      local_290 = (undefined8 **)0x0;
      uStack_288 = 0;
    }
    param_3[0xdf] = 0;
    param_3[1] = 0;
    param_3[0x2c] = 1;
    param_3[4] = 0;
    param_3[0x32e] = 1;
    param_3[0x276] = 1;
    FUN_1800079f8((longlong *)local_320,0x1800d8b58,5);
    puVar34 = local_240;
    local_208[3] = 1;
    local_208[9] = 0x20000;
    local_208[6] = 0x6b5e56;
    local_208[0x38] = 0x21;
    *(undefined2 *)(local_208 + 10) = 1;
    FUN_1800079f8((longlong *)local_240,0x1800d8b50,5);
    puVar34[3] = 1;
    puVar34[9] = 0x20005;
    puVar34[0x38] = 0x21;
    *(undefined2 *)(puVar34 + 10) = 1;
    puVar34[6] = 0xc0c0c0;
    FUN_1800079f8((longlong *)local_2f0,0x1800d8b68,5);
    local_210[3] = 1;
    local_210[0x38] = 0x21;
    local_210[9] = 3;
    *(undefined2 *)(local_210 + 10) = 1;
    local_210[6] = 0x5757d0;
    FUN_1800079f8((longlong *)local_2f8,0x1800d8b60,5);
    local_218[3] = 1;
    local_218[0x38] = 0x21;
    local_218[9] = 3;
    *(undefined2 *)(local_218 + 10) = 1;
    local_218[6] = 0x5757d0;
    FUN_1800079f8((longlong *)local_2b8,0x1800d8b78,5);
    local_2b8[3] = 1;
    local_2b8[6] = 0x80ff;
    local_2b8[7] = 0x80ff;
    local_2b8[8] = 1;
    local_2b8[9] = 0x20003;
    local_2b8[0x38] = 0x21;
    *(undefined2 *)(local_2b8 + 10) = 1;
    FUN_1800079f8((longlong *)local_230,0x1800d8b70,6);
    local_2c0[3] = 1;
    local_2c0[0x38] = 0x21;
    *(undefined2 *)(local_2c0 + 10) = 1;
    local_2c0[6] = 0x8fff20;
    local_2c0[7] = 0x8fff20;
    local_2c0[8] = 1;
    local_2c0[9] = 3;
    local_2c8[6] = 0xf0a60d;
    local_2c8[7] = 0x221f0;
    local_2c8[8] = 1;
    local_2c8[9] = 0x2001c;
    *(undefined2 *)(local_2c8 + 10) = 1;
    local_2d0[6] = 0xf0a60d;
    local_2d0[7] = 0x221f0;
    local_2d0[8] = 1;
    local_2d0[9] = 0x2001b;
    *(undefined2 *)(local_2d0 + 10) = 1;
    FUN_1800079f8((longlong *)local_2b0,0x1800d8b88,7);
    local_238[3] = 1;
    local_238[0x38] = 0x21;
    *(undefined2 *)(local_238 + 9) = 5;
    *(undefined2 *)(local_238 + 10) = 1;
    local_238[6] = 0xc0c0c0;
    FUN_1800079f8((longlong *)local_298,0x1800d8b80,7);
    local_250[3] = 1;
    local_250[0x38] = 0x21;
    *(undefined2 *)(local_250 + 9) = 5;
    *(undefined2 *)(local_250 + 10) = 1;
    local_250[6] = 0xc0c0c0;
    FUN_1800079f8((longlong *)local_258,0x1800d8b98,7);
    local_2e0[3] = 1;
    local_2e0[0x38] = 0x21;
    *(undefined2 *)(local_2e0 + 9) = 5;
    *(undefined2 *)(local_2e0 + 10) = 1;
    local_2e0[6] = 0xc0c0c0;
    FUN_1800079f8((longlong *)local_310,0x1800d8b90,6);
    local_1a8[3] = 1;
    local_1a8[0x38] = 0x21;
    *(undefined2 *)(local_1a8 + 9) = 5;
    *(undefined2 *)(local_1a8 + 10) = 1;
    local_1a8[6] = 0xc0c0c0;
    FUN_1800079f8((longlong *)local_318,0x1800d8ba8,6);
    local_180[3] = 1;
    local_180[0x38] = 0x21;
    local_180[9] = 3;
    *(undefined2 *)(local_180 + 10) = 1;
    local_180[6] = 0x5757d0;
    FUN_1800079f8((longlong *)local_308,0x1800d8ba0,6);
    *(undefined4 *)((longlong)local_1b0 + 0xc) = 1;
    *(undefined4 *)(local_1b0 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)local_1b0 + 0x24) = 3;
    *(undefined2 *)(local_1b0 + 5) = 1;
    *(undefined4 *)(local_1b0 + 3) = 0x5757d0;
    puVar15 = FUN_180029680((longlong *)local_140,&local_290);
    puVar34 = local_228;
    if (0xf < (ulonglong)puVar15[3]) {
      puVar15 = (undefined8 *)*puVar15;
    }
    if (*(code **)(local_228 + 0x14) != (code *)0x0) {
      (**(code **)(local_228 + 0x14))(local_228[0x13],puVar15);
      *(undefined1 *)(puVar34 + 6) = 0x16;
    }
    if (0xf < local_278) {
      FUN_1800966b8(local_290);
    }
    puVar34 = local_1f0;
    local_290 = (undefined8 **)((ulonglong)local_290 & 0xffffffffffffff00);
    local_280 = (undefined1 *)0x0;
    local_278 = 0xf;
    *(undefined1 *)(local_228 + 6) = 0x16;
    local_228[7] = 0;
    FUN_1800079f8((longlong *)local_1f0,0x1800d6d30,0x14);
    puVar34[3] = 1;
    *(undefined1 *)(puVar34 + 6) = 5;
    puVar34[7] = 1;
    FUN_1800079f8((longlong *)puVar37,0x1800d8a40,0x1f);
    puVar37[3] = 1;
    if (*(code **)(puVar37 + 0x14) != (code *)0x0) {
      (**(code **)(puVar37 + 0x14))(puVar37[0x13],"Default;Use Proximity Range");
    }
    *(undefined1 *)(puVar37 + 6) = 0x16;
    puVar37[7] = 0;
    FUN_1800079f8((longlong *)puVar32,0x1800d8a80,0x28);
    puVar34 = local_248;
    puVar32[3] = 1;
    *(undefined1 *)(puVar32 + 6) = 0xb;
    puVar32[7] = 0x28;
    puVar32[0xf] = 0x7fffffff;
    puVar32[0xb] = 0;
    *(undefined1 *)(local_268 + 6) = 0xb;
    local_268[0xf] = 0x96;
    local_268[7] = 0;
    local_268[0xb] = 0;
    FUN_1800079f8((longlong *)local_248,0x1800d8b00,0x29);
    puVar34[3] = 1;
    cVar2 = (char)puVar34[6];
    if ((((cVar2 == '\x1a') || ((byte)(cVar2 - 0x1bU) < 2)) && ((char)local_248[7] != '\0')) &&
       (pvVar33 = *(LPVOID *)(local_248 + 8), pvVar33 != (LPVOID)0x0)) {
      pvVar17 = GetProcessHeap();
      HeapFree(pvVar17,0,pvVar33);
    }
    puVar34 = local_270;
    *(undefined1 *)(local_248 + 6) = 0x1a;
    local_248[8] = 0;
    local_248[9] = 0;
    *(undefined1 *)(local_248 + 7) = 1;
    FUN_1800079f8((longlong *)local_270,0x1800d8ad0,0x28);
    puVar34[3] = 1;
    *(undefined1 *)(puVar34 + 6) = 0xb;
    puVar34[0xf] = 300;
    puVar34[7] = 0;
    puVar34[0xb] = 0;
    goto LAB_180058684;
  }
  if (param_3[3] == 0) {
    auVar5._4_4_ = extraout_var;
    auVar5._0_4_ = extraout_s0;
    auVar5._8_8_ = extraout_var_05;
    uVar19 = FUN_1800254e8(auVar5,CONCAT44(uVar43,uVar42),(longlong)param_3,extraout_x1,param_5,
                           param_6,param_7,param_8,param_9,param_10);
    *piVar14 = (int)uVar19;
  }
  if ((*piVar14 != 0) || (iVar11 = (**(code **)(param_3 + 0x3ec))(param_3[0x270]), iVar11 == 0))
  goto LAB_180058684;
  local_238 = *(uint **)(param_3 + 0x1c);
  if ((char)*local_238 == '\0') {
    *(char *)local_238 = '\x01';
  }
  local_32e = 0;
  fVar35 = (float)param_3[3];
  if (*local_2a8 == 1) {
    if (param_3[0x47e] != 0) {
      *local_2a8 = 0;
    }
    goto LAB_180058684;
  }
  if ((param_3[0x47e] != 0) && (fVar35 == 0.0)) {
    *local_2a8 = 0;
  }
  uVar20 = FUN_180026708((longlong)local_268);
  if ((int)uVar20 == 0) {
    uVar12 = FUN_180026550((longlong)puVar37);
    local_238[1] = uVar12;
  }
  else {
    uVar20 = FUN_180026708(extraout_x11);
    if (param_3[0x36e] == (uint)uVar20) {
      local_32e = 1;
      fVar35 = 0.0;
      local_238[1] = (uint)(param_3[0x36f] == 4);
      if (param_3[0x47e] == 0) {
        param_3[0x433] = 0;
      }
    }
  }
  puVar34 = local_268;
  uVar20 = FUN_180026708((longlong)local_268);
  if ((int)uVar20 == 0) {
LAB_180056c00:
    if (fVar35 == 0.0) goto LAB_180056c04;
  }
  else {
    FUN_180026708((longlong)puVar34);
    uVar12 = (*extraout_x11_00)();
    if (uVar12 == local_238[1]) goto LAB_180056c00;
    FUN_180026708((longlong)puVar34);
    (*extraout_x11_01)();
    fVar35 = 0.0;
    if (param_3[0x47e] == 0) {
      param_3[0x433] = 0;
    }
LAB_180056c04:
    *local_1c8 = 0.0;
    *local_260 = 0.0;
    *local_1b8 = -NAN;
    *local_1e8 = 0;
    local_32e = 1;
    if (local_220 != (int *)0x0) {
      FUN_18004a2d0(local_220);
      (**(code **)(param_3 + 0x448))(2,0);
    }
    auVar45 = FUN_180096150(0x80);
    local_2a8 = auVar45._0_8_;
    uVar42 = 0x40000000;
    uVar43 = 0;
    local_2a8[2] = 0;
    local_2a8[3] = 0;
    local_2a8[0] = 0;
    local_2a8[1] = 0;
    local_2a8[6] = 0;
    local_2a8[7] = 0;
    local_2a8[4] = 0;
    local_2a8[5] = 0;
    local_2a8[10] = 0;
    local_2a8[0xb] = 0;
    local_2a8[8] = 0;
    local_2a8[9] = 0;
    local_2a8[0xe] = 0;
    local_2a8[0xf] = 0;
    local_2a8[0xc] = 0;
    local_2a8[0xd] = 0;
    local_2a8[0x12] = 0;
    local_2a8[0x13] = 0;
    local_2a8[0x10] = 0;
    local_2a8[0x11] = 0;
    local_2a8[0x16] = 0;
    local_2a8[0x17] = 0;
    local_2a8[0x14] = 0;
    local_2a8[0x15] = 0;
    local_2a8[0x1a] = 0;
    local_2a8[0x1b] = 0;
    local_2a8[0x18] = 0;
    local_2a8[0x19] = 0;
    local_2a8[0x1e] = 0;
    local_2a8[0x1f] = 0;
    local_2a8[0x1c] = 0;
    local_2a8[0x1d] = 0;
    local_220 = FUN_18001cd30(0x3e4ccccd,0x40000000,local_2a8,auVar45._8_8_,1,0);
    if (local_220 == (int *)0x0) goto LAB_180058684;
    (**(code **)(param_3 + 0x448))(2,local_220);
    if (local_200 != (float *)0x0) {
      FUN_1800966b8(local_200);
      (**(code **)(param_3 + 0x448))(4,0);
    }
    local_2a8 = (uint *)FUN_180096150(0x28);
    auVar45 = FUN_1800043e0(0.0,(longlong *)&puStack_188);
    local_200 = auVar45._8_8_;
    local_200[4] = 0.0;
    local_200[5] = 0.0;
    *(undefined8 *)(local_200 + 4) = *auVar45._0_8_;
    *(undefined1 *)(local_200 + 8) = 1;
    *(undefined1 *)(local_200 + 6) = 0;
    local_200[0] = 0.0;
    local_200[1] = 0.0;
    local_200[2] = 0.0;
    local_200[3] = 0.0;
    local_200[7] = 0.0;
    (**(code **)(param_3 + 0x448))(4);
    if (local_328 != (uint *)0x0) {
      FUN_1800966b8(local_328);
      (**(code **)(param_3 + 0x448))(5,0);
    }
    local_2a8 = (uint *)FUN_180096150(0x10);
    local_1f8 = local_2a8;
    uVar20 = FUN_180026708((longlong)puVar32);
    extraout_x1_00[0] = 10;
    extraout_x1_00[1] = 0xb;
    extraout_x1_00[2] = (uint)uVar20;
    (**(code **)(param_3 + 0x448))(5);
    puVar37 = local_230;
    puVar32 = local_258;
    puVar34 = local_298;
    iVar11 = 0;
    do {
      uVar12 = 0;
      if (0 < (int)*param_3) {
        local_250 = (uint *)CONCAT44(local_250._4_4_,iVar11);
        do {
          lVar31 = *(longlong *)(param_3 + 0x140);
          if (lVar31 == 0) {
            if (*(code **)(param_3 + 0x144) != (code *)0x0) {
              (**(code **)(param_3 + 0x144))(param_3[0x146]);
            }
            lVar31 = *(longlong *)(param_3 + 0x140);
            if (lVar31 != 0) goto LAB_180056dbc;
            puVar22 = param_3 + 0x14a;
          }
          else {
LAB_180056dbc:
            uVar13 = param_3[0x148];
            if (uVar13 == 0) {
              puVar22 = param_3 + 0x14a;
            }
            else {
              iVar11 = (int)local_250;
              if ((int)uVar13 <= (int)local_250) {
                iVar11 = uVar13 - 1;
              }
              puVar22 = (uint *)(lVar31 + (longlong)iVar11 * 0x170);
            }
          }
          lVar31 = *(longlong *)(puVar22 + 0xc);
          if (lVar31 == 0) {
            if (*(code **)(puVar22 + 0x10) != (code *)0x0) {
              (**(code **)(puVar22 + 0x10))(puVar22[0x12]);
            }
            lVar31 = *(longlong *)(puVar22 + 0xc);
            if (lVar31 != 0) goto LAB_180056e74;
            puVar22 = puVar22 + 0x15;
          }
          else {
LAB_180056e74:
            uVar13 = puVar22[0x14];
            if (uVar13 == 0) {
              puVar22 = puVar22 + 0x15;
            }
            else {
              uVar30 = uVar12 & ((int)uVar12 >> 0x1f ^ 0xffffffffU);
              if ((int)uVar13 <= (int)uVar30) {
                uVar30 = uVar13 - 1;
              }
              puVar22 = (uint *)(lVar31 + (longlong)(int)uVar30 * 4);
            }
          }
          *puVar22 = 0;
          uVar12 = uVar12 + 1;
        } while ((int)uVar12 < (int)*param_3);
        iVar11 = (int)local_250;
      }
      puVar15 = local_150;
      puVar24 = local_270;
      puVar22 = local_2a8;
      iVar11 = iVar11 + 1;
    } while (iVar11 < 0x3c);
    local_298 = puVar34;
    local_258 = puVar32;
    local_230 = puVar37;
    for (puVar16 = (undefined8 *)*local_150; puVar16 != puVar15; puVar16 = (undefined8 *)*puVar16) {
      switch((char)puVar24[6]) {
      default:
        uVar12 = 0;
        break;
      case '\x01':
      case '\x03':
      case '\x04':
      case '\x06':
      case '\v':
      case '\r':
      case '\x0e':
      case '\x0f':
      case '\x10':
      case '\x11':
      case '\x13':
      case '\x16':
      case '\x18':
        uVar12 = puVar24[7];
        break;
      case '\x02':
        fVar40 = (float)puVar24[7];
        if (0.0 <= fVar40) {
          uVar12 = (uint)(fVar40 + 0.5);
        }
        else {
          uVar12 = (uint)(fVar40 - 0.5);
        }
        break;
      case '\x05':
        uVar12 = (uint)(puVar24[7] != 0);
        break;
      case '\b':
        if (*(double *)(puVar24 + 7) == 0.0) {
          uVar12 = 0;
        }
        else {
          dVar44 = *(double *)(puVar24 + 7) * 86400000000.0;
          if (0.0 <= dVar44) {
            uVar12 = (uint)((longlong)(dVar44 + 0.5) / 86400000000);
          }
          else {
            uVar12 = (uint)((longlong)(dVar44 - 0.5) / 86400000000);
          }
        }
        break;
      case '\t':
      case '\x19':
        if (*(double *)(puVar24 + 7) == 0.0) {
          uVar12 = 0;
        }
        else {
          dVar44 = *(double *)(puVar24 + 7) * 86400000000.0;
          if (0.0 <= dVar44) {
            dVar44 = dVar44 + 0.5;
          }
          else {
            dVar44 = dVar44 - 0.5;
          }
          lVar3 = ((longlong)dVar44 % 86400000000) / 1000000;
          lVar31 = 0;
          if (lVar3 < 0x15180) {
            lVar31 = lVar3;
          }
          uVar12 = (uint)lVar31;
        }
        break;
      case '\x17':
        dVar44 = *(double *)(puVar24 + 7);
        if (0.0 <= dVar44) {
          uVar12 = (uint)(dVar44 + 0.5);
        }
        else {
          uVar12 = (uint)(dVar44 - 0.5);
        }
      }
      FUN_1800067b8(puVar22,(int *)param_3,puVar16[2],uVar12);
    }
    uVar20 = FUN_180026708((longlong)local_268);
    if ((int)uVar20 != 0) {
      uVar20 = FUN_180026708(extraout_x12);
      (*extraout_x11_02)(uVar20,"MGI Proximity");
    }
    uStack_108 = 0;
    local_110 = 0;
    uStack_f8 = 0;
    local_100 = (uint *)0x0;
    uStack_e8 = 0;
    local_f0 = 0;
    uStack_d8 = 0;
    uStack_e0 = 0;
    uStack_c8 = 0;
    local_d0 = 0;
    uStack_b8 = 0;
    uStack_c0 = 0;
    uStack_a8 = 0;
    local_b0 = 0;
    uStack_98 = 0;
    uStack_a0 = 0;
    local_90 = 0;
    uVar12 = FUN_180026550((longlong)local_228);
    FUN_180011fd0(&local_110,(longlong)param_3,0,uVar12);
    puVar34 = local_100;
    local_2a8 = local_100;
    FUN_180012498((longlong *)&local_1f8,(longlong *)&local_2a8);
    lVar31 = 86400000000;
    if ((int)((longlong)puVar34 / 86400000000) == (int)((longlong)local_1f8 / 86400000000)) {
      lVar3 = ((longlong)puVar34 / 86400000000) * 86400000000;
    }
    else {
      local_2a8 = puVar34;
      plVar21 = (longlong *)FUN_180012498((longlong *)&local_1f8,(longlong *)&local_2a8);
      lVar3 = 0;
      if (lVar31 != 0) {
        lVar3 = *plVar21 / lVar31;
      }
      lVar3 = lVar3 * lVar31;
    }
    *local_1c0 = lVar3 + 46800000000;
    lVar4 = 0;
    if (lVar31 != 0) {
      lVar4 = (lVar3 + 46800000000) / lVar31;
    }
    *local_1e0 = lVar4 * lVar31 + 54000000000;
    local_328 = extraout_x1_00;
  }
  puVar34 = local_230;
  if (param_3[0x1b] == 0) {
    if (((local_220 != (int *)0x0) && (local_200 != (float *)0x0)) && (local_328 != (uint *)0x0)) {
      bVar9 = false;
      if ((int)fVar35 < (int)*param_3) {
        local_268 = (uint *)CONCAT44(local_268._4_4_,(int)fVar35 - 1);
        local_250 = (uint *)CONCAT44(local_250._4_4_,(int)fVar35 - 1);
        bVar9 = false;
        local_218 = local_2b0 + 0xc;
        local_270 = (uint *)CONCAT44(local_270._4_4_,fVar35);
        local_210 = local_298 + 0xc;
        local_208 = local_258 + 0xc;
        puVar32 = local_258;
        puVar37 = local_298;
        do {
          uStack_108 = 0;
          local_110 = 0;
          uStack_f8 = 0;
          local_100 = (uint *)0x0;
          uStack_e8 = 0;
          local_f0 = 0;
          uStack_d8 = 0;
          uStack_e0 = 0;
          uStack_c8 = 0;
          local_d0 = 0;
          uStack_b8 = 0;
          uStack_c0 = 0;
          uStack_a8 = 0;
          local_b0 = 0;
          uStack_98 = 0;
          uStack_a0 = 0;
          local_90 = 0;
          uVar12 = FUN_180026550((longlong)local_228);
          FUN_180011fd0(&local_110,(longlong)param_3,(int)fVar35,uVar12);
          puVar22 = local_100;
          plVar21 = local_1c0;
          bVar1 = false;
          fVar40 = *local_1b8;
          bVar10 = *local_1c0 <= (longlong)local_100;
          lVar31 = *local_1e0;
          uVar12 = (uint)(lVar31 <= (longlong)local_100);
          uVar19 = extraout_x1_01;
          bVar8 = false;
          if (fVar40 == fVar35) {
LAB_1800573d8:
            fVar38 = (float)FUN_180006bb0((int *)local_328,uVar19,(longlong)local_310,
                                          (int)*local_260);
            FUN_1800069e8(fVar38,(int *)local_328,extraout_x1_07,(longlong)local_310,(int)fVar35);
            fVar38 = (float)FUN_180006bb0((int *)local_328,extraout_x1_08,(longlong)local_318,
                                          (int)*local_260);
            FUN_1800069e8(fVar38,(int *)local_328,extraout_x1_09,(longlong)local_318,(int)fVar35);
            fVar38 = (float)FUN_180006bb0((int *)local_328,extraout_x1_10,(longlong)local_308,
                                          (int)*local_260);
            uVar19 = extraout_x1_11;
          }
          else {
            if (bVar10) {
              *local_1e8 = 1;
              local_2a8 = local_100 + 0x699caab800;
              uVar12 = (uint)(lVar31 <= (longlong)local_100);
              auVar45 = FUN_180012498((longlong *)&local_1f8,(longlong *)&local_2a8);
              uVar19 = auVar45._8_8_;
              *plVar21 = (*auVar45._0_8_ / 86400000000) * 86400000000 + 46800000000;
            }
            bVar1 = uVar12 != 0;
            if (bVar1) {
              *local_1e8 = 0;
              local_2a8 = puVar22 + 0x699caab800;
              auVar45 = FUN_180012498((longlong *)&local_190,(longlong *)&local_2a8);
              uVar19 = auVar45._8_8_;
              *local_1e0 = (*auVar45._0_8_ / 86400000000) * 86400000000 + 54000000000;
            }
            bVar8 = bVar10;
            if (!bVar10) goto LAB_1800573d8;
            fVar38 = (float)FUN_180006bb0((int *)local_328,uVar19,(longlong)local_2b0,(int)local_268
                                         );
            FUN_1800069e8(fVar38,(int *)local_328,extraout_x1_02,(longlong)local_310,(int)fVar35);
            fVar38 = (float)FUN_180006bb0((int *)local_328,extraout_x1_03,(longlong)puVar37,
                                          (int)local_250);
            FUN_1800069e8(fVar38,(int *)local_328,extraout_x1_04,(longlong)local_318,(int)fVar35);
            fVar38 = (float)FUN_180006bb0((int *)local_328,extraout_x1_05,(longlong)puVar32,
                                          (int)fVar35 + -1);
            uVar19 = extraout_x1_06;
          }
          FUN_1800069e8(fVar38,(int *)local_328,uVar19,(longlong)local_308,(int)fVar35);
          uVar39 = extraout_s0_02;
          uVar41 = extraout_var_02;
          uVar19 = extraout_var_08;
          if (bVar1) {
            FUN_18001cec0(local_220,extraout_x1_12,0);
            uVar39 = extraout_s0_03;
            uVar41 = extraout_var_03;
            uVar19 = extraout_var_09;
          }
          auVar6._4_4_ = uVar41;
          auVar6._0_4_ = uVar39;
          auVar6._8_8_ = uVar19;
          FUN_18001cf60(auVar6,CONCAT44(uVar43,uVar42),local_220,(int *)param_3,(uint)fVar35,
                        (longlong)local_2b0,(longlong)puVar37,(longlong)puVar32,0,0,'\0',0);
          lVar31 = *(longlong *)local_218;
          uVar12 = (int)fVar35 >> 0x1f;
          uVar19 = extraout_x1_13;
          if (lVar31 == 0) {
            if (*(code **)(local_218 + 4) != (code *)0x0) {
              (**(code **)(local_218 + 4))(local_218[6]);
              uVar19 = extraout_x1_14;
            }
            lVar31 = *(longlong *)local_218;
            if (lVar31 != 0) goto LAB_1800574cc;
            pfVar28 = (float *)(local_218 + 9);
          }
          else {
LAB_1800574cc:
            uVar13 = local_218[8];
            if (uVar13 == 0) {
              pfVar28 = (float *)(local_218 + 9);
            }
            else {
              uVar30 = (uint)fVar35 & (uVar12 ^ 0xffffffff);
              if ((int)uVar13 <= (int)uVar30) {
                uVar30 = uVar13 - 1;
              }
              pfVar28 = (float *)(lVar31 + (longlong)(int)uVar30 * 4);
            }
          }
          FUN_1800069e8(*pfVar28,(int *)local_328,uVar19,(longlong)local_2b0,(int)fVar35);
          lVar31 = *(longlong *)local_210;
          uVar19 = extraout_x1_15;
          if (lVar31 == 0) {
            if (*(code **)(local_210 + 4) != (code *)0x0) {
              (**(code **)(local_210 + 4))(local_210[6]);
              uVar19 = extraout_x1_16;
            }
            lVar31 = *(longlong *)local_210;
            if (lVar31 != 0) goto LAB_180057538;
            pfVar28 = (float *)(local_210 + 9);
          }
          else {
LAB_180057538:
            uVar13 = local_210[8];
            if (uVar13 == 0) {
              pfVar28 = (float *)(local_210 + 9);
            }
            else {
              uVar30 = (uint)fVar35 & (uVar12 ^ 0xffffffff);
              if ((int)uVar13 <= (int)uVar30) {
                uVar30 = uVar13 - 1;
              }
              pfVar28 = (float *)(lVar31 + (longlong)(int)uVar30 * 4);
            }
          }
          FUN_1800069e8(*pfVar28,(int *)local_328,uVar19,(longlong)puVar37,(int)fVar35);
          lVar31 = *(longlong *)local_208;
          uVar19 = extraout_x1_17;
          if (lVar31 == 0) {
            if (*(code **)(local_208 + 4) != (code *)0x0) {
              (**(code **)(local_208 + 4))(local_208[6]);
              uVar19 = extraout_x1_18;
            }
            lVar31 = *(longlong *)local_208;
            if (lVar31 != 0) goto LAB_1800575a4;
            pfVar28 = (float *)(local_208 + 9);
          }
          else {
LAB_1800575a4:
            uVar13 = local_208[8];
            if (uVar13 == 0) {
              pfVar28 = (float *)(local_208 + 9);
            }
            else {
              uVar30 = (uint)fVar35 & (uVar12 ^ 0xffffffff);
              if ((int)uVar13 <= (int)uVar30) {
                uVar30 = uVar13 - 1;
              }
              pfVar28 = (float *)(lVar31 + (longlong)(int)uVar30 * 4);
            }
          }
          FUN_1800069e8(*pfVar28,(int *)local_328,uVar19,(longlong)puVar32,(int)fVar35);
          uVar19 = extraout_x1_19;
          if (bVar8) {
            FUN_1800069e8(*local_1a0,(int *)local_328,extraout_x1_19,(longlong)local_2b8,(int)fVar35
                         );
            FUN_1800069e8(*local_1d8,(int *)local_328,extraout_x1_20,(longlong)local_2f0,(int)fVar35
                         );
            FUN_1800069e8(*local_1d0,(int *)local_328,extraout_x1_21,(longlong)local_2f8,(int)fVar35
                         );
            FUN_1800069e8(*local_198,(int *)local_328,extraout_x1_22,(longlong)local_320,(int)fVar35
                         );
            uVar19 = extraout_x1_23;
          }
          if (0 < (int)fVar35 && fVar40 != fVar35) {
            if (*local_1e8 != 0) {
              fVar40 = (float)FUN_180006bb0((int *)local_328,uVar19,(longlong)local_320,
                                            (int)*local_260);
              FUN_1800069e8(fVar40,(int *)local_328,extraout_x1_24,(longlong)local_320,(int)fVar35);
              uVar19 = extraout_x1_25;
            }
            puVar22 = local_240;
            fVar40 = (float)FUN_180006bb0((int *)local_328,uVar19,(longlong)local_240,
                                          (int)fVar35 + -1);
            FUN_1800069e8(fVar40,(int *)local_328,extraout_x1_26,(longlong)puVar22,(int)fVar35);
            fVar40 = (float)FUN_180006bb0((int *)local_328,extraout_x1_27,(longlong)local_2f0,
                                          (int)*local_260);
            FUN_1800069e8(fVar40,(int *)local_328,extraout_x1_28,(longlong)local_2f0,(int)fVar35);
            fVar40 = (float)FUN_180006bb0((int *)local_328,extraout_x1_29,(longlong)local_2f8,
                                          (int)*local_260);
            FUN_1800069e8(fVar40,(int *)local_328,extraout_x1_30,(longlong)local_2f8,(int)fVar35);
            fVar40 = (float)FUN_180006bb0((int *)local_328,extraout_x1_31,(longlong)local_2b8,
                                          (int)*local_260);
            FUN_1800069e8(fVar40,(int *)local_328,extraout_x1_32,(longlong)local_2b8,(int)fVar35);
            fVar40 = (float)FUN_180006bb0((int *)local_328,extraout_x1_33,(longlong)puVar34,
                                          (int)fVar35 + -1);
            FUN_1800069e8(fVar40,(int *)local_328,extraout_x1_34,(longlong)puVar34,(int)fVar35);
            lVar31 = *(longlong *)(puVar34 + 0xc);
            uVar19 = extraout_x1_35;
            if (lVar31 == 0) {
              if (*(code **)(puVar34 + 0x10) != (code *)0x0) {
                (**(code **)(puVar34 + 0x10))(puVar34[0x12]);
                uVar19 = extraout_x1_36;
              }
              lVar31 = *(longlong *)(puVar34 + 0xc);
              if (lVar31 != 0) goto LAB_18005777c;
              pfVar28 = (float *)(puVar34 + 0x15);
            }
            else {
LAB_18005777c:
              uVar13 = puVar34[0x14];
              if (uVar13 == 0) {
                pfVar28 = (float *)(puVar34 + 0x15);
              }
              else {
                fVar40 = fVar35;
                if ((int)uVar13 <= (int)fVar35) {
                  fVar40 = (float)(uVar13 - 1);
                }
                pfVar28 = (float *)(lVar31 + (longlong)(int)fVar40 * 4);
              }
            }
            lVar31 = *(longlong *)(local_2b8 + 0xc);
            if (lVar31 == 0) {
              if (*(code **)(local_2b8 + 0x10) != (code *)0x0) {
                (**(code **)(local_2b8 + 0x10))(local_2b8[0x12]);
                uVar19 = extraout_x1_37;
              }
              lVar31 = *(longlong *)(local_2b8 + 0xc);
              if (lVar31 != 0) goto LAB_1800577d0;
              pfVar27 = (float *)(local_2b8 + 0x15);
            }
            else {
LAB_1800577d0:
              uVar13 = local_2b8[0x14];
              if (uVar13 == 0) {
                pfVar27 = (float *)(local_2b8 + 0x15);
              }
              else {
                fVar40 = fVar35;
                if ((int)uVar13 <= (int)fVar35) {
                  fVar40 = (float)(uVar13 - 1);
                }
                pfVar27 = (float *)(lVar31 + (longlong)(int)fVar40 * 4);
              }
            }
            if (*pfVar28 < *pfVar27) {
              uVar13 = local_2c0[7];
              puVar22 = (uint *)FUN_180005d08((longlong *)(local_2c0 + 0x16),(int)fVar35);
              *puVar22 = uVar13;
              uVar13 = local_2b8[7];
              puVar22 = (uint *)FUN_180005d08((longlong *)(local_2b8 + 0x16),(int)fVar35);
              *puVar22 = uVar13;
              fVar40 = (float)FUN_180006bb0((int *)local_328,extraout_x1_44,(longlong)local_2b8,
                                            (int)fVar35);
              local_2a8 = local_2a0;
              FUN_1800069e8(fVar40,(int *)local_328,extraout_x1_45,(longlong)local_2a0,(int)fVar35);
              fVar40 = (float)FUN_180006bb0((int *)local_328,extraout_x1_46,(longlong)puVar34,
                                            (int)fVar35);
              FUN_1800069e8(fVar40,(int *)local_328,extraout_x1_47,(longlong)local_2d0,(int)fVar35);
              uVar13 = local_2c8[7];
              puVar22 = (uint *)FUN_180005d08((longlong *)(local_2a8 + 0x16),(int)fVar35);
              *puVar22 = uVar13;
              uVar13 = local_2d0[7];
              puVar22 = (uint *)FUN_180005d08((longlong *)(local_2d0 + 0x16),(int)fVar35);
              *puVar22 = uVar13;
              uVar19 = extraout_x1_48;
            }
            else {
              uVar13 = local_2c0[6];
              lVar31 = *(longlong *)(local_2c0 + 0x16);
              if (lVar31 == 0) {
                if (*(code **)(local_2c0 + 0x1a) != (code *)0x0) {
                  (**(code **)(local_2c0 + 0x1a))(local_2c0[0x1c]);
                  uVar19 = extraout_x1_38;
                }
                lVar31 = *(longlong *)(local_2c0 + 0x16);
                if (lVar31 != 0) goto LAB_180057840;
                puVar22 = local_2c0 + 0x1f;
              }
              else {
LAB_180057840:
                uVar30 = local_2c0[0x1e];
                if (uVar30 == 0) {
                  puVar22 = local_2c0 + 0x1f;
                }
                else {
                  fVar40 = fVar35;
                  if ((int)uVar30 <= (int)fVar35) {
                    fVar40 = (float)(uVar30 - 1);
                  }
                  puVar22 = (uint *)(lVar31 + (longlong)(int)fVar40 * 4);
                }
              }
              *puVar22 = uVar13;
              uVar13 = local_2b8[6];
              lVar31 = *(longlong *)(local_2b8 + 0x16);
              if (lVar31 == 0) {
                if (*(code **)(local_2b8 + 0x1a) != (code *)0x0) {
                  (**(code **)(local_2b8 + 0x1a))(local_2b8[0x1c]);
                  uVar19 = extraout_x1_39;
                }
                lVar31 = *(longlong *)(local_2b8 + 0x16);
                if (lVar31 != 0) goto LAB_1800578a4;
                puVar22 = local_2b8 + 0x1f;
              }
              else {
LAB_1800578a4:
                uVar30 = local_2b8[0x1e];
                if (uVar30 == 0) {
                  puVar22 = local_2b8 + 0x1f;
                }
                else {
                  fVar40 = fVar35;
                  if ((int)uVar30 <= (int)fVar35) {
                    fVar40 = (float)(uVar30 - 1);
                  }
                  puVar22 = (uint *)(lVar31 + (longlong)(int)fVar40 * 4);
                }
              }
              *puVar22 = uVar13;
              fVar40 = (float)FUN_180006bb0((int *)local_328,uVar19,(longlong)puVar34,(int)fVar35);
              puVar22 = local_2a0;
              FUN_1800069e8(fVar40,(int *)local_328,extraout_x1_40,(longlong)local_2a0,(int)fVar35);
              fVar40 = (float)FUN_180006bb0((int *)local_328,extraout_x1_41,(longlong)local_2b8,
                                            (int)fVar35);
              FUN_1800069e8(fVar40,(int *)local_328,extraout_x1_42,(longlong)local_2d0,(int)fVar35);
              uVar13 = local_2c8[6];
              lVar31 = *(longlong *)(puVar22 + 0x16);
              if (lVar31 == 0) {
                if (*(code **)(puVar22 + 0x1a) != (code *)0x0) {
                  (**(code **)(puVar22 + 0x1a))(puVar22[0x1c]);
                }
                lVar31 = *(longlong *)(local_2a0 + 0x16);
                if (lVar31 != 0) goto LAB_180057950;
                puVar22 = local_2a0 + 0x1f;
              }
              else {
LAB_180057950:
                uVar30 = local_2a0[0x1e];
                if (uVar30 == 0) {
                  puVar22 = local_2a0 + 0x1f;
                }
                else {
                  fVar40 = fVar35;
                  if ((int)uVar30 <= (int)fVar35) {
                    fVar40 = (float)(uVar30 - 1);
                  }
                  puVar22 = (uint *)(lVar31 + (longlong)(int)fVar40 * 4);
                }
              }
              *puVar22 = uVar13;
              uVar13 = local_2d0[6];
              puVar22 = (uint *)FUN_180005d08((longlong *)(local_2d0 + 0x16),(int)fVar35);
              *puVar22 = uVar13;
              uVar19 = extraout_x1_43;
            }
          }
          if (bVar1) {
            *local_200 = 0.0;
            local_200[1] = 0.0;
            local_200[2] = 0.0;
            local_200[7] = 0.0;
            local_200[3] = 0.0;
            fVar40 = (float)FUN_180006bb0((int *)local_328,uVar19,(longlong)puVar34,(int)fVar35 + -1
                                         );
            FUN_1800069e8(fVar40,(int *)local_328,extraout_x1_49,(longlong)local_240,(int)fVar35);
            lVar31 = *(longlong *)(param_3 + 0x28e);
            uVar19 = extraout_x1_50;
            if (lVar31 == 0) {
              if (*(code **)(param_3 + 0x292) != (code *)0x0) {
                (**(code **)(param_3 + 0x292))(param_3[0x294]);
                uVar19 = extraout_x1_51;
              }
              lVar31 = *(longlong *)(param_3 + 0x28e);
              if (lVar31 != 0) goto LAB_180057ab8;
              pfVar28 = (float *)(param_3 + 0x297);
            }
            else {
LAB_180057ab8:
              uVar13 = param_3[0x296];
              if (uVar13 == 0) {
                pfVar28 = (float *)(param_3 + 0x297);
              }
              else {
                uVar30 = (uint)fVar35 & (uVar12 ^ 0xffffffff);
                if ((int)uVar13 <= (int)uVar30) {
                  uVar30 = uVar13 - 1;
                }
                pfVar28 = (float *)(lVar31 + (longlong)(int)uVar30 * 4);
              }
            }
            FUN_1800069e8(*pfVar28,(int *)local_328,uVar19,(longlong)puVar34,(int)fVar35);
            lVar31 = *(longlong *)(param_3 + 0x298);
            if (lVar31 == 0) {
              if (*(code **)(param_3 + 0x29c) != (code *)0x0) {
                (**(code **)(param_3 + 0x29c))(param_3[0x29e]);
              }
              lVar31 = *(longlong *)(param_3 + 0x298);
              if (lVar31 != 0) goto LAB_180057b18;
              pfVar28 = (float *)(param_3 + 0x2a1);
            }
            else {
LAB_180057b18:
              uVar13 = param_3[0x2a0];
              if (uVar13 == 0) {
                pfVar28 = (float *)(param_3 + 0x2a1);
              }
              else {
                uVar30 = (uint)fVar35 & (uVar12 ^ 0xffffffff);
                if ((int)uVar13 <= (int)uVar30) {
                  uVar30 = uVar13 - 1;
                }
                pfVar28 = (float *)(lVar31 + (longlong)(int)uVar30 * 4);
              }
            }
            *local_1d8 = *pfVar28;
            lVar31 = *(longlong *)(param_3 + 0x2a2);
            if (lVar31 == 0) {
              if (*(code **)(param_3 + 0x2a6) != (code *)0x0) {
                (**(code **)(param_3 + 0x2a6))(param_3[0x2a8]);
              }
              lVar31 = *(longlong *)(param_3 + 0x2a2);
              if (lVar31 == 0) {
                fVar40 = (float)param_3[0x2ab];
                pfVar28 = local_1d0;
                goto LAB_180057d6c;
              }
            }
            uVar13 = param_3[0x2aa];
            pfVar28 = local_1d0;
            if (uVar13 == 0) {
              fVar40 = (float)param_3[0x2ab];
            }
            else {
              uVar30 = (uint)fVar35 & (uVar12 ^ 0xffffffff);
              if ((int)uVar13 <= (int)uVar30) {
                uVar30 = uVar13 - 1;
              }
              fVar40 = *(float *)(lVar31 + (longlong)(int)uVar30 * 4);
            }
          }
          else {
            lVar31 = *(longlong *)(param_3 + 0x298);
            if (lVar31 == 0) {
              if (*(code **)(param_3 + 0x29c) != (code *)0x0) {
                (**(code **)(param_3 + 0x29c))(param_3[0x29e]);
              }
              lVar31 = *(longlong *)(param_3 + 0x298);
              if (lVar31 != 0) goto LAB_180057bd8;
              pfVar28 = (float *)(param_3 + 0x2a1);
            }
            else {
LAB_180057bd8:
              uVar13 = param_3[0x2a0];
              if (uVar13 == 0) {
                pfVar28 = (float *)(param_3 + 0x2a1);
              }
              else {
                uVar30 = (uint)fVar35 & (uVar12 ^ 0xffffffff);
                if ((int)uVar13 <= (int)uVar30) {
                  uVar30 = uVar13 - 1;
                }
                pfVar28 = (float *)(lVar31 + (longlong)(int)uVar30 * 4);
              }
            }
            fVar40 = *local_1d8;
            if (fVar40 <= *pfVar28) {
              if (lVar31 == 0) {
                if (*(code **)(param_3 + 0x29c) != (code *)0x0) {
                  (**(code **)(param_3 + 0x29c))(param_3[0x29e]);
                }
                lVar31 = *(longlong *)(param_3 + 0x298);
                if (lVar31 != 0) goto LAB_180057c34;
                pfVar28 = (float *)(param_3 + 0x2a1);
              }
              else {
LAB_180057c34:
                uVar13 = param_3[0x2a0];
                if (uVar13 == 0) {
                  pfVar28 = (float *)(param_3 + 0x2a1);
                }
                else {
                  uVar30 = (uint)fVar35 & (uVar12 ^ 0xffffffff);
                  if ((int)uVar13 <= (int)uVar30) {
                    uVar30 = uVar13 - 1;
                  }
                  pfVar28 = (float *)(lVar31 + (longlong)(int)uVar30 * 4);
                }
              }
              fVar40 = *pfVar28;
            }
            *local_1d8 = fVar40;
            lVar31 = *(longlong *)(param_3 + 0x2a2);
            if (lVar31 == 0) {
              if (*(code **)(param_3 + 0x2a6) != (code *)0x0) {
                (**(code **)(param_3 + 0x2a6))(param_3[0x2a8]);
              }
              lVar31 = *(longlong *)(param_3 + 0x2a2);
              if (lVar31 != 0) goto LAB_180057c8c;
              pfVar28 = (float *)(param_3 + 0x2ab);
            }
            else {
LAB_180057c8c:
              uVar13 = param_3[0x2aa];
              if (uVar13 == 0) {
                pfVar28 = (float *)(param_3 + 0x2ab);
              }
              else {
                uVar30 = (uint)fVar35 & (uVar12 ^ 0xffffffff);
                if ((int)uVar13 <= (int)uVar30) {
                  uVar30 = uVar13 - 1;
                }
                pfVar28 = (float *)(lVar31 + (longlong)(int)uVar30 * 4);
              }
            }
            fVar40 = *local_1d0;
            if (*pfVar28 <= fVar40) {
              if (lVar31 == 0) {
                if (*(code **)(param_3 + 0x2a6) != (code *)0x0) {
                  (**(code **)(param_3 + 0x2a6))(param_3[0x2a8]);
                }
                lVar31 = *(longlong *)(param_3 + 0x2a2);
                if (lVar31 != 0) goto LAB_180057ce8;
                pfVar28 = (float *)(param_3 + 0x2ab);
              }
              else {
LAB_180057ce8:
                uVar13 = param_3[0x2aa];
                if (uVar13 == 0) {
                  pfVar28 = (float *)(param_3 + 0x2ab);
                }
                else {
                  uVar30 = (uint)fVar35 & (uVar12 ^ 0xffffffff);
                  if ((int)uVar13 <= (int)uVar30) {
                    uVar30 = uVar13 - 1;
                  }
                  pfVar28 = (float *)(lVar31 + (longlong)(int)uVar30 * 4);
                }
              }
              fVar40 = *pfVar28;
            }
            *local_1d0 = fVar40;
            lVar31 = *(longlong *)(param_3 + 0x2ac);
            if (lVar31 == 0) {
              if (*(code **)(param_3 + 0x2b0) != (code *)0x0) {
                (**(code **)(param_3 + 0x2b0))(param_3[0x2b2]);
              }
              lVar31 = *(longlong *)(param_3 + 0x2ac);
              if (lVar31 != 0) goto LAB_180057d40;
              pfVar28 = (float *)(param_3 + 0x2b5);
            }
            else {
LAB_180057d40:
              uVar13 = param_3[0x2b4];
              if (uVar13 == 0) {
                pfVar28 = (float *)(param_3 + 0x2b5);
              }
              else {
                uVar30 = (uint)fVar35 & (uVar12 ^ 0xffffffff);
                if ((int)uVar13 <= (int)uVar30) {
                  uVar30 = uVar13 - 1;
                }
                pfVar28 = (float *)(lVar31 + (longlong)(int)uVar30 * 4);
              }
            }
            fVar40 = *pfVar28;
            pfVar28 = local_1a0;
          }
LAB_180057d6c:
          *pfVar28 = fVar40;
          fVar40 = FUN_180021950(local_200,(int *)param_3,fVar35);
          *local_198 = fVar40;
          uVar20 = extraout_x1_52;
          if (*local_1e8 == 0) {
            FUN_1800069e8(fVar40,(int *)local_328,extraout_x1_52,(longlong)local_320,(int)fVar35);
            uVar20 = extraout_x1_53;
          }
          if ((local_238[1] == 1) && (fVar35 == (float)(*param_3 - 1))) {
            local_1b0 = &local_290;
            puVar15 = FUN_1800553b8(&local_290,&local_160);
            uVar13 = FUN_180006c90(local_328,(int *)param_3,puVar15,(int)fVar35,(uint)local_32e);
            uVar20 = extraout_x1_54;
            if ((uVar13 & 1) != 0) {
              local_270 = (uint *)((ulonglong)local_270 & 0xffffffff00000000);
              bVar9 = true;
            }
          }
          puVar22 = local_2a0;
          iVar11 = 0;
          do {
            lVar31 = *(longlong *)(param_3 + 0x140);
            if (lVar31 == 0) {
              if (*(code **)(param_3 + 0x144) != (code *)0x0) {
                (**(code **)(param_3 + 0x144))(param_3[0x146]);
                uVar20 = extraout_x1_55;
              }
              lVar31 = *(longlong *)(param_3 + 0x140);
              if (lVar31 != 0) goto LAB_180057e34;
              puVar24 = param_3 + 0x14a;
            }
            else {
LAB_180057e34:
              uVar13 = param_3[0x148];
              if (uVar13 == 0) {
                puVar24 = param_3 + 0x14a;
              }
              else {
                iVar25 = iVar11;
                if ((int)uVar13 <= iVar11) {
                  iVar25 = uVar13 - 1;
                }
                puVar24 = (uint *)(lVar31 + (longlong)iVar25 * 0x170);
              }
            }
            lVar31 = *(longlong *)(puVar24 + 0x20);
            local_298 = (uint *)CONCAT44(local_298._4_4_,*local_328);
            if (lVar31 == 0) {
              if (*(code **)(puVar24 + 0x24) != (code *)0x0) {
                (**(code **)(puVar24 + 0x24))(puVar24[0x26]);
                uVar20 = extraout_x1_56;
              }
              lVar31 = *(longlong *)(puVar24 + 0x20);
              if (lVar31 != 0) goto LAB_180057ef0;
              puVar24 = puVar24 + 0x2a;
            }
            else {
LAB_180057ef0:
              uVar13 = puVar24[0x28];
              if (uVar13 == 0) {
                puVar24 = puVar24 + 0x2a;
              }
              else {
                uVar30 = (uint)local_298._0_4_ & ((int)local_298._0_4_ >> 0x1f ^ 0xffffffffU);
                if ((int)uVar13 <= (int)uVar30) {
                  uVar30 = uVar13 - 1;
                }
                puVar24 = (uint *)(lVar31 + (longlong)(int)uVar30 * 0x28);
              }
            }
            lVar31 = *(longlong *)puVar24;
            if (lVar31 == 0) {
              if (*(code **)(puVar24 + 4) != (code *)0x0) {
                (**(code **)(puVar24 + 4))(puVar24[6]);
                uVar20 = extraout_x1_57;
              }
              lVar31 = *(longlong *)puVar24;
              if (lVar31 != 0) goto LAB_180057f44;
              pfVar28 = (float *)(puVar24 + 9);
            }
            else {
LAB_180057f44:
              uVar13 = puVar24[8];
              if (uVar13 == 0) {
                pfVar28 = (float *)(puVar24 + 9);
              }
              else {
                uVar30 = (uint)fVar35 & (uVar12 ^ 0xffffffff);
                if ((int)uVar13 <= (int)uVar30) {
                  uVar30 = uVar13 - 1;
                }
                pfVar28 = (float *)(lVar31 + (longlong)(int)uVar30 * 4);
              }
            }
            if (*pfVar28 == -2.1474836e+09) {
LAB_18005803c:
              lVar31 = *(longlong *)(param_3 + 0x140);
              if (lVar31 == 0) {
                if (*(code **)(param_3 + 0x144) != (code *)0x0) {
                  (**(code **)(param_3 + 0x144))(param_3[0x146]);
                  uVar20 = extraout_x1_60;
                }
                lVar31 = *(longlong *)(param_3 + 0x140);
                if (lVar31 != 0) goto LAB_180058064;
                puVar24 = param_3 + 0x14a;
              }
              else {
LAB_180058064:
                uVar13 = param_3[0x148];
                if (uVar13 == 0) {
                  puVar24 = param_3 + 0x14a;
                }
                else {
                  iVar25 = iVar11;
                  if ((int)uVar13 <= iVar11) {
                    iVar25 = uVar13 - 1;
                  }
                  puVar24 = (uint *)(lVar31 + (longlong)iVar25 * 0x170);
                }
              }
              FUN_1800069e8(0.0,(int *)local_328,uVar20,(longlong)puVar24,(int)fVar35);
              uVar20 = extraout_x1_61;
            }
            else {
              lVar31 = *(longlong *)(param_3 + 0x140);
              if (lVar31 == 0) {
                if (*(code **)(param_3 + 0x144) != (code *)0x0) {
                  (**(code **)(param_3 + 0x144))(param_3[0x146]);
                  uVar20 = extraout_x1_58;
                }
                lVar31 = *(longlong *)(param_3 + 0x140);
                if (lVar31 != 0) goto LAB_180057fa0;
                puVar24 = param_3 + 0x14a;
              }
              else {
LAB_180057fa0:
                uVar13 = param_3[0x148];
                if (uVar13 == 0) {
                  puVar24 = param_3 + 0x14a;
                }
                else {
                  iVar25 = iVar11;
                  if ((int)uVar13 <= iVar11) {
                    iVar25 = uVar13 - 1;
                  }
                  puVar24 = (uint *)(lVar31 + (longlong)iVar25 * 0x170);
                }
              }
              fVar40 = (float)FUN_180006bb0((int *)local_328,uVar20,(longlong)puVar24,(int)fVar35);
              uVar20 = extraout_x1_59;
              if (fVar40 == 2.1474836e+09) goto LAB_18005803c;
            }
            iVar11 = iVar11 + 1;
          } while (iVar11 < 0x3c);
          local_2a0 = puVar22;
          if (bVar8) {
            switch((char)local_1f0[6]) {
            default:
              goto switchD_180058144_caseD_0;
            case '\x01':
            case '\x03':
            case '\x04':
            case '\x05':
            case '\x06':
            case '\v':
            case '\r':
            case '\x0e':
            case '\x0f':
            case '\x10':
            case '\x11':
            case '\x13':
            case '\x16':
            case '\x18':
              bVar10 = local_1f0[7] == 0;
              break;
            case '\x02':
              bVar10 = (float)local_1f0[7] == 0.0;
              break;
            case '\b':
            case '\t':
            case '\n':
            case '\x17':
            case '\x19':
              bVar10 = false;
              if (!NAN(*(double *)(local_1f0 + 7))) {
                bVar10 = *(double *)(local_1f0 + 7) == 0.0;
              }
            }
            if (!bVar10) {
              iVar11 = 0;
              fVar40 = *local_1c8;
              if ((int)*local_260 < (int)*local_1c8) {
                fVar40 = *local_260;
              }
              do {
                lVar31 = *(longlong *)(param_3 + 0x140);
                if (lVar31 == 0) {
                  if (*(code **)(param_3 + 0x144) != (code *)0x0) {
                    (**(code **)(param_3 + 0x144))(param_3[0x146]);
                    uVar20 = extraout_x1_62;
                  }
                  lVar31 = *(longlong *)(param_3 + 0x140);
                  if (lVar31 != 0) goto LAB_1800581c4;
                  puVar24 = param_3 + 0x14a;
                }
                else {
LAB_1800581c4:
                  uVar12 = param_3[0x148];
                  if (uVar12 == 0) {
                    puVar24 = param_3 + 0x14a;
                  }
                  else {
                    iVar25 = iVar11;
                    if ((int)uVar12 <= iVar11) {
                      iVar25 = uVar12 - 1;
                    }
                    puVar24 = (uint *)(lVar31 + (longlong)iVar25 * 0x170);
                  }
                }
                if (puVar24 != local_240) {
                  if (lVar31 == 0) {
                    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
                      (**(code **)(param_3 + 0x144))(param_3[0x146]);
                      uVar20 = extraout_x1_63;
                    }
                    lVar31 = *(longlong *)(param_3 + 0x140);
                    if (lVar31 != 0) goto LAB_18005827c;
                    puVar24 = param_3 + 0x14a;
                  }
                  else {
LAB_18005827c:
                    uVar12 = param_3[0x148];
                    if (uVar12 == 0) {
                      puVar24 = param_3 + 0x14a;
                    }
                    else {
                      iVar25 = iVar11;
                      if ((int)uVar12 <= iVar11) {
                        iVar25 = uVar12 - 1;
                      }
                      puVar24 = (uint *)(lVar31 + (longlong)iVar25 * 0x170);
                    }
                  }
                  if (puVar24 != puVar34) {
                    if (lVar31 == 0) {
                      if (*(code **)(param_3 + 0x144) != (code *)0x0) {
                        (**(code **)(param_3 + 0x144))(param_3[0x146]);
                        uVar20 = extraout_x1_64;
                      }
                      lVar31 = *(longlong *)(param_3 + 0x140);
                      if (lVar31 != 0) goto LAB_180058330;
                      puVar24 = param_3 + 0x14a;
                    }
                    else {
LAB_180058330:
                      uVar12 = param_3[0x148];
                      if (uVar12 == 0) {
                        puVar24 = param_3 + 0x14a;
                      }
                      else {
                        iVar25 = iVar11;
                        if ((int)uVar12 <= iVar11) {
                          iVar25 = uVar12 - 1;
                        }
                        puVar24 = (uint *)(lVar31 + (longlong)iVar25 * 0x170);
                      }
                    }
                    if (((puVar24 != local_2b0) &&
                        (puVar24 = (uint *)FUN_18000f880((longlong *)(param_3 + 0x140),iVar11),
                        uVar20 = extraout_x1_65, puVar24 != puVar37)) &&
                       (puVar24 = (uint *)FUN_18000f880((longlong *)(param_3 + 0x140),iVar11),
                       uVar20 = extraout_x1_66, puVar24 != puVar32)) {
                      uVar20 = (ulonglong)(uint)fVar35;
                      for (fVar38 = fVar40; (int)fVar38 < (int)fVar35;
                          fVar38 = (float)((int)fVar38 + 1)) {
                        local_2a0 = puVar22;
                        local_298 = puVar37;
                        local_258 = puVar32;
                        plVar21 = FUN_18000f880((longlong *)(param_3 + 0x140),iVar11);
                        FUN_1800069e8(0.0,(int *)local_328,extraout_x1_67,(longlong)plVar21,
                                      (int)fVar38);
                        puVar22 = local_2a0;
                        puVar32 = local_258;
                        puVar37 = local_298;
                        uVar20 = extraout_x1_68;
                      }
                    }
                  }
                }
                iVar11 = iVar11 + 1;
              } while (iVar11 < 0x3c);
              fVar38 = local_270._0_4_;
              if ((int)fVar40 <= (int)local_270._0_4_) {
                fVar38 = fVar40;
              }
              local_270 = (uint *)CONCAT44(local_270._4_4_,fVar38);
              local_2a0 = puVar22;
            }
          }
switchD_180058144_caseD_0:
          puVar22 = local_240;
          if (bVar1) {
            switch((char)local_1f0[6]) {
            default:
              goto switchD_180058494_caseD_0;
            case '\x01':
            case '\x03':
            case '\x04':
            case '\x05':
            case '\x06':
            case '\v':
            case '\r':
            case '\x0e':
            case '\x0f':
            case '\x10':
            case '\x11':
            case '\x13':
            case '\x16':
            case '\x18':
              bVar10 = local_1f0[7] == 0;
              break;
            case '\x02':
              bVar10 = (float)local_1f0[7] == 0.0;
              break;
            case '\b':
            case '\t':
            case '\n':
            case '\x17':
            case '\x19':
              bVar10 = false;
              if (!NAN(*(double *)(local_1f0 + 7))) {
                bVar10 = *(double *)(local_1f0 + 7) == 0.0;
              }
            }
            if (!bVar10) {
              fVar40 = *local_1c8;
              if ((int)*local_260 < (int)*local_1c8) {
                fVar40 = *local_260;
              }
              if ((int)fVar40 < (int)fVar35) {
                local_298 = (uint *)CONCAT44(local_298._4_4_,fVar40);
                local_2a8 = param_3;
                do {
                  FUN_1800069e8(0.0,(int *)local_328,uVar20,(longlong)puVar22,(int)fVar40);
                  FUN_1800069e8(0.0,(int *)local_328,extraout_x1_69,(longlong)puVar34,(int)fVar40);
                  FUN_1800069e8(0.0,(int *)local_328,extraout_x1_70,(longlong)local_2b0,(int)fVar40)
                  ;
                  FUN_1800069e8(0.0,(int *)local_328,extraout_x1_71,(longlong)puVar37,(int)fVar40);
                  FUN_1800069e8(0.0,(int *)local_328,extraout_x1_72,(longlong)puVar32,(int)fVar40);
                  fVar40 = (float)((int)fVar40 + 1);
                  uVar20 = extraout_x1_73;
                } while ((int)fVar40 < (int)fVar35);
                param_3 = local_2a8;
                fVar40 = local_298._0_4_;
              }
              fVar38 = local_270._0_4_;
              if ((int)fVar40 <= (int)local_270._0_4_) {
                fVar38 = fVar40;
              }
              local_270 = (uint *)CONCAT44(local_270._4_4_,fVar38);
            }
          }
switchD_180058494_caseD_0:
          if (bVar8) {
            *local_260 = fVar35;
          }
          if (bVar1) {
            *local_1c8 = fVar35;
          }
          *local_1b8 = fVar35;
          fVar35 = (float)((int)fVar35 + 1);
          local_268 = (uint *)CONCAT44(local_268._4_4_,(int)local_268 + 1);
          local_250 = (uint *)CONCAT44(local_250._4_4_,(int)local_250 + 1);
        } while ((int)fVar35 < (int)*param_3);
        fVar35 = local_270._0_4_;
      }
      if (param_3[0x47e] == 0) {
        param_3[0x433] = (uint)fVar35;
      }
      if ((bVar9) || (local_32e != 0)) {
        if ((((char)local_248[6] != '\x1a') && (1 < (byte)((char)local_248[6] - 0x1bU))) ||
           (pcVar23 = *(char **)(local_248 + 8), pcVar23 == (char *)0x0)) {
          pcVar23 = "Unset";
        }
        local_280 = (undefined1 *)0x0;
        local_278 = 0;
        cVar2 = *pcVar23;
        uStack_288 = 0;
        local_290 = (undefined8 **)0x0;
        pcVar29 = pcVar23;
        while (cVar2 != '\0') {
          pcVar29 = pcVar29 + 1;
          cVar2 = *pcVar29;
        }
        FUN_18000ddb8(&local_290,(undefined8 *)pcVar23,(longlong)pcVar29 - (longlong)pcVar23);
        auVar7._4_4_ = extraout_var_04;
        auVar7._0_4_ = extraout_s0_04;
        auVar7._8_8_ = extraout_var_10;
        FUN_180025678(auVar7,CONCAT44(uVar43,uVar42),(longlong)param_3,&local_290);
        if (0xf < local_278) {
          FUN_1800966b8(local_290);
        }
        local_290 = (undefined8 **)((ulonglong)local_290 & 0xffffffffffffff00);
        local_280 = (undefined1 *)0x0;
        local_278 = 0xf;
      }
    }
  }
  else {
    if (local_220 != (int *)0x0) {
      FUN_18004a2d0(local_220);
      (**(code **)(param_3 + 0x448))(2,0);
    }
    if (local_200 != (float *)0x0) {
      FUN_1800966b8(local_200);
      (**(code **)(param_3 + 0x448))(4,0);
    }
    if (local_328 != (uint *)0x0) {
      FUN_1800966b8(local_328);
      (**(code **)(param_3 + 0x448))(5,0);
    }
  }
LAB_180058684:
  *(undefined8 *)local_160[1] = 0;
  puVar15 = (undefined8 *)*local_160;
  while (puVar15 != (undefined8 *)0x0) {
    pvVar33 = (LPVOID)*puVar15;
    FUN_1800966b8(puVar15);
    puVar15 = (undefined8 *)pvVar33;
  }
  FUN_1800966b8(local_160);
  *(undefined8 *)local_150[1] = 0;
  puVar15 = (undefined8 *)*local_150;
  while (puVar15 != (undefined8 *)0x0) {
    pvVar33 = (LPVOID)*puVar15;
    FUN_1800966b8(puVar15);
    puVar15 = (undefined8 *)pvVar33;
  }
  FUN_1800966b8(local_150);
  return;
}

