
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */
/* WARNING: Removing unreachable block (ram,0x00018008b094) */
/* WARNING: Removing unreachable block (ram,0x00018008b09c) */

void scsf_RotationCalculator
               (undefined1 param_1 [16],undefined1 param_2 [16],undefined8 param_3,
               undefined8 param_4,undefined8 param_5,undefined8 param_6,undefined8 param_7,
               undefined8 param_8,undefined8 param_9,undefined8 param_10)

{
  undefined8 uVar1;
  undefined8 uVar2;
  undefined8 uVar3;
  undefined8 uVar4;
  undefined8 uVar5;
  undefined8 *puVar6;
  longlong lVar7;
  int iVar8;
  char cVar9;
  undefined1 auVar10 [16];
  undefined1 auVar11 [16];
  longlong *plVar12;
  uint uVar13;
  int *piVar14;
  int *piVar15;
  undefined8 uVar16;
  undefined8 *puVar17;
  HANDLE pvVar18;
  char *pcVar19;
  longlong *plVar20;
  char *pcVar21;
  ulonglong uVar22;
  undefined4 *puVar23;
  float *pfVar24;
  float *pfVar25;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  undefined8 extraout_x1_10;
  undefined8 extraout_x1_11;
  undefined8 extraout_x1_12;
  undefined8 extraout_x1_13;
  undefined8 extraout_x1_14;
  undefined8 extraout_x1_15;
  undefined8 extraout_x1_16;
  undefined8 extraout_x1_17;
  undefined8 extraout_x1_18;
  ulonglong extraout_x1_19;
  undefined8 uVar26;
  code *pcVar27;
  longlong lVar28;
  undefined8 uVar29;
  longlong lVar30;
  int iVar31;
  uint uVar32;
  uint uVar33;
  longlong lVar34;
  longlong lVar35;
  longlong lVar36;
  undefined4 *puVar37;
  longlong extraout_x11;
  longlong *plVar38;
  longlong extraout_x15;
  float *pfVar39;
  int *piVar40;
  longlong lVar41;
  longlong *plVar42;
  undefined8 uVar43;
  int *piVar44;
  float *pfVar45;
  longlong lVar46;
  LPVOID lpMem;
  float *pfVar47;
  char *pcVar48;
  int *piVar49;
  undefined8 uVar50;
  longlong lVar51;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  undefined4 extraout_s0_02;
  undefined4 extraout_s0_03;
  undefined4 extraout_s0_04;
  undefined4 extraout_s0_05;
  undefined4 extraout_s0_06;
  undefined4 extraout_s0_07;
  undefined4 extraout_s0_08;
  undefined4 extraout_s0_09;
  undefined4 extraout_s0_10;
  undefined4 extraout_s0_11;
  undefined4 extraout_s0_12;
  undefined4 extraout_s0_13;
  undefined4 extraout_s0_14;
  undefined4 extraout_s0_15;
  undefined4 extraout_s0_16;
  undefined4 extraout_s0_17;
  undefined4 extraout_s0_18;
  undefined4 extraout_s0_19;
  undefined4 extraout_s0_20;
  float fVar52;
  undefined4 extraout_var;
  undefined4 extraout_var_00;
  undefined4 extraout_var_01;
  undefined4 extraout_var_02;
  undefined4 extraout_var_03;
  undefined4 extraout_var_04;
  undefined4 extraout_var_05;
  undefined4 extraout_var_06;
  undefined4 extraout_var_07;
  undefined4 extraout_var_08;
  undefined4 extraout_var_09;
  undefined4 extraout_var_10;
  undefined4 extraout_var_11;
  undefined4 extraout_var_12;
  undefined4 extraout_var_13;
  undefined4 extraout_var_14;
  undefined4 extraout_var_15;
  undefined4 extraout_var_16;
  undefined4 extraout_var_17;
  undefined4 extraout_var_18;
  undefined4 extraout_var_19;
  undefined4 extraout_var_20;
  undefined4 uVar53;
  undefined8 extraout_var_21;
  undefined8 extraout_var_22;
  undefined8 extraout_var_23;
  undefined8 extraout_var_24;
  undefined8 extraout_var_25;
  undefined8 extraout_var_26;
  undefined8 extraout_var_27;
  undefined8 extraout_var_28;
  undefined8 extraout_var_29;
  undefined8 extraout_var_30;
  undefined8 extraout_var_31;
  undefined8 extraout_var_32;
  undefined8 extraout_var_33;
  undefined8 extraout_var_34;
  undefined8 extraout_var_35;
  undefined8 extraout_var_36;
  undefined8 extraout_var_37;
  undefined8 extraout_var_38;
  undefined8 extraout_var_39;
  undefined8 extraout_var_40;
  undefined8 extraout_var_41;
  undefined8 extraout_var_42;
  undefined4 uVar54;
  int iVar55;
  undefined4 uVar56;
  int iVar57;
  int iVar58;
  int iVar59;
  undefined4 uVar60;
  float fVar61;
  float fVar62;
  undefined1 uVar63;
  undefined1 uVar64;
  undefined1 uVar65;
  undefined1 uVar66;
  float fVar67;
  float fVar68;
  float fVar69;
  longlong alStack_90 [2];
  
  uVar56 = param_2._4_4_;
                    /* 0x8a7a8  23  scsf_RotationCalculator */
  uVar54 = param_2._0_4_;
  piVar14 = (int *)FUN_1800010c0();
  lVar35 = extraout_x15 * -0x10;
  pcVar21 = "";
  *(undefined8 *)(&stack0x00000110 + lVar35) = 0xfffffffffffffffe;
  *(undefined8 *)(&stack0x00000168 + lVar35) = 0;
  *(undefined8 *)(&stack0x00000160 + lVar35) = 0;
  *(undefined8 *)(&stack0x00000170 + lVar35) = 0;
  *(undefined1 **)(&stack0x00000170 + lVar35) = &DAT_1800d4ecd;
  *(undefined1 **)(&stack0x00000160 + lVar35) = &DAT_1800d4ecd;
  *(undefined8 *)(&stack0x00000168 + lVar35) = 0;
  (**(code **)(piVar14 + 0x478))(0x20241105);
  piVar15 = (int *)(**(code **)(piVar14 + 0x62e))(0);
  uVar16 = (**(code **)(piVar14 + 0x62e))(1);
  pcVar27 = *(code **)(piVar14 + 0x62e);
  *(undefined8 *)(&stack0x000000c8 + lVar35) = uVar16;
  uVar16 = (*pcVar27)(2);
  pcVar27 = *(code **)(piVar14 + 0x62e);
  *(undefined8 *)(&stack0x00000018 + lVar35) = uVar16;
  uVar16 = (*pcVar27)(3);
  pcVar27 = *(code **)(piVar14 + 0x62e);
  *(undefined8 *)(&stack0xffffffffffffffd8 + lVar35) = uVar16;
  uVar16 = (*pcVar27)(4);
  pcVar27 = *(code **)(piVar14 + 0x62e);
  *(undefined8 *)(&stack0x00000010 + lVar35) = uVar16;
  uVar16 = (*pcVar27)(5);
  pcVar27 = *(code **)(piVar14 + 0x62e);
  *(undefined8 *)(&stack0x000000b0 + lVar35) = uVar16;
  uVar16 = (*pcVar27)(6);
  pcVar27 = *(code **)(piVar14 + 0x62e);
  *(undefined8 *)(&stack0x000000f0 + lVar35) = uVar16;
  uVar16 = (*pcVar27)(7);
  pcVar27 = *(code **)(piVar14 + 0x630);
  *(undefined8 *)(&stack0x00000090 + lVar35) = uVar16;
  uVar16 = (*pcVar27)(0);
  pcVar27 = *(code **)(piVar14 + 0x630);
  *(undefined8 *)(&stack0x000000d8 + lVar35) = uVar16;
  uVar16 = (*pcVar27)(1);
  pcVar27 = *(code **)(piVar14 + 0x630);
  *(undefined8 *)(&stack0x000000d0 + lVar35) = uVar16;
  uVar16 = (*pcVar27)(2);
  pcVar27 = *(code **)(piVar14 + 0x630);
  *(undefined8 *)(&stack0x000000c0 + lVar35) = uVar16;
  uVar16 = (*pcVar27)(4);
  pcVar27 = *(code **)(piVar14 + 0x630);
  *(undefined8 *)(&stack0x000000e0 + lVar35) = uVar16;
  uVar16 = (*pcVar27)(5);
  pcVar27 = *(code **)(piVar14 + 0x446);
  *(undefined8 *)(&stack0x000000e8 + lVar35) = uVar16;
  puVar17 = (undefined8 *)(*pcVar27)(0);
  *(undefined8 *)(&stack0x000000a0 + lVar35) = *puVar17;
  puVar17 = (undefined8 *)(**(code **)(piVar14 + 0x446))(1);
  *(undefined8 *)(&stack0x00000000 + lVar35) = *puVar17;
  puVar17 = (undefined8 *)(**(code **)(piVar14 + 0x446))(2);
  lVar34 = *(longlong *)(piVar14 + 0x140);
  *(undefined8 *)(&stack0x000000a8 + lVar35) = *puVar17;
  fVar61 = 0.0;
  fVar62 = 0.0;
  *(undefined4 *)(&stack0xffffffffffffffe8 + lVar35) = 0;
  *(undefined4 *)(&stack0xffffffffffffffec + lVar35) = 0;
  uVar16 = extraout_x1;
  uVar60 = extraout_s0;
  uVar53 = extraout_var;
  uVar29 = extraout_var_21;
  if (lVar34 == 0) {
    if (*(code **)(piVar14 + 0x144) != (code *)0x0) {
      (**(code **)(piVar14 + 0x144))(piVar14[0x146]);
      uVar16 = extraout_x1_00;
      uVar60 = extraout_s0_00;
      uVar53 = extraout_var_00;
      uVar29 = extraout_var_22;
    }
    lVar34 = *(longlong *)(piVar14 + 0x140);
    if (lVar34 != 0) goto LAB_18008a974;
    *(int **)(&stack0xffffffffffffffe0 + lVar35) = piVar14 + 0x14a;
    *(int **)(&stack0x00000038 + lVar35) = piVar14 + 0x14a;
LAB_18008a9a8:
    if (*(code **)(piVar14 + 0x144) != (code *)0x0) {
      (**(code **)(piVar14 + 0x144))(piVar14[0x146]);
      uVar16 = extraout_x1_01;
      uVar60 = extraout_s0_01;
      uVar53 = extraout_var_01;
      uVar29 = extraout_var_23;
    }
    lVar34 = *(longlong *)(piVar14 + 0x140);
    if (lVar34 != 0) goto LAB_18008a9d8;
    *(int **)(&stack0xffffffffffffff80 + lVar35) = piVar14 + 0x14a;
    *(int **)(&stack0xfffffffffffffff8 + lVar35) = piVar14 + 0x14a;
LAB_18008aa0c:
    if (*(code **)(piVar14 + 0x144) != (code *)0x0) {
      (**(code **)(piVar14 + 0x144))(piVar14[0x146]);
      uVar16 = extraout_x1_02;
      uVar60 = extraout_s0_02;
      uVar53 = extraout_var_02;
      uVar29 = extraout_var_24;
    }
    lVar34 = *(longlong *)(piVar14 + 0x140);
    if (lVar34 != 0) goto LAB_18008aa38;
    *(int **)(&stack0xffffffffffffffc0 + lVar35) = piVar14 + 0x14a;
    *(int **)(&stack0xffffffffffffffc8 + lVar35) = piVar14 + 0x14a;
LAB_18008aa6c:
    if (*(code **)(piVar14 + 0x144) != (code *)0x0) {
      (**(code **)(piVar14 + 0x144))(piVar14[0x146]);
      uVar16 = extraout_x1_03;
      uVar60 = extraout_s0_03;
      uVar53 = extraout_var_03;
      uVar29 = extraout_var_25;
    }
    lVar34 = *(longlong *)(piVar14 + 0x140);
    if (lVar34 != 0) goto LAB_18008aa9c;
    *(int **)(&stack0xffffffffffffffb0 + lVar35) = piVar14 + 0x14a;
    *(int **)(&stack0x000000b8 + lVar35) = piVar14 + 0x14a;
LAB_18008aad4:
    if (*(code **)(piVar14 + 0x144) != (code *)0x0) {
      (**(code **)(piVar14 + 0x144))(piVar14[0x146]);
      uVar16 = extraout_x1_04;
      uVar60 = extraout_s0_04;
      uVar53 = extraout_var_04;
      uVar29 = extraout_var_26;
    }
    lVar34 = *(longlong *)(piVar14 + 0x140);
    if (lVar34 != 0) goto LAB_18008ab04;
    alStack_90[extraout_x15 * -2] = (longlong)(piVar14 + 0x14a);
    *(int **)(&stack0x00000088 + lVar35) = piVar14 + 0x14a;
LAB_18008ab3c:
    if (*(code **)(piVar14 + 0x144) != (code *)0x0) {
      (**(code **)(piVar14 + 0x144))(piVar14[0x146]);
      uVar16 = extraout_x1_05;
      uVar60 = extraout_s0_05;
      uVar53 = extraout_var_05;
      uVar29 = extraout_var_27;
    }
    lVar34 = *(longlong *)(piVar14 + 0x140);
    if (lVar34 != 0) goto LAB_18008ab6c;
    *(int **)(&stack0xffffffffffffff88 + lVar35) = piVar14 + 0x14a;
    *(int **)(&stack0x00000070 + lVar35) = piVar14 + 0x14a;
LAB_18008aba4:
    if (*(code **)(piVar14 + 0x144) != (code *)0x0) {
      (**(code **)(piVar14 + 0x144))(piVar14[0x146]);
      uVar16 = extraout_x1_06;
      uVar60 = extraout_s0_06;
      uVar53 = extraout_var_06;
      uVar29 = extraout_var_28;
    }
    lVar34 = *(longlong *)(piVar14 + 0x140);
    if (lVar34 != 0) goto LAB_18008abd4;
    *(int **)(&stack0xffffffffffffff90 + lVar35) = piVar14 + 0x14a;
    *(int **)(&stack0x00000040 + lVar35) = piVar14 + 0x14a;
LAB_18008ac0c:
    if (*(code **)(piVar14 + 0x144) != (code *)0x0) {
      (**(code **)(piVar14 + 0x144))(piVar14[0x146]);
      uVar16 = extraout_x1_07;
      uVar60 = extraout_s0_07;
      uVar53 = extraout_var_07;
      uVar29 = extraout_var_29;
    }
    lVar34 = *(longlong *)(piVar14 + 0x140);
    if (lVar34 != 0) goto LAB_18008ac3c;
    *(int **)(&stack0xffffffffffffffa8 + lVar35) = piVar14 + 0x14a;
    *(int **)(&stack0x00000048 + lVar35) = piVar14 + 0x14a;
LAB_18008ac74:
    if (*(code **)(piVar14 + 0x144) != (code *)0x0) {
      (**(code **)(piVar14 + 0x144))(piVar14[0x146]);
      uVar16 = extraout_x1_08;
      uVar60 = extraout_s0_08;
      uVar53 = extraout_var_08;
      uVar29 = extraout_var_30;
    }
    lVar34 = *(longlong *)(piVar14 + 0x140);
    if (lVar34 != 0) goto LAB_18008aca4;
    *(int **)(&stack0xffffffffffffff98 + lVar35) = piVar14 + 0x14a;
    *(int **)(&stack0x00000050 + lVar35) = piVar14 + 0x14a;
LAB_18008acdc:
    if (*(code **)(piVar14 + 0x144) != (code *)0x0) {
      (**(code **)(piVar14 + 0x144))(piVar14[0x146]);
      uVar16 = extraout_x1_09;
      uVar60 = extraout_s0_09;
      uVar53 = extraout_var_09;
      uVar29 = extraout_var_31;
    }
    lVar34 = *(longlong *)(piVar14 + 0x140);
    if (lVar34 != 0) goto LAB_18008ad0c;
    *(int **)(&stack0xffffffffffffffb8 + lVar35) = piVar14 + 0x14a;
    *(int **)(&stack0x00000058 + lVar35) = piVar14 + 0x14a;
LAB_18008ad44:
    if (*(code **)(piVar14 + 0x144) != (code *)0x0) {
      (**(code **)(piVar14 + 0x144))(piVar14[0x146]);
      uVar16 = extraout_x1_10;
      uVar60 = extraout_s0_10;
      uVar53 = extraout_var_10;
      uVar29 = extraout_var_32;
    }
    lVar34 = *(longlong *)(piVar14 + 0x140);
    if (lVar34 != 0) goto LAB_18008ad74;
    *(int **)(&stack0xffffffffffffffa0 + lVar35) = piVar14 + 0x14a;
    *(int **)(&stack0x00000060 + lVar35) = piVar14 + 0x14a;
LAB_18008adac:
    if (*(code **)(piVar14 + 0x144) != (code *)0x0) {
      (**(code **)(piVar14 + 0x144))(piVar14[0x146]);
      uVar16 = extraout_x1_11;
      uVar60 = extraout_s0_11;
      uVar53 = extraout_var_11;
      uVar29 = extraout_var_33;
    }
    lVar34 = *(longlong *)(piVar14 + 0x140);
    if (lVar34 != 0) goto LAB_18008addc;
    alStack_90[extraout_x15 * -2 + 1] = (longlong)(piVar14 + 0x14a);
    *(int **)(&stack0x00000078 + lVar35) = piVar14 + 0x14a;
LAB_18008ae14:
    if (*(code **)(piVar14 + 0x144) != (code *)0x0) {
      (**(code **)(piVar14 + 0x144))(piVar14[0x146]);
      uVar16 = extraout_x1_12;
      uVar60 = extraout_s0_12;
      uVar53 = extraout_var_12;
      uVar29 = extraout_var_34;
    }
    lVar34 = *(longlong *)(piVar14 + 0x140);
    if (lVar34 != 0) goto LAB_18008ae34;
    piVar40 = piVar14 + 0x14a;
  }
  else {
LAB_18008a974:
    iVar58 = piVar14[0x148];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x14a;
    }
    else {
      iVar55 = 0;
      if (iVar58 < 1) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x170);
    }
    *(int **)(&stack0xffffffffffffffe0 + lVar35) = piVar40;
    *(int **)(&stack0x00000038 + lVar35) = piVar40;
    if (lVar34 == 0) goto LAB_18008a9a8;
LAB_18008a9d8:
    iVar58 = piVar14[0x148];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x14a;
    }
    else {
      iVar55 = 1;
      if (iVar58 + -1 == 0 || iVar58 < 1) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x170);
    }
    *(int **)(&stack0xffffffffffffff80 + lVar35) = piVar40;
    *(int **)(&stack0xfffffffffffffff8 + lVar35) = piVar40;
    if (lVar34 == 0) goto LAB_18008aa0c;
LAB_18008aa38:
    iVar58 = piVar14[0x148];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x14a;
    }
    else {
      iVar55 = 2;
      if (iVar58 < 3) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x170);
    }
    *(int **)(&stack0xffffffffffffffc0 + lVar35) = piVar40;
    *(int **)(&stack0xffffffffffffffc8 + lVar35) = piVar40;
    if (lVar34 == 0) goto LAB_18008aa6c;
LAB_18008aa9c:
    iVar58 = piVar14[0x148];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x14a;
    }
    else {
      iVar55 = 3;
      if (iVar58 < 4) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x170);
    }
    *(int **)(&stack0xffffffffffffffb0 + lVar35) = piVar40;
    *(int **)(&stack0x000000b8 + lVar35) = piVar40;
    if (lVar34 == 0) goto LAB_18008aad4;
LAB_18008ab04:
    iVar58 = piVar14[0x148];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x14a;
    }
    else {
      iVar55 = 4;
      if (iVar58 < 5) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x170);
    }
    alStack_90[extraout_x15 * -2] = (longlong)piVar40;
    *(int **)(&stack0x00000088 + lVar35) = piVar40;
    if (lVar34 == 0) goto LAB_18008ab3c;
LAB_18008ab6c:
    iVar58 = piVar14[0x148];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x14a;
    }
    else {
      iVar55 = 5;
      if (iVar58 < 6) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x170);
    }
    *(int **)(&stack0xffffffffffffff88 + lVar35) = piVar40;
    *(int **)(&stack0x00000070 + lVar35) = piVar40;
    if (lVar34 == 0) goto LAB_18008aba4;
LAB_18008abd4:
    iVar58 = piVar14[0x148];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x14a;
    }
    else {
      iVar55 = 6;
      if (iVar58 < 7) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x170);
    }
    *(int **)(&stack0xffffffffffffff90 + lVar35) = piVar40;
    *(int **)(&stack0x00000040 + lVar35) = piVar40;
    if (lVar34 == 0) goto LAB_18008ac0c;
LAB_18008ac3c:
    iVar58 = piVar14[0x148];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x14a;
    }
    else {
      iVar55 = 7;
      if (iVar58 < 8) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x170);
    }
    *(int **)(&stack0xffffffffffffffa8 + lVar35) = piVar40;
    *(int **)(&stack0x00000048 + lVar35) = piVar40;
    if (lVar34 == 0) goto LAB_18008ac74;
LAB_18008aca4:
    iVar58 = piVar14[0x148];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x14a;
    }
    else {
      iVar55 = 8;
      if (iVar58 < 9) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x170);
    }
    *(int **)(&stack0xffffffffffffff98 + lVar35) = piVar40;
    *(int **)(&stack0x00000050 + lVar35) = piVar40;
    if (lVar34 == 0) goto LAB_18008acdc;
LAB_18008ad0c:
    iVar58 = piVar14[0x148];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x14a;
    }
    else {
      iVar55 = 9;
      if (iVar58 < 10) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x170);
    }
    *(int **)(&stack0xffffffffffffffb8 + lVar35) = piVar40;
    *(int **)(&stack0x00000058 + lVar35) = piVar40;
    if (lVar34 == 0) goto LAB_18008ad44;
LAB_18008ad74:
    iVar58 = piVar14[0x148];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x14a;
    }
    else {
      iVar55 = 10;
      if (iVar58 < 0xb) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x170);
    }
    *(int **)(&stack0xffffffffffffffa0 + lVar35) = piVar40;
    *(int **)(&stack0x00000060 + lVar35) = piVar40;
    if (lVar34 == 0) goto LAB_18008adac;
LAB_18008addc:
    iVar58 = piVar14[0x148];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x14a;
    }
    else {
      iVar55 = 0xb;
      if (iVar58 < 0xc) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x170);
    }
    alStack_90[extraout_x15 * -2 + 1] = (longlong)piVar40;
    *(int **)(&stack0x00000078 + lVar35) = piVar40;
    if (lVar34 == 0) goto LAB_18008ae14;
LAB_18008ae34:
    iVar58 = piVar14[0x148];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x14a;
    }
    else {
      iVar55 = 0xc;
      if (iVar58 < 0xd) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x170);
    }
  }
  *(int **)(&stack0x00000008 + lVar35) = piVar40;
  lVar34 = *(longlong *)(piVar14 + 0x84);
  if (lVar34 == 0) {
    if (*(code **)(piVar14 + 0x88) != (code *)0x0) {
      (**(code **)(piVar14 + 0x88))(piVar14[0x8a]);
      uVar16 = extraout_x1_13;
      uVar60 = extraout_s0_13;
      uVar53 = extraout_var_13;
      uVar29 = extraout_var_35;
    }
    lVar34 = *(longlong *)(piVar14 + 0x84);
    if (lVar34 != 0) goto LAB_18008ae94;
    *(int **)(&stack0x00000068 + lVar35) = piVar14 + 0x8e;
LAB_18008aec4:
    if (*(code **)(piVar14 + 0x88) != (code *)0x0) {
      (**(code **)(piVar14 + 0x88))(piVar14[0x8a]);
      uVar16 = extraout_x1_14;
      uVar60 = extraout_s0_14;
      uVar53 = extraout_var_14;
      uVar29 = extraout_var_36;
    }
    lVar34 = *(longlong *)(piVar14 + 0x84);
    if (lVar34 != 0) goto LAB_18008aef0;
    *(int **)(&stack0xfffffffffffffff0 + lVar35) = piVar14 + 0x8e;
LAB_18008af20:
    if (*(code **)(piVar14 + 0x88) != (code *)0x0) {
      (**(code **)(piVar14 + 0x88))(piVar14[0x8a]);
      uVar16 = extraout_x1_15;
      uVar60 = extraout_s0_15;
      uVar53 = extraout_var_15;
      uVar29 = extraout_var_37;
    }
    lVar34 = *(longlong *)(piVar14 + 0x84);
    if (lVar34 != 0) goto LAB_18008af48;
    piVar40 = piVar14 + 0x8e;
LAB_18008af78:
    if (*(code **)(piVar14 + 0x88) != (code *)0x0) {
      (**(code **)(piVar14 + 0x88))(piVar14[0x8a]);
      uVar16 = extraout_x1_16;
      uVar60 = extraout_s0_16;
      uVar53 = extraout_var_16;
      uVar29 = extraout_var_38;
    }
    lVar34 = *(longlong *)(piVar14 + 0x84);
    if (lVar34 != 0) goto LAB_18008afa0;
    piVar49 = piVar14 + 0x8e;
LAB_18008afd0:
    if (*(code **)(piVar14 + 0x88) != (code *)0x0) {
      (**(code **)(piVar14 + 0x88))(piVar14[0x8a]);
      uVar16 = extraout_x1_17;
      uVar60 = extraout_s0_17;
      uVar53 = extraout_var_17;
      uVar29 = extraout_var_39;
    }
    lVar34 = *(longlong *)(piVar14 + 0x84);
    if (lVar34 != 0) goto LAB_18008b000;
    *(int **)(&stack0x00000118 + lVar35) = piVar14 + 0x8e;
    *(int **)(&stack0xffffffffffffffd0 + lVar35) = piVar14 + 0x8e;
LAB_18008b038:
    if (*(code **)(piVar14 + 0x88) != (code *)0x0) {
      (**(code **)(piVar14 + 0x88))(piVar14[0x8a]);
      uVar16 = extraout_x1_18;
      uVar60 = extraout_s0_18;
      uVar53 = extraout_var_18;
      uVar29 = extraout_var_40;
    }
    lVar34 = *(longlong *)(piVar14 + 0x84);
    if (lVar34 != 0) goto LAB_18008b058;
    piVar44 = piVar14 + 0x8e;
  }
  else {
LAB_18008ae94:
    iVar58 = piVar14[0x8c];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x8e;
    }
    else {
      iVar55 = 0;
      if (iVar58 < 1) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x98);
    }
    *(int **)(&stack0x00000068 + lVar35) = piVar40;
    if (lVar34 == 0) goto LAB_18008aec4;
LAB_18008aef0:
    iVar58 = piVar14[0x8c];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x8e;
    }
    else {
      iVar55 = 1;
      if (iVar58 + -1 == 0 || iVar58 < 1) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x98);
    }
    *(int **)(&stack0xfffffffffffffff0 + lVar35) = piVar40;
    if (lVar34 == 0) goto LAB_18008af20;
LAB_18008af48:
    iVar58 = piVar14[0x8c];
    if (iVar58 == 0) {
      piVar40 = piVar14 + 0x8e;
    }
    else {
      iVar55 = 2;
      if (iVar58 < 3) {
        iVar55 = iVar58 + -1;
      }
      piVar40 = (int *)(lVar34 + (longlong)iVar55 * 0x98);
    }
    if (lVar34 == 0) goto LAB_18008af78;
LAB_18008afa0:
    iVar58 = piVar14[0x8c];
    if (iVar58 == 0) {
      piVar49 = piVar14 + 0x8e;
    }
    else {
      iVar55 = 3;
      if (iVar58 < 4) {
        iVar55 = iVar58 + -1;
      }
      piVar49 = (int *)(lVar34 + (longlong)iVar55 * 0x98);
    }
    if (lVar34 == 0) goto LAB_18008afd0;
LAB_18008b000:
    iVar58 = piVar14[0x8c];
    if (iVar58 == 0) {
      piVar44 = piVar14 + 0x8e;
    }
    else {
      iVar55 = 4;
      if (iVar58 < 5) {
        iVar55 = iVar58 + -1;
      }
      piVar44 = (int *)(lVar34 + (longlong)iVar55 * 0x98);
    }
    *(int **)(&stack0x00000118 + lVar35) = piVar44;
    *(int **)(&stack0xffffffffffffffd0 + lVar35) = piVar44;
    if (lVar34 == 0) goto LAB_18008b038;
LAB_18008b058:
    iVar58 = piVar14[0x8c];
    if (iVar58 == 0) {
      piVar44 = piVar14 + 0x8e;
    }
    else {
      iVar55 = 5;
      if (iVar58 < 6) {
        iVar55 = iVar58 + -1;
      }
      piVar44 = (int *)(lVar34 + (longlong)iVar55 * 0x98);
    }
  }
  *(int **)(&stack0x00000098 + lVar35) = piVar44;
  if (piVar14[0x2b] != 0) {
    FUN_1800079f8((longlong *)(piVar14 + 0x46),0x1800d74c0,0x13);
    piVar14[0x49] = 1;
    if ((*(char **)(piVar14 + 0xce) != (char *)0x0) && (**(char **)(piVar14 + 0xce) != '\0')) {
      FUN_1800079f8((longlong *)(piVar14 + 0xce),0x1800d4ecd,0);
      piVar14[0xd1] = 1;
    }
    *(undefined1 **)(&stack0x00000120 + lVar35) = &DAT_1800d4ecd;
    *(undefined8 *)(&stack0x00000128 + lVar35) = 0;
    *(undefined1 **)(&stack0x00000130 + lVar35) = &DAT_1800d4ecd;
    pvVar18 = GetProcessHeap();
    pcVar19 = (char *)HeapAlloc(pvVar18,0,0x15);
    if (pcVar19 == (char *)0x0) {
      *(undefined8 *)(&stack0x00000120 + lVar35) = 0;
      *(undefined1 **)(&stack0x00000120 + lVar35) = &DAT_1800d4ecd;
      pcVar48 = pcVar21;
    }
    else {
      param_6 = 0x14;
      *(char **)(&stack0x00000120 + lVar35) = pcVar19;
      pcVar19[8] = '\0';
      pcVar19[9] = '\0';
      pcVar19[10] = '\0';
      pcVar19[0xb] = '\0';
      pcVar19[0xc] = '\0';
      pcVar19[0xd] = '\0';
      pcVar19[0xe] = '\0';
      pcVar19[0xf] = '\0';
      pcVar19[0] = '\0';
      pcVar19[1] = '\0';
      pcVar19[2] = '\0';
      pcVar19[3] = '\0';
      pcVar19[4] = '\0';
      pcVar19[5] = '\0';
      pcVar19[6] = '\0';
      pcVar19[7] = '\0';
      pcVar19[0x10] = '\0';
      pcVar19[0x11] = '\0';
      pcVar19[0x12] = '\0';
      pcVar19[0x13] = '\0';
      pcVar19[0x14] = '\0';
      FUN_180099d78(pcVar19,0x15,0x1800d6c40,0x14);
      *(undefined8 *)(&stack0x00000128 + lVar35) = 0x100000001;
      pcVar48 = pcVar19;
    }
    FUN_1800263b0((undefined8 *)(piVar14 + 0xce),(longlong *)(&stack0x00000120 + lVar35));
    if ((pcVar19 != (char *)0x0) && (pcVar48 != (char *)0x0)) {
      pvVar18 = GetProcessHeap();
      HeapFree(pvVar18,0,pcVar48);
      *(undefined8 *)(&stack0x00000120 + lVar35) = 0;
      *(undefined8 *)(&stack0x00000128 + lVar35) = 0;
    }
    *(undefined1 **)(&stack0x00000120 + lVar35) = &DAT_1800d4ecd;
    *(undefined8 *)(&stack0x00000128 + lVar35) = 0;
    *(undefined1 **)(&stack0x00000130 + lVar35) = &DAT_1800d4ecd;
    pvVar18 = GetProcessHeap();
    pcVar19 = (char *)HeapAlloc(pvVar18,0,0x8d);
    if (pcVar19 == (char *)0x0) {
      *(undefined8 *)(&stack0x00000120 + lVar35) = 0;
      *(undefined1 **)(&stack0x00000120 + lVar35) = &DAT_1800d4ecd;
      pcVar48 = pcVar21;
    }
    else {
      param_6 = 0x8c;
      *(char **)(&stack0x00000120 + lVar35) = pcVar19;
      pcVar19[8] = '\0';
      pcVar19[9] = '\0';
      pcVar19[10] = '\0';
      pcVar19[0xb] = '\0';
      pcVar19[0xc] = '\0';
      pcVar19[0xd] = '\0';
      pcVar19[0xe] = '\0';
      pcVar19[0xf] = '\0';
      pcVar19[0] = '\0';
      pcVar19[1] = '\0';
      pcVar19[2] = '\0';
      pcVar19[3] = '\0';
      pcVar19[4] = '\0';
      pcVar19[5] = '\0';
      pcVar19[6] = '\0';
      pcVar19[7] = '\0';
      pcVar19[0x18] = '\0';
      pcVar19[0x19] = '\0';
      pcVar19[0x1a] = '\0';
      pcVar19[0x1b] = '\0';
      pcVar19[0x1c] = '\0';
      pcVar19[0x1d] = '\0';
      pcVar19[0x1e] = '\0';
      pcVar19[0x1f] = '\0';
      pcVar19[0x10] = '\0';
      pcVar19[0x11] = '\0';
      pcVar19[0x12] = '\0';
      pcVar19[0x13] = '\0';
      pcVar19[0x14] = '\0';
      pcVar19[0x15] = '\0';
      pcVar19[0x16] = '\0';
      pcVar19[0x17] = '\0';
      pcVar19[0x28] = '\0';
      pcVar19[0x29] = '\0';
      pcVar19[0x2a] = '\0';
      pcVar19[0x2b] = '\0';
      pcVar19[0x2c] = '\0';
      pcVar19[0x2d] = '\0';
      pcVar19[0x2e] = '\0';
      pcVar19[0x2f] = '\0';
      pcVar19[0x20] = '\0';
      pcVar19[0x21] = '\0';
      pcVar19[0x22] = '\0';
      pcVar19[0x23] = '\0';
      pcVar19[0x24] = '\0';
      pcVar19[0x25] = '\0';
      pcVar19[0x26] = '\0';
      pcVar19[0x27] = '\0';
      pcVar19[0x38] = '\0';
      pcVar19[0x39] = '\0';
      pcVar19[0x3a] = '\0';
      pcVar19[0x3b] = '\0';
      pcVar19[0x3c] = '\0';
      pcVar19[0x3d] = '\0';
      pcVar19[0x3e] = '\0';
      pcVar19[0x3f] = '\0';
      pcVar19[0x30] = '\0';
      pcVar19[0x31] = '\0';
      pcVar19[0x32] = '\0';
      pcVar19[0x33] = '\0';
      pcVar19[0x34] = '\0';
      pcVar19[0x35] = '\0';
      pcVar19[0x36] = '\0';
      pcVar19[0x37] = '\0';
      pcVar19[0x48] = '\0';
      pcVar19[0x49] = '\0';
      pcVar19[0x4a] = '\0';
      pcVar19[0x4b] = '\0';
      pcVar19[0x4c] = '\0';
      pcVar19[0x4d] = '\0';
      pcVar19[0x4e] = '\0';
      pcVar19[0x4f] = '\0';
      pcVar19[0x40] = '\0';
      pcVar19[0x41] = '\0';
      pcVar19[0x42] = '\0';
      pcVar19[0x43] = '\0';
      pcVar19[0x44] = '\0';
      pcVar19[0x45] = '\0';
      pcVar19[0x46] = '\0';
      pcVar19[0x47] = '\0';
      pcVar19[0x58] = '\0';
      pcVar19[0x59] = '\0';
      pcVar19[0x5a] = '\0';
      pcVar19[0x5b] = '\0';
      pcVar19[0x5c] = '\0';
      pcVar19[0x5d] = '\0';
      pcVar19[0x5e] = '\0';
      pcVar19[0x5f] = '\0';
      pcVar19[0x50] = '\0';
      pcVar19[0x51] = '\0';
      pcVar19[0x52] = '\0';
      pcVar19[0x53] = '\0';
      pcVar19[0x54] = '\0';
      pcVar19[0x55] = '\0';
      pcVar19[0x56] = '\0';
      pcVar19[0x57] = '\0';
      pcVar19[0x68] = '\0';
      pcVar19[0x69] = '\0';
      pcVar19[0x6a] = '\0';
      pcVar19[0x6b] = '\0';
      pcVar19[0x6c] = '\0';
      pcVar19[0x6d] = '\0';
      pcVar19[0x6e] = '\0';
      pcVar19[0x6f] = '\0';
      pcVar19[0x60] = '\0';
      pcVar19[0x61] = '\0';
      pcVar19[0x62] = '\0';
      pcVar19[99] = '\0';
      pcVar19[100] = '\0';
      pcVar19[0x65] = '\0';
      pcVar19[0x66] = '\0';
      pcVar19[0x67] = '\0';
      pcVar19[0x78] = '\0';
      pcVar19[0x79] = '\0';
      pcVar19[0x7a] = '\0';
      pcVar19[0x7b] = '\0';
      pcVar19[0x7c] = '\0';
      pcVar19[0x7d] = '\0';
      pcVar19[0x7e] = '\0';
      pcVar19[0x7f] = '\0';
      pcVar19[0x70] = '\0';
      pcVar19[0x71] = '\0';
      pcVar19[0x72] = '\0';
      pcVar19[0x73] = '\0';
      pcVar19[0x74] = '\0';
      pcVar19[0x75] = '\0';
      pcVar19[0x76] = '\0';
      pcVar19[0x77] = '\0';
      pcVar19[0x80] = '\0';
      pcVar19[0x81] = '\0';
      pcVar19[0x82] = '\0';
      pcVar19[0x83] = '\0';
      pcVar19[0x84] = '\0';
      pcVar19[0x85] = '\0';
      pcVar19[0x86] = '\0';
      pcVar19[0x87] = '\0';
      pcVar19[0x88] = '\0';
      pcVar19[0x89] = '\0';
      pcVar19[0x8a] = '\0';
      pcVar19[0x8b] = '\0';
      pcVar19[0x8c] = '\0';
      FUN_180099d78(pcVar19,0x8d,0x1800d6bb0,0x8c);
      *(undefined8 *)(&stack0x00000128 + lVar35) = 0x100000001;
      pcVar48 = pcVar19;
    }
    FUN_1800263b0((undefined8 *)(piVar14 + 0xce),(longlong *)(&stack0x00000120 + lVar35));
    uVar60 = extraout_s0_19;
    uVar53 = extraout_var_19;
    uVar16 = extraout_var_41;
    if ((pcVar19 != (char *)0x0) && (pcVar48 != (char *)0x0)) {
      pvVar18 = GetProcessHeap();
      HeapFree(pvVar18,0,pcVar48);
      *(undefined8 *)(&stack0x00000120 + lVar35) = 0;
      *(undefined8 *)(&stack0x00000128 + lVar35) = 0;
      uVar60 = extraout_s0_20;
      uVar53 = extraout_var_20;
      uVar16 = extraout_var_42;
    }
    pcVar19 = *(char **)(piVar14 + 0x46);
    if (*(char **)(piVar14 + 0x46) == (char *)0x0) {
      pcVar19 = pcVar21;
    }
    auVar11._4_4_ = uVar53;
    auVar11._0_4_ = uVar60;
    auVar11._8_8_ = uVar16;
    FUN_180026368(auVar11,CONCAT44(uVar56,uVar54),(undefined8 *)(piVar14 + 0xce),0x1800d6c60,pcVar19
                  ,param_6,param_7,param_8,param_9,param_10);
    *(undefined1 **)(&stack0x00000120 + lVar35) = &DAT_1800d4ecd;
    *(undefined8 *)(&stack0x00000128 + lVar35) = 0;
    *(undefined1 **)(&stack0x00000130 + lVar35) = &DAT_1800d4ecd;
    pvVar18 = GetProcessHeap();
    pcVar19 = (char *)HeapAlloc(pvVar18,0,7);
    if (pcVar19 == (char *)0x0) {
      *(undefined8 *)(&stack0x00000120 + lVar35) = 0;
      *(undefined1 **)(&stack0x00000120 + lVar35) = &DAT_1800d4ecd;
    }
    else {
      pcVar19[0] = '\0';
      pcVar19[1] = '\0';
      pcVar19[2] = '\0';
      pcVar19[3] = '\0';
      pcVar19[4] = '\0';
      pcVar19[5] = '\0';
      pcVar19[6] = '\0';
      *(char **)(&stack0x00000120 + lVar35) = pcVar19;
      FUN_180099d78(pcVar19,7,0x1800d6c58,6);
      *(undefined8 *)(&stack0x00000128 + lVar35) = 0x100000001;
      pcVar21 = pcVar19;
    }
    FUN_1800263b0((undefined8 *)(piVar14 + 0xce),(longlong *)(&stack0x00000120 + lVar35));
    if ((pcVar19 != (char *)0x0) && (pcVar21 != (char *)0x0)) {
      pvVar18 = GetProcessHeap();
      HeapFree(pvVar18,0,pcVar21);
      *(undefined8 *)(&stack0x00000120 + lVar35) = 0;
      *(undefined8 *)(&stack0x00000128 + lVar35) = 0;
    }
    plVar20 = *(longlong **)(&stack0xffffffffffffffe0 + lVar35);
    piVar14[0x1fc] = 0x1b;
    piVar14[0xdf] = 1;
    piVar14[4] = 2;
    FUN_1800079f8(plVar20,0x1800db710,10);
    lVar34 = *(longlong *)(&stack0x00000038 + lVar35);
    *(undefined4 *)(lVar34 + 0xc) = 1;
    *(undefined4 *)(lVar34 + 0x18) = 0x808080;
    *(undefined2 *)(lVar34 + 0x24) = 0;
    FUN_1800079f8(*(longlong **)(&stack0xffffffffffffff80 + lVar35),0x1800db6f8,8);
    lVar34 = *(longlong *)(&stack0xfffffffffffffff8 + lVar35);
    plVar20 = (longlong *)alStack_90[extraout_x15 * -2];
    *(undefined4 *)(lVar34 + 0xc) = 1;
    *(undefined2 *)(lVar34 + 0x24) = 1;
    *(undefined4 *)(lVar34 + 0x18) = 0xffff;
    lVar34 = *(longlong *)(&stack0xffffffffffffffc8 + lVar35);
    *(undefined2 *)(lVar34 + 0x24) = 0;
    *(undefined4 *)(lVar34 + 0x18) = 0xffff;
    lVar34 = *(longlong *)(&stack0x000000b8 + lVar35);
    *(undefined2 *)(lVar34 + 0x24) = 0;
    *(undefined4 *)(lVar34 + 0x18) = 0xffff;
    FUN_1800079f8(plVar20,0x1800db708,7);
    lVar34 = *(longlong *)(&stack0x00000088 + lVar35);
    plVar20 = (longlong *)alStack_90[extraout_x15 * -2 + 1];
    *(undefined4 *)(lVar34 + 0xc) = 1;
    *(undefined2 *)(lVar34 + 0x24) = 0;
    *(undefined4 *)(lVar34 + 0x18) = 0xffff;
    FUN_1800079f8(plVar20,0x1800db740,10);
    lVar34 = *(longlong *)(&stack0x00000078 + lVar35);
    plVar42 = *(longlong **)(&stack0x00000008 + lVar35);
    *(undefined4 *)(lVar34 + 0xc) = 1;
    *(undefined2 *)(lVar34 + 0x24) = 0;
    *(undefined4 *)(lVar34 + 0x18) = 0xffff;
    FUN_1800079f8(plVar42,0x1800db750,0xc);
    plVar20 = *(longlong **)(&stack0xffffffffffffff88 + lVar35);
    *(undefined4 *)((longlong)plVar42 + 0xc) = 1;
    *(undefined2 *)((longlong)plVar42 + 0x24) = 0;
    *(undefined4 *)(plVar42 + 3) = 0xffff;
    FUN_1800079f8(plVar20,0x1800d7b00,4);
    lVar34 = *(longlong *)(&stack0x00000070 + lVar35);
    plVar20 = *(longlong **)(&stack0xffffffffffffff90 + lVar35);
    *(undefined4 *)(lVar34 + 0xc) = 1;
    *(undefined2 *)(lVar34 + 0x24) = 0;
    *(undefined4 *)(lVar34 + 0x18) = 0xffff;
    FUN_1800079f8(plVar20,0x1800db720,10);
    lVar34 = *(longlong *)(&stack0x00000040 + lVar35);
    plVar20 = *(longlong **)(&stack0xffffffffffffffa8 + lVar35);
    *(undefined4 *)(lVar34 + 0xc) = 1;
    *(undefined2 *)(lVar34 + 0x24) = 0;
    *(undefined4 *)(lVar34 + 0x18) = 0xffff;
    FUN_1800079f8(plVar20,0x1800db730,10);
    lVar34 = *(longlong *)(&stack0x00000048 + lVar35);
    plVar20 = *(longlong **)(&stack0xffffffffffffff98 + lVar35);
    *(undefined4 *)(lVar34 + 0xc) = 1;
    *(undefined2 *)(lVar34 + 0x24) = 0;
    *(undefined4 *)(lVar34 + 0x18) = 0xffff;
    FUN_1800079f8(plVar20,0x1800db790,0xf);
    lVar34 = *(longlong *)(&stack0x00000050 + lVar35);
    plVar20 = *(longlong **)(&stack0xffffffffffffffb8 + lVar35);
    *(undefined4 *)(lVar34 + 0xc) = 1;
    *(undefined2 *)(lVar34 + 0x24) = 0;
    *(undefined4 *)(lVar34 + 0x18) = 0xffff;
    FUN_1800079f8(plVar20,0x1800db7a0,0x17);
    lVar34 = *(longlong *)(&stack0x00000058 + lVar35);
    plVar20 = *(longlong **)(&stack0xffffffffffffffa0 + lVar35);
    *(undefined4 *)(lVar34 + 0xc) = 1;
    *(undefined2 *)(lVar34 + 0x24) = 0;
    *(undefined4 *)(lVar34 + 0x18) = 0xffff;
    FUN_1800079f8(plVar20,0x1800db760,0x10);
    lVar34 = *(longlong *)(&stack0x00000060 + lVar35);
    lVar28 = *(longlong *)(&stack0x00000068 + lVar35);
    plVar20 = *(longlong **)(&stack0x00000118 + lVar35);
    *(undefined4 *)(lVar34 + 0xc) = 1;
    *(undefined2 *)(lVar34 + 0x24) = 0;
    *(undefined4 *)(lVar34 + 0x18) = 0xffff;
    *(undefined1 *)(lVar28 + 0x18) = 0xb;
    *(undefined4 *)(lVar28 + 0x1c) = 2;
    lVar34 = *(longlong *)(&stack0xfffffffffffffff0 + lVar35);
    *(undefined1 *)(lVar34 + 0x18) = 0xb;
    *(undefined4 *)(lVar34 + 0x1c) = 3;
    piVar40[7] = 0x42880000;
    *(undefined1 *)(piVar40 + 6) = 2;
    piVar40[0xb] = 0;
    piVar40[0xf] = 0x42c80000;
    *(undefined1 *)(piVar49 + 6) = 0xb;
    piVar49[7] = 8;
    piVar49[0xf] = 100;
    piVar49[0xb] = 0;
    FUN_1800079f8(plVar20,0x1800db778,0x11);
    lVar34 = *(longlong *)(&stack0xffffffffffffffd0 + lVar35);
    *(undefined4 *)(lVar34 + 0xc) = 1;
    if (*(code **)(lVar34 + 0x50) != (code *)0x0) {
      (**(code **)(lVar34 + 0x50))(*(undefined4 *)(lVar34 + 0x4c),"2 | 3;3 | 4;4 | 5");
    }
    lVar34 = *(longlong *)(&stack0xffffffffffffffd0 + lVar35);
    plVar20 = *(longlong **)(&stack0x00000098 + lVar35);
    *(undefined4 *)(lVar34 + 0x1c) = 1;
    *(undefined1 *)(lVar34 + 0x18) = 0x16;
    FUN_1800079f8(plVar20,0x1800db7e0,0x1e);
    *(undefined4 *)((longlong)plVar20 + 0xc) = 1;
    if ((code *)plVar20[10] != (code *)0x0) {
      (*(code *)plVar20[10])(*(undefined4 *)((longlong)plVar20 + 0x4c),"5 days;10 days");
    }
    lVar35 = *(longlong *)(&stack0x00000098 + lVar35);
    *(undefined1 *)(lVar35 + 0x18) = 0x16;
    *(undefined4 *)(lVar35 + 0x1c) = 1;
    return;
  }
  if (piVar14[0xe1] == 0) {
    auVar10._4_4_ = uVar53;
    auVar10._0_4_ = uVar60;
    auVar10._8_8_ = uVar29;
    uVar16 = FUN_1800254e8(auVar10,CONCAT44(uVar56,uVar54),(longlong)piVar14,uVar16,param_5,param_6,
                           param_7,param_8,param_9,param_10);
    *piVar15 = (int)uVar16;
  }
  if (*piVar15 != 0) {
    return;
  }
  uVar13 = piVar14[0xe1];
  lVar34 = *(longlong *)(piVar14 + 0x58);
  if (lVar34 == 0) {
    if (*(code **)(piVar14 + 0x5c) != (code *)0x0) {
      (**(code **)(piVar14 + 0x5c))(piVar14[0x5e]);
    }
    lVar34 = *(longlong *)(piVar14 + 0x58);
    if (lVar34 != 0) goto LAB_18008b624;
    piVar15 = piVar14 + 0x62;
  }
  else {
LAB_18008b624:
    iVar58 = piVar14[0x60];
    if (iVar58 == 0) {
      piVar15 = piVar14 + 0x62;
    }
    else {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar58 <= (int)uVar13) {
        uVar13 = iVar58 - 1;
      }
      piVar15 = (int *)(lVar34 + (longlong)(int)uVar13 * 8);
    }
  }
  pcVar27 = *(code **)(piVar14 + 0x4d4);
  *(undefined8 *)(&stack0x00000140 + lVar35) = *(undefined8 *)piVar15;
  (*pcVar27)(&stack0x00000180 + lVar35,&stack0x00000140 + lVar35,
             "PST-08PDT+01,M3.2.0/02:00,M11.1.0/02:00");
  if ((piVar14[0x47e] != 0) && (piVar14[0xe1] == 0)) {
    pcVar27 = *(code **)(piVar14 + 0x60c);
    *(undefined1 **)(&stack0x00000030 + lVar35) = &DAT_1800d4ecd;
    *(undefined1 **)(&stack0x00000020 + lVar35) = &DAT_1800d4ecd;
    *(undefined8 *)(&stack0x00000028 + lVar35) = 0;
    pvVar18 = GetProcessHeap();
    pcVar21 = (char *)HeapAlloc(pvVar18,0,0x14);
    if (pcVar21 == (char *)0x0) {
      *(undefined8 *)(&stack0x00000020 + lVar35) = 0;
      *(undefined8 *)(&stack0x00000020 + lVar35) = *(undefined8 *)(&stack0x00000030 + lVar35);
    }
    else {
      pcVar21[8] = '\0';
      pcVar21[9] = '\0';
      pcVar21[10] = '\0';
      pcVar21[0xb] = '\0';
      pcVar21[0xc] = '\0';
      pcVar21[0xd] = '\0';
      pcVar21[0xe] = '\0';
      pcVar21[0xf] = '\0';
      pcVar21[0] = '\0';
      pcVar21[1] = '\0';
      pcVar21[2] = '\0';
      pcVar21[3] = '\0';
      pcVar21[4] = '\0';
      pcVar21[5] = '\0';
      pcVar21[6] = '\0';
      pcVar21[7] = '\0';
      pcVar21[0x10] = '\0';
      pcVar21[0x11] = '\0';
      pcVar21[0x12] = '\0';
      pcVar21[0x13] = '\0';
      *(char **)(&stack0x00000020 + lVar35) = pcVar21;
      FUN_180099d78(pcVar21,0x14,0x1800d74c0,0x13);
      *(undefined8 *)(&stack0x00000028 + lVar35) = 0x100000001;
    }
    (*pcVar27)(0x20241105,&stack0x00000020 + lVar35);
    if ((*(int *)(&stack0x00000028 + lVar35) != 0) &&
       (lpMem = *(LPVOID *)(&stack0x00000020 + lVar35), lpMem != (LPVOID)0x0)) {
      pvVar18 = GetProcessHeap();
      HeapFree(pvVar18,0,lpMem);
      *(undefined8 *)(&stack0x00000020 + lVar35) = 0;
      *(undefined8 *)(&stack0x00000028 + lVar35) = 0;
    }
    if (*(longlong **)(&stack0x000000a0 + lVar35) != (longlong *)0x0) {
      FUN_18008d4f0(*(longlong **)(&stack0x000000a0 + lVar35));
      (**(code **)(piVar14 + 0x448))(0,0);
    }
    plVar20 = (longlong *)FUN_180096150(0x10);
    *(longlong **)(&stack0x00000020 + lVar35) = plVar20;
    plVar20[1] = 0;
    *plVar20 = 0;
    *plVar20 = 0;
    plVar20[1] = 0;
    lVar34 = FUN_180096150(0x28);
    *(undefined2 *)(lVar34 + 0x18) = 0x101;
    *(longlong *)lVar34 = lVar34;
    *(longlong *)(lVar34 + 8) = lVar34;
    *(longlong *)(lVar34 + 0x10) = lVar34;
    *plVar20 = lVar34;
    pcVar27 = *(code **)(piVar14 + 0x448);
    *(longlong **)(&stack0x000000a0 + lVar35) = plVar20;
    (*pcVar27)(0,plVar20);
    if (*(undefined8 **)(&stack0x00000000 + lVar35) != (undefined8 *)0x0) {
      FUN_18008d568(*(undefined8 **)(&stack0x00000000 + lVar35));
      (**(code **)(piVar14 + 0x448))(1,0);
    }
    puVar17 = (undefined8 *)FUN_180096150(0x18);
    *puVar17 = 0;
    puVar17[1] = 0;
    puVar17[2] = 0;
    pcVar27 = *(code **)(piVar14 + 0x448);
    *(undefined8 **)(&stack0x00000000 + lVar35) = puVar17;
    (*pcVar27)(1,puVar17);
    if (*(LPVOID *)(&stack0x000000a8 + lVar35) != (LPVOID)0x0) {
      FUN_1800966b8(*(LPVOID *)(&stack0x000000a8 + lVar35));
      (**(code **)(piVar14 + 0x448))(2,0);
    }
    uVar16 = FUN_180096718(0xc);
    pcVar27 = *(code **)(piVar14 + 0x448);
    *(undefined8 *)(&stack0x000000a8 + lVar35) = uVar16;
    (*pcVar27)(2,uVar16);
    *(undefined4 *)(&stack0x00000198 + lVar35) = 0;
    **(undefined4 **)(&stack0xffffffffffffffd8 + lVar35) = 0;
    **(undefined4 **)(&stack0x00000018 + lVar35) = 0;
    **(undefined4 **)(&stack0x000000c8 + lVar35) = 0;
    **(undefined4 **)(&stack0x000000b0 + lVar35) = 0;
    **(undefined4 **)(&stack0x00000010 + lVar35) = 0;
    **(undefined4 **)(&stack0x000000c0 + lVar35) = 0;
    **(undefined4 **)(&stack0x000000d0 + lVar35) = 0;
    **(undefined4 **)(&stack0x000000d8 + lVar35) = 0;
    **(undefined4 **)(&stack0x000000e0 + lVar35) = 0;
    **(undefined4 **)(&stack0x000000f0 + lVar35) = 0;
    **(undefined4 **)(&stack0x00000090 + lVar35) = 0;
    **(undefined4 **)(&stack0x000000e8 + lVar35) = 0;
    *(undefined8 *)(&stack0x00000188 + lVar35) = 0x100000100000000;
    *(undefined8 *)(&stack0x00000190 + lVar35) = 0xffffffff;
    *(undefined8 *)(&stack0x000001a4 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000019c + lVar35) = 0;
    *(undefined8 *)(&stack0x000001b4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000001ac + lVar35) = 0;
    *(undefined8 *)(&stack0x000001c4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000001bc + lVar35) = 0;
    *(undefined8 *)(&stack0x000001d4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000001cc + lVar35) = 0;
    *(undefined8 *)(&stack0x000001e4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000001dc + lVar35) = 0;
    *(undefined8 *)(&stack0x000001f4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000001ec + lVar35) = 0;
    *(undefined8 *)(&stack0x00000204 + lVar35) = 0;
    *(undefined8 *)(&stack0x000001fc + lVar35) = 0;
    *(undefined8 *)(&stack0x00000214 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000020c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000224 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000021c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000234 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000022c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000244 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000023c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000254 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000024c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000264 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000025c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000274 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000026c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000284 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000027c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000294 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000028c + lVar35) = 0;
    *(undefined8 *)(&stack0x000002a4 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000029c + lVar35) = 0;
    *(undefined8 *)(&stack0x000002b4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000002ac + lVar35) = 0;
    *(undefined8 *)(&stack0x000002c4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000002bc + lVar35) = 0;
    *(undefined8 *)(&stack0x000002d4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000002cc + lVar35) = 0;
    *(undefined8 *)(&stack0x000002e4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000002dc + lVar35) = 0;
    *(undefined8 *)(&stack0x000002f4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000002ec + lVar35) = 0;
    *(undefined8 *)(&stack0x00000304 + lVar35) = 0;
    *(undefined8 *)(&stack0x000002fc + lVar35) = 0;
    *(undefined8 *)(&stack0x00000314 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000030c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000324 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000031c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000334 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000032c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000344 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000033c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000354 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000034c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000364 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000035c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000374 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000036c + lVar35) = 0;
    (&stack0x0000037c)[lVar35] = 0;
    *(undefined4 *)(&stack0x00000380 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000038c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000384 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000039c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000394 + lVar35) = 0;
    *(undefined8 *)(&stack0x000003ac + lVar35) = 0;
    *(undefined8 *)(&stack0x000003a4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000003bc + lVar35) = 0;
    *(undefined8 *)(&stack0x000003b4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000003cc + lVar35) = 0;
    *(undefined8 *)(&stack0x000003c4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000003dc + lVar35) = 0;
    *(undefined8 *)(&stack0x000003d4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000003ec + lVar35) = 0;
    *(undefined8 *)(&stack0x000003e4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000003fc + lVar35) = 0;
    *(undefined8 *)(&stack0x000003f4 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000040c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000404 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000041c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000414 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000042c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000424 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000043c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000434 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000044c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000444 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000045c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000454 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000046c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000464 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000047c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000474 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000048c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000484 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000049c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000494 + lVar35) = 0;
    *(undefined8 *)(&stack0x000004ac + lVar35) = 0;
    *(undefined8 *)(&stack0x000004a4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000004bc + lVar35) = 0;
    *(undefined8 *)(&stack0x000004b4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000004cc + lVar35) = 0;
    *(undefined8 *)(&stack0x000004c4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000004dc + lVar35) = 0;
    *(undefined8 *)(&stack0x000004d4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000004ec + lVar35) = 0;
    *(undefined8 *)(&stack0x000004e4 + lVar35) = 0;
    *(undefined8 *)(&stack0x000004fc + lVar35) = 0;
    *(undefined8 *)(&stack0x000004f4 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000050c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000504 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000051c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000514 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000052c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000524 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000053c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000534 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000054c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000544 + lVar35) = 0;
    *(undefined8 *)(&stack0x0000055c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000554 + lVar35) = 0;
    *(undefined8 *)(&stack0x00000564 + lVar35) = 0;
    *(undefined4 *)(&stack0x0000056c + lVar35) = 0;
    *(undefined8 *)(&stack0x00000570 + lVar35) = 0;
    *(undefined8 *)(&stack0x00000578 + lVar35) = 0;
    *(undefined8 *)(&stack0x00000580 + lVar35) = 0;
    *(undefined8 *)(&stack0x00000588 + lVar35) = 0;
    *(undefined4 *)(&stack0x00000594 + lVar35) = 0xffffff;
    (&stack0x00000590)[lVar35] = 0;
    *(undefined2 *)(&stack0x00000598 + lVar35) = 0;
    FUN_180096870(&stack0x000005a0 + lVar35,0x18,0x3c);
    FUN_180096870(&stack0x00000b40 + lVar35,0x18,0x3c);
    *(undefined8 *)(&stack0x000010e0 + lVar35) = 0;
    *(undefined8 *)(&stack0x000010e8 + lVar35) = 0;
    *(undefined4 *)(&stack0x000010f0 + lVar35) = 0;
    puVar23 = (undefined4 *)(&stack0x000001a0 + lVar35);
    iVar58 = 2;
    do {
      puVar23[0x79] = 0xff7fffff;
      puVar23[0xb5] = 0x7f7fffff;
      puVar23[-1] = 0x28;
      puVar23[0x3b] = iVar58 + -2;
      puVar23[0x3c] = iVar58 + -1;
      *puVar23 = 0x28;
      puVar23[0x7a] = 0xff7fffff;
      puVar23[0xb6] = 0x7f7fffff;
      puVar23[0x3d] = iVar58;
      puVar23[1] = 0x28;
      puVar23[0x7b] = 0xff7fffff;
      puVar23[0xb7] = 0x7f7fffff;
      puVar23[0x3e] = iVar58 + 1;
      puVar23[2] = 0x28;
      puVar23[0x7c] = 0xff7fffff;
      puVar23[0xb8] = 0x7f7fffff;
      puVar23[0x3f] = iVar58 + 2;
      puVar23[3] = 0x28;
      puVar23[0x7d] = 0xff7fffff;
      puVar23[0xb9] = 0x7f7fffff;
      puVar23[0x40] = iVar58 + 3;
      puVar23[4] = 0x28;
      puVar23[0x7e] = 0xff7fffff;
      puVar23[0xba] = 0x7f7fffff;
      puVar23[0x41] = iVar58 + 4;
      puVar23[5] = 0x28;
      puVar23[0x7f] = 0xff7fffff;
      puVar23[0xbb] = 0x7f7fffff;
      puVar23[0x42] = iVar58 + 5;
      puVar23[6] = 0x28;
      puVar23[0x80] = 0xff7fffff;
      puVar23[0xbc] = 0x7f7fffff;
      puVar23[0x43] = iVar58 + 6;
      puVar23[7] = 0x28;
      puVar23[0x81] = 0xff7fffff;
      puVar23[0xbd] = 0x7f7fffff;
      puVar23[0x44] = iVar58 + 7;
      puVar23[8] = 0x28;
      puVar23[0x82] = 0xff7fffff;
      puVar23[0xbe] = 0x7f7fffff;
      puVar23[0x45] = iVar58 + 8;
      puVar23[9] = 0x28;
      puVar23[0x83] = 0xff7fffff;
      puVar23[0xbf] = 0x7f7fffff;
      puVar23[0x46] = iVar58 + 9;
      puVar23[10] = 0x28;
      puVar23[0x84] = 0xff7fffff;
      puVar23[0xc0] = 0x7f7fffff;
      puVar23[0x47] = iVar58 + 10;
      puVar23[0xb] = 0x28;
      puVar23[0x85] = 0xff7fffff;
      puVar23[0xc1] = 0x7f7fffff;
      puVar23[0x48] = iVar58 + 0xb;
      puVar23[0xc] = 0x28;
      puVar23[0x86] = 0xff7fffff;
      puVar23[0xc2] = 0x7f7fffff;
      puVar23[0x49] = iVar58 + 0xc;
      puVar23[0xd] = 0x28;
      puVar23[0x87] = 0xff7fffff;
      puVar23[0xc3] = 0x7f7fffff;
      puVar23[0x4a] = iVar58 + 0xd;
      puVar23[0xe] = 0x28;
      puVar23[0x88] = 0xff7fffff;
      puVar23[0xc4] = 0x7f7fffff;
      puVar23[0x4b] = iVar58 + 0xe;
      puVar23[0xf] = 0x28;
      puVar23[0x89] = 0xff7fffff;
      puVar23[0xc5] = 0x7f7fffff;
      puVar23[0x4c] = iVar58 + 0xf;
      puVar23[0x10] = 0x28;
      puVar23[0x8a] = 0xff7fffff;
      puVar23[0xc6] = 0x7f7fffff;
      puVar23[0x4d] = iVar58 + 0x10;
      puVar23[0x11] = 0x28;
      puVar23[0x8b] = 0xff7fffff;
      puVar23[199] = 0x7f7fffff;
      puVar23[0x4e] = iVar58 + 0x11;
      iVar55 = iVar58 + 0x12;
      puVar23[0x12] = 0x28;
      puVar23[0x8c] = 0xff7fffff;
      puVar23[200] = 0x7f7fffff;
      puVar23 = puVar23 + 0x14;
      iVar58 = iVar58 + 0x14;
    } while (iVar55 < 0x3c);
    (&stack0x0000018c)[lVar35] = 0;
    *(undefined4 *)(&stack0x00000190 + lVar35) = 0;
    uVar22 = FUN_180026708((longlong)piVar49);
    (&stack0x00000198)[lVar35] = 1;
    pcVar27 = *(code **)(piVar14 + 0x3be);
    *(int *)(&stack0x00000194 + lVar35) = (int)uVar22;
    *(undefined4 *)(&stack0x000001b8 + lVar35) = 0;
    (*pcVar27)(&stack0x00000188 + lVar35);
    FUN_180096738(&stack0x00000b40 + lVar35,0x18,0x3c);
    FUN_180096738(&stack0x000005a0 + lVar35,0x18,0x3c);
  }
  if (piVar14[0x1b] != 0) {
    if (*(longlong **)(&stack0x000000a0 + lVar35) != (longlong *)0x0) {
      FUN_18008d4f0(*(longlong **)(&stack0x000000a0 + lVar35));
      (**(code **)(piVar14 + 0x448))(0,0);
    }
    if (*(undefined8 **)(&stack0x00000000 + lVar35) != (undefined8 *)0x0) {
      FUN_18008d568(*(undefined8 **)(&stack0x00000000 + lVar35));
      (**(code **)(piVar14 + 0x448))(1,0);
    }
    if (*(LPVOID *)(&stack0x000000a8 + lVar35) == (LPVOID)0x0) {
      return;
    }
    FUN_1800966b8(*(LPVOID *)(&stack0x000000a8 + lVar35));
    (**(code **)(piVar14 + 0x448))(2,0);
    return;
  }
  if (*(longlong *)(&stack0x000000a0 + lVar35) == 0) {
    return;
  }
  if (*(longlong *)(&stack0x00000000 + lVar35) == 0) {
    return;
  }
  if (*(longlong *)(&stack0x000000a8 + lVar35) == 0) {
    return;
  }
  (**(code **)(piVar14 + 0x3ec))(piVar14[0x270]);
  if (piVar14[0xe1] == 0) {
    puVar23 = (undefined4 *)FUN_18000f448(*(longlong *)(&stack0xffffffffffffff80 + lVar35),0);
    *puVar23 = 0;
    puVar23 = (undefined4 *)FUN_18000f448(alStack_90[extraout_x15 * -2],piVar14[0xe1]);
    *puVar23 = 0;
    puVar23 = (undefined4 *)FUN_18000f448(alStack_90[extraout_x15 * -2 + 1],piVar14[0xe1]);
    *puVar23 = 0;
    puVar23 = (undefined4 *)FUN_18000f448(*(longlong *)(&stack0x00000008 + lVar35),piVar14[0xe1]);
    *puVar23 = 0;
    puVar23 = (undefined4 *)
              FUN_18000f448(*(longlong *)(&stack0xffffffffffffff88 + lVar35),piVar14[0xe1]);
    *puVar23 = 0;
    puVar23 = (undefined4 *)
              FUN_18000f448(*(longlong *)(&stack0xffffffffffffff90 + lVar35),piVar14[0xe1]);
    *puVar23 = 0;
    iVar58 = piVar14[0xe1];
    pfVar24 = (float *)FUN_18000f448(*(longlong *)(&stack0xffffffffffffffa8 + lVar35),iVar58);
    *pfVar24 = (float)iVar58;
    puVar23 = (undefined4 *)
              FUN_18000f448(*(longlong *)(&stack0xffffffffffffff98 + lVar35),piVar14[0xe1]);
    *puVar23 = 0;
    puVar23 = (undefined4 *)
              FUN_18000f448(*(longlong *)(&stack0xffffffffffffffa0 + lVar35),piVar14[0xe1]);
    *puVar23 = 0;
  }
  else {
    lVar34 = *(longlong *)(&stack0xffffffffffffff80 + lVar35);
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
    uVar60 = *puVar23;
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
    lVar34 = alStack_90[extraout_x15 * -2];
    *puVar23 = uVar60;
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
    uVar60 = *puVar23;
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
    lVar34 = alStack_90[extraout_x15 * -2 + 1];
    *puVar23 = uVar60;
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
    uVar60 = *puVar23;
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
    lVar34 = *(longlong *)(&stack0x00000008 + lVar35);
    *puVar23 = uVar60;
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
    uVar60 = *puVar23;
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
    lVar34 = *(longlong *)(&stack0xffffffffffffff88 + lVar35);
    *puVar23 = uVar60;
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
    uVar60 = *puVar23;
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
    lVar34 = *(longlong *)(&stack0xffffffffffffff90 + lVar35);
    *puVar23 = uVar60;
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
    uVar60 = *puVar23;
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
    *puVar23 = uVar60;
    iVar58 = piVar14[0xe1];
    pfVar24 = (float *)FUN_18000f448(*(longlong *)(&stack0xffffffffffffffa8 + lVar35),iVar58);
    lVar34 = *(longlong *)(&stack0xffffffffffffff98 + lVar35);
    *pfVar24 = (float)iVar58;
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
    uVar60 = *puVar23;
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
    lVar34 = *(longlong *)(&stack0xffffffffffffffa0 + lVar35);
    *puVar23 = uVar60;
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
    uVar60 = *puVar23;
    puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
    *puVar23 = uVar60;
  }
  if ((piVar14[0x2b] == 0) && (piVar14[0xe1] == *piVar14 + -1)) {
    return;
  }
  plVar20 = FUN_180005d78((longlong *)(piVar14 + 0x58),piVar14[0xe1] + -1);
  (**(code **)(piVar14 + 0x3a4))(&stack0x00000150 + lVar35,plVar20,1,1,0,0);
  plVar20 = FUN_180005d78((longlong *)(piVar14 + 0x58),piVar14[0xe1]);
  (**(code **)(piVar14 + 0x3a4))(&stack0x00000148 + lVar35,plVar20,1,1,0,0);
  if (*(longlong *)(&stack0x00000148 + lVar35) != *(longlong *)(&stack0x00000150 + lVar35)) {
    iVar58 = piVar14[0xe1];
    **(int **)(&stack0xffffffffffffffd8 + lVar35) = iVar58;
    **(int **)(&stack0x00000018 + lVar35) = iVar58;
    **(int **)(&stack0x000000c8 + lVar35) = iVar58;
    **(undefined4 **)(&stack0x000000b0 + lVar35) = 0;
    **(undefined4 **)(&stack0x00000010 + lVar35) = 0;
    **(int **)(&stack0x00000090 + lVar35) = piVar14[0xe1];
  }
  uVar13 = FUN_180026550(*(longlong *)(&stack0x00000118 + lVar35));
  if (uVar13 == 0) {
    iVar58 = 2;
    iVar55 = 3;
  }
  else {
    uVar13 = FUN_180026550(extraout_x11);
    if (uVar13 == 1) {
      iVar58 = 3;
      iVar55 = 4;
    }
    else {
      iVar58 = 4;
      iVar55 = 5;
    }
  }
  iVar57 = piVar14[0xe1];
  *(int *)(&stack0x00000038 + lVar35) = iVar55;
  iVar59 = iVar57 - iVar55;
  *(int *)(&stack0xfffffffffffffff8 + lVar35) = iVar59;
  if (iVar59 - iVar58 < 0) {
    return;
  }
  *(undefined4 *)(&stack0xffffffffffffffc8 + lVar35) = 0;
  piVar14[0x433] = **(int **)(&stack0xffffffffffffffd8 + lVar35);
  if (iVar57 < **(int **)(&stack0x00000090 + lVar35) + iVar55 + iVar58) {
LAB_18008c798:
    iVar58 = **(int **)(&stack0x00000018 + lVar35);
    iVar55 = **(int **)(&stack0x00000090 + lVar35);
    if (*(int *)(&stack0xffffffffffffffc8 + lVar35) == 0) goto LAB_18008d080;
  }
  else {
    iVar55 = 0;
    do {
      lVar34 = *(longlong *)(piVar14 + 0x298);
      if (lVar34 == 0) {
        if (*(code **)(piVar14 + 0x29c) != (code *)0x0) {
          (**(code **)(piVar14 + 0x29c))(piVar14[0x29e]);
        }
        lVar34 = *(longlong *)(piVar14 + 0x298);
        if (lVar34 != 0) goto LAB_18008bf98;
        iVar57 = piVar14[0x2a1];
LAB_18008bfc4:
        if (*(code **)(piVar14 + 0x29c) != (code *)0x0) {
          (**(code **)(piVar14 + 0x29c))(piVar14[0x29e]);
        }
        lVar34 = *(longlong *)(piVar14 + 0x298);
        if (lVar34 != 0) goto LAB_18008bfe4;
        piVar15 = piVar14 + 0x2a1;
      }
      else {
LAB_18008bf98:
        iVar57 = piVar14[0x2a0];
        if (iVar57 == 0) {
          piVar15 = piVar14 + 0x2a1;
        }
        else {
          uVar13 = iVar59 - 1U & ((int)(iVar59 - 1U) >> 0x1f ^ 0xffffffffU);
          if (iVar57 <= (int)uVar13) {
            uVar13 = iVar57 - 1;
          }
          piVar15 = (int *)(lVar34 + (longlong)(int)uVar13 * 4);
        }
        iVar57 = *piVar15;
        if (lVar34 == 0) goto LAB_18008bfc4;
LAB_18008bfe4:
        iVar8 = piVar14[0x2a0];
        if (iVar8 == 0) {
          piVar15 = piVar14 + 0x2a1;
        }
        else {
          iVar31 = iVar59;
          if (iVar59 < 0) {
            iVar31 = 0;
          }
          if (iVar8 <= iVar31) {
            iVar31 = iVar8 + -1;
          }
          piVar15 = (int *)(lVar34 + (longlong)iVar31 * 4);
        }
      }
      iVar57 = (**(code **)(piVar14 + 0x134))(iVar57,*piVar15,0,0,piVar14[0x3c3],7,piVar14[0x3c3],0)
      ;
      if (iVar57 != 0) goto LAB_18008c378;
      iVar55 = iVar55 + 1;
      iVar59 = iVar59 + -1;
    } while (iVar55 < iVar58);
    uVar16 = *(undefined8 *)(&stack0xffffffffffffffa0 + lVar35);
    *(undefined4 *)(&stack0xffffffffffffffd0 + lVar35) = 0;
    uVar13 = *(uint *)(&stack0xfffffffffffffff8 + lVar35);
    *(int *)(&stack0x00000118 + lVar35) = iVar58;
    *(undefined8 *)(&stack0xffffffffffffff98 + lVar35) =
         *(undefined8 *)(&stack0xffffffffffffff98 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffffa0 + lVar35) = uVar16;
    *(undefined8 *)(&stack0xffffffffffffffa8 + lVar35) =
         *(undefined8 *)(&stack0xffffffffffffffa8 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffff90 + lVar35) =
         *(undefined8 *)(&stack0xffffffffffffff90 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffff88 + lVar35) =
         *(undefined8 *)(&stack0xffffffffffffff88 + lVar35);
    lVar34 = alStack_90[extraout_x15 * -2 + 1];
    alStack_90[extraout_x15 * -2] = alStack_90[extraout_x15 * -2];
    alStack_90[extraout_x15 * -2 + 1] = lVar34;
    uVar29 = *(undefined8 *)(&stack0xffffffffffffffb8 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffffb0 + lVar35) =
         *(undefined8 *)(&stack0xffffffffffffffb0 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffffb8 + lVar35) = uVar29;
    *(undefined8 *)(&stack0xffffffffffffffc0 + lVar35) =
         *(undefined8 *)(&stack0xffffffffffffffc0 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffff80 + lVar35) =
         *(undefined8 *)(&stack0xffffffffffffff80 + lVar35);
    do {
      lVar28 = *(longlong *)(piVar14 + 0x298);
      if (lVar28 == 0) {
        if (*(code **)(piVar14 + 0x29c) != (code *)0x0) {
          (**(code **)(piVar14 + 0x29c))(piVar14[0x29e]);
        }
        lVar28 = *(longlong *)(piVar14 + 0x298);
        if (lVar28 != 0) goto LAB_18008c0c4;
        iVar58 = piVar14[0x2a1];
LAB_18008c0f8:
        if (*(code **)(piVar14 + 0x29c) != (code *)0x0) {
          (**(code **)(piVar14 + 0x29c))(piVar14[0x29e]);
        }
        lVar28 = *(longlong *)(piVar14 + 0x298);
        if (lVar28 != 0) goto LAB_18008c118;
        piVar15 = piVar14 + 0x2a1;
      }
      else {
LAB_18008c0c4:
        iVar58 = piVar14[0x2a0];
        if (iVar58 == 0) {
          piVar15 = piVar14 + 0x2a1;
        }
        else {
          uVar32 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
          if (iVar58 <= (int)uVar32) {
            uVar32 = iVar58 - 1;
          }
          piVar15 = (int *)(lVar28 + (longlong)(int)uVar32 * 4);
        }
        iVar58 = *piVar15;
        if (lVar28 == 0) goto LAB_18008c0f8;
LAB_18008c118:
        iVar55 = uVar13 + 1;
        iVar57 = piVar14[0x2a0];
        if (iVar57 == 0) {
          piVar15 = piVar14 + 0x2a1;
        }
        else {
          if (iVar55 < 0) {
            iVar55 = 0;
          }
          if (iVar57 <= iVar55) {
            iVar55 = iVar57 + -1;
          }
          piVar15 = (int *)(lVar28 + (longlong)iVar55 * 4);
        }
      }
      iVar55 = piVar14[0x3c3];
      iVar57 = *piVar15;
      pcVar27 = *(code **)(piVar14 + 0x134);
      *(undefined8 *)(&stack0x00000078 + lVar35) =
           *(undefined8 *)(&stack0xffffffffffffff80 + lVar35);
      *(undefined8 *)(&stack0x00000070 + lVar35) =
           *(undefined8 *)(&stack0xffffffffffffffc0 + lVar35);
      uVar43 = *(undefined8 *)(&stack0xffffffffffffffa8 + lVar35);
      *(undefined8 *)(&stack0x00000040 + lVar35) =
           *(undefined8 *)(&stack0xffffffffffffffb0 + lVar35);
      *(longlong *)(&stack0x00000048 + lVar35) = alStack_90[extraout_x15 * -2];
      *(undefined8 *)(&stack0x00000050 + lVar35) =
           *(undefined8 *)(&stack0xffffffffffffff88 + lVar35);
      uVar50 = *(undefined8 *)(&stack0xffffffffffffff98 + lVar35);
      *(undefined8 *)(&stack0x00000058 + lVar35) =
           *(undefined8 *)(&stack0xffffffffffffff90 + lVar35);
      iVar58 = (*pcVar27)(iVar58,iVar57,0,0,iVar55,6,iVar55,0);
      if (iVar58 != 0) {
        iVar58 = *(int *)(&stack0x00000118 + lVar35);
        goto LAB_18008c378;
      }
      iVar58 = *(int *)(&stack0xffffffffffffffd0 + lVar35);
      uVar13 = uVar13 + 1;
      *(int *)(&stack0xffffffffffffffd0 + lVar35) = iVar58 + 1;
    } while (iVar58 + 1 < *(int *)(&stack0x00000038 + lVar35));
    *(undefined8 *)(&stack0xfffffffffffffff0 + lVar35) = uVar29;
    piVar15 = *(int **)(&stack0x00000010 + lVar35);
    *(undefined8 *)(&stack0x00000060 + lVar35) = uVar43;
    *(undefined8 *)(&stack0x00000068 + lVar35) = uVar50;
    iVar58 = *(int *)(&stack0x00000118 + lVar35);
    if (*piVar15 == 1) {
      piVar49 = *(int **)(&stack0xffffffffffffffd8 + lVar35);
      lVar28 = FUN_180005d08((longlong *)(piVar14 + 0x298),*piVar49);
      iVar55 = *(int *)(&stack0xfffffffffffffff8 + lVar35);
      *(longlong *)(&stack0x00000088 + lVar35) = lVar28;
      pfVar24 = (float *)FUN_180005d08((longlong *)(piVar14 + 0x298),iVar55);
      fVar67 = *pfVar24;
      *(undefined8 *)(&stack0x000000b8 + lVar35) =
           *(undefined8 *)(&stack0xfffffffffffffff0 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffffb8 + lVar35) =
           *(undefined8 *)(&stack0xfffffffffffffff0 + lVar35);
      fVar68 = **(float **)(&stack0x00000088 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffffe0 + lVar35) =
           *(undefined8 *)(&stack0x00000068 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffff98 + lVar35) =
           *(undefined8 *)(&stack0x00000068 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffffa0 + lVar35) = uVar16;
      *(undefined8 *)(&stack0x00000118 + lVar35) = *(undefined8 *)(&stack0x00000060 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffffa8 + lVar35) =
           *(undefined8 *)(&stack0x00000060 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffffd0 + lVar35) =
           *(undefined8 *)(&stack0x00000058 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffff90 + lVar35) =
           *(undefined8 *)(&stack0x00000058 + lVar35);
      *(undefined8 *)(&stack0x000000f8 + lVar35) = *(undefined8 *)(&stack0x00000050 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffff88 + lVar35) =
           *(undefined8 *)(&stack0x00000050 + lVar35);
      *(undefined8 *)(&stack0x00000100 + lVar35) = *(undefined8 *)(&stack0x00000048 + lVar35);
      alStack_90[extraout_x15 * -2] = *(undefined8 *)(&stack0x00000048 + lVar35);
      alStack_90[extraout_x15 * -2 + 1] = lVar34;
      *(undefined8 *)(&stack0x00000108 + lVar35) = *(undefined8 *)(&stack0x00000040 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffffb0 + lVar35) =
           *(undefined8 *)(&stack0x00000040 + lVar35);
      *(undefined8 *)(&stack0x00000080 + lVar35) = *(undefined8 *)(&stack0x00000070 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffffc0 + lVar35) =
           *(undefined8 *)(&stack0x00000070 + lVar35);
      *(undefined8 *)(&stack0x00000020 + lVar35) = *(undefined8 *)(&stack0x00000078 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffff80 + lVar35) =
           *(undefined8 *)(&stack0x00000078 + lVar35);
      if (fVar68 <= fVar67) {
        if (*piVar15 == 1) {
          pfVar24 = (float *)FUN_180005d08((longlong *)(piVar14 + 0x298),*piVar49);
          pfVar25 = (float *)FUN_180005d08((longlong *)(piVar14 + 0x298),iVar55);
          if (*pfVar24 <= *pfVar25) {
            uVar29 = *(undefined8 *)(&stack0x000000b8 + lVar35);
            *piVar49 = iVar55;
            *(undefined8 *)(&stack0xffffffffffffffb8 + lVar35) = uVar29;
            *(undefined8 *)(&stack0xffffffffffffff98 + lVar35) =
                 *(undefined8 *)(&stack0xffffffffffffffe0 + lVar35);
            *(undefined8 *)(&stack0xffffffffffffffa0 + lVar35) = uVar16;
            *(undefined8 *)(&stack0xffffffffffffffa8 + lVar35) =
                 *(undefined8 *)(&stack0x00000118 + lVar35);
            *(undefined8 *)(&stack0xffffffffffffff90 + lVar35) =
                 *(undefined8 *)(&stack0xffffffffffffffd0 + lVar35);
            *(undefined8 *)(&stack0xffffffffffffff88 + lVar35) =
                 *(undefined8 *)(&stack0x000000f8 + lVar35);
            alStack_90[extraout_x15 * -2] = *(undefined8 *)(&stack0x00000100 + lVar35);
            alStack_90[extraout_x15 * -2 + 1] = lVar34;
            *(undefined8 *)(&stack0xffffffffffffffb0 + lVar35) =
                 *(undefined8 *)(&stack0x00000108 + lVar35);
            *(undefined8 *)(&stack0xffffffffffffffc0 + lVar35) =
                 *(undefined8 *)(&stack0x00000080 + lVar35);
            *(undefined8 *)(&stack0xffffffffffffff80 + lVar35) =
                 *(undefined8 *)(&stack0x00000020 + lVar35);
            goto LAB_18008c378;
          }
        }
        goto LAB_18008c300;
      }
    }
    else {
LAB_18008c300:
      uVar29 = *(undefined8 *)(&stack0x00000060 + lVar35);
      uVar2 = *(undefined8 *)(&stack0x00000068 + lVar35);
      uVar43 = *(undefined8 *)(&stack0x00000050 + lVar35);
      uVar3 = *(undefined8 *)(&stack0x00000058 + lVar35);
      uVar50 = *(undefined8 *)(&stack0x00000040 + lVar35);
      uVar4 = *(undefined8 *)(&stack0x00000048 + lVar35);
      uVar1 = *(undefined8 *)(&stack0x00000070 + lVar35);
      uVar5 = *(undefined8 *)(&stack0x00000078 + lVar35);
      iVar55 = **(int **)(&stack0x00000010 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffffa0 + lVar35) = uVar16;
      *(undefined8 *)(&stack0xffffffffffffffa8 + lVar35) = uVar29;
      uVar26 = *(undefined8 *)(&stack0xfffffffffffffff0 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffff90 + lVar35) = uVar3;
      *(undefined8 *)(&stack0xffffffffffffff98 + lVar35) = uVar2;
      alStack_90[extraout_x15 * -2] = uVar4;
      alStack_90[extraout_x15 * -2 + 1] = lVar34;
      *(undefined8 *)(&stack0xffffffffffffffc0 + lVar35) = uVar1;
      *(undefined8 *)(&stack0xffffffffffffff80 + lVar35) = uVar5;
      *(undefined8 *)(&stack0xffffffffffffff88 + lVar35) = uVar43;
      *(undefined8 *)(&stack0xffffffffffffffb0 + lVar35) = uVar50;
      *(undefined8 *)(&stack0xffffffffffffffb8 + lVar35) = uVar26;
      if (iVar55 < 1) {
        *(undefined8 *)(&stack0xffffffffffffffa0 + lVar35) = uVar16;
        *(undefined8 *)(&stack0xffffffffffffffa8 + lVar35) = uVar29;
        *(undefined4 *)(&stack0xffffffffffffffc8 + lVar35) = 1;
        *(undefined8 *)(&stack0xffffffffffffff90 + lVar35) = uVar3;
        *(undefined8 *)(&stack0xffffffffffffff98 + lVar35) = uVar2;
        alStack_90[extraout_x15 * -2] = uVar4;
        alStack_90[extraout_x15 * -2 + 1] = lVar34;
        *(undefined8 *)(&stack0xffffffffffffffb0 + lVar35) = uVar50;
        *(undefined8 *)(&stack0xffffffffffffffb8 + lVar35) = uVar26;
        *(undefined8 *)(&stack0xffffffffffffffc0 + lVar35) = uVar1;
        *(undefined8 *)(&stack0xffffffffffffff80 + lVar35) = uVar5;
        *(undefined8 *)(&stack0xffffffffffffff88 + lVar35) = uVar43;
      }
    }
LAB_18008c378:
    plVar20 = (longlong *)(piVar14 + 0x2a2);
    iVar57 = *(int *)(&stack0xfffffffffffffff8 + lVar35);
    iVar55 = 0;
    do {
      lVar34 = *plVar20;
      if (lVar34 == 0) {
        if (*(code **)(piVar14 + 0x2a6) != (code *)0x0) {
          (**(code **)(piVar14 + 0x2a6))(piVar14[0x2a8]);
        }
        lVar34 = *plVar20;
        if (lVar34 != 0) goto LAB_18008c3ac;
        iVar59 = piVar14[0x2ab];
LAB_18008c3d8:
        if (*(code **)(piVar14 + 0x2a6) != (code *)0x0) {
          (**(code **)(piVar14 + 0x2a6))(piVar14[0x2a8]);
        }
        lVar34 = *plVar20;
        if (lVar34 != 0) goto LAB_18008c3f8;
        piVar15 = piVar14 + 0x2ab;
      }
      else {
LAB_18008c3ac:
        iVar59 = piVar14[0x2aa];
        if (iVar59 == 0) {
          piVar15 = piVar14 + 0x2ab;
        }
        else {
          uVar13 = iVar57 - 1U & ((int)(iVar57 - 1U) >> 0x1f ^ 0xffffffffU);
          if (iVar59 <= (int)uVar13) {
            uVar13 = iVar59 - 1;
          }
          piVar15 = (int *)(lVar34 + (longlong)(int)uVar13 * 4);
        }
        iVar59 = *piVar15;
        if (lVar34 == 0) goto LAB_18008c3d8;
LAB_18008c3f8:
        iVar8 = piVar14[0x2aa];
        if (iVar8 == 0) {
          piVar15 = piVar14 + 0x2ab;
        }
        else {
          iVar31 = iVar57;
          if (iVar57 < 0) {
            iVar31 = 0;
          }
          if (iVar8 <= iVar31) {
            iVar31 = iVar8 + -1;
          }
          piVar15 = (int *)(lVar34 + (longlong)iVar31 * 4);
        }
      }
      iVar59 = (**(code **)(piVar14 + 0x134))(iVar59,*piVar15,0,0,piVar14[0x3c3],6,piVar14[0x3c3],0)
      ;
      if (iVar59 != 0) goto LAB_18008c798;
      iVar55 = iVar55 + 1;
      iVar57 = iVar57 + -1;
    } while (iVar55 < iVar58);
    *(undefined4 *)(&stack0x00000118 + lVar35) = 0;
    uVar13 = *(uint *)(&stack0xfffffffffffffff8 + lVar35);
    *(int **)(&stack0xffffffffffffffe0 + lVar35) = piVar14;
    *(undefined4 *)(&stack0xffffffffffffffc8 + lVar35) =
         *(undefined4 *)(&stack0xffffffffffffffc8 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffffb8 + lVar35) =
         *(undefined8 *)(&stack0xffffffffffffffb8 + lVar35);
    uVar16 = *(undefined8 *)(&stack0xffffffffffffffa0 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffff98 + lVar35) =
         *(undefined8 *)(&stack0xffffffffffffff98 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffffa0 + lVar35) = uVar16;
    *(undefined8 *)(&stack0xffffffffffffffa8 + lVar35) =
         *(undefined8 *)(&stack0xffffffffffffffa8 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffff90 + lVar35) =
         *(undefined8 *)(&stack0xffffffffffffff90 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffff88 + lVar35) =
         *(undefined8 *)(&stack0xffffffffffffff88 + lVar35);
    lVar34 = alStack_90[extraout_x15 * -2 + 1];
    alStack_90[extraout_x15 * -2] = alStack_90[extraout_x15 * -2];
    alStack_90[extraout_x15 * -2 + 1] = lVar34;
    *(undefined8 *)(&stack0xffffffffffffffb0 + lVar35) =
         *(undefined8 *)(&stack0xffffffffffffffb0 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffffc0 + lVar35) =
         *(undefined8 *)(&stack0xffffffffffffffc0 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffff80 + lVar35) =
         *(undefined8 *)(&stack0xffffffffffffff80 + lVar35);
    do {
      lVar28 = *plVar20;
      if (lVar28 == 0) {
        if (*(code **)(piVar14 + 0x2a6) != (code *)0x0) {
          (**(code **)(piVar14 + 0x2a6))(piVar14[0x2a8]);
        }
        lVar28 = *plVar20;
        if (lVar28 != 0) goto LAB_18008c4e8;
        iVar58 = piVar14[0x2ab];
LAB_18008c51c:
        if (*(code **)(piVar14 + 0x2a6) != (code *)0x0) {
          (**(code **)(piVar14 + 0x2a6))(piVar14[0x2a8]);
        }
        lVar28 = *plVar20;
        if (lVar28 != 0) goto LAB_18008c53c;
        piVar15 = piVar14 + 0x2ab;
      }
      else {
LAB_18008c4e8:
        iVar58 = piVar14[0x2aa];
        if (iVar58 == 0) {
          piVar15 = piVar14 + 0x2ab;
        }
        else {
          uVar32 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
          if (iVar58 <= (int)uVar32) {
            uVar32 = iVar58 - 1;
          }
          piVar15 = (int *)(lVar28 + (longlong)(int)uVar32 * 4);
        }
        iVar58 = *piVar15;
        if (lVar28 == 0) goto LAB_18008c51c;
LAB_18008c53c:
        iVar55 = uVar13 + 1;
        iVar57 = piVar14[0x2aa];
        if (iVar57 == 0) {
          piVar15 = piVar14 + 0x2ab;
        }
        else {
          if (iVar55 < 0) {
            iVar55 = 0;
          }
          if (iVar57 <= iVar55) {
            iVar55 = iVar57 + -1;
          }
          piVar15 = (int *)(lVar28 + (longlong)iVar55 * 4);
        }
      }
      iVar55 = *piVar15;
      *(undefined8 *)(&stack0x00000040 + lVar35) =
           *(undefined8 *)(&stack0xffffffffffffff80 + lVar35);
      uVar60 = *(undefined4 *)(*(longlong *)(&stack0xffffffffffffffe0 + lVar35) + 0xf0c);
      pcVar27 = *(code **)(*(longlong *)(&stack0xffffffffffffffe0 + lVar35) + 0x4d0);
      *(undefined8 *)(&stack0x00000048 + lVar35) =
           *(undefined8 *)(&stack0xffffffffffffffc0 + lVar35);
      uVar29 = *(undefined8 *)(&stack0xffffffffffffffb8 + lVar35);
      *(undefined8 *)(&stack0x00000050 + lVar35) =
           *(undefined8 *)(&stack0xffffffffffffffb0 + lVar35);
      *(longlong *)(&stack0x00000058 + lVar35) = alStack_90[extraout_x15 * -2];
      *(undefined8 *)(&stack0x00000060 + lVar35) =
           *(undefined8 *)(&stack0xffffffffffffff88 + lVar35);
      uVar43 = *(undefined8 *)(&stack0xffffffffffffff98 + lVar35);
      *(undefined8 *)(&stack0x00000020 + lVar35) =
           *(undefined8 *)(&stack0xffffffffffffff90 + lVar35);
      *(undefined8 *)(&stack0x00000080 + lVar35) =
           *(undefined8 *)(&stack0xffffffffffffffa8 + lVar35);
      *(undefined4 *)(&stack0xffffffffffffffd0 + lVar35) =
           *(undefined4 *)(&stack0xffffffffffffffc8 + lVar35);
      iVar58 = (*pcVar27)(iVar58,iVar55,0,0,uVar60,7,uVar60,0);
      if (iVar58 != 0) {
        piVar14 = *(int **)(&stack0xffffffffffffffe0 + lVar35);
        goto LAB_18008c798;
      }
      iVar58 = *(int *)(&stack0x00000118 + lVar35);
      uVar13 = uVar13 + 1;
      *(int *)(&stack0x00000118 + lVar35) = iVar58 + 1;
    } while (iVar58 + 1 < *(int *)(&stack0x00000038 + lVar35));
    *(undefined8 *)(&stack0x00000068 + lVar35) = uVar43;
    uVar43 = *(undefined8 *)(&stack0x00000080 + lVar35);
    piVar14 = *(int **)(&stack0xffffffffffffffe0 + lVar35);
    uVar50 = *(undefined8 *)(&stack0x00000020 + lVar35);
    *(int **)(&stack0x00000080 + lVar35) = *(int **)(&stack0x00000010 + lVar35);
    if (**(int **)(&stack0x00000010 + lVar35) == -1) {
      iVar58 = **(int **)(&stack0xffffffffffffffd8 + lVar35);
      *(int **)(&stack0xfffffffffffffff0 + lVar35) = *(int **)(&stack0xffffffffffffffd8 + lVar35);
      lVar28 = FUN_180005d08((longlong *)(piVar14 + 0x2a2),iVar58);
      iVar58 = *(int *)(&stack0xfffffffffffffff8 + lVar35);
      *(longlong *)(&stack0x00000020 + lVar35) = lVar28;
      pfVar24 = (float *)FUN_180005d08((longlong *)(piVar14 + 0x2a2),iVar58);
      fVar67 = *pfVar24;
      *(undefined4 *)(&stack0xffffffffffffffc8 + lVar35) =
           *(undefined4 *)(&stack0xffffffffffffffd0 + lVar35);
      fVar68 = **(float **)(&stack0x00000020 + lVar35);
      *(undefined8 *)(&stack0x00000108 + lVar35) = *(undefined8 *)(&stack0x00000068 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffff98 + lVar35) =
           *(undefined8 *)(&stack0x00000068 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffffa0 + lVar35) = uVar16;
      *(undefined8 *)(&stack0x00000100 + lVar35) = *(undefined8 *)(&stack0x00000060 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffff88 + lVar35) =
           *(undefined8 *)(&stack0x00000060 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffff90 + lVar35) = uVar50;
      *(undefined8 *)(&stack0x000000f8 + lVar35) = *(undefined8 *)(&stack0x00000058 + lVar35);
      alStack_90[extraout_x15 * -2] = *(undefined8 *)(&stack0x00000058 + lVar35);
      alStack_90[extraout_x15 * -2 + 1] = lVar34;
      *(undefined8 *)(&stack0x00000070 + lVar35) = *(undefined8 *)(&stack0x00000050 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffffa8 + lVar35) = uVar43;
      *(undefined8 *)(&stack0xffffffffffffffb0 + lVar35) =
           *(undefined8 *)(&stack0x00000050 + lVar35);
      *(undefined8 *)(&stack0x00000078 + lVar35) = *(undefined8 *)(&stack0x00000048 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffffb8 + lVar35) = uVar29;
      *(undefined8 *)(&stack0xffffffffffffffc0 + lVar35) =
           *(undefined8 *)(&stack0x00000048 + lVar35);
      *(undefined8 *)(&stack0x00000088 + lVar35) = *(undefined8 *)(&stack0x00000040 + lVar35);
      *(undefined8 *)(&stack0xffffffffffffff80 + lVar35) =
           *(undefined8 *)(&stack0x00000040 + lVar35);
      if (fVar68 < fVar67) goto LAB_18008c798;
      if (**(int **)(&stack0x00000080 + lVar35) == -1) {
        lVar28 = FUN_180005d08((longlong *)(piVar14 + 0x2a2),
                               **(int **)(&stack0xfffffffffffffff0 + lVar35));
        *(longlong *)(&stack0x00000020 + lVar35) = lVar28;
        pfVar24 = (float *)FUN_180005d08((longlong *)(piVar14 + 0x2a2),iVar58);
        if (*pfVar24 <= **(float **)(&stack0x00000020 + lVar35)) {
          **(int **)(&stack0xfffffffffffffff0 + lVar35) = iVar58;
          *(undefined4 *)(&stack0xffffffffffffffc8 + lVar35) =
               *(undefined4 *)(&stack0xffffffffffffffd0 + lVar35);
          *(undefined8 *)(&stack0xffffffffffffff98 + lVar35) =
               *(undefined8 *)(&stack0x00000108 + lVar35);
          *(undefined8 *)(&stack0xffffffffffffffa0 + lVar35) = uVar16;
          *(undefined8 *)(&stack0xffffffffffffff88 + lVar35) =
               *(undefined8 *)(&stack0x00000100 + lVar35);
          *(undefined8 *)(&stack0xffffffffffffff90 + lVar35) = uVar50;
          alStack_90[extraout_x15 * -2] = *(undefined8 *)(&stack0x000000f8 + lVar35);
          alStack_90[extraout_x15 * -2 + 1] = lVar34;
          *(undefined8 *)(&stack0xffffffffffffffa8 + lVar35) = uVar43;
          *(undefined8 *)(&stack0xffffffffffffffb0 + lVar35) =
               *(undefined8 *)(&stack0x00000070 + lVar35);
          *(undefined8 *)(&stack0xffffffffffffffb8 + lVar35) = uVar29;
          *(undefined8 *)(&stack0xffffffffffffffc0 + lVar35) =
               *(undefined8 *)(&stack0x00000078 + lVar35);
          *(undefined8 *)(&stack0xffffffffffffff80 + lVar35) =
               *(undefined8 *)(&stack0x00000088 + lVar35);
          goto LAB_18008c798;
        }
      }
    }
    *(undefined8 *)(&stack0xffffffffffffffa0 + lVar35) = uVar16;
    *(undefined8 *)(&stack0xffffffffffffffa8 + lVar35) = uVar43;
    uVar1 = *(undefined8 *)(&stack0x00000060 + lVar35);
    uVar4 = *(undefined8 *)(&stack0x00000068 + lVar35);
    uVar2 = *(undefined8 *)(&stack0x00000050 + lVar35);
    uVar5 = *(undefined8 *)(&stack0x00000058 + lVar35);
    uVar3 = *(undefined8 *)(&stack0x00000040 + lVar35);
    uVar26 = *(undefined8 *)(&stack0x00000048 + lVar35);
    *(undefined4 *)(&stack0xffffffffffffffc8 + lVar35) =
         *(undefined4 *)(&stack0xffffffffffffffd0 + lVar35);
    piVar15 = *(int **)(&stack0x00000010 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffff90 + lVar35) = uVar50;
    *(undefined8 *)(&stack0xffffffffffffff98 + lVar35) = uVar4;
    alStack_90[extraout_x15 * -2] = uVar5;
    alStack_90[extraout_x15 * -2 + 1] = lVar34;
    *(undefined8 *)(&stack0xffffffffffffffb0 + lVar35) = uVar2;
    *(undefined8 *)(&stack0xffffffffffffffb8 + lVar35) = uVar29;
    iVar58 = *piVar15;
    *(undefined8 *)(&stack0xffffffffffffffc0 + lVar35) = uVar26;
    *(undefined8 *)(&stack0xffffffffffffff80 + lVar35) = uVar3;
    *(undefined8 *)(&stack0xffffffffffffff88 + lVar35) = uVar1;
    if (iVar58 < 0) goto LAB_18008c798;
    *(undefined8 *)(&stack0xffffffffffffff80 + lVar35) = uVar3;
    *(undefined8 *)(&stack0xffffffffffffff88 + lVar35) = uVar1;
    *(undefined8 *)(&stack0xffffffffffffffb0 + lVar35) = uVar2;
    *(undefined8 *)(&stack0xffffffffffffffb8 + lVar35) = uVar29;
    *(undefined8 *)(&stack0xffffffffffffffc0 + lVar35) = uVar26;
    *(undefined4 *)(&stack0xffffffffffffffc8 + lVar35) = 0xffffffff;
    iVar58 = **(int **)(&stack0x00000018 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffffa0 + lVar35) = uVar16;
    *(undefined8 *)(&stack0xffffffffffffffa8 + lVar35) = uVar43;
    piVar15 = *(int **)(&stack0x00000090 + lVar35);
    *(undefined8 *)(&stack0xffffffffffffff90 + lVar35) = uVar50;
    *(undefined8 *)(&stack0xffffffffffffff98 + lVar35) = uVar4;
    alStack_90[extraout_x15 * -2] = uVar5;
    alStack_90[extraout_x15 * -2 + 1] = lVar34;
    iVar55 = *piVar15;
  }
  if (iVar58 == iVar55) {
    piVar15 = *(int **)(&stack0x00000010 + lVar35);
    iVar58 = *(int *)(&stack0xffffffffffffffc8 + lVar35);
    if (*piVar15 != iVar58) {
      lVar34 = *(longlong *)(&stack0xffffffffffffff80 + lVar35);
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
      uVar60 = *puVar23;
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
      lVar34 = alStack_90[extraout_x15 * -2];
      *puVar23 = uVar60;
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
      uVar60 = *puVar23;
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
      lVar34 = alStack_90[extraout_x15 * -2 + 1];
      *puVar23 = uVar60;
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
      uVar60 = *puVar23;
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
      lVar34 = *(longlong *)(&stack0x00000008 + lVar35);
      *puVar23 = uVar60;
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
      uVar60 = *puVar23;
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
      lVar34 = *(longlong *)(&stack0xffffffffffffff88 + lVar35);
      *puVar23 = uVar60;
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
      uVar60 = *puVar23;
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
      lVar34 = *(longlong *)(&stack0xffffffffffffff90 + lVar35);
      *puVar23 = uVar60;
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
      uVar60 = *puVar23;
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
      lVar34 = *(longlong *)(&stack0xffffffffffffffa8 + lVar35);
      *puVar23 = uVar60;
      pfVar24 = (float *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
      fVar68 = *pfVar24;
      pfVar24 = (float *)FUN_18000f448(lVar34,piVar14[0xe1]);
      lVar34 = *(longlong *)(&stack0xffffffffffffff98 + lVar35);
      *pfVar24 = fVar68 + 1.0;
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
      uVar60 = *puVar23;
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
      lVar34 = *(longlong *)(&stack0xffffffffffffffa0 + lVar35);
      *puVar23 = uVar60;
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1] + -1);
      uVar60 = *puVar23;
      puVar23 = (undefined4 *)FUN_18000f448(lVar34,piVar14[0xe1]);
      puVar37 = *(undefined4 **)(&stack0x00000018 + lVar35);
      *puVar23 = uVar60;
      **(undefined4 **)(&stack0x000000c8 + lVar35) = *puVar37;
      puVar23 = *(undefined4 **)(&stack0xffffffffffffffd8 + lVar35);
      *puVar37 = *puVar23;
      *puVar23 = *(undefined4 *)(&stack0xfffffffffffffff8 + lVar35);
      *piVar15 = iVar58;
      goto LAB_18008d080;
    }
  }
  piVar15 = *(int **)(&stack0x00000010 + lVar35);
  if (*piVar15 != *(int *)(&stack0xffffffffffffffc8 + lVar35)) {
    piVar44 = *(int **)(&stack0xffffffffffffffd8 + lVar35);
    plVar20 = FUN_180005d78((longlong *)(piVar14 + 0x58),*piVar44);
    piVar49 = *(int **)(&stack0x00000018 + lVar35);
    lVar34 = *plVar20;
    plVar20 = FUN_180005d78((longlong *)(piVar14 + 0x58),*piVar49);
    lVar34 = lVar34 % 86400000000;
    iVar58 = ((int)(lVar34 / 1000000) + (int)(lVar34 >> 0x3f)) -
             (SUB164(SEXT816(lVar34) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
    if (86399999999 < lVar34) {
      iVar58 = 0;
    }
    lVar34 = *plVar20 % 86400000000;
    iVar55 = ((int)(lVar34 / 1000000) + (int)(lVar34 >> 0x3f)) -
             (SUB164(SEXT816(lVar34) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
    if (86399999999 < lVar34) {
      iVar55 = 0;
    }
    iVar55 = (iVar58 - iVar55) / 0x3c;
    pfVar24 = (float *)FUN_18000f448(*(longlong *)(&stack0xffffffffffffffb8 + lVar35),*piVar44);
    *pfVar24 = (float)iVar55;
    iVar58 = 0;
    if (0 < *piVar44) {
      iVar58 = *piVar44;
    }
    FUN_180005d08((longlong *)(piVar14 + 0x2a2),iVar58);
    iVar58 = 0;
    if (0 < *piVar44) {
      iVar58 = *piVar44;
    }
    FUN_180005d08((longlong *)(piVar14 + 0x298),iVar58);
    iVar58 = 0;
    if (0 < *piVar49) {
      iVar58 = *piVar49;
    }
    FUN_180005d08((longlong *)(piVar14 + 0x2a2),iVar58);
    iVar58 = 0;
    if (0 < *piVar49) {
      iVar58 = *piVar49;
    }
    FUN_180005d08((longlong *)(piVar14 + 0x298),iVar58);
    iVar58 = *piVar44;
    if (*piVar15 == 1) {
      iVar57 = 0;
      if (0 < iVar58) {
        iVar57 = iVar58;
      }
      pfVar24 = (float *)FUN_180005d08((longlong *)(piVar14 + 0x298),iVar57);
      fVar68 = *pfVar24;
      plVar20 = (longlong *)(piVar14 + 0x2a2);
    }
    else {
      iVar57 = 0;
      if (0 < iVar58) {
        iVar57 = iVar58;
      }
      pfVar24 = (float *)FUN_180005d08((longlong *)(piVar14 + 0x2a2),iVar57);
      fVar68 = *pfVar24;
      plVar20 = (longlong *)(piVar14 + 0x298);
    }
    iVar58 = 0;
    if (0 < *piVar49) {
      iVar58 = *piVar49;
    }
    pfVar24 = (float *)FUN_180005d08(plVar20,iVar58);
    iVar58 = **(int **)(&stack0xffffffffffffffd8 + lVar35);
    fVar68 = fVar68 - *pfVar24;
    *(float *)(&stack0x00000118 + lVar35) = ABS(fVar68);
    pfVar24 = (float *)FUN_18000f448(*(longlong *)(&stack0xffffffffffffff80 + lVar35),iVar58);
    *pfVar24 = fVar68;
    plVar20 = *(longlong **)(&stack0x00000000 + lVar35);
    iVar58 = piVar14[0xe1];
    *(float *)(&stack0x00000020 + lVar35) = fVar68;
    puVar17 = (undefined8 *)plVar20[1];
    puVar6 = (undefined8 *)plVar20[2];
    *(int *)(&stack0x00000024 + lVar35) = iVar55;
    *(int *)(&stack0x00000028 + lVar35) = iVar58;
    if (puVar17 == puVar6) {
      FUN_18001e9d8(plVar20,puVar17,(undefined8 *)(&stack0x00000020 + lVar35));
    }
    else {
      *puVar17 = *(undefined8 *)(&stack0x00000020 + lVar35);
      *(int *)(puVar17 + 1) = iVar58;
      plVar20[1] = plVar20[1] + 0xc;
    }
    lVar34 = *(longlong *)(&stack0xffffffffffffff98 + lVar35);
    iVar58 = **(int **)(&stack0x000000f0 + lVar35) + 1;
    **(int **)(&stack0x000000f0 + lVar35) = iVar58;
    pfVar24 = (float *)FUN_18000f448(lVar34,**(int **)(&stack0xffffffffffffffd8 + lVar35));
    plVar42 = *(longlong **)(&stack0x00000000 + lVar35);
    *pfVar24 = (float)iVar58;
    plVar20 = FUN_180005d78((longlong *)(piVar14 + 0x58),*(int *)(plVar42[1] + -4));
    lVar34 = *plVar20;
    plVar20 = FUN_180005d78((longlong *)(piVar14 + 0x58),*(int *)(*plVar42 + 8));
    FUN_18008a6f0((int)(*plVar20 / 86400000000),(int)(lVar34 / 86400000000));
    uVar13 = FUN_180026550(*(longlong *)(&stack0x00000098 + lVar35));
    iVar58 = 0xb;
    if (uVar13 == 0) {
      iVar58 = 6;
    }
    puVar17 = (undefined8 *)*plVar42;
    lVar34 = plVar42[1];
    if (1 < (ulonglong)((lVar34 - (longlong)puVar17 >> 2) * -0x5555555555555555)) {
      *(int **)(&stack0xfffffffffffffff0 + lVar35) = piVar40;
      uVar22 = extraout_x1_19;
      do {
        if ((int)uVar22 <= iVar58) break;
        FUN_1800bed00(puVar17,(undefined8 *)((longlong)puVar17 + 0xc),
                      lVar34 - ((longlong)puVar17 + 0xc));
        lVar34 = plVar42[1];
        plVar42[1] = lVar34 + -0xc;
        uVar13 = *(uint *)(lVar34 + -0x10);
        lVar34 = *(longlong *)(piVar14 + 0x58);
        if (lVar34 == 0) {
          if (*(code **)(piVar14 + 0x5c) != (code *)0x0) {
            (**(code **)(piVar14 + 0x5c))(piVar14[0x5e]);
          }
          lVar34 = *(longlong *)(piVar14 + 0x58);
          if (lVar34 != 0) goto LAB_18008cc14;
          lVar28 = *(longlong *)(piVar14 + 0x62);
          iVar55 = *(int *)(*plVar42 + 8);
LAB_18008cc50:
          if (*(code **)(piVar14 + 0x5c) != (code *)0x0) {
            (**(code **)(piVar14 + 0x5c))(piVar14[0x5e]);
          }
          lVar34 = *(longlong *)(piVar14 + 0x58);
          if (lVar34 != 0) goto LAB_18008cc70;
          plVar20 = (longlong *)(piVar14 + 0x62);
        }
        else {
LAB_18008cc14:
          iVar55 = piVar14[0x60];
          if (iVar55 == 0) {
            plVar20 = (longlong *)(piVar14 + 0x62);
          }
          else {
            uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
            if (iVar55 <= (int)uVar13) {
              uVar13 = iVar55 - 1;
            }
            plVar20 = (longlong *)(lVar34 + (longlong)(int)uVar13 * 8);
          }
          lVar28 = *plVar20;
          iVar55 = *(int *)(*plVar42 + 8);
          if (lVar34 == 0) goto LAB_18008cc50;
LAB_18008cc70:
          iVar57 = piVar14[0x60];
          if (iVar57 == 0) {
            plVar20 = (longlong *)(piVar14 + 0x62);
          }
          else {
            if (iVar55 < 0) {
              iVar55 = 0;
            }
            if (iVar57 <= iVar55) {
              iVar55 = iVar57 + -1;
            }
            plVar20 = (longlong *)(lVar34 + (longlong)iVar55 * 8);
          }
        }
        uVar13 = FUN_18008a6f0((int)(*plVar20 / 86400000000),(int)(lVar28 / 86400000000));
        uVar22 = (ulonglong)uVar13;
        puVar17 = (undefined8 *)*plVar42;
        lVar34 = plVar42[1];
      } while (1 < (ulonglong)((lVar34 - (longlong)puVar17 >> 2) * -0x5555555555555555));
      piVar40 = *(int **)(&stack0xfffffffffffffff0 + lVar35);
    }
    pfVar47 = (float *)0x0;
    pfVar24 = (float *)0x0;
    pfVar25 = (float *)0x0;
    *(undefined8 *)(&stack0x00000128 + lVar35) = 0;
    *(undefined8 *)(&stack0x00000120 + lVar35) = 0;
    *(undefined8 *)(&stack0x00000120 + lVar35) = 0;
    *(undefined8 *)(&stack0x00000128 + lVar35) = 0;
    *(undefined8 *)(&stack0x00000130 + lVar35) = 0;
    *(undefined8 *)(&stack0x00000130 + lVar35) = 0;
    plVar20 = *(longlong **)(&stack0x00000000 + lVar35);
    fVar67 = 0.0;
    fVar68 = 0.0;
    if ((float *)*plVar20 != (float *)plVar20[1]) {
      iVar58 = *(int *)(&stack0xffffffffffffffe8 + lVar35);
      iVar55 = *(int *)(&stack0xffffffffffffffec + lVar35);
      pfVar47 = pfVar24;
      pfVar45 = (float *)*plVar20;
      do {
        fVar52 = ABS(*pfVar45);
        *(float *)(&stack0xffffffffffffffd0 + lVar35) = fVar52;
        fVar67 = fVar67 + fVar52;
        if (pfVar47 == pfVar25) {
          FUN_18008d9f0((longlong *)(&stack0x00000120 + lVar35),(undefined8 *)pfVar47,
                        (undefined4 *)(&stack0xffffffffffffffd0 + lVar35));
          pfVar24 = *(float **)(&stack0x00000128 + lVar35);
          pfVar25 = *(float **)(&stack0x00000130 + lVar35);
        }
        else {
          pfVar24 = pfVar47 + 1;
          *pfVar47 = fVar52;
          *(float **)(&stack0x00000128 + lVar35) = pfVar24;
        }
        fVar68 = (float)(int)pfVar45[1] + fVar68;
        pfVar39 = pfVar45 + 3;
        fVar52 = *pfVar45;
        if (fVar52 <= 0.0) {
          if (fVar52 < 0.0) {
            fVar62 = fVar52 + fVar62;
            iVar55 = iVar55 + 1;
          }
        }
        else {
          fVar61 = fVar52 + fVar61;
          iVar58 = iVar58 + 1;
        }
        pfVar47 = pfVar24;
        pfVar45 = pfVar39;
      } while (pfVar39 != (float *)plVar20[1]);
      pfVar47 = *(float **)(&stack0x00000120 + lVar35);
      *(int *)(&stack0xffffffffffffffe8 + lVar35) = iVar58;
      *(int *)(&stack0xffffffffffffffec + lVar35) = iVar55;
    }
    uVar22 = (longlong)pfVar24 - (longlong)pfVar47 >> 2;
    FUN_18008db38(pfVar47,pfVar24,uVar22,0);
    fVar52 = FUN_180026608((longlong)piVar40);
    fVar52 = FUN_1800bebe0((fVar52 / 100.0) * (float)uVar22);
    piVar15 = *(int **)(&stack0xffffffffffffffd8 + lVar35);
    lVar34 = *(longlong *)(&stack0xffffffffffffff90 + lVar35);
    fVar52 = pfVar47[(longlong)(int)fVar52 + -1];
    **(float **)(&stack0x000000e8 + lVar35) = fVar52;
    pfVar24 = (float *)FUN_18000f448(lVar34,*piVar15);
    plVar20 = *(longlong **)(&stack0x00000000 + lVar35);
    *pfVar24 = fVar52;
    lVar41 = *(longlong *)(&stack0xffffffffffffff80 + lVar35);
    lVar34 = *plVar20;
    lVar28 = plVar20[1];
    pfVar24 = (float *)FUN_18000f448(lVar41,*piVar15);
    pfVar45 = *(float **)(&stack0x000000d0 + lVar35);
    fVar52 = *pfVar24;
    fVar69 = *pfVar45;
    uVar63 = SUB41(fVar69,0);
    uVar64 = (undefined1)((uint)fVar69 >> 8);
    uVar65 = (undefined1)((uint)fVar69 >> 0x10);
    uVar66 = (undefined1)((uint)fVar69 >> 0x18);
    if (fVar69 <= fVar52) {
      uVar63 = SUB41(fVar52,0);
      uVar64 = (undefined1)((uint)fVar52 >> 8);
      uVar65 = (undefined1)((uint)fVar52 >> 0x10);
      uVar66 = (undefined1)((uint)fVar52 >> 0x18);
    }
    *pfVar45 = (float)CONCAT13(uVar66,CONCAT12(uVar65,CONCAT11(uVar64,uVar63)));
    pfVar24 = (float *)FUN_18000f448(lVar41,*piVar15);
    pfVar39 = *(float **)(&stack0x000000d8 + lVar35);
    fVar52 = *pfVar24;
    pfVar24 = *(float **)(&stack0x000000c0 + lVar35);
    lVar41 = *(longlong *)(&stack0xffffffffffffffc0 + lVar35);
    fVar69 = *pfVar39;
    uVar63 = SUB41(fVar69,0);
    uVar64 = (undefined1)((uint)fVar69 >> 8);
    uVar65 = (undefined1)((uint)fVar69 >> 0x10);
    uVar66 = (undefined1)((uint)fVar69 >> 0x18);
    if (fVar52 <= fVar69) {
      uVar63 = SUB41(fVar52,0);
      uVar64 = (undefined1)((uint)fVar52 >> 8);
      uVar65 = (undefined1)((uint)fVar52 >> 0x10);
      uVar66 = (undefined1)((uint)fVar52 >> 0x18);
    }
    *pfVar39 = (float)CONCAT13(uVar66,CONCAT12(uVar65,CONCAT11(uVar64,uVar63)));
    *pfVar24 = fVar67;
    piVar40 = *(int **)(&stack0x000000b0 + lVar35);
    *piVar40 = (int)(plVar20[1] - *plVar20 >> 2) * -0x55555555;
    fVar67 = *pfVar45;
    pfVar45 = (float *)FUN_18000f448(lVar41,*piVar15);
    *pfVar45 = fVar67;
    fVar67 = *pfVar39;
    pfVar45 = (float *)FUN_18000f448(*(longlong *)(&stack0xffffffffffffffb0 + lVar35),*piVar15);
    *pfVar45 = fVar67;
    iVar58 = *piVar40;
    if (0 < iVar58) {
      fVar67 = *pfVar24;
      pfVar24 = (float *)FUN_18000f448(alStack_90[extraout_x15 * -2],*piVar15);
      *pfVar24 = fVar67 / (float)iVar58;
      pfVar24 = (float *)FUN_18000f448(*(longlong *)(&stack0xffffffffffffffa0 + lVar35),*piVar15);
      iVar58 = *(int *)(&stack0xffffffffffffffe8 + lVar35);
      *pfVar24 = fVar68 / (float)(ulonglong)((lVar28 - lVar34 >> 2) * -0x5555555555555555);
      if (0 < iVar58) {
        pfVar24 = (float *)FUN_18000f448(alStack_90[extraout_x15 * -2 + 1],*piVar15);
        *pfVar24 = fVar61 / (float)iVar58;
      }
      iVar58 = *(int *)(&stack0xffffffffffffffec + lVar35);
      if (0 < iVar58) {
        pfVar24 = (float *)FUN_18000f448(*(longlong *)(&stack0x00000008 + lVar35),
                                         **(int **)(&stack0xffffffffffffffd8 + lVar35));
        *pfVar24 = fVar62 / (float)iVar58;
      }
    }
    plVar42 = *(longlong **)(&stack0x000000a0 + lVar35);
    plVar20 = FUN_18008d628(plVar42,(float *)(&stack0x00000118 + lVar35));
    lVar34 = *plVar20;
    plVar20 = FUN_18008d628(plVar42,(float *)(&stack0x00000118 + lVar35));
    fVar68 = 0.0;
    *(int *)plVar20 = (int)lVar34 + 1;
    plVar42 = (longlong *)*plVar42;
    iVar58 = 0;
    plVar20 = (longlong *)*plVar42;
    if (plVar20 != plVar42) {
      *(float **)(&stack0x00000080 + lVar35) = pfVar25;
      *(float **)(&stack0x00000020 + lVar35) = pfVar47;
      do {
        if ((iVar58 < (int)plVar20[4]) ||
           (((int)plVar20[4] == iVar58 && (fVar68 < *(float *)((longlong)plVar20 + 0x1c))))) {
          fVar68 = *(float *)((longlong)plVar20 + 0x1c);
          iVar58 = (int)plVar20[4];
        }
        plVar38 = (longlong *)plVar20[2];
        if (*(char *)((longlong)plVar38 + 0x19) == '\0') {
          cVar9 = *(char *)(*plVar38 + 0x19);
          plVar20 = plVar38;
          plVar38 = (longlong *)*plVar38;
          while (cVar9 == '\0') {
            cVar9 = *(char *)(*plVar38 + 0x19);
            plVar20 = plVar38;
            plVar38 = (longlong *)*plVar38;
          }
        }
        else {
          cVar9 = *(char *)(plVar20[1] + 0x19);
          plVar12 = (longlong *)plVar20[1];
          plVar38 = plVar20;
          while ((plVar20 = plVar12, cVar9 == '\0' && (plVar38 == (longlong *)plVar20[2]))) {
            cVar9 = *(char *)(plVar20[1] + 0x19);
            plVar12 = (longlong *)plVar20[1];
            plVar38 = plVar20;
          }
        }
      } while (plVar20 != plVar42);
      pfVar47 = *(float **)(&stack0x00000020 + lVar35);
    }
    piVar49 = *(int **)(&stack0xffffffffffffffd8 + lVar35);
    pfVar24 = (float *)FUN_18000f448(*(longlong *)(&stack0xffffffffffffff88 + lVar35),*piVar49);
    pfVar25 = *(float **)(&stack0x000000e0 + lVar35);
    piVar40 = *(int **)(&stack0x00000018 + lVar35);
    *pfVar24 = fVar68;
    piVar15 = *(int **)(&stack0x000000c8 + lVar35);
    *pfVar25 = fVar68;
    *piVar15 = *piVar40;
    uVar60 = *(undefined4 *)(&stack0xffffffffffffffc8 + lVar35);
    *piVar40 = *piVar49;
    *piVar49 = *(int *)(&stack0xfffffffffffffff8 + lVar35);
    **(undefined4 **)(&stack0x00000010 + lVar35) = uVar60;
    puVar23 = *(undefined4 **)(&stack0x000000a8 + lVar35);
    piVar15 = *(int **)(&stack0x000000b0 + lVar35);
    *puVar23 = **(undefined4 **)(&stack0x000000c0 + lVar35);
    puVar23[1] = (float)*piVar15;
    puVar23[2] = *pfVar25;
    if (pfVar47 != (float *)0x0) {
      FUN_1800966b8(pfVar47);
      *(undefined8 *)(&stack0x00000120 + lVar35) = 0;
      *(undefined8 *)(&stack0x00000128 + lVar35) = 0;
      *(undefined8 *)(&stack0x00000130 + lVar35) = 0;
    }
  }
LAB_18008d080:
  uVar13 = **(int **)(&stack0x00000018 + lVar35) + 1;
  if ((int)uVar13 <= piVar14[0xe1]) {
    *(int **)(&stack0xffffffffffffffe0 + lVar35) = piVar14;
    lVar34 = *(longlong *)(&stack0xffffffffffffff98 + lVar35);
    lVar41 = *(longlong *)(&stack0xffffffffffffffa0 + lVar35);
    lVar51 = *(longlong *)(&stack0xffffffffffffffc0 + lVar35);
    lVar46 = *(longlong *)(&stack0xffffffffffffffb0 + lVar35);
    lVar28 = *(longlong *)(&stack0xffffffffffffff88 + lVar35);
    lVar7 = *(longlong *)(&stack0xffffffffffffff90 + lVar35);
    do {
      uVar60 = **(undefined4 **)(&stack0x000000d0 + lVar35);
      lVar30 = *(longlong *)(lVar51 + 0x30);
      uVar32 = (int)uVar13 >> 0x1f;
      if (lVar30 == 0) {
        if (*(code **)(lVar51 + 0x40) != (code *)0x0) {
          (**(code **)(lVar51 + 0x40))(*(undefined4 *)(lVar51 + 0x48));
        }
        lVar30 = *(longlong *)(lVar51 + 0x30);
        if (lVar30 != 0) goto LAB_18008d0dc;
        puVar23 = (undefined4 *)(lVar51 + 0x54);
      }
      else {
LAB_18008d0dc:
        iVar58 = *(int *)(lVar51 + 0x50);
        if (iVar58 == 0) {
          puVar23 = (undefined4 *)(lVar51 + 0x54);
        }
        else {
          uVar33 = uVar13 & (uVar32 ^ 0xffffffff);
          if (iVar58 <= (int)uVar33) {
            uVar33 = iVar58 - 1;
          }
          puVar23 = (undefined4 *)(lVar30 + (longlong)(int)uVar33 * 4);
        }
      }
      *puVar23 = uVar60;
      uVar60 = **(undefined4 **)(&stack0x000000d8 + lVar35);
      lVar30 = *(longlong *)(lVar46 + 0x30);
      if (lVar30 == 0) {
        if (*(code **)(lVar46 + 0x40) != (code *)0x0) {
          (**(code **)(lVar46 + 0x40))(*(undefined4 *)(lVar46 + 0x48));
        }
        lVar30 = *(longlong *)(lVar46 + 0x30);
        if (lVar30 != 0) goto LAB_18008d134;
        puVar23 = (undefined4 *)(lVar46 + 0x54);
      }
      else {
LAB_18008d134:
        iVar58 = *(int *)(lVar46 + 0x50);
        if (iVar58 == 0) {
          puVar23 = (undefined4 *)(lVar46 + 0x54);
        }
        else {
          uVar33 = uVar13 & (uVar32 ^ 0xffffffff);
          if (iVar58 <= (int)uVar33) {
            uVar33 = iVar58 - 1;
          }
          puVar23 = (undefined4 *)(lVar30 + (longlong)(int)uVar33 * 4);
        }
      }
      *puVar23 = uVar60;
      iVar58 = **(int **)(&stack0x000000b0 + lVar35);
      if (0 < iVar58) {
        lVar36 = alStack_90[extraout_x15 * -2];
        fVar68 = **(float **)(&stack0x000000c0 + lVar35);
        lVar30 = *(longlong *)(lVar36 + 0x30);
        if (lVar30 == 0) {
          if (*(code **)(lVar36 + 0x40) != (code *)0x0) {
            (**(code **)(lVar36 + 0x40))(*(undefined4 *)(lVar36 + 0x48));
          }
          lVar30 = *(longlong *)(alStack_90[extraout_x15 * -2] + 0x30);
          if (lVar30 != 0) goto LAB_18008d1ac;
          pfVar24 = (float *)(alStack_90[extraout_x15 * -2] + 0x54);
        }
        else {
LAB_18008d1ac:
          iVar55 = *(int *)(alStack_90[extraout_x15 * -2] + 0x50);
          if (iVar55 == 0) {
            pfVar24 = (float *)(alStack_90[extraout_x15 * -2] + 0x54);
          }
          else {
            uVar33 = uVar13 & (uVar32 ^ 0xffffffff);
            if (iVar55 <= (int)uVar33) {
              uVar33 = iVar55 - 1;
            }
            pfVar24 = (float *)(lVar30 + (longlong)(int)uVar33 * 4);
          }
        }
        *pfVar24 = fVar68 / (float)iVar58;
        if (0 < *(int *)(&stack0xffffffffffffffe8 + lVar35)) {
          lVar36 = alStack_90[extraout_x15 * -2 + 1];
          lVar30 = *(longlong *)(lVar36 + 0x30);
          if (lVar30 == 0) {
            if (*(code **)(lVar36 + 0x40) != (code *)0x0) {
              (**(code **)(lVar36 + 0x40))(*(undefined4 *)(lVar36 + 0x48));
            }
            lVar30 = *(longlong *)(alStack_90[extraout_x15 * -2 + 1] + 0x30);
            if (lVar30 != 0) goto LAB_18008d214;
            pfVar24 = (float *)(alStack_90[extraout_x15 * -2 + 1] + 0x54);
          }
          else {
LAB_18008d214:
            iVar58 = *(int *)(alStack_90[extraout_x15 * -2 + 1] + 0x50);
            if (iVar58 == 0) {
              pfVar24 = (float *)(alStack_90[extraout_x15 * -2 + 1] + 0x54);
            }
            else {
              uVar33 = uVar13 & (uVar32 ^ 0xffffffff);
              if (iVar58 <= (int)uVar33) {
                uVar33 = iVar58 - 1;
              }
              pfVar24 = (float *)(lVar30 + (longlong)(int)uVar33 * 4);
            }
          }
          *pfVar24 = fVar61 / (float)*(int *)(&stack0xffffffffffffffe8 + lVar35);
        }
        if (0 < *(int *)(&stack0xffffffffffffffec + lVar35)) {
          lVar36 = *(longlong *)(&stack0x00000008 + lVar35);
          lVar30 = *(longlong *)(lVar36 + 0x30);
          if (lVar30 == 0) {
            if (*(code **)(lVar36 + 0x40) != (code *)0x0) {
              (**(code **)(lVar36 + 0x40))(*(undefined4 *)(lVar36 + 0x48));
            }
            lVar30 = *(longlong *)(*(longlong *)(&stack0x00000008 + lVar35) + 0x30);
            if (lVar30 != 0) goto LAB_18008d288;
            pfVar24 = (float *)(*(longlong *)(&stack0x00000008 + lVar35) + 0x54);
          }
          else {
LAB_18008d288:
            iVar58 = *(int *)(*(longlong *)(&stack0x00000008 + lVar35) + 0x50);
            if (iVar58 == 0) {
              pfVar24 = (float *)(*(longlong *)(&stack0x00000008 + lVar35) + 0x54);
            }
            else {
              uVar33 = uVar13 & (uVar32 ^ 0xffffffff);
              if (iVar58 <= (int)uVar33) {
                uVar33 = iVar58 - 1;
              }
              pfVar24 = (float *)(lVar30 + (longlong)(int)uVar33 * 4);
            }
          }
          *pfVar24 = fVar62 / (float)*(int *)(&stack0xffffffffffffffec + lVar35);
        }
      }
      uVar60 = **(undefined4 **)(&stack0x000000e0 + lVar35);
      lVar30 = *(longlong *)(lVar28 + 0x30);
      if (lVar30 == 0) {
        if (*(code **)(lVar28 + 0x40) != (code *)0x0) {
          (**(code **)(lVar28 + 0x40))(*(undefined4 *)(lVar28 + 0x48));
        }
        lVar30 = *(longlong *)(lVar28 + 0x30);
        if (lVar30 != 0) goto LAB_18008d2f0;
        puVar23 = (undefined4 *)(lVar28 + 0x54);
      }
      else {
LAB_18008d2f0:
        iVar58 = *(int *)(lVar28 + 0x50);
        if (iVar58 == 0) {
          puVar23 = (undefined4 *)(lVar28 + 0x54);
        }
        else {
          uVar33 = uVar13 & (uVar32 ^ 0xffffffff);
          if (iVar58 <= (int)uVar33) {
            uVar33 = iVar58 - 1;
          }
          puVar23 = (undefined4 *)(lVar30 + (longlong)(int)uVar33 * 4);
        }
      }
      *puVar23 = uVar60;
      uVar60 = **(undefined4 **)(&stack0x000000e8 + lVar35);
      lVar30 = *(longlong *)(lVar7 + 0x30);
      if (lVar30 == 0) {
        if (*(code **)(lVar7 + 0x40) != (code *)0x0) {
          (**(code **)(lVar7 + 0x40))(*(undefined4 *)(lVar7 + 0x48));
        }
        lVar30 = *(longlong *)(lVar7 + 0x30);
        if (lVar30 != 0) goto LAB_18008d348;
        puVar23 = (undefined4 *)(lVar7 + 0x54);
      }
      else {
LAB_18008d348:
        iVar58 = *(int *)(lVar7 + 0x50);
        if (iVar58 == 0) {
          puVar23 = (undefined4 *)(lVar7 + 0x54);
        }
        else {
          uVar33 = uVar13 & (uVar32 ^ 0xffffffff);
          if (iVar58 <= (int)uVar33) {
            uVar33 = iVar58 - 1;
          }
          puVar23 = (undefined4 *)(lVar30 + (longlong)(int)uVar33 * 4);
        }
      }
      *puVar23 = uVar60;
      iVar58 = **(int **)(&stack0x000000f0 + lVar35);
      lVar30 = *(longlong *)(lVar34 + 0x30);
      if (lVar30 == 0) {
        if (*(code **)(lVar34 + 0x40) != (code *)0x0) {
          (**(code **)(lVar34 + 0x40))(*(undefined4 *)(lVar34 + 0x48));
        }
        lVar30 = *(longlong *)(lVar34 + 0x30);
        if (lVar30 != 0) goto LAB_18008d3a4;
        pfVar24 = (float *)(lVar34 + 0x54);
      }
      else {
LAB_18008d3a4:
        iVar55 = *(int *)(lVar34 + 0x50);
        if (iVar55 == 0) {
          pfVar24 = (float *)(lVar34 + 0x54);
        }
        else {
          uVar32 = uVar13 & (uVar32 ^ 0xffffffff);
          if (iVar55 <= (int)uVar32) {
            uVar32 = iVar55 - 1;
          }
          pfVar24 = (float *)(lVar30 + (longlong)(int)uVar32 * 4);
        }
      }
      *pfVar24 = (float)iVar58;
      uVar32 = **(uint **)(&stack0x00000018 + lVar35);
      lVar30 = *(longlong *)(lVar41 + 0x30);
      if (lVar30 == 0) {
        if (*(code **)(lVar41 + 0x40) != (code *)0x0) {
          (**(code **)(lVar41 + 0x40))(*(undefined4 *)(lVar41 + 0x48));
        }
        lVar30 = *(longlong *)(lVar41 + 0x30);
        if (lVar30 != 0) goto LAB_18008d3fc;
        uVar60 = *(undefined4 *)(lVar41 + 0x54);
LAB_18008d428:
        if (*(code **)(lVar41 + 0x40) != (code *)0x0) {
          (**(code **)(lVar41 + 0x40))(*(undefined4 *)(lVar41 + 0x48));
        }
        lVar30 = *(longlong *)(lVar41 + 0x30);
        if (lVar30 != 0) goto LAB_18008d448;
        puVar23 = (undefined4 *)(lVar41 + 0x54);
      }
      else {
LAB_18008d3fc:
        iVar58 = *(int *)(lVar41 + 0x50);
        if (iVar58 == 0) {
          puVar23 = (undefined4 *)(lVar41 + 0x54);
        }
        else {
          uVar32 = uVar32 & ((int)uVar32 >> 0x1f ^ 0xffffffffU);
          if (iVar58 <= (int)uVar32) {
            uVar32 = iVar58 - 1;
          }
          puVar23 = (undefined4 *)(lVar30 + (longlong)(int)uVar32 * 4);
        }
        uVar60 = *puVar23;
        if (lVar30 == 0) goto LAB_18008d428;
LAB_18008d448:
        iVar58 = *(int *)(lVar41 + 0x50);
        if (iVar58 == 0) {
          puVar23 = (undefined4 *)(lVar41 + 0x54);
        }
        else {
          uVar32 = uVar13;
          if ((int)uVar13 < 0) {
            uVar32 = 0;
          }
          if (iVar58 <= (int)uVar32) {
            uVar32 = iVar58 - 1;
          }
          puVar23 = (undefined4 *)(lVar30 + (longlong)(int)uVar32 * 4);
        }
      }
      *puVar23 = uVar60;
      uVar13 = uVar13 + 1;
    } while ((int)uVar13 <= *(int *)(*(longlong *)(&stack0xffffffffffffffe0 + lVar35) + 900));
  }
  return;
}

