
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_OFL_Overlay(undefined1 param_1 [16],undefined8 param_2,int *param_3,undefined8 param_4,
                     undefined8 param_5,undefined8 param_6,undefined8 param_7,undefined8 param_8,
                     undefined8 param_9,undefined8 param_10)

{
  char cVar1;
  longlong *plVar2;
  uint *puVar3;
  bool bVar4;
  int iVar5;
  uint *puVar6;
  undefined8 *puVar7;
  HANDLE pvVar8;
  undefined8 uVar9;
  ulonglong uVar10;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  int iVar11;
  longlong lVar12;
  uint uVar13;
  code *pcVar14;
  code *extraout_x11;
  code *extraout_x11_00;
  uint uVar15;
  longlong *plVar16;
  longlong *plVar17;
  int *piVar18;
  longlong *plVar19;
  uint uVar20;
  longlong *plVar21;
  longlong *plVar22;
  longlong *plVar23;
  undefined1 extraout_q0 [16];
  undefined1 auVar24 [16];
  undefined1 extraout_q0_00 [16];
  undefined1 extraout_q0_01 [16];
  longlong *local_1b0;
  longlong *local_1a0;
  undefined8 local_198;
  longlong *local_190;
  longlong *local_188;
  longlong *local_180;
  longlong *local_178;
  longlong *local_170;
  longlong *local_168;
  uint *local_160;
  uint *local_158;
  undefined8 local_150;
  undefined1 *local_140;
  undefined8 uStack_138;
  undefined1 *local_130;
  longlong local_120;
  undefined8 local_118;
  undefined8 local_110;
  undefined8 uStack_108;
  undefined8 local_100;
  longlong local_f8;
  undefined8 local_f0;
  undefined8 local_e8;
  undefined8 uStack_e0;
  undefined8 local_d8;
  longlong local_d0;
  undefined8 local_c8;
  undefined8 local_c0;
  undefined8 uStack_b8;
  undefined8 local_b0;
  undefined8 uStack_a8;
  longlong local_a0;
  undefined8 local_98;
  undefined8 local_90;
  undefined8 uStack_88;
  undefined8 local_80;
  undefined8 uStack_78;
  
                    /* 0x7daf8  19  scsf_OFL_Overlay */
  local_150 = 0xfffffffffffffffe;
  local_188 = (longlong *)&DAT_1800d4ecd;
  local_130 = &DAT_1800d4ecd;
  local_140 = &DAT_1800d4ecd;
  uStack_138 = 0;
  local_170 = (longlong *)(**(code **)(param_3 + 0x62e))(0);
  puVar6 = (uint *)(**(code **)(param_3 + 0x62e))(1);
  local_168 = (longlong *)(**(code **)(param_3 + 0x62e))(2);
  local_160 = (uint *)(**(code **)(param_3 + 0x62e))(3);
  local_158 = (uint *)(**(code **)(param_3 + 0x62e))(4);
  puVar7 = (undefined8 *)(**(code **)(param_3 + 0x446))(0);
  plVar16 = (longlong *)*puVar7;
  puVar7 = (undefined8 *)(**(code **)(param_3 + 0x446))(1);
  lVar12 = *(longlong *)(param_3 + 0x140);
  plVar19 = (longlong *)*puVar7;
  uVar9 = extraout_x1;
  auVar24 = extraout_q0;
  if (lVar12 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar9 = extraout_x1_00;
    }
    lVar12 = *(longlong *)(param_3 + 0x140);
    if (lVar12 != 0) goto LAB_18007dbec;
    plVar23 = (longlong *)(param_3 + 0x14a);
LAB_18007dc1c:
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar9 = extraout_x1_01;
    }
    lVar12 = *(longlong *)(param_3 + 0x140);
    if (lVar12 != 0) goto LAB_18007dc3c;
    local_178 = (longlong *)(param_3 + 0x14a);
  }
  else {
LAB_18007dbec:
    iVar5 = param_3[0x148];
    if (iVar5 == 0) {
      plVar23 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar11 = 0;
      if (iVar5 < 1) {
        iVar11 = iVar5 + -1;
      }
      plVar23 = (longlong *)(lVar12 + (longlong)iVar11 * 0x170);
    }
    if (lVar12 == 0) goto LAB_18007dc1c;
LAB_18007dc3c:
    iVar5 = param_3[0x148];
    if (iVar5 == 0) {
      local_178 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar11 = 1;
      if (iVar5 + -1 == 0 || iVar5 < 1) {
        iVar11 = iVar5 + -1;
      }
      local_178 = (longlong *)(lVar12 + (longlong)iVar11 * 0x170);
    }
  }
  lVar12 = *(longlong *)(param_3 + 0x84);
  if (lVar12 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar9 = extraout_x1_02;
    }
    lVar12 = *(longlong *)(param_3 + 0x84);
    if (lVar12 != 0) goto LAB_18007dc98;
    plVar21 = (longlong *)(param_3 + 0x8e);
LAB_18007dccc:
    local_180 = plVar21;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar9 = extraout_x1_03;
    }
    lVar12 = *(longlong *)(param_3 + 0x84);
    if (lVar12 != 0) goto LAB_18007dcfc;
    plVar22 = (longlong *)(param_3 + 0x8e);
LAB_18007dd34:
    local_1b0 = (longlong *)(param_3 + 0x84);
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar9 = extraout_x1_04;
    }
    lVar12 = *local_1b0;
    if (lVar12 == 0) {
      plVar17 = (longlong *)(param_3 + 0x8e);
      goto LAB_18007dd84;
    }
  }
  else {
LAB_18007dc98:
    iVar5 = param_3[0x8c];
    if (iVar5 == 0) {
      plVar21 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar11 = 0;
      if (iVar5 < 1) {
        iVar11 = iVar5 + -1;
      }
      plVar21 = (longlong *)(lVar12 + (longlong)iVar11 * 0x98);
    }
    local_180 = plVar21;
    if (lVar12 == 0) goto LAB_18007dccc;
LAB_18007dcfc:
    iVar5 = param_3[0x8c];
    if (iVar5 == 0) {
      plVar22 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar11 = 1;
      if (iVar5 + -1 == 0 || iVar5 < 1) {
        iVar11 = iVar5 + -1;
      }
      plVar22 = (longlong *)(lVar12 + (longlong)iVar11 * 0x98);
    }
    if (lVar12 == 0) goto LAB_18007dd34;
  }
  iVar5 = param_3[0x8c];
  if (iVar5 == 0) {
    plVar17 = (longlong *)(param_3 + 0x8e);
  }
  else {
    iVar11 = 2;
    if (iVar5 < 3) {
      iVar11 = iVar5 + -1;
    }
    plVar17 = (longlong *)(lVar12 + (longlong)iVar11 * 0x98);
  }
LAB_18007dd84:
  if (param_3[0x2b] == 0) {
    if (param_3[0xe1] == 0) {
      uVar9 = FUN_1800254e8(auVar24,param_2,(longlong)param_3,uVar9,param_5,param_6,param_7,param_8,
                            param_9,param_10);
      *(int *)local_170 = (int)uVar9;
    }
    if ((int)*local_170 == 0) {
      iVar5 = (**(code **)(param_3 + 0x3ec))(param_3[0x270]);
      uVar20 = param_3[3];
      local_188 = (longlong *)CONCAT44(local_188._4_4_,iVar5);
      if ((uVar20 == 0) || ((*puVar6 == 0 && (iVar5 != 0)))) {
        uVar20 = 0;
      }
      if (param_3[0x1b] == 0) {
        if (plVar16 == (longlong *)0x0) {
          plVar16 = (longlong *)FUN_180096150(0x18);
          *plVar16 = 0;
          plVar16[1] = 0;
          plVar16[2] = 0;
          (**(code **)(param_3 + 0x448))(0,plVar16);
        }
        if (plVar19 == (longlong *)0x0) {
          plVar19 = (longlong *)FUN_180096150(0x18);
          *plVar19 = 0;
          plVar19[1] = 0;
          plVar19[2] = 0;
          (**(code **)(param_3 + 0x448))(1,plVar19);
        }
        plVar2 = local_180;
        if ((plVar16 != (longlong *)0x0) && (plVar19 != (longlong *)0x0)) {
          if (uVar20 == 0) {
            *puVar6 = (uint)((int)local_188 != 0);
            *(undefined4 *)local_168 = 0;
            iVar5 = 0;
            local_1a0 = plVar17;
            local_170 = plVar19;
            local_168 = plVar16;
            do {
              uVar15 = 0;
              if (0 < *param_3) {
                local_180 = (longlong *)CONCAT44(local_180._4_4_,iVar5);
                do {
                  lVar12 = *(longlong *)(param_3 + 0x140);
                  if (lVar12 == 0) {
                    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
                      (**(code **)(param_3 + 0x144))(param_3[0x146]);
                    }
                    lVar12 = *(longlong *)(param_3 + 0x140);
                    if (lVar12 != 0) goto LAB_18007e290;
                    piVar18 = param_3 + 0x14a;
                  }
                  else {
LAB_18007e290:
                    iVar5 = param_3[0x148];
                    if (iVar5 == 0) {
                      piVar18 = param_3 + 0x14a;
                    }
                    else {
                      iVar11 = (int)local_180;
                      if (iVar5 <= (int)local_180) {
                        iVar11 = iVar5 + -1;
                      }
                      piVar18 = (int *)(lVar12 + (longlong)iVar11 * 0x170);
                    }
                  }
                  lVar12 = *(longlong *)(piVar18 + 0xc);
                  if (lVar12 == 0) {
                    if (*(code **)(piVar18 + 0x10) != (code *)0x0) {
                      (**(code **)(piVar18 + 0x10))(piVar18[0x12]);
                    }
                    lVar12 = *(longlong *)(piVar18 + 0xc);
                    if (lVar12 != 0) goto LAB_18007e2e0;
                    piVar18 = piVar18 + 0x15;
                  }
                  else {
LAB_18007e2e0:
                    iVar5 = piVar18[0x14];
                    if (iVar5 == 0) {
                      piVar18 = piVar18 + 0x15;
                    }
                    else {
                      uVar13 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
                      if (iVar5 <= (int)uVar13) {
                        uVar13 = iVar5 - 1;
                      }
                      piVar18 = (int *)(lVar12 + (longlong)(int)uVar13 * 4);
                    }
                  }
                  *piVar18 = 0;
                  uVar15 = uVar15 + 1;
                } while ((int)uVar15 < *param_3);
                iVar5 = (int)local_180;
              }
              iVar5 = iVar5 + 1;
            } while (iVar5 < 0x3c);
            *local_160 = 0;
            *local_158 = 0;
            if (*local_168 != local_168[1]) {
              local_168[1] = *local_168;
            }
            plVar16 = local_168;
            plVar17 = local_1a0;
            plVar19 = local_170;
            if (*local_170 != local_170[1]) {
              local_170[1] = *local_170;
            }
          }
          local_120 = 0;
          local_118 = 0;
          local_100 = 0;
          local_110 = 0;
          uStack_108 = 0;
          local_1a0 = *(longlong **)((longlong)plVar21 + 0x1c);
          local_198 = CONCAT44(local_198._4_4_,*(undefined4 *)((longlong)plVar21 + 0x24));
          local_180 = plVar2;
          (**(code **)(param_3 + 0x286))(&local_1a0,&local_120);
          if ((int)local_118 != 0) {
            local_d0 = 0;
            local_c8 = 0;
            local_c0 = 0;
            uStack_b8 = 0;
            pcVar14 = *(code **)(param_3 + 0xc6);
            local_b0 = 0;
            uStack_a8 = 0;
            cVar1 = (char)local_180[3];
            if ((cVar1 == '\x12' || cVar1 == '\x15') || (cVar1 == '\x13')) {
              uVar10 = (ulonglong)*(uint *)((longlong)plVar21 + 0x1c);
            }
            else {
              uVar10 = FUN_180026708((longlong)plVar21);
              pcVar14 = extraout_x11;
            }
            (*pcVar14)(uVar10,&local_d0);
            if ((int)local_c8 != 0) {
              local_f8 = 0;
              local_f0 = 0;
              local_d8 = 0;
              local_e8 = 0;
              uStack_e0 = 0;
              bVar4 = FUN_180026690((longlong)plVar22);
              if (bVar4) {
                local_1a0 = *(longlong **)((longlong)plVar17 + 0x1c);
                local_198 = CONCAT44(local_198._4_4_,*(undefined4 *)((longlong)plVar17 + 0x24));
                (**(code **)(param_3 + 0x286))(&local_1a0,&local_f8);
                if ((int)local_f0 == 0) {
                  return;
                }
              }
              local_a0 = 0;
              local_98 = 0;
              local_90 = 0;
              uStack_88 = 0;
              local_80 = 0;
              uStack_78 = 0;
              bVar4 = FUN_180026690((longlong)plVar22);
              if (bVar4) {
                cVar1 = (char)plVar17[3];
                pcVar14 = *(code **)(param_3 + 0xc6);
                if ((cVar1 == '\x12' || cVar1 == '\x15') || (cVar1 == '\x13')) {
                  uVar10 = (ulonglong)*(uint *)((longlong)plVar17 + 0x1c);
                }
                else {
                  uVar10 = FUN_180026708((longlong)plVar17);
                  pcVar14 = extraout_x11_00;
                }
                (*pcVar14)(uVar10,&local_a0);
                if ((int)local_98 == 0) {
                  return;
                }
              }
              puVar3 = local_160;
              plVar21 = local_178;
              if ((int)uVar20 < *param_3) {
                do {
                  FUN_18000d3e8((longlong)param_3,uVar20,param_5,puVar3,plVar23 + 6,plVar16,
                                &local_120,&local_d0);
                  switch((char)plVar22[3]) {
                  default:
                    goto switchD_18007e4c8_caseD_0;
                  case '\x01':
                  case '\x03':
                  case '\x04':
                  case '\x05':
                  case '\x06':
                  case '\v':
                  case '\r':
                  case '\x0e':
                  case '\x0f':
                  case '\x10':
                  case '\x11':
                  case '\x13':
                  case '\x16':
                  case '\x18':
                    bVar4 = *(int *)((longlong)plVar22 + 0x1c) == 0;
                    break;
                  case '\x02':
                    bVar4 = *(float *)((longlong)plVar22 + 0x1c) == 0.0;
                    break;
                  case '\b':
                  case '\t':
                  case '\n':
                  case '\x17':
                  case '\x19':
                    bVar4 = false;
                    if (!NAN(*(double *)((longlong)plVar22 + 0x1c))) {
                      bVar4 = *(double *)((longlong)plVar22 + 0x1c) == 0.0;
                    }
                  }
                  if (!bVar4) {
                    FUN_18000d3e8((longlong)param_3,uVar20,param_5,local_158,plVar21 + 6,plVar19,
                                  &local_f8,&local_a0);
                  }
switchD_18007e4c8_caseD_0:
                  uVar20 = uVar20 + 1;
                } while ((int)uVar20 < *param_3);
              }
              *puVar6 = (uint)((int)local_188 != 0);
            }
          }
        }
      }
      else {
        *puVar6 = (uint)(iVar5 != 0);
        if (plVar16 != (longlong *)0x0) {
          FUN_18007e580(plVar16);
          (**(code **)(param_3 + 0x448))(0,0);
        }
        if (plVar19 != (longlong *)0x0) {
          FUN_18007e580(plVar19);
          (**(code **)(param_3 + 0x448))(1,0);
        }
      }
    }
  }
  else {
    FUN_1800079f8((longlong *)(param_3 + 0x46),0x1800dabd0,0xb);
    param_3[0x49] = 1;
    if ((*(char **)(param_3 + 0xce) != (char *)0x0) && (**(char **)(param_3 + 0xce) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0xce),(longlong)local_188,0);
      param_3[0xd1] = 1;
    }
    plVar16 = local_188;
    local_1a0 = local_188;
    local_198 = 0;
    local_190 = local_188;
    pvVar8 = GetProcessHeap();
    local_1b0 = (longlong *)HeapAlloc(pvVar8,0,0x15);
    bVar4 = local_1b0 == (longlong *)0x0;
    if (bVar4) {
      local_1b0 = plVar16;
      local_1a0 = plVar16;
    }
    else {
      param_6 = 0x14;
      local_1b0[1] = 0;
      *local_1b0 = 0;
      *(undefined4 *)(local_1b0 + 2) = 0;
      *(char *)((longlong)local_1b0 + 0x14) = '\0';
      local_1a0 = local_1b0;
      FUN_180099d78((char *)local_1b0,0x15,0x1800d6c40,0x14);
      local_198 = 0x100000001;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_1a0);
    if ((!bVar4) && (local_1b0 != (longlong *)0x0)) {
      pvVar8 = GetProcessHeap();
      HeapFree(pvVar8,0,local_1b0);
    }
    plVar16 = local_188;
    local_1a0 = local_188;
    local_198 = 0;
    local_190 = local_188;
    pvVar8 = GetProcessHeap();
    local_1b0 = (longlong *)HeapAlloc(pvVar8,0,0x8d);
    bVar4 = local_1b0 == (longlong *)0x0;
    if (bVar4) {
      local_1a0 = plVar16;
      local_1b0 = plVar16;
    }
    else {
      param_6 = 0x8c;
      local_1b0[1] = 0;
      *local_1b0 = 0;
      local_1b0[3] = 0;
      local_1b0[2] = 0;
      local_1b0[5] = 0;
      local_1b0[4] = 0;
      local_1b0[7] = 0;
      local_1b0[6] = 0;
      local_1b0[9] = 0;
      local_1b0[8] = 0;
      local_1b0[0xb] = 0;
      local_1b0[10] = 0;
      local_1b0[0xd] = 0;
      local_1b0[0xc] = 0;
      local_1b0[0xf] = 0;
      local_1b0[0xe] = 0;
      local_1b0[0x10] = 0;
      *(undefined4 *)(local_1b0 + 0x11) = 0;
      *(char *)((longlong)local_1b0 + 0x8c) = '\0';
      local_1a0 = local_1b0;
      FUN_180099d78((char *)local_1b0,0x8d,0x1800d6bb0,0x8c);
      local_198 = 0x100000001;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_1a0);
    auVar24 = extraout_q0_00;
    if ((!bVar4) && (local_1b0 != (longlong *)0x0)) {
      pvVar8 = GetProcessHeap();
      HeapFree(pvVar8,0,local_1b0);
      local_1a0 = (longlong *)0x0;
      local_198 = 0;
      auVar24 = extraout_q0_01;
    }
    plVar19 = local_188;
    plVar16 = *(longlong **)(param_3 + 0x46);
    if (*(longlong **)(param_3 + 0x46) == (longlong *)0x0) {
      plVar16 = local_188;
    }
    FUN_180026368(auVar24,param_2,(undefined8 *)(param_3 + 0xce),0x1800d6c60,plVar16,param_6,param_7
                  ,param_8,param_9,param_10);
    local_1a0 = plVar19;
    local_198 = 0;
    local_190 = plVar19;
    pvVar8 = GetProcessHeap();
    plVar16 = (longlong *)HeapAlloc(pvVar8,0,7);
    if (plVar16 == (longlong *)0x0) {
      local_1a0 = plVar19;
      local_188 = plVar19;
    }
    else {
      *(undefined4 *)plVar16 = 0;
      ((char *)((longlong)plVar16 + 4))[0] = '\0';
      ((char *)((longlong)plVar16 + 4))[1] = '\0';
      *(char *)((longlong)plVar16 + 6) = '\0';
      local_1a0 = plVar16;
      local_188 = plVar16;
      FUN_180099d78((char *)plVar16,7,0x1800d6c58,6);
      local_198 = 0x100000001;
      plVar19 = plVar16;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_1a0);
    if ((plVar16 != (longlong *)0x0) && (plVar19 != (longlong *)0x0)) {
      pvVar8 = GetProcessHeap();
      HeapFree(pvVar8,0,local_188);
      local_1a0 = (longlong *)0x0;
      local_198 = 0;
    }
    param_3[0xdf] = 0;
    param_3[0xe3] = 0;
    param_3[4] = 0;
    param_3[0xcc] = 1;
    param_3[0x3ac] = 0;
    param_3[0x1fc] = 0;
    FUN_1800079f8(plVar23,0x1800dabdc,5);
    plVar16 = local_178;
    *(undefined4 *)((longlong)plVar23 + 0xc) = 1;
    *(undefined2 *)((longlong)plVar23 + 0x24) = 2;
    *(undefined4 *)(plVar23 + 3) = 0xd200;
    *(undefined2 *)(plVar23 + 5) = 6;
    *(undefined4 *)(plVar23 + 0x1b) = 0;
    FUN_1800079f8(local_178,0x1800dac14,5);
    *(undefined4 *)((longlong)plVar16 + 0xc) = 1;
    *(undefined2 *)((longlong)plVar16 + 0x24) = 2;
    *(undefined4 *)(plVar16 + 3) = 0xd2;
    *(undefined2 *)(plVar16 + 5) = 6;
    *(undefined4 *)(plVar16 + 0x1b) = 0;
    FUN_1800079f8(plVar21,0x1800dac20,0x13);
    *(int *)((longlong)local_180 + 0xc) = 1;
    *(undefined1 *)(local_180 + 3) = 0x12;
    ((int *)((longlong)local_180 + 0x1c))[0] = 1;
    ((int *)((longlong)local_180 + 0x1c))[1] = 1;
    *(int *)((longlong)local_180 + 0x24) = 0;
    FUN_1800079f8(plVar22,0x1800dabe8,0x15);
    *(undefined4 *)((longlong)plVar22 + 0xc) = 1;
    *(undefined4 *)((longlong)plVar22 + 0x1c) = 0;
    *(undefined1 *)(plVar22 + 3) = 5;
    FUN_1800079f8(plVar17,0x1800dac00,0x13);
    *(undefined4 *)((longlong)plVar17 + 0xc) = 1;
    *(undefined1 *)(plVar17 + 3) = 0x12;
    *(undefined8 *)((longlong)plVar17 + 0x1c) = 0x100000001;
    *(undefined4 *)((longlong)plVar17 + 0x24) = 0;
  }
  return;
}

