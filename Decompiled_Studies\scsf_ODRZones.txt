
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_ODRZones(undefined1 param_1 [16],undefined1 param_2 [16],int *param_3,undefined8 param_4,
                  ulonglong param_5,undefined8 param_6,ulonglong param_7,ulonglong param_8,
                  ulonglong param_9,longlong **param_10)

{
  longlong lVar1;
  byte *pbVar2;
  char cVar3;
  undefined1 auVar4 [16];
  undefined1 auVar5 [16];
  undefined1 auVar6 [16];
  undefined1 auVar7 [16];
  undefined1 auVar8 [16];
  undefined1 auVar9 [16];
  undefined1 *lpMem;
  bool bVar10;
  bool bVar11;
  int iVar12;
  uint uVar13;
  int iVar14;
  int iVar15;
  uint uVar16;
  uint uVar17;
  int *piVar18;
  uint *puVar19;
  uint *puVar20;
  undefined8 *puVar21;
  char *pcVar22;
  HANDLE pvVar23;
  undefined8 uVar24;
  ulonglong uVar25;
  undefined4 *puVar26;
  longlong **pplVar27;
  uint *puVar28;
  float *pfVar29;
  uint extraout_w1;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  undefined8 extraout_x1_10;
  undefined8 extraout_x1_11;
  undefined8 extraout_x1_12;
  undefined8 extraout_x1_13;
  undefined8 extraout_x1_14;
  undefined8 extraout_x1_15;
  undefined8 extraout_x1_16;
  undefined8 extraout_x1_17;
  undefined8 extraout_x1_18;
  undefined8 extraout_x1_19;
  undefined8 extraout_x1_20;
  undefined8 extraout_x1_21;
  undefined8 extraout_x1_22;
  undefined8 extraout_x1_23;
  undefined8 extraout_x1_24;
  undefined8 extraout_x1_25;
  undefined8 extraout_x1_26;
  undefined8 extraout_x1_27;
  int iVar30;
  longlong *plVar31;
  float *pfVar32;
  undefined8 *puVar33;
  uint uVar34;
  longlong lVar35;
  int *extraout_x11;
  longlong extraout_x11_00;
  longlong extraout_x11_01;
  longlong extraout_x11_02;
  int extraout_w12;
  longlong extraout_x12;
  int extraout_w13;
  longlong extraout_x13;
  longlong extraout_x13_00;
  longlong extraout_x13_01;
  longlong extraout_x13_02;
  int extraout_w14;
  longlong extraout_x14;
  int extraout_w15;
  longlong *plVar36;
  longlong *plVar37;
  char **ppcVar38;
  int *piVar39;
  uint *puVar40;
  byte *pbVar41;
  int *piVar42;
  char *pcVar43;
  LPVOID pvVar44;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  undefined4 extraout_s0_02;
  undefined4 extraout_s0_03;
  undefined4 extraout_s0_04;
  undefined4 extraout_s0_05;
  undefined4 extraout_s0_06;
  undefined4 extraout_s0_07;
  undefined4 extraout_s0_08;
  undefined4 extraout_s0_09;
  undefined4 extraout_s0_10;
  undefined4 extraout_s0_11;
  undefined4 extraout_s0_12;
  undefined4 extraout_s0_13;
  undefined4 extraout_s0_14;
  undefined4 extraout_s0_15;
  undefined4 extraout_s0_16;
  undefined4 extraout_s0_17;
  undefined4 extraout_s0_18;
  undefined4 extraout_s0_19;
  undefined4 extraout_s0_20;
  undefined4 extraout_s0_21;
  undefined4 extraout_s0_22;
  undefined4 extraout_s0_23;
  undefined4 extraout_s0_24;
  undefined4 extraout_s0_25;
  undefined4 extraout_s0_26;
  undefined4 extraout_s0_27;
  undefined4 extraout_s0_28;
  undefined4 extraout_s0_29;
  undefined4 extraout_s0_30;
  undefined4 extraout_s0_31;
  undefined4 extraout_s0_32;
  undefined4 extraout_s0_33;
  undefined4 extraout_s0_34;
  undefined4 extraout_s0_35;
  undefined4 extraout_s0_36;
  undefined4 extraout_s0_37;
  undefined4 extraout_s0_38;
  undefined4 extraout_s0_39;
  undefined4 extraout_s0_40;
  undefined4 extraout_s0_41;
  undefined4 extraout_var;
  undefined4 extraout_var_00;
  undefined4 extraout_var_01;
  undefined4 extraout_var_02;
  undefined4 extraout_var_03;
  undefined4 extraout_var_04;
  undefined4 extraout_var_05;
  undefined4 extraout_var_06;
  undefined4 extraout_var_07;
  undefined4 extraout_var_08;
  undefined4 extraout_var_09;
  undefined4 extraout_var_10;
  undefined4 extraout_var_11;
  undefined4 extraout_var_12;
  undefined4 extraout_var_13;
  undefined4 extraout_var_14;
  undefined4 extraout_var_15;
  undefined4 extraout_var_16;
  undefined4 extraout_var_17;
  undefined4 extraout_var_18;
  undefined4 extraout_var_19;
  undefined4 extraout_var_20;
  undefined4 extraout_var_21;
  undefined4 extraout_var_22;
  undefined4 extraout_var_23;
  undefined4 extraout_var_24;
  undefined4 extraout_var_25;
  undefined4 extraout_var_26;
  undefined4 extraout_var_27;
  undefined4 extraout_var_28;
  undefined4 extraout_var_29;
  undefined4 extraout_var_30;
  undefined4 extraout_var_31;
  undefined4 extraout_var_32;
  undefined4 extraout_var_33;
  undefined4 extraout_var_34;
  undefined4 extraout_var_35;
  undefined4 extraout_var_36;
  undefined4 extraout_var_37;
  undefined4 extraout_var_38;
  undefined4 extraout_var_39;
  undefined4 extraout_var_40;
  undefined4 uVar45;
  undefined4 extraout_var_41;
  undefined8 extraout_var_42;
  undefined8 extraout_var_43;
  undefined8 extraout_var_44;
  undefined8 extraout_var_45;
  undefined8 extraout_var_46;
  undefined8 extraout_var_47;
  undefined8 extraout_var_48;
  undefined8 extraout_var_49;
  undefined8 extraout_var_50;
  undefined8 extraout_var_51;
  undefined8 extraout_var_52;
  undefined8 extraout_var_53;
  undefined8 extraout_var_54;
  undefined8 extraout_var_55;
  undefined8 extraout_var_56;
  undefined8 extraout_var_57;
  undefined8 extraout_var_58;
  undefined8 extraout_var_59;
  undefined8 extraout_var_60;
  undefined8 extraout_var_61;
  undefined8 extraout_var_62;
  undefined8 extraout_var_63;
  undefined8 extraout_var_64;
  undefined8 extraout_var_65;
  undefined8 extraout_var_66;
  undefined8 extraout_var_67;
  undefined8 extraout_var_68;
  undefined8 extraout_var_69;
  undefined8 extraout_var_70;
  undefined8 uVar46;
  undefined8 extraout_var_71;
  undefined8 extraout_var_72;
  undefined8 extraout_var_73;
  undefined8 extraout_var_74;
  undefined8 extraout_var_75;
  undefined8 extraout_var_76;
  undefined8 extraout_var_77;
  undefined8 extraout_var_78;
  undefined8 extraout_var_79;
  undefined8 extraout_var_80;
  undefined8 extraout_var_81;
  undefined8 extraout_var_82;
  undefined8 extraout_var_83;
  undefined8 extraout_var_84;
  undefined4 uVar47;
  undefined8 uVar48;
  undefined4 uVar49;
  double dVar50;
  uint in_stack_fffffffffffffa58;
  uint uVar51;
  undefined4 in_stack_fffffffffffffa5c;
  undefined8 in_stack_fffffffffffffab0;
  longlong *local_540;
  char **local_538;
  longlong *local_530;
  longlong *local_518;
  longlong *local_508;
  longlong *local_500;
  longlong *local_4f0;
  longlong *local_4e0;
  int *local_4d8;
  longlong *local_4d0;
  longlong *local_4b8;
  longlong *local_4b0;
  char *local_488;
  longlong *local_478;
  longlong **local_468;
  longlong **local_460;
  longlong *local_448;
  longlong *local_440;
  longlong *local_438;
  longlong *local_430;
  longlong *local_428;
  longlong *local_420;
  longlong *local_418;
  longlong **local_410;
  longlong *local_408;
  longlong *local_3f8;
  longlong *local_3f0;
  int *local_3e8;
  longlong **local_3e0;
  undefined8 *local_3d8;
  longlong **local_3d0;
  int *local_3c8;
  uint *local_3c0;
  longlong *local_3b8;
  longlong *local_3b0;
  int *local_3a8;
  longlong **local_3a0;
  longlong *local_398 [2];
  int *local_388;
  undefined8 local_380;
  undefined8 uStack_378;
  undefined8 uStack_370;
  undefined8 uStack_368;
  undefined8 local_360;
  undefined8 uStack_358;
  double local_350;
  double dStack_348;
  undefined1 *local_340;
  undefined8 uStack_338;
  undefined1 *local_330;
  ulonglong local_328;
  undefined1 *puStack_320;
  undefined8 local_318;
  undefined1 *puStack_310;
  ulonglong local_308;
  int local_300;
  undefined4 local_2fc;
  undefined4 local_2f8;
  undefined4 uStack_2f4;
  undefined1 *local_2f0;
  undefined8 uStack_2e8;
  undefined1 *local_2e0;
  undefined8 local_2d8;
  undefined1 *puStack_2d0;
  undefined8 local_2c8;
  undefined1 *puStack_2c0;
  ulonglong local_2b8;
  char *local_2b0;
  undefined8 local_2a8;
  undefined1 *puStack_2a0;
  undefined8 local_298;
  undefined8 uStack_290;
  undefined8 local_288;
  undefined8 uStack_280;
  undefined8 local_278;
  char *local_270;
  undefined8 uStack_268;
  undefined1 *puStack_260;
  ulonglong uStack_258;
  undefined4 local_250;
  undefined8 local_240;
  char *local_230;
  undefined8 uStack_228;
  undefined1 *local_220;
  ulonglong local_218;
  undefined4 local_210;
  char **local_200 [2];
  longlong *local_1f0;
  longlong lStack_1e8;
  longlong *local_1e0;
  longlong *local_1d8;
  int *local_1d0;
  longlong *local_1c8;
  undefined **local_1c0;
  undefined **local_1b8;
  undefined **local_1b0;
  undefined **local_1a8;
  undefined **local_1a0;
  undefined **local_198;
  longlong *local_190;
  undefined4 local_188;
  char *local_180;
  undefined8 uStack_178;
  undefined1 *local_170;
  undefined8 local_160;
  undefined8 uStack_158;
  undefined8 local_150;
  undefined8 uStack_148;
  undefined8 local_140;
  undefined8 local_138;
  undefined1 *local_130;
  undefined1 *local_120;
  undefined8 uStack_118;
  undefined1 *local_110;
  longlong local_100;
  undefined8 uStack_f8;
  longlong local_f0;
  undefined8 uStack_e8;
  undefined8 local_e0;
  undefined8 uStack_d8;
  undefined8 uStack_d0;
  longlong local_c8;
  longlong local_c0;
  longlong lStack_b8;
  undefined8 uStack_b0;
  undefined8 uStack_a8;
  undefined8 local_a0;
  undefined8 uStack_98;
  undefined8 uStack_90;
  undefined8 uStack_88;
  undefined8 local_80;
  
                    /* 0x5b250  17  scsf_ODRZones */
  uVar17 = param_2._0_4_;
  uVar47 = param_2._4_4_;
  uVar48 = param_2._8_8_;
  local_240 = 0xfffffffffffffffe;
  local_110 = &DAT_1800d4ecd;
  local_120 = &DAT_1800d4ecd;
  uStack_118 = 0;
  piVar18 = (int *)(**(code **)(param_3 + 0x62e))(param_1._0_4_,0);
  puVar19 = (uint *)(**(code **)(param_3 + 0x62e))(1);
  puVar20 = (uint *)(**(code **)(param_3 + 0x62e))(2);
  local_3e8 = (int *)(**(code **)(param_3 + 0x62e))(3);
  local_3c0 = (uint *)(**(code **)(param_3 + 0x62e))(4);
  local_388 = (int *)(**(code **)(param_3 + 0x62e))(5);
  puVar21 = (undefined8 *)(**(code **)(param_3 + 0x446))(2);
  puVar40 = (uint *)*puVar21;
  puVar21 = (undefined8 *)(**(code **)(param_3 + 0x446))(3);
  lVar35 = *(longlong *)(param_3 + 0x140);
  local_488 = (char *)*puVar21;
  uVar24 = extraout_x1;
  uVar49 = extraout_s0;
  uVar45 = extraout_var;
  uVar46 = extraout_var_42;
  if (lVar35 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar24 = extraout_x1_00;
      uVar49 = extraout_s0_00;
      uVar45 = extraout_var_00;
      uVar46 = extraout_var_43;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_18005b368;
    local_3b8 = (longlong *)(param_3 + 0x14a);
LAB_18005b3a0:
    local_540 = local_3b8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar24 = extraout_x1_01;
      uVar49 = extraout_s0_01;
      uVar45 = extraout_var_01;
      uVar46 = extraout_var_44;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_18005b3d0;
    local_3b0 = (longlong *)(param_3 + 0x14a);
LAB_18005b408:
    local_518 = local_3b0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar24 = extraout_x1_02;
      uVar49 = extraout_s0_02;
      uVar45 = extraout_var_02;
      uVar46 = extraout_var_45;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_18005b438;
    local_3a8 = param_3 + 0x14a;
LAB_18005b474:
    local_4d8 = local_3a8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar24 = extraout_x1_03;
      uVar49 = extraout_s0_03;
      uVar45 = extraout_var_03;
      uVar46 = extraout_var_46;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_18005b4a4;
    local_4e0 = (longlong *)(param_3 + 0x14a);
LAB_18005b4e0:
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar24 = extraout_x1_04;
      uVar49 = extraout_s0_04;
      uVar45 = extraout_var_04;
      uVar46 = extraout_var_47;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_18005b500;
    piVar39 = param_3 + 0x14a;
  }
  else {
LAB_18005b368:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_540 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar14 = 0;
      if (iVar12 < 1) {
        iVar14 = iVar12 + -1;
      }
      local_540 = (longlong *)(lVar35 + (longlong)iVar14 * 0x170);
    }
    local_3b8 = local_540;
    if (lVar35 == 0) goto LAB_18005b3a0;
LAB_18005b3d0:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_518 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar14 = 1;
      if (iVar12 + -1 == 0 || iVar12 < 1) {
        iVar14 = iVar12 + -1;
      }
      local_518 = (longlong *)(lVar35 + (longlong)iVar14 * 0x170);
    }
    local_3b0 = local_518;
    if (lVar35 == 0) goto LAB_18005b408;
LAB_18005b438:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_4d8 = param_3 + 0x14a;
    }
    else {
      iVar14 = 2;
      if (iVar12 < 3) {
        iVar14 = iVar12 + -1;
      }
      local_4d8 = (int *)(lVar35 + (longlong)iVar14 * 0x170);
    }
    local_3a8 = local_4d8;
    if (lVar35 == 0) goto LAB_18005b474;
LAB_18005b4a4:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_4e0 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar14 = 3;
      if (iVar12 < 4) {
        iVar14 = iVar12 + -1;
      }
      local_4e0 = (longlong *)(lVar35 + (longlong)iVar14 * 0x170);
    }
    if (lVar35 == 0) goto LAB_18005b4e0;
LAB_18005b500:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      piVar39 = param_3 + 0x14a;
    }
    else {
      iVar14 = 4;
      if (iVar12 < 5) {
        iVar14 = iVar12 + -1;
      }
      piVar39 = (int *)(lVar35 + (longlong)iVar14 * 0x170);
    }
  }
  lVar35 = *(longlong *)(param_3 + 0x84);
  if (lVar35 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_05;
      uVar49 = extraout_s0_05;
      uVar45 = extraout_var_05;
      uVar46 = extraout_var_48;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005b568;
    local_4d0 = (longlong *)(param_3 + 0x8e);
LAB_18005b5a0:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_06;
      uVar49 = extraout_s0_06;
      uVar45 = extraout_var_06;
      uVar46 = extraout_var_49;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005b5d0;
    local_478 = (longlong *)(param_3 + 0x8e);
LAB_18005b604:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_07;
      uVar49 = extraout_s0_07;
      uVar45 = extraout_var_07;
      uVar46 = extraout_var_50;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005b638;
    local_3a0 = (longlong **)(param_3 + 0x8e);
LAB_18005b674:
    local_460 = local_3a0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_08;
      uVar49 = extraout_s0_08;
      uVar45 = extraout_var_08;
      uVar46 = extraout_var_51;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005b6a4;
    local_398[0] = (longlong *)(param_3 + 0x8e);
LAB_18005b6dc:
    local_500 = local_398[0];
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_09;
      uVar49 = extraout_s0_09;
      uVar45 = extraout_var_09;
      uVar46 = extraout_var_52;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005b710;
    local_3e0 = (longlong **)(param_3 + 0x8e);
LAB_18005b74c:
    local_468 = local_3e0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_10;
      uVar49 = extraout_s0_10;
      uVar45 = extraout_var_10;
      uVar46 = extraout_var_53;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005b77c;
    local_1f0 = (longlong *)(param_3 + 0x8e);
LAB_18005b7b4:
    local_508 = local_1f0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_11;
      uVar49 = extraout_s0_11;
      uVar45 = extraout_var_11;
      uVar46 = extraout_var_54;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005b7e4;
    local_190 = (longlong *)(param_3 + 0x8e);
LAB_18005b81c:
    local_530 = local_190;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_12;
      uVar49 = extraout_s0_12;
      uVar45 = extraout_var_12;
      uVar46 = extraout_var_55;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005b84c;
    local_3d0 = (longlong **)(param_3 + 0x8e);
LAB_18005b884:
    local_410 = local_3d0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_13;
      uVar49 = extraout_s0_13;
      uVar45 = extraout_var_13;
      uVar46 = extraout_var_56;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005b8b4;
    local_1d8 = (longlong *)(param_3 + 0x8e);
LAB_18005b8ec:
    local_418 = local_1d8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_14;
      uVar49 = extraout_s0_14;
      uVar45 = extraout_var_14;
      uVar46 = extraout_var_57;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005b91c;
    local_3f8 = (longlong *)(param_3 + 0x8e);
LAB_18005b954:
    local_420 = local_3f8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_15;
      uVar49 = extraout_s0_15;
      uVar45 = extraout_var_15;
      uVar46 = extraout_var_58;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005b984;
    local_3f0 = (longlong *)(param_3 + 0x8e);
LAB_18005b9bc:
    local_428 = local_3f0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_16;
      uVar49 = extraout_s0_16;
      uVar45 = extraout_var_16;
      uVar46 = extraout_var_59;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005b9e8;
    piVar42 = param_3 + 0x8e;
LAB_18005ba1c:
    local_1d0 = piVar42;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_17;
      uVar49 = extraout_s0_17;
      uVar45 = extraout_var_17;
      uVar46 = extraout_var_60;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005ba4c;
    local_200[0] = (char **)(param_3 + 0x8e);
LAB_18005ba84:
    local_538 = local_200[0];
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_18;
      uVar49 = extraout_s0_18;
      uVar45 = extraout_var_18;
      uVar46 = extraout_var_61;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005bab4;
    local_1c8 = (longlong *)(param_3 + 0x8e);
LAB_18005baec:
    local_4f0 = local_1c8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_19;
      uVar49 = extraout_s0_19;
      uVar45 = extraout_var_19;
      uVar46 = extraout_var_62;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005bb1c;
    local_4b8 = (longlong *)(param_3 + 0x8e);
LAB_18005bb54:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_20;
      uVar49 = extraout_s0_20;
      uVar45 = extraout_var_20;
      uVar46 = extraout_var_63;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005bb84;
    local_4b0 = (longlong *)(param_3 + 0x8e);
LAB_18005bbbc:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_21;
      uVar49 = extraout_s0_21;
      uVar45 = extraout_var_21;
      uVar46 = extraout_var_64;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005bbec;
    local_1e0 = (longlong *)(param_3 + 0x8e);
LAB_18005bc24:
    local_408 = local_1e0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_22;
      uVar49 = extraout_s0_22;
      uVar45 = extraout_var_22;
      uVar46 = extraout_var_65;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005bc54;
    local_430 = (longlong *)(param_3 + 0x8e);
LAB_18005bc8c:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_23;
      uVar49 = extraout_s0_23;
      uVar45 = extraout_var_23;
      uVar46 = extraout_var_66;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005bcbc;
    local_438 = (longlong *)(param_3 + 0x8e);
LAB_18005bcf4:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_24;
      uVar49 = extraout_s0_24;
      uVar45 = extraout_var_24;
      uVar46 = extraout_var_67;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005bd24;
    local_440 = (longlong *)(param_3 + 0x8e);
LAB_18005bd5c:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_25;
      uVar49 = extraout_s0_25;
      uVar45 = extraout_var_25;
      uVar46 = extraout_var_68;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005bd8c;
    local_448 = (longlong *)(param_3 + 0x8e);
LAB_18005bdc4:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_26;
      uVar49 = extraout_s0_26;
      uVar45 = extraout_var_26;
      uVar46 = extraout_var_69;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18005bdf0;
    plVar36 = (longlong *)(param_3 + 0x8e);
LAB_18005be24:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar24 = extraout_x1_27;
      uVar49 = extraout_s0_27;
      uVar45 = extraout_var_27;
      uVar46 = extraout_var_70;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 == 0) {
      plVar31 = (longlong *)(param_3 + 0x8e);
      goto LAB_18005be68;
    }
  }
  else {
LAB_18005b568:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_4d0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 0;
      if (iVar12 < 1) {
        iVar14 = iVar12 + -1;
      }
      local_4d0 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    if (lVar35 == 0) goto LAB_18005b5a0;
LAB_18005b5d0:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_478 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 1;
      if (iVar12 + -1 == 0 || iVar12 < 1) {
        iVar14 = iVar12 + -1;
      }
      local_478 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    if (lVar35 == 0) goto LAB_18005b604;
LAB_18005b638:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_460 = (longlong **)(param_3 + 0x8e);
    }
    else {
      iVar14 = 2;
      if (iVar12 < 3) {
        iVar14 = iVar12 + -1;
      }
      local_460 = (longlong **)(lVar35 + (longlong)iVar14 * 0x98);
    }
    local_3a0 = local_460;
    if (lVar35 == 0) goto LAB_18005b674;
LAB_18005b6a4:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_500 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 3;
      if (iVar12 < 4) {
        iVar14 = iVar12 + -1;
      }
      local_500 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    local_398[0] = local_500;
    if (lVar35 == 0) goto LAB_18005b6dc;
LAB_18005b710:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_468 = (longlong **)(param_3 + 0x8e);
    }
    else {
      iVar14 = 4;
      if (iVar12 < 5) {
        iVar14 = iVar12 + -1;
      }
      local_468 = (longlong **)(lVar35 + (longlong)iVar14 * 0x98);
    }
    local_3e0 = local_468;
    if (lVar35 == 0) goto LAB_18005b74c;
LAB_18005b77c:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_508 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 5;
      if (iVar12 < 6) {
        iVar14 = iVar12 + -1;
      }
      local_508 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    local_1f0 = local_508;
    if (lVar35 == 0) goto LAB_18005b7b4;
LAB_18005b7e4:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_530 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 6;
      if (iVar12 < 7) {
        iVar14 = iVar12 + -1;
      }
      local_530 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    local_190 = local_530;
    if (lVar35 == 0) goto LAB_18005b81c;
LAB_18005b84c:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_410 = (longlong **)(param_3 + 0x8e);
    }
    else {
      iVar14 = 7;
      if (iVar12 < 8) {
        iVar14 = iVar12 + -1;
      }
      local_410 = (longlong **)(lVar35 + (longlong)iVar14 * 0x98);
    }
    local_3d0 = local_410;
    if (lVar35 == 0) goto LAB_18005b884;
LAB_18005b8b4:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_418 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 8;
      if (iVar12 < 9) {
        iVar14 = iVar12 + -1;
      }
      local_418 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    local_1d8 = local_418;
    if (lVar35 == 0) goto LAB_18005b8ec;
LAB_18005b91c:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_420 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 9;
      if (iVar12 < 10) {
        iVar14 = iVar12 + -1;
      }
      local_420 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    local_3f8 = local_420;
    if (lVar35 == 0) goto LAB_18005b954;
LAB_18005b984:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_428 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 10;
      if (iVar12 < 0xb) {
        iVar14 = iVar12 + -1;
      }
      local_428 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    local_3f0 = local_428;
    if (lVar35 == 0) goto LAB_18005b9bc;
LAB_18005b9e8:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      piVar42 = param_3 + 0x8e;
    }
    else {
      iVar14 = 0xb;
      if (iVar12 < 0xc) {
        iVar14 = iVar12 + -1;
      }
      piVar42 = (int *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    local_1d0 = piVar42;
    if (lVar35 == 0) goto LAB_18005ba1c;
LAB_18005ba4c:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_538 = (char **)(param_3 + 0x8e);
    }
    else {
      iVar14 = 0xc;
      if (iVar12 < 0xd) {
        iVar14 = iVar12 + -1;
      }
      local_538 = (char **)(lVar35 + (longlong)iVar14 * 0x98);
    }
    local_200[0] = local_538;
    if (lVar35 == 0) goto LAB_18005ba84;
LAB_18005bab4:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_4f0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 0xd;
      if (iVar12 < 0xe) {
        iVar14 = iVar12 + -1;
      }
      local_4f0 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    local_1c8 = local_4f0;
    if (lVar35 == 0) goto LAB_18005baec;
LAB_18005bb1c:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_4b8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 0xe;
      if (iVar12 < 0xf) {
        iVar14 = iVar12 + -1;
      }
      local_4b8 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    if (lVar35 == 0) goto LAB_18005bb54;
LAB_18005bb84:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_4b0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 0xf;
      if (iVar12 < 0x10) {
        iVar14 = iVar12 + -1;
      }
      local_4b0 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    if (lVar35 == 0) goto LAB_18005bbbc;
LAB_18005bbec:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_408 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 0x10;
      if (iVar12 < 0x11) {
        iVar14 = iVar12 + -1;
      }
      local_408 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    local_1e0 = local_408;
    if (lVar35 == 0) goto LAB_18005bc24;
LAB_18005bc54:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_430 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 0x11;
      if (iVar12 < 0x12) {
        iVar14 = iVar12 + -1;
      }
      local_430 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    if (lVar35 == 0) goto LAB_18005bc8c;
LAB_18005bcbc:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_438 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 0x12;
      if (iVar12 < 0x13) {
        iVar14 = iVar12 + -1;
      }
      local_438 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    if (lVar35 == 0) goto LAB_18005bcf4;
LAB_18005bd24:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_440 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 0x13;
      if (iVar12 < 0x14) {
        iVar14 = iVar12 + -1;
      }
      local_440 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    if (lVar35 == 0) goto LAB_18005bd5c;
LAB_18005bd8c:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_448 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 0x14;
      if (iVar12 < 0x15) {
        iVar14 = iVar12 + -1;
      }
      local_448 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    if (lVar35 == 0) goto LAB_18005bdc4;
LAB_18005bdf0:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      plVar36 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar14 = 0x15;
      if (iVar12 < 0x16) {
        iVar14 = iVar12 + -1;
      }
      plVar36 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
    }
    if (lVar35 == 0) goto LAB_18005be24;
  }
  iVar12 = param_3[0x8c];
  if (iVar12 == 0) {
    plVar31 = (longlong *)(param_3 + 0x8e);
  }
  else {
    iVar14 = 0x16;
    if (iVar12 < 0x17) {
      iVar14 = iVar12 + -1;
    }
    plVar31 = (longlong *)(lVar35 + (longlong)iVar14 * 0x98);
  }
LAB_18005be68:
  local_198 = LineStyles::vftable;
  local_1a0 = ErasureModes::vftable;
  local_1a8 = PitSessions::vftable;
  local_1b0 = PricePositions::vftable;
  local_1b8 = OverlapModes::vftable;
  local_1c0 = ShowZonePrices::vftable;
  local_3c8 = piVar42;
  if (param_3[0x2b] == 0) {
    if (param_3[3] == 0) {
      auVar4._4_4_ = uVar45;
      auVar4._0_4_ = uVar49;
      auVar4._8_8_ = uVar46;
      uVar24 = FUN_1800254e8(auVar4,CONCAT44(uVar47,uVar17),(longlong)param_3,uVar24,param_5,param_6
                             ,param_7,param_8,param_9,param_10);
      *piVar18 = (int)uVar24;
    }
    if (*piVar18 == 0) {
      iVar12 = (**(code **)(param_3 + 0x3ec))(param_3[0x270]);
      uVar13 = param_3[3];
      if ((uVar13 == 0) || ((*puVar19 == 0 && (iVar12 != 0)))) {
        iVar14 = 0;
        *puVar19 = (uint)(iVar12 != 0);
        do {
          uVar13 = 0;
          if (0 < *param_3) {
            do {
              lVar35 = *(longlong *)(param_3 + 0x140);
              if (lVar35 == 0) {
                if (*(code **)(param_3 + 0x144) != (code *)0x0) {
                  (**(code **)(param_3 + 0x144))(param_3[0x146]);
                }
                lVar35 = *(longlong *)(param_3 + 0x140);
                if (lVar35 != 0) goto LAB_18005cc58;
                piVar18 = param_3 + 0x14a;
              }
              else {
LAB_18005cc58:
                iVar15 = param_3[0x148];
                if (iVar15 == 0) {
                  piVar18 = param_3 + 0x14a;
                }
                else {
                  iVar30 = iVar14;
                  if (iVar15 <= iVar14) {
                    iVar30 = iVar15 + -1;
                  }
                  piVar18 = (int *)(lVar35 + (longlong)iVar30 * 0x170);
                }
              }
              lVar35 = *(longlong *)(piVar18 + 0xc);
              if (lVar35 == 0) {
                if (*(code **)(piVar18 + 0x10) != (code *)0x0) {
                  (**(code **)(piVar18 + 0x10))(piVar18[0x12]);
                }
                lVar35 = *(longlong *)(piVar18 + 0xc);
                if (lVar35 != 0) goto LAB_18005cd58;
                piVar18 = piVar18 + 0x15;
              }
              else {
LAB_18005cd58:
                iVar15 = piVar18[0x14];
                if (iVar15 == 0) {
                  piVar18 = piVar18 + 0x15;
                }
                else {
                  uVar16 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
                  if (iVar15 <= (int)uVar16) {
                    uVar16 = iVar15 - 1;
                  }
                  piVar18 = (int *)(lVar35 + (longlong)(int)uVar16 * 4);
                }
              }
              *piVar18 = 0;
              uVar13 = uVar13 + 1;
            } while ((int)uVar13 < *param_3);
          }
          iVar14 = iVar14 + 1;
        } while (iVar14 < 0x3c);
        switch((char)local_440[3]) {
        default:
          bVar11 = false;
          break;
        case '\x01':
        case '\x03':
        case '\x04':
        case '\x05':
        case '\x06':
        case '\v':
        case '\r':
        case '\x0e':
        case '\x0f':
        case '\x10':
        case '\x11':
        case '\x13':
        case '\x16':
        case '\x18':
          bVar11 = *(int *)((longlong)local_440 + 0x1c) != 0;
          break;
        case '\x02':
          bVar11 = *(float *)((longlong)local_440 + 0x1c) != 0.0;
          break;
        case '\b':
        case '\t':
        case '\n':
        case '\x17':
        case '\x19':
          bVar11 = *(double *)((longlong)local_440 + 0x1c) != 0.0;
        }
        puVar21 = (undefined8 *)(**(code **)(param_3 + 0x446))(3);
        pcVar43 = (char *)*puVar21;
        if (pcVar43 != (char *)0x0) {
          FUN_18000af28(pcVar43,(longlong)param_3);
          puVar21 = *(undefined8 **)(pcVar43 + 8);
          *(undefined8 *)puVar21[1] = 0;
          puVar21 = (undefined8 *)*puVar21;
          while (puVar21 != (undefined8 *)0x0) {
            pvVar44 = (LPVOID)*puVar21;
            FUN_180005eb8((longlong)(puVar21 + 2));
            FUN_1800966b8(puVar21);
            puVar21 = (undefined8 *)pvVar44;
          }
          FUN_1800966b8(*(LPVOID *)(pcVar43 + 8));
          FUN_1800966b8(pcVar43);
          (**(code **)(param_3 + 0x448))(3,0);
        }
        local_488 = (char *)FUN_180096840(0x18);
        if (local_488 == (char *)0x0) {
          local_488 = (char *)0x0;
        }
        else {
          local_488[8] = '\0';
          local_488[9] = '\0';
          local_488[10] = '\0';
          local_488[0xb] = '\0';
          local_488[0xc] = '\0';
          local_488[0xd] = '\0';
          local_488[0xe] = '\0';
          local_488[0xf] = '\0';
          local_488[0x10] = '\0';
          local_488[0x11] = '\0';
          local_488[0x12] = '\0';
          local_488[0x13] = '\0';
          local_488[0x14] = '\0';
          local_488[0x15] = '\0';
          local_488[0x16] = '\0';
          local_488[0x17] = '\0';
          lVar35 = FUN_180096150(0x120);
          *(longlong *)lVar35 = lVar35;
          *(longlong *)(lVar35 + 8) = lVar35;
          *(longlong *)(local_488 + 8) = lVar35;
          *local_488 = bVar11;
          FUN_18000af28(local_488,(longlong)param_3);
        }
        pcVar43 = local_488;
        if (local_488 == (char *)0x0) {
          pcVar43 = (char *)0x0;
        }
        (**(code **)(param_3 + 0x448))(3,pcVar43);
        if (puVar40 != (uint *)0x0) {
          FUN_180022590((int *)puVar40,(longlong)param_3,param_5);
          FUN_18000bcc0(puVar40);
          (**(code **)(param_3 + 0x448))(2,0);
        }
        uStack_268 = 0;
        local_270 = (char *)0x0;
        uStack_258 = 0;
        puStack_260 = (undefined1 *)0x0;
        local_250 = 0;
        uStack_228 = 0;
        local_230 = (char *)0x0;
        local_218 = 0;
        local_220 = (undefined1 *)0x0;
        local_210 = 0;
        puVar21 = (undefined8 *)FUN_180096150(0xa8);
        puVar21[1] = 0;
        *puVar21 = 0;
        puVar21[3] = 0;
        puVar21[2] = 0;
        puVar21[5] = 0;
        puVar21[4] = 0;
        puVar21[7] = 0;
        puVar21[6] = 0;
        puVar21[9] = 0;
        puVar21[8] = 0;
        puVar21[0xb] = 0;
        puVar21[10] = 0;
        puVar21[0xd] = 0;
        puVar21[0xc] = 0;
        puVar21[0xf] = 0;
        puVar21[0xe] = 0;
        puVar21[0x11] = 0;
        puVar21[0x10] = 0;
        puVar21[0x13] = 0;
        puVar21[0x12] = 0;
        puVar21[0x14] = 0;
        uVar13 = FUN_180026550((longlong)local_448);
        bVar11 = FUN_180026690((longlong)local_440);
        iVar14 = FUN_180026888((longlong)local_3c8);
        iVar15 = FUN_180026888((longlong)local_428);
        FUN_180026888((longlong)local_420);
        uVar16 = FUN_180026888((longlong)local_418);
        param_5 = (ulonglong)uVar16;
        FUN_180026888((longlong)local_410);
        FUN_180026888((longlong)local_530);
        FUN_180026708((longlong)local_438);
        FUN_180026708((longlong)local_430);
        uVar25 = FUN_180026708((longlong)local_408);
        param_10 = (longlong **)(uVar25 & 0xffffffff);
        uVar16 = FUN_180026550((longlong)local_4b0);
        param_9 = (ulonglong)uVar16;
        uVar25 = FUN_180026708((longlong)local_4b8);
        param_8 = uVar25 & 0xffffffff;
        uVar25 = FUN_180026708((longlong)local_4f0);
        param_7 = uVar25 & 0xffffffff;
        uVar25 = FUN_180026708((longlong)local_538);
        local_140 = (undefined1 *)CONCAT44(local_140._4_4_,local_210);
        uStack_158 = uStack_228;
        local_160 = local_230;
        uStack_148 = local_218;
        local_150 = local_220;
        local_210 = local_250;
        uStack_228 = uStack_268;
        local_230 = local_270;
        local_218 = uStack_258;
        local_220 = puStack_260;
        in_stack_fffffffffffffa58 = extraout_w1;
        puVar40 = (uint *)FUN_180022430(extraout_x11,(longlong)param_3,param_5,(int)uVar25,
                                        (int)param_7,(int)param_8,(int)param_9,(int)param_10,
                                        extraout_w12,extraout_w13,extraout_w14,extraout_w15,
                                        (int)param_5,extraout_w1,iVar15,iVar14,bVar11,&local_230,
                                        &local_160,1,uVar13,0,0,0,in_stack_fffffffffffffab0,0);
        if (puVar40 == (uint *)0x0) {
          return;
        }
        (**(code **)(param_3 + 0x448))(2,puVar40);
        uVar13 = 0;
        *local_3c0 = 0xffffffff;
        *local_3e8 = 0;
      }
      if (param_3[0x1b] == 0) {
        if ((local_488 != (char *)0x0) && (puVar40 != (uint *)0x0)) {
          if (iVar12 == 0) {
            iVar12 = 0;
            *puVar19 = 0;
            do {
              uVar17 = 0;
              if (0 < *param_3) {
                do {
                  lVar35 = *(longlong *)(param_3 + 0x140);
                  if (lVar35 == 0) {
                    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
                      (**(code **)(param_3 + 0x144))(param_3[0x146]);
                    }
                    lVar35 = *(longlong *)(param_3 + 0x140);
                    if (lVar35 != 0) goto LAB_18005d18c;
                    piVar18 = param_3 + 0x14a;
                  }
                  else {
LAB_18005d18c:
                    iVar14 = param_3[0x148];
                    if (iVar14 == 0) {
                      piVar18 = param_3 + 0x14a;
                    }
                    else {
                      iVar15 = iVar12;
                      if (iVar14 <= iVar12) {
                        iVar15 = iVar14 + -1;
                      }
                      piVar18 = (int *)(lVar35 + (longlong)iVar15 * 0x170);
                    }
                  }
                  lVar35 = *(longlong *)(piVar18 + 0xc);
                  if (lVar35 == 0) {
                    if (*(code **)(piVar18 + 0x10) != (code *)0x0) {
                      (**(code **)(piVar18 + 0x10))(piVar18[0x12]);
                    }
                    lVar35 = *(longlong *)(piVar18 + 0xc);
                    if (lVar35 != 0) goto LAB_18005d1d8;
                    piVar18 = piVar18 + 0x15;
                  }
                  else {
LAB_18005d1d8:
                    iVar14 = piVar18[0x14];
                    if (iVar14 == 0) {
                      piVar18 = piVar18 + 0x15;
                    }
                    else {
                      uVar13 = uVar17 & ((int)uVar17 >> 0x1f ^ 0xffffffffU);
                      if (iVar14 <= (int)uVar13) {
                        uVar13 = iVar14 - 1;
                      }
                      piVar18 = (int *)(lVar35 + (longlong)(int)uVar13 * 4);
                    }
                  }
                  *piVar18 = 0;
                  uVar17 = uVar17 + 1;
                } while ((int)uVar17 < *param_3);
              }
              iVar12 = iVar12 + 1;
              if (0x3b < iVar12) {
                FUN_18000af28(local_488,(longlong)param_3);
                FUN_180022590((int *)puVar40,(longlong)param_3,param_5);
                return;
              }
            } while( true );
          }
          if ((int)uVar13 < *param_3) {
            do {
              uStack_f8 = 0;
              local_100 = 0;
              uStack_e8 = 0;
              local_f0 = 0;
              uStack_d8 = 0;
              local_e0 = 0;
              local_c8 = 0;
              uStack_d0 = 0;
              lStack_b8 = 0;
              local_c0 = 0;
              uStack_a8 = 0;
              uStack_b0 = 0;
              uStack_98 = 0;
              local_a0 = 0;
              uStack_88 = 0;
              uStack_90 = 0;
              local_80 = 0;
              uVar16 = FUN_180026550((longlong)local_4d0);
              pvVar44 = (LPVOID)(ulonglong)uVar13;
              FUN_180011fd0(&local_100,(longlong)param_3,uVar13,uVar16);
              uVar16 = FUN_180026550((longlong)plVar36);
              lVar35 = local_c8;
              if (((uVar16 != 0) &&
                  (uVar16 = FUN_180026550(extraout_x11_00), lVar35 = local_c0, uVar16 != 1)) &&
                 (uVar16 = FUN_180026550(extraout_x11_01), lVar35 = lStack_b8, uVar16 != 2)) {
                lVar35 = 0;
              }
              lVar1 = local_f0;
              lVar35 = lVar35 % 86400000000;
              iVar14 = ((int)(lVar35 / 1000000) + (int)(lVar35 >> 0x3f)) -
                       (SUB164(SEXT816(lVar35) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
              if (86399999999 < lVar35) {
                iVar14 = 0;
              }
              lVar35 = local_f0 % 86400000000;
              iVar15 = ((int)(lVar35 / 1000000) + (int)(lVar35 >> 0x3f)) -
                       (SUB164(SEXT816(lVar35) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
              if (86399999999 < lVar35) {
                iVar15 = 0;
              }
              if (iVar15 == iVar14) {
                puVar26 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x2ac),uVar13);
                uVar49 = *puVar26;
                puVar26 = (undefined4 *)FUN_18000f448((longlong)local_540,uVar13);
                *puVar26 = uVar49;
                *puVar20 = uVar13;
              }
              local_160 = (char *)0x0;
              uStack_158 = 0;
              local_150 = 0;
              uStack_148 = 1;
              local_130 = &DAT_1800d4ecd;
              local_140 = &DAT_1800d4ecd;
              local_138 = 0;
              (**(code **)(param_3 + 0x55a))(&local_160);
              if (((int)local_160 == 2) && (local_160._4_4_ == 0)) {
                uVar16 = FUN_180026550((longlong)plVar36);
                if ((uVar16 != 0) && (uVar16 = FUN_180026550(extraout_x13), uVar16 != 1)) {
                  FUN_180026550(extraout_x13_00);
                }
                uVar16 = FUN_180026550((longlong)local_478);
                if (uVar16 == 1) {
                  if (extraout_x13_01 < extraout_x11_02) {
                    bVar11 = true;
                    bVar10 = false;
                    if (extraout_x14 < lVar1) {
                      bVar10 = SBORROW8(extraout_x14,extraout_x13_01);
                      bVar11 = extraout_x14 - extraout_x13_01 < 0;
                    }
                  }
                  else {
                    bVar10 = lVar1 <= extraout_x14 && SBORROW8(extraout_x14,extraout_x13_01);
                    bVar11 = lVar1 <= extraout_x14 && extraout_x14 - extraout_x13_01 < 0;
                  }
                  if (bVar11 == bVar10) goto LAB_18005d460;
LAB_18005d490:
                  puVar26 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x2ac),uVar13);
                  uVar49 = *puVar26;
                  puVar26 = (undefined4 *)FUN_18000f448((longlong)local_518,uVar13);
                  iVar14 = 0;
                  *puVar26 = uVar49;
                  iVar15 = 0;
                  uVar16 = *puVar20;
                  piVar18 = local_3e8;
                  puVar28 = local_3c0;
                  while (uVar16 = uVar16 + 1, local_3e8 = piVar18, local_3c0 = puVar28,
                        (int)uVar16 <= (int)uVar13) {
                    uVar34 = *puVar20;
                    lVar35 = *(longlong *)(param_3 + 0x2a2);
                    if (lVar35 == 0) {
                      if (*(code **)(param_3 + 0x2a6) != (code *)0x0) {
                        (**(code **)(param_3 + 0x2a6))(param_3[0x2a8]);
                      }
                      lVar35 = *(longlong *)(param_3 + 0x2a2);
                      if (lVar35 != 0) goto LAB_18005d4f8;
                      pfVar29 = (float *)(param_3 + 0x2ab);
LAB_18005d520:
                      if (*(code **)(param_3 + 0x2a6) != (code *)0x0) {
                        (**(code **)(param_3 + 0x2a6))(param_3[0x2a8]);
                      }
                      lVar35 = *(longlong *)(param_3 + 0x2a2);
                      if (lVar35 != 0) goto LAB_18005d540;
                      pfVar32 = (float *)(param_3 + 0x2ab);
                    }
                    else {
LAB_18005d4f8:
                      iVar30 = param_3[0x2aa];
                      if (iVar30 == 0) {
                        pfVar29 = (float *)(param_3 + 0x2ab);
                      }
                      else {
                        uVar34 = uVar34 & ((int)uVar34 >> 0x1f ^ 0xffffffffU);
                        if (iVar30 <= (int)uVar34) {
                          uVar34 = iVar30 - 1;
                        }
                        pfVar29 = (float *)(lVar35 + (longlong)(int)uVar34 * 4);
                      }
                      if (lVar35 == 0) goto LAB_18005d520;
LAB_18005d540:
                      iVar30 = param_3[0x2aa];
                      if (iVar30 == 0) {
                        pfVar32 = (float *)(param_3 + 0x2ab);
                      }
                      else {
                        uVar34 = uVar16;
                        if ((int)uVar16 < 0) {
                          uVar34 = 0;
                        }
                        if (iVar30 <= (int)uVar34) {
                          uVar34 = iVar30 - 1;
                        }
                        pfVar32 = (float *)(lVar35 + (longlong)(int)uVar34 * 4);
                      }
                    }
                    uVar34 = *puVar20;
                    lVar35 = *(longlong *)(param_3 + 0x298);
                    if (*pfVar32 < *pfVar29) {
                      iVar14 = 1;
                    }
                    if (lVar35 == 0) {
                      if (*(code **)(param_3 + 0x29c) != (code *)0x0) {
                        (**(code **)(param_3 + 0x29c))(param_3[0x29e]);
                      }
                      lVar35 = *(longlong *)(param_3 + 0x298);
                      if (lVar35 != 0) goto LAB_18005d5a4;
                      pfVar29 = (float *)(param_3 + 0x2a1);
LAB_18005d5cc:
                      if (*(code **)(param_3 + 0x29c) != (code *)0x0) {
                        (**(code **)(param_3 + 0x29c))(param_3[0x29e]);
                      }
                      lVar35 = *(longlong *)(param_3 + 0x298);
                      if (lVar35 != 0) goto LAB_18005d5ec;
                      pfVar32 = (float *)(param_3 + 0x2a1);
                    }
                    else {
LAB_18005d5a4:
                      iVar30 = param_3[0x2a0];
                      if (iVar30 == 0) {
                        pfVar29 = (float *)(param_3 + 0x2a1);
                      }
                      else {
                        uVar34 = uVar34 & ((int)uVar34 >> 0x1f ^ 0xffffffffU);
                        if (iVar30 <= (int)uVar34) {
                          uVar34 = iVar30 - 1;
                        }
                        pfVar29 = (float *)(lVar35 + (longlong)(int)uVar34 * 4);
                      }
                      if (lVar35 == 0) goto LAB_18005d5cc;
LAB_18005d5ec:
                      iVar30 = param_3[0x2a0];
                      if (iVar30 == 0) {
                        pfVar32 = (float *)(param_3 + 0x2a1);
                      }
                      else {
                        uVar34 = uVar16;
                        if ((int)uVar16 < 0) {
                          uVar34 = 0;
                        }
                        if (iVar30 <= (int)uVar34) {
                          uVar34 = iVar30 - 1;
                        }
                        pfVar32 = (float *)(lVar35 + (longlong)(int)uVar34 * 4);
                      }
                    }
                    piVar18 = local_3e8;
                    puVar28 = local_3c0;
                    if (*pfVar29 < *pfVar32) {
                      iVar15 = 1;
                    }
                  }
                  if (iVar15 == iVar14) {
                    if ((*puVar28 == uVar13) && (*piVar18 != 0)) {
                      FUN_18000b1e8(local_488,(longlong)param_3,pvVar44,*local_388);
                      FUN_180024f70((int *)puVar40,(longlong)param_3,pvVar44,*piVar18);
                      *piVar18 = 0;
                      *puVar28 = 0;
                    }
                  }
                  else if (*puVar28 != uVar13) {
                    local_1e0 = (longlong *)0x0;
                    local_1d0 = (int *)0x0;
                    local_1c8 = (longlong *)0x0;
                    (**(code **)(param_3 + 0x640))
                              (0x428c0000,*puVar20,&local_1e0,&local_1d0,&local_1c8);
                    local_1f0 = (longlong *)0x0;
                    lStack_1e8 = 0;
                    local_1f0 = (longlong *)FUN_180096150(0x120);
                    *local_1f0 = (longlong)local_1f0;
                    local_1f0[1] = (longlong)local_1f0;
                    uVar17 = *puVar20;
                    if (*(short *)((longlong)local_4e0 + 0x24) != 5) {
                      local_330 = &DAT_1800d4ecd;
                      local_340 = &DAT_1800d4ecd;
                      uStack_338 = 0;
                      uStack_378 = 0;
                      local_380 = 0;
                      uStack_368 = 0;
                      uStack_370 = 0;
                      uStack_358 = 0;
                      local_360 = 0;
                      puStack_320 = &DAT_1800d4ecd;
                      local_318 = 0;
                      puStack_310 = &DAT_1800d4ecd;
                      local_2e0 = &DAT_1800d4ecd;
                      local_2f0 = &DAT_1800d4ecd;
                      uStack_2e8 = 0;
                      uStack_2f4 = 0;
                      puStack_2d0 = &DAT_1800d4ecd;
                      local_2c8 = 0;
                      puStack_2c0 = &DAT_1800d4ecd;
                      local_2b8 = 0;
                      local_2b0 = "";
                      local_2a8 = 0;
                      puStack_2a0 = &DAT_1800d4ecd;
                      uStack_290 = 0;
                      local_298 = 0;
                      uStack_280 = 0;
                      local_288 = 0;
                      local_278 = 0;
                      local_350 = (double)(float)(double)local_1e0;
                      dStack_348 = (double)(float)(double)local_1e0;
                      local_328 = (ulonglong)*(uint *)(local_4e0 + 3);
                      local_308 = (ulonglong)CONCAT24((short)local_4e0[5],*(uint *)(local_4e0 + 3));
                      local_300 = (int)*(short *)((longlong)local_4e0 + 0x26);
                      local_2fc = 6;
                      local_2f8 = 1;
                      local_2d8 = 1;
                      FUN_1800079f8((longlong *)&puStack_2d0,0x1800d81ac,2);
                      local_2c8 = CONCAT44(1,(undefined4)local_2c8);
                      local_2b8 = CONCAT71(local_2b8._1_7_,1);
                      FUN_180007920((longlong *)&local_2b0,local_4e0 + 0x21);
                      local_288 = 0x28;
                      uStack_290 = CONCAT44(uStack_290._4_4_,uVar17);
                      local_298 = CONCAT44((float)local_350,(float)local_350);
                      if ((((*(uint *)(local_4e0 + 0x1c) & 1) == 0) &&
                          (local_2b8 = local_2b8 & 0xffffffffffffff00, local_2b0 != (char *)0x0)) &&
                         (*local_2b0 != '\0')) {
                        FUN_1800079f8((longlong *)&local_2b0,0x1800d4ecd,0);
                        local_2a8 = CONCAT44(1,(undefined4)local_2a8);
                      }
                      plVar37 = local_1f0;
                      uVar47 = 0;
                      if ((*(uint *)(local_4e0 + 0x1c) & 0x100) != 0) {
                        uVar47 = local_2f8;
                      }
                      uVar49 = 4;
                      if ((*(uint *)(local_4e0 + 0x1c) & 0x2020) != 0) {
                        uVar49 = local_2fc;
                      }
                      local_2fc = uVar49;
                      local_2f8 = uVar47;
                      if (lStack_1e8 == 0xe38e38e38e38e3) {
                    /* WARNING: Subroutine does not return */
                        FUN_1800941a8(0x1800d5268);
                      }
                      local_3e0 = &local_1f0;
                      local_3d8 = (undefined8 *)0x0;
                      puVar21 = (undefined8 *)FUN_180096150(0x120);
                      local_3d8 = puVar21;
                      FUN_18000b980(puVar21 + 2,&local_380);
                      local_3d8 = (undefined8 *)0x0;
                      lStack_1e8 = lStack_1e8 + 1;
                      puVar33 = (undefined8 *)plVar37[1];
                      *puVar21 = plVar37;
                      puVar21[1] = puVar33;
                      plVar37[1] = (longlong)puVar21;
                      *puVar33 = puVar21;
                      FUN_180005eb8((longlong)&local_380);
                    }
                    bVar11 = FUN_180026690((longlong)plVar31);
                    if (bVar11) {
                      uVar16 = 7;
                    }
                    else {
                      uVar16 = 9;
                    }
                    if (iVar14 == 0) {
                      uVar16 = uVar16 + 1;
                    }
                    local_3d0 = local_398;
                    local_3f0 = FUN_180025308(local_398,&local_1f0);
                    local_3f8 = (longlong *)0x0;
                    if (iVar14 == 0) {
                      uVar17 = FUN_180026888((longlong)local_500);
                      pplVar27 = local_460;
                    }
                    else {
                      uVar17 = FUN_180026888((longlong)local_508);
                      pplVar27 = local_468;
                    }
                    param_8 = (ulonglong)uVar17;
                    uVar17 = FUN_180026888((longlong)pplVar27);
                    param_7 = (ulonglong)uVar17;
                    uVar34 = *puVar20;
                    puVar28 = (uint *)FUN_180005d08((longlong *)(param_3 + 0x298),uVar34);
                    uVar17 = *puVar28;
                    pfVar29 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),*puVar20);
                    uVar47 = 0;
                    uVar48 = 0;
                    param_10 = &local_3f8;
                    uVar51 = in_stack_fffffffffffffa58 & 0xffffff00;
                    param_9 = (ulonglong)uVar16;
                    iVar14 = FUN_180023270(*pfVar29,ZEXT416(uVar17),puVar40,(longlong)param_3,
                                           (ulonglong)uVar13,(ulonglong)uVar34,param_7,param_8,
                                           param_9,param_10,0,'\0',0,0,0,
                                           CONCAT44(in_stack_fffffffffffffa5c,
                                                    in_stack_fffffffffffffa58) & 0xffffffffffffff00,
                                           local_3f0);
                    *local_3e8 = iVar14;
                    *local_3c0 = uVar13;
                    *(undefined8 *)local_1f0[1] = 0;
                    puVar21 = (undefined8 *)*local_1f0;
                    in_stack_fffffffffffffa58 = uVar51;
                    while (puVar21 != (undefined8 *)0x0) {
                      pvVar44 = (LPVOID)*puVar21;
                      FUN_180005eb8((longlong)(puVar21 + 2));
                      FUN_1800966b8(puVar21);
                      puVar21 = (undefined8 *)pvVar44;
                    }
                    FUN_1800966b8(local_1f0);
                  }
                }
                else {
LAB_18005d460:
                  uVar16 = FUN_180026550((longlong)local_478);
                  if (uVar16 == 0) {
                    if (extraout_x13_02 < lVar1) {
                      bVar11 = true;
                      bVar10 = false;
                      if (extraout_x12 < lVar1) {
                        bVar10 = SBORROW8(extraout_x12,extraout_x13_02);
                        bVar11 = extraout_x12 - extraout_x13_02 < 0;
                      }
                    }
                    else {
                      bVar10 = lVar1 <= extraout_x12 && SBORROW8(extraout_x12,extraout_x13_02);
                      bVar11 = lVar1 <= extraout_x12 && extraout_x12 - extraout_x13_02 < 0;
                    }
                    if (bVar11 != bVar10) goto LAB_18005d490;
                  }
                }
              }
              local_220 = &DAT_1800d4ecd;
              local_230 = "";
              uStack_228 = 0;
              lVar35 = *(longlong *)(param_3 + 0x58);
              uVar16 = (int)uVar13 >> 0x1f;
              if (lVar35 == 0) {
                if (*(code **)(param_3 + 0x5c) != (code *)0x0) {
                  (**(code **)(param_3 + 0x5c))(param_3[0x5e]);
                }
                lVar35 = *(longlong *)(param_3 + 0x58);
                if (lVar35 != 0) goto LAB_18005da38;
                local_1d8 = (longlong *)(param_3 + 0x62);
              }
              else {
LAB_18005da38:
                iVar14 = param_3[0x60];
                if (iVar14 == 0) {
                  local_1d8 = (longlong *)(param_3 + 0x62);
                }
                else {
                  uVar34 = uVar13 & (uVar16 ^ 0xffffffff);
                  if (iVar14 <= (int)uVar34) {
                    uVar34 = iVar14 - 1;
                  }
                  local_1d8 = (longlong *)(lVar35 + (longlong)(int)uVar34 * 8);
                }
              }
              local_1d8 = (longlong *)*local_1d8;
              (**(code **)(param_3 + 0x4d4))
                        (local_200,&local_1d8,"PST-08PDT+01,M3.2.0/02:00,M11.1.0/02:00");
              plVar37 = (longlong *)**(longlong **)(local_488 + 8);
              ppcVar38 = local_200[0];
              uVar49 = extraout_s0_31;
              uVar45 = extraout_var_31;
              uVar24 = extraout_var_74;
              if (plVar37 != *(longlong **)(local_488 + 8)) {
                do {
                  if (*(char *)((longlong)plVar37 + 0x114) == '\0') {
LAB_18005db04:
                    pbVar41 = (byte *)plVar37[0x14];
                    dVar50 = (double)plVar37[8];
                    if ((double)plVar37[9] != 0.0) {
                      dVar50 = (double)plVar37[9];
                    }
                    pbVar2 = pbVar41;
                    if (pbVar41 == (byte *)0x0) {
                      pbVar2 = &DAT_1800d4ecd;
                    }
                    iVar14 = FUN_1800a7250(&DAT_1800d538c,pbVar2);
                    uVar49 = extraout_s0_32;
                    uVar45 = extraout_var_32;
                    uVar24 = extraout_var_75;
                    if (((iVar14 != 0) && (*(uint *)(plVar37 + 0x20) != uVar13)) &&
                       ((int)plVar37[0x21] != 1)) {
                      if (pbVar41 == (byte *)0x0) {
                        pbVar41 = &DAT_1800d4ecd;
                      }
                      iVar14 = FUN_1800a7250((byte *)"Cross_Above",pbVar41);
                      if (iVar14 == 0) {
                        lVar35 = *(longlong *)(param_3 + 0x298);
                        uVar49 = extraout_s0_33;
                        uVar45 = extraout_var_33;
                        uVar24 = extraout_var_76;
                        if (lVar35 == 0) {
                          if (*(code **)(param_3 + 0x29c) != (code *)0x0) {
                            (**(code **)(param_3 + 0x29c))(param_3[0x29e]);
                            ppcVar38 = local_200[0];
                            uVar49 = extraout_s0_34;
                            uVar45 = extraout_var_34;
                            uVar24 = extraout_var_77;
                          }
                          lVar35 = *(longlong *)(param_3 + 0x298);
                          if (lVar35 != 0) goto LAB_18005db88;
                          pfVar29 = (float *)(param_3 + 0x2a1);
                        }
                        else {
LAB_18005db88:
                          iVar14 = param_3[0x2a0];
                          if (iVar14 == 0) {
                            pfVar29 = (float *)(param_3 + 0x2a1);
                          }
                          else {
                            uVar34 = uVar13 & (uVar16 ^ 0xffffffff);
                            if (iVar14 <= (int)uVar34) {
                              uVar34 = iVar14 - 1;
                            }
                            pfVar29 = (float *)(lVar35 + (longlong)(int)uVar34 * 4);
                          }
                        }
                        if ((double)*pfVar29 < dVar50) goto LAB_18005dbbc;
                      }
                      else {
LAB_18005dbbc:
                        pbVar41 = (byte *)plVar37[0x14];
                        if (pbVar41 == (byte *)0x0) {
                          pbVar41 = &DAT_1800d4ecd;
                        }
                        iVar14 = FUN_1800a7250((byte *)"Cross_Below",pbVar41);
                        if (iVar14 == 0) {
                          lVar35 = *(longlong *)(param_3 + 0x2a2);
                          uVar49 = extraout_s0_35;
                          uVar45 = extraout_var_35;
                          uVar24 = extraout_var_78;
                          if (lVar35 == 0) {
                            if (*(code **)(param_3 + 0x2a6) != (code *)0x0) {
                              (**(code **)(param_3 + 0x2a6))(param_3[0x2a8]);
                              ppcVar38 = local_200[0];
                              uVar49 = extraout_s0_36;
                              uVar45 = extraout_var_36;
                              uVar24 = extraout_var_79;
                            }
                            lVar35 = *(longlong *)(param_3 + 0x2a2);
                            if (lVar35 != 0) goto LAB_18005dc04;
                            pfVar29 = (float *)(param_3 + 0x2ab);
                          }
                          else {
LAB_18005dc04:
                            iVar14 = param_3[0x2aa];
                            if (iVar14 == 0) {
                              pfVar29 = (float *)(param_3 + 0x2ab);
                            }
                            else {
                              uVar34 = uVar13 & (uVar16 ^ 0xffffffff);
                              if (iVar14 <= (int)uVar34) {
                                uVar34 = iVar14 - 1;
                              }
                              pfVar29 = (float *)(lVar35 + (longlong)(int)uVar34 * 4);
                            }
                          }
                          if ((double)*pfVar29 <= dVar50) goto LAB_18005dd28;
                        }
                        pbVar41 = (byte *)plVar37[0x14];
                        if (pbVar41 == (byte *)0x0) {
                          pbVar41 = &DAT_1800d4ecd;
                        }
                        iVar14 = FUN_1800a7250((byte *)"Cross_Any",pbVar41);
                        if (iVar14 == 0) {
                          lVar35 = *(longlong *)(param_3 + 0x2a2);
                          if (lVar35 == 0) {
                            if (*(code **)(param_3 + 0x2a6) != (code *)0x0) {
                              (**(code **)(param_3 + 0x2a6))(param_3[0x2a8]);
                              ppcVar38 = local_200[0];
                            }
                            lVar35 = *(longlong *)(param_3 + 0x2a2);
                            if (lVar35 != 0) goto LAB_18005dc80;
                            pfVar29 = (float *)(param_3 + 0x2ab);
                          }
                          else {
LAB_18005dc80:
                            iVar14 = param_3[0x2aa];
                            if (iVar14 == 0) {
                              pfVar29 = (float *)(param_3 + 0x2ab);
                            }
                            else {
                              uVar34 = uVar13 & (uVar16 ^ 0xffffffff);
                              if (iVar14 <= (int)uVar34) {
                                uVar34 = iVar14 - 1;
                              }
                              pfVar29 = (float *)(lVar35 + (longlong)(int)uVar34 * 4);
                            }
                          }
                          if (((double)*pfVar29 <= dVar50) &&
                             (pfVar29 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),uVar13)
                             , ppcVar38 = local_200[0], uVar49 = extraout_s0_37,
                             uVar45 = extraout_var_37, uVar24 = extraout_var_80,
                             dVar50 < (double)*pfVar29)) goto LAB_18005dd28;
                        }
                        pbVar41 = (byte *)plVar37[0x14];
                        if (pbVar41 == (byte *)0x0) {
                          pbVar41 = &DAT_1800d4ecd;
                        }
                        iVar14 = FUN_1800a7250((byte *)"Cross_Any",pbVar41);
                        uVar49 = extraout_s0_38;
                        uVar45 = extraout_var_38;
                        uVar24 = extraout_var_81;
                        if (((iVar14 != 0) ||
                            (pfVar29 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),uVar13),
                            ppcVar38 = local_200[0], uVar49 = extraout_s0_39,
                            uVar45 = extraout_var_39, uVar24 = extraout_var_82,
                            (double)*pfVar29 < dVar50)) ||
                           (pfVar29 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),uVar13),
                           ppcVar38 = local_200[0], uVar49 = extraout_s0_40,
                           uVar45 = extraout_var_40, uVar24 = extraout_var_83,
                           dVar50 <= (double)*pfVar29)) goto LAB_18005dd40;
                      }
LAB_18005dd28:
                      auVar5._4_4_ = uVar45;
                      auVar5._0_4_ = uVar49;
                      auVar5._8_8_ = uVar24;
                      FUN_18000b2a0(auVar5,CONCAT44(uVar47,uVar17),local_488,(longlong)param_3,
                                    (ulonglong)uVar13,plVar37 + 2,param_7,param_8,param_9,param_10);
                      ppcVar38 = local_200[0];
                      uVar49 = extraout_s0_41;
                      uVar45 = extraout_var_41;
                      uVar24 = extraout_var_84;
                    }
                  }
                  else {
                    lVar35 = ((longlong)(int)plVar37[0x23] * 1000000 -
                             (longlong)ppcVar38 % 86400000000) + (longlong)ppcVar38;
                    lVar1 = ((longlong)*(int *)((longlong)plVar37 + 0x11c) * 1000000 -
                            (longlong)ppcVar38 % 86400000000) + (longlong)ppcVar38;
                    if (*(int *)((longlong)plVar37 + 0x11c) < (int)plVar37[0x23]) {
                      lVar1 = lVar1 + 86400000000;
                    }
                    if (lVar1 < lVar35) {
                      bVar11 = true;
                      bVar10 = false;
                      if ((longlong)ppcVar38 < lVar35) {
                        bVar10 = SBORROW8((longlong)ppcVar38,lVar1);
                        bVar11 = (longlong)ppcVar38 - lVar1 < 0;
                      }
                    }
                    else {
                      bVar10 = lVar35 <= (longlong)ppcVar38 && SBORROW8((longlong)ppcVar38,lVar1);
                      bVar11 = lVar35 <= (longlong)ppcVar38 && (longlong)ppcVar38 - lVar1 < 0;
                    }
                    if (bVar11 != bVar10) goto LAB_18005db04;
                  }
LAB_18005dd40:
                  plVar37 = (longlong *)*plVar37;
                } while (plVar37 != (longlong *)*(longlong *)(local_488 + 8));
              }
              local_190 = (longlong *)0x0;
              local_188 = 0;
              auVar6._4_4_ = uVar45;
              auVar6._0_4_ = uVar49;
              auVar6._8_8_ = uVar24;
              auVar9._4_4_ = uVar47;
              auVar9._0_4_ = uVar17;
              auVar9._8_8_ = uVar48;
              FUN_180023e50(auVar6,auVar9,(int *)puVar40,(longlong)param_3,(ulonglong)uVar13,
                            (longlong)&local_190,param_7,param_8,param_9,param_10);
              lpMem = local_140;
              if (((int)local_138 != 0) && (local_140 != (undefined1 *)0x0)) {
                pvVar23 = GetProcessHeap();
                HeapFree(pvVar23,0,lpMem);
                local_140 = (undefined1 *)0x0;
                local_138 = 0;
              }
              uVar13 = uVar13 + 1;
            } while ((int)uVar13 < *param_3);
          }
        }
      }
      else {
        if (local_488 != (char *)0x0) {
          FUN_18000af28(local_488,(longlong)param_3);
          FUN_1800477f8(local_488);
          (**(code **)(param_3 + 0x448))(3,0);
        }
        if (puVar40 != (uint *)0x0) {
          FUN_180022590((int *)puVar40,(longlong)param_3,param_5);
          FUN_18000bcc0(puVar40);
          (**(code **)(param_3 + 0x448))(2,0);
        }
      }
      *puVar19 = (uint)(iVar12 != 0);
    }
  }
  else {
    FUN_1800079f8((longlong *)(param_3 + 0x46),0x1800d8cd0,9);
    param_3[0x49] = 1;
    if ((*(char **)(param_3 + 0xce) != (char *)0x0) && (**(char **)(param_3 + 0xce) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0xce),0x1800d4ecd,0);
      param_3[0xd1] = 1;
    }
    pcVar43 = "";
    local_220 = &DAT_1800d4ecd;
    local_230 = "";
    uStack_228 = 0;
    pcVar22 = FUN_180004620(0x15);
    if (pcVar22 == (char *)0x0) {
      local_230 = "";
    }
    else {
      param_6 = 0x14;
      local_230 = pcVar22;
      FUN_180099d78(pcVar22,0x15,0x1800d6c40,0x14);
      uStack_228 = 0x100000001;
      pcVar43 = pcVar22;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_230);
    if ((pcVar22 != (char *)0x0) && (pcVar43 != (char *)0x0)) {
      pvVar23 = GetProcessHeap();
      HeapFree(pvVar23,0,pcVar43);
    }
    pcVar43 = "";
    local_220 = &DAT_1800d4ecd;
    local_230 = "";
    uStack_228 = 0;
    pcVar22 = FUN_180004620(0x8d);
    if (pcVar22 == (char *)0x0) {
      local_230 = "";
    }
    else {
      param_6 = 0x8c;
      local_230 = pcVar22;
      FUN_180099d78(pcVar22,0x8d,0x1800d6bb0,0x8c);
      uStack_228 = 0x100000001;
      pcVar43 = pcVar22;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_230);
    uVar49 = extraout_s0_28;
    uVar45 = extraout_var_28;
    uVar48 = extraout_var_71;
    if ((pcVar22 != (char *)0x0) && (pcVar43 != (char *)0x0)) {
      pvVar23 = GetProcessHeap();
      HeapFree(pvVar23,0,pcVar43);
      local_230 = (char *)0x0;
      uStack_228 = 0;
      uVar49 = extraout_s0_29;
      uVar45 = extraout_var_29;
      uVar48 = extraout_var_72;
    }
    pcVar43 = "";
    pcVar22 = *(char **)(param_3 + 0x46);
    if (*(char **)(param_3 + 0x46) == (char *)0x0) {
      pcVar22 = pcVar43;
    }
    auVar7._4_4_ = uVar45;
    auVar7._0_4_ = uVar49;
    auVar7._8_8_ = uVar48;
    FUN_180026368(auVar7,CONCAT44(uVar47,uVar17),(undefined8 *)(param_3 + 0xce),0x1800d6c60,pcVar22,
                  param_6,param_7,param_8,param_9,param_10);
    local_220 = &DAT_1800d4ecd;
    local_230 = "";
    uStack_228 = 0;
    pcVar22 = FUN_180004620(7);
    if (pcVar22 == (char *)0x0) {
      local_230 = "";
    }
    else {
      param_6 = 6;
      local_230 = pcVar22;
      FUN_180099d78(pcVar22,7,0x1800d6c58,6);
      uStack_228 = 0x100000001;
      pcVar43 = pcVar22;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_230);
    if ((pcVar22 != (char *)0x0) && (pcVar43 != (char *)0x0)) {
      pvVar23 = GetProcessHeap();
      HeapFree(pvVar23,0,pcVar43);
      local_230 = (char *)0x0;
      uStack_228 = 0;
    }
    param_3[0xdf] = 0;
    param_3[4] = 0;
    param_3[0x276] = 1;
    FUN_1800079f8(local_540,0x1800d8cb8,9);
    *(int *)((longlong)local_3b8 + 0xc) = 1;
    *(undefined2 *)((longlong)local_3b8 + 0x24) = 5;
    *(undefined2 *)(local_3b8 + 5) = 6;
    *(int *)(local_3b8 + 3) = 0xffff;
    FUN_1800079f8(local_518,0x1800d8cc8,7);
    *(int *)((longlong)local_3b0 + 0xc) = 1;
    *(undefined2 *)((longlong)local_3b0 + 0x24) = 5;
    *(int *)(local_3b0 + 3) = 0xff00ff;
    *(undefined2 *)(local_3b0 + 5) = 6;
    local_3a8[9] = 0x10005;
    *(undefined2 *)(local_3a8 + 10) = 1;
    local_3a8[0x38] = 0x82121;
    local_3a8[6] = 0xff00ff;
    FUN_1800079f8((longlong *)(local_4d8 + 0x42),0x1800d8ce8,3);
    local_4d8[0x45] = 1;
    FUN_1800079f8(local_4e0,0x1800d8cf0,7);
    *(undefined4 *)((longlong)local_4e0 + 0xc) = 1;
    *(undefined4 *)((longlong)local_4e0 + 0x24) = 3;
    *(undefined4 *)(local_4e0 + 3) = 0xeccaa8;
    *(undefined2 *)(local_4e0 + 5) = 1;
    *(undefined4 *)(local_4e0 + 0x1c) = 0x82121;
    FUN_1800079f8(local_4e0 + 0x21,0x1800d8cf0,7);
    *(undefined4 *)((longlong)local_4e0 + 0x114) = 1;
    piVar39[9] = 0x10005;
    *(undefined2 *)(piVar39 + 10) = 1;
    piVar39[0x38] = 0x82121;
    piVar39[6] = 0xff00ff;
    FUN_1800079f8((longlong *)(piVar39 + 0x42),0x1800d8cdc,3);
    piVar39[0x45] = 1;
    FUN_1800079f8(plVar36,0x1800d8ce0,7);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    if ((code *)plVar36[10] != (code *)0x0) {
      (*(code *)plVar36[10])(*(undefined4 *)((longlong)plVar36 + 0x4c),"RTH;Asia;Euro");
    }
    *(undefined1 *)(plVar36 + 3) = 0x16;
    *(undefined2 *)((longlong)plVar36 + 0x1a) = 1;
    *(undefined4 *)((longlong)plVar36 + 0x1c) = 0;
    FUN_1800079f8(local_4d0,0x1800d8d38,0x15);
    *(undefined4 *)((longlong)local_4d0 + 0xc) = 1;
    puVar21 = FUN_180029680((longlong *)&local_1a8,&local_230);
    if (0xf < (ulonglong)puVar21[3]) {
      puVar21 = (undefined8 *)*puVar21;
    }
    if ((code *)local_4d0[10] != (code *)0x0) {
      (*(code *)local_4d0[10])(*(undefined4 *)((longlong)local_4d0 + 0x4c),puVar21);
      *(undefined1 *)(local_4d0 + 3) = 0x16;
    }
    if (0xf < local_218) {
      FUN_1800966b8(local_230);
    }
    local_230 = (char *)((ulonglong)local_230 & 0xffffffffffffff00);
    local_220 = (undefined1 *)0x0;
    local_218 = 0xf;
    *(undefined2 *)((longlong)local_4d0 + 0x1a) = 2;
    *(undefined1 *)(local_4d0 + 3) = 0x16;
    *(undefined4 *)((longlong)local_4d0 + 0x1c) = 0;
    FUN_1800079f8(local_478,0x1800d8cf8,0xc);
    *(undefined4 *)((longlong)local_478 + 0xc) = 1;
    if ((code *)local_478[10] != (code *)0x0) {
      (*(code *)local_478[10])
                (*(undefined4 *)((longlong)local_478 + 0x4c),"Session Close;Chart End Time");
    }
    *(undefined2 *)((longlong)local_478 + 0x1a) = 3;
    *(undefined1 *)(local_478 + 3) = 0x16;
    *(undefined4 *)((longlong)local_478 + 0x1c) = 0;
    FUN_1800079f8((longlong *)local_460,0x1800d8d88,0x12);
    *(undefined4 *)((longlong)local_3a0 + 0xc) = 1;
    *(undefined1 *)(local_3a0 + 3) = 0xe;
    *(undefined2 *)((longlong)local_3a0 + 0x1a) = 4;
    *(undefined4 *)((longlong)local_3a0 + 0x1c) = 0x302825;
    FUN_1800079f8(local_500,0x1800d8da0,0x19);
    *(int *)((longlong)local_398[0] + 0xc) = 1;
    *(undefined1 *)(local_398[0] + 3) = 0xe;
    *(int *)((longlong)local_398[0] + 0x1c) = 0x694536;
    *(undefined2 *)((longlong)local_398[0] + 0x1a) = 5;
    FUN_1800079f8((longlong *)local_468,0x1800d8d50,0x13);
    *(undefined4 *)((longlong)local_3e0 + 0xc) = 1;
    *(undefined1 *)(local_3e0 + 3) = 0xe;
    *(undefined4 *)((longlong)local_3e0 + 0x1c) = 0x302825;
    *(undefined2 *)((longlong)local_3e0 + 0x1a) = 6;
    FUN_1800079f8(local_508,0x1800d8d68,0x1a);
    *(int *)((longlong)local_1f0 + 0xc) = 1;
    *(undefined1 *)(local_1f0 + 3) = 0xe;
    *(int *)((longlong)local_1f0 + 0x1c) = 0x313162;
    *(undefined2 *)((longlong)local_1f0 + 0x1a) = 7;
    FUN_1800079f8(local_530,0x1800d8e08,0x1a);
    *(int *)((longlong)local_190 + 0xc) = 1;
    *(undefined1 *)(local_190 + 3) = 0xe;
    *(int *)((longlong)local_190 + 0x1c) = 0xa66626;
    *(undefined2 *)((longlong)local_190 + 0x1a) = 8;
    FUN_1800079f8((longlong *)local_410,0x1800d8e28,0x21);
    *(undefined4 *)((longlong)local_3d0 + 0xc) = 1;
    *(undefined1 *)(local_3d0 + 3) = 0xe;
    *(undefined4 *)((longlong)local_3d0 + 0x1c) = 0xc6792d;
    *(undefined2 *)((longlong)local_3d0 + 0x1a) = 9;
    FUN_1800079f8(local_418,0x1800d8dc0,0x1b);
    *(int *)((longlong)local_1d8 + 0xc) = 1;
    *(undefined1 *)(local_1d8 + 3) = 0xe;
    *(undefined2 *)((longlong)local_1d8 + 0x1a) = 10;
    *(int *)((longlong)local_1d8 + 0x1c) = 0x494ab0;
    FUN_1800079f8(local_420,0x1800d8de0,0x22);
    *(int *)((longlong)local_3f8 + 0xc) = 1;
    *(undefined1 *)(local_3f8 + 3) = 0xe;
    *(undefined2 *)((longlong)local_3f8 + 0x1a) = 0xb;
    *(int *)((longlong)local_3f8 + 0x1c) = 0x494ab0;
    FUN_1800079f8(local_428,0x1800d8e50,0x16);
    *(int *)((longlong)local_3f0 + 0xc) = 1;
    *(undefined1 *)(local_3f0 + 3) = 0xe;
    *(int *)((longlong)local_3f0 + 0x1c) = 0x413934;
    *(undefined2 *)((longlong)local_3f0 + 0x1a) = 0xc;
    *(undefined1 *)(local_1d0 + 6) = 0xe;
    local_1d0[7] = 0xd3d3d3;
    local_170 = &DAT_1800d4ecd;
    local_180 = "";
    uStack_178 = 0;
    auVar8._4_4_ = extraout_var_30;
    auVar8._0_4_ = extraout_s0_30;
    auVar8._8_8_ = extraout_var_73;
    FUN_180006050(auVar8,CONCAT44(uVar47,uVar17),&local_180,0x1800d8410,100,param_6,param_7,param_8,
                  param_9,param_10);
    pcVar43 = local_180;
    if ((&local_180 != local_538) &&
       (((local_180 != (char *)0x0 && (*local_180 != '\0')) ||
        ((*local_538 != (char *)0x0 && (**local_538 != '\0')))))) {
      if (local_180 == (char *)0x0) {
        if ((*(int *)(local_200[0] + 1) != 0) && (pcVar22 = *local_538, pcVar22 != (char *)0x0)) {
          pvVar23 = GetProcessHeap();
          HeapFree(pvVar23,0,pcVar22);
        }
        *(undefined4 *)(local_200[0] + 1) = 0;
        *local_538 = local_200[0][2];
      }
      else {
        cVar3 = *local_180;
        pcVar22 = local_180;
        while (cVar3 != '\0') {
          pcVar22 = pcVar22 + 1;
          cVar3 = *pcVar22;
        }
        iVar12 = (int)((longlong)pcVar22 - (longlong)local_180);
        if (0x7ffffffe < (ulonglong)((longlong)pcVar22 - (longlong)local_180)) {
          iVar12 = 0x7ffffffe;
        }
        FUN_1800079f8((longlong *)local_538,(longlong)local_180,iVar12);
      }
      *(undefined4 *)((longlong)local_200[0] + 0xc) = 1;
    }
    *(undefined1 *)(local_200[0] + 3) = 0xb;
    *(undefined4 *)((longlong)local_200[0] + 0x1c) = 0x14;
    *(undefined2 *)((longlong)local_200[0] + 0x1a) = 0xd;
    *(undefined4 *)((longlong)local_200[0] + 0x2c) = 0;
    *(undefined4 *)((longlong)local_200[0] + 0x3c) = 100;
    FUN_1800079f8(local_4f0,0x1800d83f8,0x11);
    *(int *)((longlong)local_1c8 + 0xc) = 1;
    *(undefined1 *)(local_1c8 + 3) = 0xb;
    *(undefined2 *)((longlong)local_1c8 + 0x1a) = 0xe;
    *(int *)((longlong)local_1c8 + 0x1c) = 1;
    FUN_1800079f8(local_4b8,0x1800d8448,0x10);
    *(undefined4 *)((longlong)local_4b8 + 0xc) = 1;
    puVar21 = FUN_180029680((longlong *)&local_1c0,&local_230);
    if (0xf < (ulonglong)puVar21[3]) {
      puVar21 = (undefined8 *)*puVar21;
    }
    if ((code *)local_4b8[10] != (code *)0x0) {
      (*(code *)local_4b8[10])(*(undefined4 *)((longlong)local_4b8 + 0x4c),puVar21);
      *(undefined1 *)(local_4b8 + 3) = 0x16;
    }
    if (0xf < local_218) {
      FUN_1800966b8(local_230);
    }
    local_230 = (char *)((ulonglong)local_230 & 0xffffffffffffff00);
    local_220 = (undefined1 *)0x0;
    local_218 = 0xf;
    *(undefined4 *)((longlong)local_4b8 + 0x1c) = 3;
    *(undefined1 *)(local_4b8 + 3) = 0x16;
    *(undefined2 *)((longlong)local_4b8 + 0x1a) = 0xf;
    FUN_1800079f8(local_4b0,0x1800d8430,0x13);
    *(undefined4 *)((longlong)local_4b0 + 0xc) = 1;
    puVar21 = FUN_180029680((longlong *)&local_1b0,&local_230);
    if (0xf < (ulonglong)puVar21[3]) {
      puVar21 = (undefined8 *)*puVar21;
    }
    if ((code *)local_4b0[10] != (code *)0x0) {
      (*(code *)local_4b0[10])(*(undefined4 *)((longlong)local_4b0 + 0x4c),puVar21);
      *(undefined1 *)(local_4b0 + 3) = 0x16;
    }
    if (0xf < local_218) {
      FUN_1800966b8(local_230);
    }
    local_230 = (char *)((ulonglong)local_230 & 0xffffffffffffff00);
    local_220 = (undefined1 *)0x0;
    local_218 = 0xf;
    *(undefined2 *)((longlong)local_4b0 + 0x1a) = 0x10;
    *(undefined1 *)(local_4b0 + 3) = 0x16;
    *(undefined4 *)((longlong)local_4b0 + 0x1c) = 1;
    FUN_1800079f8(local_408,0x1800d7210,0x12);
    *(int *)((longlong)local_1e0 + 0xc) = 1;
    *(undefined1 *)(local_1e0 + 3) = 0xb;
    *(int *)((longlong)local_1e0 + 0x1c) = 0x5a;
    *(undefined2 *)((longlong)local_1e0 + 0x1a) = 0x11;
    *(int *)((longlong)local_1e0 + 0x2c) = 0;
    *(int *)((longlong)local_1e0 + 0x3c) = 100;
    FUN_1800079f8(local_430,0x1800d71f8,0x14);
    *(undefined4 *)((longlong)local_430 + 0xc) = 1;
    puVar21 = FUN_180029680((longlong *)&local_1a0,&local_230);
    if (0xf < (ulonglong)puVar21[3]) {
      puVar21 = (undefined8 *)*puVar21;
    }
    if ((code *)local_430[10] != (code *)0x0) {
      (*(code *)local_430[10])(*(undefined4 *)((longlong)local_430 + 0x4c),puVar21);
      *(undefined1 *)(local_430 + 3) = 0x16;
    }
    if (0xf < local_218) {
      FUN_1800966b8(local_230);
    }
    local_230 = (char *)((ulonglong)local_230 & 0xffffffffffffff00);
    local_220 = (undefined1 *)0x0;
    local_218 = 0xf;
    *(undefined2 *)((longlong)local_430 + 0x1a) = 0x12;
    *(undefined1 *)(local_430 + 3) = 0x16;
    *(undefined4 *)((longlong)local_430 + 0x1c) = 0;
    FUN_1800079f8(local_438,0x1800d8480,0x14);
    *(undefined4 *)((longlong)local_438 + 0xc) = 1;
    puVar21 = FUN_180029680((longlong *)&local_1b8,&local_230);
    if (0xf < (ulonglong)puVar21[3]) {
      puVar21 = (undefined8 *)*puVar21;
    }
    if ((code *)local_438[10] != (code *)0x0) {
      (*(code *)local_438[10])(*(undefined4 *)((longlong)local_438 + 0x4c),puVar21);
      *(undefined1 *)(local_438 + 3) = 0x16;
    }
    if (0xf < local_218) {
      FUN_1800966b8(local_230);
    }
    local_230 = (char *)((ulonglong)local_230 & 0xffffffffffffff00);
    local_220 = (undefined1 *)0x0;
    local_218 = 0xf;
    *(undefined4 *)((longlong)local_438 + 0x1c) = 2;
    *(undefined2 *)((longlong)local_438 + 0x1a) = 0x13;
    *(undefined1 *)(local_438 + 3) = 0x16;
    FUN_1800079f8(local_440,0x1800d8460,0x19);
    *(undefined4 *)((longlong)local_440 + 0xc) = 1;
    *(undefined1 *)(local_440 + 3) = 5;
    *(undefined2 *)((longlong)local_440 + 0x1a) = 0x14;
    *(undefined4 *)((longlong)local_440 + 0x1c) = 1;
    FUN_1800079f8(local_448,0x1800d7388,0x11);
    *(undefined4 *)((longlong)local_448 + 0xc) = 1;
    puVar21 = FUN_180029680((longlong *)&local_198,&local_230);
    if (0xf < (ulonglong)puVar21[3]) {
      puVar21 = (undefined8 *)*puVar21;
    }
    if ((code *)local_448[10] != (code *)0x0) {
      (*(code *)local_448[10])(*(undefined4 *)((longlong)local_448 + 0x4c),puVar21);
      *(undefined1 *)(local_448 + 3) = 0x16;
    }
    if (0xf < local_218) {
      FUN_1800966b8(local_230);
    }
    local_230 = (char *)((ulonglong)local_230 & 0xffffffffffffff00);
    local_220 = (undefined1 *)0x0;
    local_218 = 0xf;
    *(undefined2 *)((longlong)local_448 + 0x1a) = 0x15;
    *(undefined1 *)(local_448 + 3) = 0x16;
    *(undefined4 *)((longlong)local_448 + 0x1c) = 0;
    FUN_1800079f8(plVar31,0x1800d8e68,0xe);
    *(undefined4 *)((longlong)plVar31 + 0xc) = 1;
    *(undefined1 *)(plVar31 + 3) = 5;
    *(undefined4 *)((longlong)plVar31 + 0x1c) = 0;
    *(undefined2 *)((longlong)plVar31 + 0x1a) = 0x16;
    if (((int)uStack_178 != 0) && (pcVar43 != (char *)0x0)) {
      pvVar23 = GetProcessHeap();
      HeapFree(pvVar23,0,pcVar43);
    }
  }
  return;
}

