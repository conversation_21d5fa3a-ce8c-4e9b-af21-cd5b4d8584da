
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_MGI_Weekly(undefined1 param_1 [16],undefined1 param_2 [16],longlong *param_3,
                    undefined8 param_4,undefined8 param_5,undefined8 param_6,undefined8 param_7,
                    undefined8 param_8,undefined8 param_9,undefined8 param_10)

{
  int iVar1;
  char cVar2;
  undefined1 auVar3 [16];
  undefined1 auVar4 [16];
  undefined1 auVar5 [16];
  bool bVar6;
  bool bVar7;
  longlong *plVar8;
  longlong *plVar9;
  bool bVar10;
  int iVar11;
  uint uVar12;
  int *piVar13;
  undefined8 *puVar14;
  HANDLE pvVar15;
  undefined8 uVar16;
  ulonglong uVar17;
  longlong *plVar18;
  float *pfVar19;
  float *pfVar20;
  undefined4 *puVar21;
  undefined8 *puVar22;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  undefined8 extraout_x1_10;
  undefined8 extraout_x1_11;
  undefined8 extraout_x1_12;
  undefined8 extraout_x1_13;
  undefined8 extraout_x1_14;
  undefined8 extraout_x1_15;
  undefined8 extraout_x1_16;
  undefined8 extraout_x1_17;
  undefined8 extraout_x1_18;
  undefined8 extraout_x1_19;
  undefined8 extraout_x1_20;
  undefined8 extraout_x1_21;
  undefined8 extraout_x1_22;
  undefined8 extraout_x1_23;
  undefined8 extraout_x1_24;
  undefined8 extraout_x1_25;
  undefined8 extraout_x1_26;
  undefined8 extraout_x1_27;
  undefined8 extraout_x1_28;
  undefined8 extraout_x1_29;
  undefined8 extraout_x1_30;
  undefined8 extraout_x1_31;
  undefined8 extraout_x1_32;
  undefined8 extraout_x1_33;
  undefined8 extraout_x1_34;
  undefined8 extraout_x1_35;
  undefined8 extraout_x1_36;
  undefined8 extraout_x1_37;
  undefined8 extraout_x1_38;
  undefined8 extraout_x1_39;
  undefined8 extraout_x1_40;
  undefined8 extraout_x1_41;
  undefined8 extraout_x1_42;
  undefined8 extraout_x1_43;
  undefined8 extraout_x1_44;
  undefined8 extraout_x1_45;
  undefined8 extraout_x1_46;
  undefined8 extraout_x1_47;
  undefined8 extraout_x1_48;
  undefined8 extraout_x1_49;
  undefined8 extraout_x1_50;
  undefined8 extraout_x1_51;
  undefined8 extraout_x1_52;
  undefined8 extraout_x1_53;
  undefined8 extraout_x1_54;
  undefined8 extraout_x1_55;
  undefined8 extraout_x1_56;
  undefined8 extraout_x1_57;
  undefined8 extraout_x1_58;
  undefined8 extraout_x1_59;
  undefined8 extraout_x1_60;
  undefined8 extraout_x1_61;
  undefined8 extraout_x1_62;
  undefined8 extraout_x1_63;
  undefined8 extraout_x1_64;
  undefined8 extraout_x1_65;
  undefined8 extraout_x1_66;
  undefined8 extraout_x1_67;
  undefined8 extraout_x1_68;
  undefined8 extraout_x1_69;
  undefined8 extraout_x1_70;
  undefined8 extraout_x1_71;
  undefined8 extraout_x1_72;
  undefined8 extraout_x1_73;
  undefined8 extraout_x1_74;
  undefined8 extraout_x1_75;
  undefined8 extraout_x1_76;
  undefined8 extraout_x1_77;
  undefined8 extraout_x1_78;
  undefined8 extraout_x1_79;
  undefined8 extraout_x1_80;
  undefined8 extraout_x1_81;
  undefined8 extraout_x1_82;
  undefined8 extraout_x1_83;
  undefined8 extraout_x1_84;
  undefined8 extraout_x1_85;
  undefined8 extraout_x1_86;
  undefined8 extraout_x1_87;
  undefined8 extraout_x1_88;
  undefined8 extraout_x1_89;
  undefined8 extraout_x1_90;
  undefined8 extraout_x1_91;
  undefined8 extraout_x1_92;
  undefined8 extraout_x1_93;
  undefined8 extraout_x1_94;
  undefined8 extraout_x1_95;
  undefined8 extraout_x1_96;
  undefined8 extraout_x1_97;
  undefined8 extraout_x1_98;
  undefined8 extraout_x1_99;
  undefined8 extraout_x1_x00100;
  undefined8 extraout_x1_x00101;
  undefined8 extraout_x1_x00102;
  undefined8 extraout_x1_x00103;
  undefined8 extraout_x1_x00104;
  undefined8 extraout_x1_x00105;
  undefined8 extraout_x1_x00106;
  undefined8 extraout_x1_x00107;
  undefined8 extraout_x1_x00108;
  undefined8 extraout_x1_x00109;
  undefined8 extraout_x1_x00110;
  undefined8 extraout_x1_x00111;
  undefined8 extraout_x1_x00112;
  undefined8 extraout_x1_x00113;
  undefined8 extraout_x1_x00114;
  undefined8 extraout_x1_x00115;
  undefined8 extraout_x1_x00116;
  char *pcVar23;
  longlong *plVar24;
  longlong lVar25;
  undefined8 *puVar26;
  char *pcVar27;
  uint uVar28;
  int iVar29;
  longlong lVar30;
  longlong extraout_x11;
  code *extraout_x11_00;
  code *extraout_x11_01;
  code *extraout_x11_02;
  longlong extraout_x12;
  longlong **pplVar31;
  LPVOID pvVar32;
  longlong **pplVar33;
  longlong *plVar34;
  longlong **pplVar35;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  float fVar36;
  undefined4 extraout_s0_02;
  undefined4 extraout_s0_03;
  undefined4 uVar37;
  float fVar38;
  undefined4 extraout_s0_04;
  undefined4 extraout_var;
  undefined4 extraout_var_00;
  undefined4 extraout_var_01;
  undefined4 extraout_var_02;
  undefined4 extraout_var_03;
  undefined4 uVar39;
  undefined4 extraout_var_04;
  undefined8 extraout_var_05;
  undefined8 extraout_var_06;
  undefined8 extraout_var_07;
  undefined8 extraout_var_08;
  undefined8 extraout_var_09;
  undefined8 extraout_var_10;
  undefined4 uVar40;
  undefined4 uVar41;
  float fVar42;
  float fVar43;
  double dVar44;
  undefined1 auVar45 [16];
  byte local_38f;
  longlong **local_380;
  undefined8 uStack_378;
  undefined1 *local_370;
  ulonglong local_368;
  undefined8 local_360;
  longlong *local_358;
  longlong *local_350;
  longlong *local_348;
  int *local_340;
  int *local_338;
  float *local_330;
  longlong *local_328;
  longlong *local_320;
  longlong **local_318;
  float *local_310;
  longlong *local_308;
  longlong *local_300;
  longlong *local_2f8;
  longlong *local_2f0;
  longlong *local_2e8;
  longlong *local_2e0;
  longlong *local_2d8;
  longlong *local_2d0;
  longlong *local_2c8;
  longlong *local_2c0;
  longlong *local_2b8;
  longlong *local_2b0;
  longlong *local_2a8;
  longlong *local_2a0;
  longlong **local_298;
  undefined8 local_290;
  longlong *local_288;
  longlong lStack_280;
  longlong *local_278;
  longlong *local_270;
  longlong *local_268;
  longlong *local_260;
  longlong *local_258;
  longlong *local_250;
  longlong *local_248;
  float *local_240;
  float *local_238;
  float *local_230;
  float *local_228;
  float *local_220;
  float *local_218;
  longlong *local_210;
  longlong *local_208;
  longlong *local_200;
  float *local_1f8;
  float *local_1f0;
  longlong *local_1e8;
  longlong *local_1e0;
  float *local_1d8;
  longlong *local_1d0;
  longlong **local_1c8;
  longlong local_1c0;
  undefined8 *local_1b8;
  undefined8 *local_1b0;
  longlong *local_1a8;
  longlong *local_1a0;
  undefined4 local_198;
  float *local_190;
  undefined8 local_188;
  longlong *local_180;
  longlong lStack_178;
  longlong *local_170;
  longlong lStack_168;
  undefined **local_160 [2];
  undefined1 *local_150;
  undefined8 uStack_148;
  undefined1 *local_140;
  longlong local_130;
  undefined8 uStack_128;
  longlong local_120;
  undefined8 uStack_118;
  undefined8 local_110;
  undefined8 uStack_108;
  undefined8 uStack_100;
  undefined8 uStack_f8;
  undefined8 local_f0;
  undefined8 uStack_e8;
  longlong local_e0;
  longlong lStack_d8;
  undefined8 local_d0;
  undefined8 uStack_c8;
  undefined8 uStack_c0;
  undefined8 uStack_b8;
  undefined8 local_b0;
  
  uVar41 = param_2._4_4_;
                    /* 0x588f8  15  scsf_MGI_Weekly */
  uVar40 = param_2._0_4_;
  local_198 = 0;
  pplVar33 = (longlong **)&DAT_1800d4ecd;
  local_188 = 0xfffffffffffffffe;
  local_140 = &DAT_1800d4ecd;
  local_150 = &DAT_1800d4ecd;
  uStack_148 = 0;
  piVar13 = (int *)(*(code *)param_3[0x317])(param_1._0_4_,0);
  local_1d8 = (float *)(*(code *)param_3[0x317])(1);
  local_1f8 = (float *)(*(code *)param_3[0x317])(2);
  local_1f0 = (float *)(*(code *)param_3[0x317])(3);
  local_240 = (float *)(*(code *)param_3[0x317])(4);
  local_318 = (longlong **)(*(code *)param_3[0x317])(5);
  puVar14 = (undefined8 *)(*(code *)param_3[0x223])(2);
  local_338 = (int *)*puVar14;
  puVar14 = (undefined8 *)(*(code *)param_3[0x223])(4);
  local_330 = (float *)*puVar14;
  puVar14 = (undefined8 *)(*(code *)param_3[0x223])(5);
  pplVar31 = (longlong **)*puVar14;
  local_228 = (float *)(*(code *)param_3[0x318])(2);
  local_220 = (float *)(*(code *)param_3[0x318])(3);
  local_238 = (float *)(*(code *)param_3[0x318])(4);
  local_230 = (float *)(*(code *)param_3[0x318])(5);
  local_190 = (float *)(*(code *)param_3[0x318])(6);
  local_310 = (float *)(*(code *)param_3[0x318])(7);
  local_218 = (float *)(*(code *)param_3[0x318])(8);
  local_1e8 = (longlong *)(*(code *)param_3[0x31a])(0);
  local_278 = FUN_18000f880(param_3 + 0xa0,0);
  local_2d8 = FUN_18000f880(param_3 + 0xa0,1);
  local_2f0 = FUN_18000f880(param_3 + 0xa0,2);
  local_2d0 = FUN_18000f880(param_3 + 0xa0,3);
  local_2c8 = FUN_18000f880(param_3 + 0xa0,4);
  local_2c0 = FUN_18000f880(param_3 + 0xa0,5);
  local_2b8 = FUN_18000f880(param_3 + 0xa0,6);
  local_2b0 = FUN_18000f880(param_3 + 0xa0,7);
  local_2a8 = FUN_18000f880(param_3 + 0xa0,8);
  local_2e8 = FUN_18000f880(param_3 + 0xa0,9);
  local_2e0 = FUN_18000f880(param_3 + 0xa0,10);
  local_210 = FUN_18000f880(param_3 + 0xa0,0xb);
  local_258 = FUN_18000f880(param_3 + 0xa0,0xc);
  local_250 = FUN_18000f880(param_3 + 0xa0,0xd);
  local_308 = FUN_18000f880(param_3 + 0xa0,0xe);
  local_328 = FUN_18000f880(param_3 + 0xa0,0xf);
  local_1e0 = FUN_18000f880(param_3 + 0xa0,0x10);
  local_1d0 = FUN_18000f880(param_3 + 0xa0,0x11);
  local_300 = FUN_18000f880(param_3 + 0xa0,0x12);
  local_2f8 = FUN_18000f880(param_3 + 0xa0,0x13);
  local_348 = FUN_18000f880(param_3 + 0xa0,0x14);
  local_268 = FUN_18000f880(param_3 + 0xa0,0x15);
  local_260 = FUN_18000f880(param_3 + 0xa0,0x16);
  local_270 = FUN_18000f880(param_3 + 0xa0,0x17);
  local_248 = FUN_18000f880(param_3 + 0xa0,0x18);
  local_288 = FUN_18000f880(param_3 + 0xa0,0x19);
  local_1a0 = FUN_18000f880(param_3 + 0xa0,0x1a);
  local_208 = FUN_18000f880(param_3 + 0xa0,0x1b);
  local_200 = FUN_180029980(param_3 + 0x42,0);
  *(undefined2 *)((longlong)local_200 + 0x1a) = 2;
  local_1a8 = FUN_180029980(param_3 + 0x42,1);
  *(undefined2 *)((longlong)local_1a8 + 0x1a) = 1;
  local_350 = FUN_180029980(param_3 + 0x42,2);
  *(undefined2 *)((longlong)local_350 + 0x1a) = 3;
  local_320 = FUN_180029980(param_3 + 0x42,3);
  *(undefined2 *)((longlong)local_320 + 0x1a) = 4;
  local_358 = FUN_180029980(param_3 + 0x42,4);
  *(undefined2 *)((longlong)local_358 + 0x1a) = 5;
  local_2a0 = FUN_180029980(param_3 + 0x42,5);
  *(undefined2 *)((longlong)local_2a0 + 0x1a) = 6;
  local_298 = (longlong **)FUN_180029980(param_3 + 0x42,6);
  *(undefined2 *)((longlong)local_298 + 0x1a) = 7;
  local_160[0] = PitSessions::vftable;
  local_170 = (longlong *)0x0;
  lStack_168 = 0;
  local_170 = (longlong *)FUN_180096150(0x18);
  *local_170 = (longlong)local_170;
  local_170[1] = (longlong)local_170;
  local_180 = (longlong *)0x0;
  lStack_178 = 0;
  local_180 = (longlong *)FUN_180096150(0x18);
  *local_180 = (longlong)local_180;
  local_180[1] = (longlong)local_180;
  uVar12 = 0;
  local_360 = pplVar31;
  local_340 = piVar13;
  do {
    lVar25 = param_3[0xa0];
    if (lVar25 == 0) {
      if ((code *)param_3[0xa2] != (code *)0x0) {
        (*(code *)param_3[0xa2])((int)param_3[0xa3]);
      }
      lVar25 = param_3[0xa0];
      if (lVar25 != 0) goto LAB_180058d74;
      plVar24 = param_3 + 0xa5;
    }
    else {
LAB_180058d74:
      iVar11 = (int)param_3[0xa4];
      if (iVar11 == 0) {
        plVar24 = param_3 + 0xa5;
      }
      else {
        uVar28 = uVar12 & ((int)uVar12 >> 0x1f ^ 0xffffffffU);
        if (iVar11 <= (int)uVar28) {
          uVar28 = iVar11 - 1;
        }
        plVar24 = (longlong *)(lVar25 + (longlong)(int)uVar28 * 0x170);
      }
    }
    plVar34 = local_170;
    if (lStack_168 == 0xaaaaaaaaaaaaaaa) goto LAB_18005b104;
    local_1c8 = &local_170;
    local_1c0 = 0;
    puVar14 = (undefined8 *)FUN_180096150(0x18);
    puVar14[2] = plVar24;
    local_1c0 = 0;
    lStack_168 = lStack_168 + 1;
    puVar26 = (undefined8 *)plVar34[1];
    *puVar14 = plVar34;
    puVar14[1] = puVar26;
    plVar34[1] = (longlong)puVar14;
    *puVar26 = puVar14;
    lVar25 = param_3[0xa0];
    if (lVar25 == 0) {
      if ((code *)param_3[0xa2] != (code *)0x0) {
        (*(code *)param_3[0xa2])((int)param_3[0xa3]);
      }
      lVar25 = param_3[0xa0];
      if (lVar25 != 0) goto LAB_180058e0c;
      plVar24 = param_3 + 0xa5;
    }
    else {
LAB_180058e0c:
      iVar11 = (int)param_3[0xa4];
      if (iVar11 == 0) {
        plVar24 = param_3 + 0xa5;
      }
      else {
        uVar28 = uVar12 & ((int)uVar12 >> 0x1f ^ 0xffffffffU);
        if (iVar11 <= (int)uVar28) {
          uVar28 = iVar11 - 1;
        }
        plVar24 = (longlong *)(lVar25 + (longlong)(int)uVar28 * 0x170);
      }
    }
    plVar34 = local_180;
    if (lStack_178 == 0xaaaaaaaaaaaaaaa) {
LAB_18005b104:
                    /* WARNING: Subroutine does not return */
      FUN_1800941a8(0x1800d5268);
    }
    local_380 = &local_180;
    uStack_378 = 0;
    puVar14 = (undefined8 *)FUN_180096150(0x18);
    piVar13 = local_340;
    pplVar31 = local_360;
    puVar14[2] = plVar24;
    uStack_378 = 0;
    lStack_178 = lStack_178 + 1;
    puVar26 = (undefined8 *)plVar34[1];
    *puVar14 = plVar34;
    puVar14[1] = puVar26;
    plVar34[1] = (longlong)puVar14;
    *puVar26 = puVar14;
    uVar12 = uVar12 + 1;
  } while ((int)uVar12 < 0x1c);
  if (*(int *)((longlong)param_3 + 0xac) != 0) {
    FUN_1800079f8(param_3 + 0x23,0x1800d8bb0,0xc);
    *(undefined4 *)((longlong)param_3 + 0x124) = 1;
    if (((char *)param_3[0x67] != (char *)0x0) && (*(char *)param_3[0x67] != '\0')) {
      FUN_1800079f8(param_3 + 0x67,0x1800d4ecd,0);
      *(undefined4 *)((longlong)param_3 + 0x344) = 1;
    }
    local_380 = (longlong **)&DAT_1800d4ecd;
    uStack_378 = 0;
    local_370 = &DAT_1800d4ecd;
    pvVar15 = GetProcessHeap();
    pplVar31 = (longlong **)HeapAlloc(pvVar15,0,0x15);
    if (pplVar31 == (longlong **)0x0) {
      local_380 = (longlong **)&DAT_1800d4ecd;
      pplVar35 = pplVar33;
    }
    else {
      param_6 = 0x14;
      pplVar31[1] = (longlong *)0x0;
      *pplVar31 = (longlong *)0x0;
      *(undefined4 *)(pplVar31 + 2) = 0;
      *(char *)((longlong)pplVar31 + 0x14) = '\0';
      local_380 = pplVar31;
      FUN_180099d78((char *)pplVar31,0x15,0x1800d6c40,0x14);
      uStack_378 = 0x100000001;
      pplVar35 = pplVar31;
    }
    FUN_1800263b0(param_3 + 0x67,(longlong *)&local_380);
    if ((pplVar31 != (longlong **)0x0) && (pplVar35 != (longlong **)0x0)) {
      pvVar15 = GetProcessHeap();
      HeapFree(pvVar15,0,pplVar35);
    }
    local_380 = (longlong **)&DAT_1800d4ecd;
    uStack_378 = 0;
    local_370 = &DAT_1800d4ecd;
    pvVar15 = GetProcessHeap();
    pplVar31 = (longlong **)HeapAlloc(pvVar15,0,0x8d);
    if (pplVar31 == (longlong **)0x0) {
      local_380 = (longlong **)&DAT_1800d4ecd;
      pplVar35 = pplVar33;
    }
    else {
      param_6 = 0x8c;
      pplVar31[1] = (longlong *)0x0;
      *pplVar31 = (longlong *)0x0;
      pplVar31[3] = (longlong *)0x0;
      pplVar31[2] = (longlong *)0x0;
      pplVar31[5] = (longlong *)0x0;
      pplVar31[4] = (longlong *)0x0;
      pplVar31[7] = (longlong *)0x0;
      pplVar31[6] = (longlong *)0x0;
      pplVar31[9] = (longlong *)0x0;
      pplVar31[8] = (longlong *)0x0;
      pplVar31[0xb] = (longlong *)0x0;
      pplVar31[10] = (longlong *)0x0;
      pplVar31[0xd] = (longlong *)0x0;
      pplVar31[0xc] = (longlong *)0x0;
      pplVar31[0xf] = (longlong *)0x0;
      pplVar31[0xe] = (longlong *)0x0;
      pplVar31[0x10] = (longlong *)0x0;
      *(undefined4 *)(pplVar31 + 0x11) = 0;
      *(char *)((longlong)pplVar31 + 0x8c) = '\0';
      local_380 = pplVar31;
      FUN_180099d78((char *)pplVar31,0x8d,0x1800d6bb0,0x8c);
      uStack_378 = 0x100000001;
      pplVar35 = pplVar31;
    }
    FUN_1800263b0(param_3 + 0x67,(longlong *)&local_380);
    uVar37 = extraout_s0_00;
    uVar39 = extraout_var_00;
    uVar16 = extraout_var_06;
    if ((pplVar31 != (longlong **)0x0) && (pplVar35 != (longlong **)0x0)) {
      pvVar15 = GetProcessHeap();
      HeapFree(pvVar15,0,pplVar35);
      local_380 = (longlong **)0x0;
      uStack_378 = 0;
      uVar37 = extraout_s0_01;
      uVar39 = extraout_var_01;
      uVar16 = extraout_var_07;
    }
    pplVar31 = (longlong **)param_3[0x23];
    if ((longlong **)param_3[0x23] == (longlong **)0x0) {
      pplVar31 = pplVar33;
    }
    auVar45._4_4_ = uVar39;
    auVar45._0_4_ = uVar37;
    auVar45._8_8_ = uVar16;
    FUN_180026368(auVar45,CONCAT44(uVar41,uVar40),param_3 + 0x67,0x1800d6c60,pplVar31,param_6,
                  param_7,param_8,param_9,param_10);
    local_380 = (longlong **)&DAT_1800d4ecd;
    uStack_378 = 0;
    local_370 = &DAT_1800d4ecd;
    pvVar15 = GetProcessHeap();
    pplVar31 = (longlong **)HeapAlloc(pvVar15,0,7);
    if (pplVar31 == (longlong **)0x0) {
      local_380 = (longlong **)&DAT_1800d4ecd;
    }
    else {
      *(undefined4 *)pplVar31 = 0;
      ((char *)((longlong)pplVar31 + 4))[0] = '\0';
      ((char *)((longlong)pplVar31 + 4))[1] = '\0';
      *(char *)((longlong)pplVar31 + 6) = '\0';
      local_380 = pplVar31;
      FUN_180099d78((char *)pplVar31,7,0x1800d6c58,6);
      uStack_378 = 0x100000001;
      pplVar33 = pplVar31;
    }
    FUN_1800263b0(param_3 + 0x67,(longlong *)&local_380);
    if ((pplVar31 != (longlong **)0x0) && (pplVar33 != (longlong **)0x0)) {
      pvVar15 = GetProcessHeap();
      HeapFree(pvVar15,0,pplVar33);
      local_380 = (longlong **)0x0;
      uStack_378 = 0;
    }
    plVar24 = local_278;
    *(undefined4 *)((longlong)param_3 + 0x37c) = 0;
    *(undefined4 *)(param_3 + 0x16) = 1;
    *(undefined4 *)((longlong)param_3 + 4) = 0;
    *(undefined4 *)(param_3 + 2) = 0;
    *(undefined4 *)(param_3 + 0x197) = 1;
    *(undefined4 *)(param_3 + 0x13b) = 1;
    FUN_1800079f8(local_278,0x1800d8bc8,6);
    plVar34 = local_2d8;
    *(undefined4 *)((longlong)plVar24 + 0xc) = 1;
    *(undefined4 *)(plVar24 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)plVar24 + 0x24) = 0x20005;
    *(undefined4 *)(plVar24 + 3) = 0xf5f5f5;
    *(undefined2 *)(plVar24 + 5) = 1;
    FUN_1800079f8(local_2d8,0x1800d8bc0,7);
    plVar24 = local_2f0;
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined4 *)(plVar34 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)plVar34 + 0x24) = 0x20005;
    *(undefined2 *)(plVar34 + 5) = 1;
    *(undefined4 *)(plVar34 + 3) = 0xf5f5f5;
    FUN_1800079f8(local_2f0,0x1800d8bd8,6);
    plVar34 = local_2d0;
    *(undefined4 *)((longlong)plVar24 + 0xc) = 1;
    *(undefined4 *)(plVar24 + 3) = 0xf5f5f5;
    *(undefined4 *)(plVar24 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)plVar24 + 0x24) = 0x20005;
    *(undefined2 *)(plVar24 + 5) = 1;
    FUN_1800079f8(local_2d0,0x1800d8bd0,7);
    plVar24 = local_2c8;
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined4 *)(plVar34 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)plVar34 + 0x24) = 0x20005;
    *(undefined2 *)(plVar34 + 5) = 1;
    *(undefined4 *)(plVar34 + 3) = 0xf0a60d;
    FUN_1800079f8(local_2c8,0x1800d8bf0,8);
    plVar34 = local_2c0;
    *(undefined4 *)((longlong)plVar24 + 0xc) = 1;
    *(undefined4 *)(plVar24 + 3) = 0x221f0;
    *(undefined4 *)(plVar24 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)plVar24 + 0x24) = 0x20005;
    *(undefined2 *)(plVar24 + 5) = 1;
    FUN_1800079f8(local_2c0,0x1800d8be0,8);
    plVar24 = local_2b8;
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined4 *)(plVar34 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)plVar34 + 0x24) = 0x20005;
    *(undefined2 *)(plVar34 + 5) = 1;
    *(undefined4 *)(plVar34 + 3) = 0xf0a60d;
    FUN_1800079f8(local_2b8,0x1800d8c10,9);
    plVar34 = local_2b0;
    *(undefined4 *)((longlong)plVar24 + 0xc) = 1;
    *(undefined4 *)(plVar24 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)plVar24 + 0x24) = 0x20005;
    *(undefined2 *)(plVar24 + 5) = 1;
    *(undefined4 *)(plVar24 + 3) = 0x221f0;
    FUN_1800079f8(local_2b0,0x1800d8c00,8);
    plVar18 = local_2a8;
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined4 *)(plVar34 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)plVar34 + 0x24) = 0x20005;
    *(undefined2 *)(plVar34 + 5) = 1;
    *(undefined4 *)(plVar34 + 3) = 0xf0a60d;
    FUN_1800079f8(local_2a8,0x1800d8c30,9);
    plVar24 = local_2e8;
    *(undefined4 *)((longlong)plVar18 + 0xc) = 1;
    *(undefined4 *)(plVar18 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)plVar18 + 0x24) = 0x20005;
    *(undefined2 *)(plVar18 + 5) = 1;
    *(undefined4 *)(plVar18 + 3) = 0x221f0;
    FUN_1800079f8(local_2e8,0x1800d8c20,8);
    plVar34 = local_2e0;
    *(undefined4 *)((longlong)plVar24 + 0xc) = 1;
    *(undefined4 *)(plVar24 + 3) = 0xf0a60d;
    *(undefined4 *)(plVar24 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)plVar24 + 0x24) = 0x20005;
    *(undefined2 *)(plVar24 + 5) = 1;
    FUN_1800079f8(local_2e0,0x1800d8c48,9);
    plVar24 = local_210;
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined4 *)(plVar34 + 3) = 0x221f0;
    *(undefined4 *)(plVar34 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)plVar34 + 0x24) = 0x20005;
    *(undefined2 *)(plVar34 + 5) = 1;
    FUN_1800079f8(local_210,0x1800d8c3c,5);
    plVar34 = local_248;
    *(undefined4 *)((longlong)plVar24 + 0xc) = 1;
    *(undefined4 *)((longlong)plVar24 + 0x24) = 0x20000;
    *(undefined4 *)(plVar24 + 3) = 0x6b5e56;
    *(undefined4 *)(plVar24 + 0x1c) = 0x21;
    *(undefined2 *)(plVar24 + 5) = 1;
    FUN_1800079f8(local_248,0x1800d8c5c,5);
    plVar24 = local_258;
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined4 *)((longlong)plVar34 + 0x24) = 0x20005;
    *(undefined4 *)(plVar34 + 3) = 0xc0c0c0;
    *(undefined4 *)(plVar34 + 0x1c) = 0x21;
    *(undefined2 *)(plVar34 + 5) = 1;
    FUN_1800079f8(local_258,0x1800d8c54,5);
    plVar34 = local_250;
    *(undefined4 *)((longlong)plVar24 + 0xc) = 1;
    *(undefined4 *)((longlong)plVar24 + 0x24) = 0x20003;
    *(undefined4 *)(plVar24 + 3) = 0xd1a4ff;
    *(undefined4 *)(plVar24 + 0x1c) = 0x21;
    *(undefined2 *)(plVar24 + 5) = 1;
    FUN_1800079f8(local_250,0x1800d8c6c,5);
    plVar24 = local_308;
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined4 *)(plVar34 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)plVar34 + 0x24) = 0x20003;
    *(undefined2 *)(plVar34 + 5) = 1;
    *(undefined4 *)(plVar34 + 3) = 0xd1a4ff;
    FUN_1800079f8(local_308,0x1800d8c64,5);
    plVar34 = local_328;
    *(undefined4 *)((longlong)plVar24 + 0xc) = 1;
    *(undefined4 *)(plVar24 + 0x1c) = 0x21;
    plVar24[3] = 0x80ff000080ff;
    plVar24[4] = 0x2000500000001;
    *(undefined2 *)(plVar24 + 5) = 1;
    FUN_1800079f8(local_328,0x1800d8c7c,5);
    plVar24 = local_348;
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined4 *)(plVar34 + 0x1c) = 0x21;
    plVar34[3] = 0x8fff20008fff20;
    plVar34[4] = 0x300000001;
    *(undefined2 *)(plVar34 + 5) = 1;
    local_1e0[3] = 0x221f000f0a60d;
    local_1e0[4] = 0x2002200000001;
    *(undefined2 *)(local_1e0 + 5) = 1;
    local_1d0[3] = 0x221f000f0a60d;
    local_1d0[4] = 0x2002300000001;
    *(undefined2 *)(local_1d0 + 5) = 1;
    FUN_1800079f8(local_348,0x1800d8c74,6);
    plVar34 = local_300;
    *(undefined4 *)((longlong)plVar24 + 0xc) = 1;
    *(undefined2 *)((longlong)plVar24 + 0x24) = 5;
    *(undefined4 *)(plVar24 + 0x1c) = 0x21;
    *(undefined2 *)(plVar24 + 5) = 1;
    *(undefined4 *)(plVar24 + 3) = 0xc0c0c0;
    FUN_1800079f8(local_300,0x1800d8c8c,6);
    plVar24 = local_2f8;
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined4 *)(plVar34 + 0x1c) = 0x21;
    *(undefined2 *)((longlong)plVar34 + 0x24) = 5;
    *(undefined2 *)(plVar34 + 5) = 1;
    *(undefined4 *)(plVar34 + 3) = 0xc0c0c0;
    FUN_1800079f8(local_2f8,0x1800d8c84,6);
    plVar34 = local_270;
    *(undefined4 *)((longlong)plVar24 + 0xc) = 1;
    *(undefined4 *)(plVar24 + 0x1c) = 0x21;
    *(undefined2 *)((longlong)plVar24 + 0x24) = 5;
    *(undefined2 *)(plVar24 + 5) = 1;
    *(undefined4 *)(plVar24 + 3) = 0xc0c0c0;
    FUN_1800079f8(local_270,0x1800d8c9c,6);
    plVar24 = local_268;
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined2 *)((longlong)plVar34 + 0x24) = 5;
    *(undefined4 *)(plVar34 + 0x1c) = 0x21;
    *(undefined2 *)(plVar34 + 5) = 1;
    *(undefined4 *)(plVar34 + 3) = 0xc0c0c0;
    FUN_1800079f8(local_268,0x1800d8c94,6);
    plVar34 = local_260;
    *(undefined4 *)((longlong)plVar24 + 0xc) = 1;
    *(undefined4 *)((longlong)plVar24 + 0x24) = 3;
    *(undefined4 *)(plVar24 + 0x1c) = 0x21;
    *(undefined2 *)(plVar24 + 5) = 1;
    *(undefined4 *)(plVar24 + 3) = 0xd1a4ff;
    FUN_1800079f8(local_260,0x1800d8cac,6);
    plVar24 = local_208;
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined4 *)(plVar34 + 3) = 0xd1a4ff;
    *(undefined4 *)(plVar34 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)plVar34 + 0x24) = 3;
    *(undefined2 *)(plVar34 + 5) = 1;
    *(undefined4 *)(local_288 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)local_288 + 0x24) = 0x10005;
    *(undefined2 *)(local_288 + 5) = 1;
    *(undefined4 *)(local_288 + 3) = 0xc0c0c0;
    *(undefined4 *)(local_1a0 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)local_1a0 + 0x24) = 0x10005;
    *(undefined2 *)(local_1a0 + 5) = 1;
    *(undefined4 *)(local_1a0 + 3) = 0xc0c0c0;
    FUN_1800079f8(local_208,0x1800d8ca4,6);
    *(undefined4 *)((longlong)plVar24 + 0xc) = 1;
    *(undefined4 *)(plVar24 + 0x1c) = 0x21;
    *(undefined4 *)((longlong)plVar24 + 0x24) = 0x10005;
    *(undefined2 *)(plVar24 + 5) = 1;
    *(undefined4 *)(plVar24 + 3) = 0xc0c0c0;
    puVar14 = FUN_180029680((longlong *)local_160,&local_380);
    plVar24 = local_200;
    if (0xf < (ulonglong)puVar14[3]) {
      puVar14 = (undefined8 *)*puVar14;
    }
    if ((code *)local_200[10] != (code *)0x0) {
      (*(code *)local_200[10])(*(undefined4 *)((longlong)local_200 + 0x4c),puVar14);
      *(undefined1 *)(plVar24 + 3) = 0x16;
    }
    if (0xf < local_368) {
      FUN_1800966b8(local_380);
    }
    plVar34 = local_1a8;
    local_380 = (longlong **)((ulonglong)local_380 & 0xffffffffffffff00);
    local_370 = (undefined1 *)0x0;
    local_368 = 0xf;
    *(undefined1 *)(local_200 + 3) = 0x16;
    *(undefined4 *)((longlong)local_200 + 0x1c) = 0;
    FUN_1800079f8(local_1a8,0x1800d6d30,0x14);
    plVar24 = local_350;
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined4 *)((longlong)plVar34 + 0x1c) = 1;
    *(undefined1 *)(plVar34 + 3) = 5;
    FUN_1800079f8(local_350,0x1800d8a40,0x1f);
    *(undefined4 *)((longlong)plVar24 + 0xc) = 1;
    if ((code *)plVar24[10] != (code *)0x0) {
      (*(code *)plVar24[10])
                (*(undefined4 *)((longlong)plVar24 + 0x4c),"Default;Use Proximity Range");
    }
    plVar24 = local_320;
    *(undefined1 *)(local_350 + 3) = 0x16;
    *(undefined4 *)((longlong)local_350 + 0x1c) = 0;
    FUN_1800079f8(local_320,0x1800d8a80,0x28);
    plVar34 = local_2a0;
    *(undefined4 *)((longlong)plVar24 + 0xc) = 1;
    *(undefined4 *)((longlong)plVar24 + 0x1c) = 0x28;
    *(undefined4 *)((longlong)plVar24 + 0x3c) = 0x7fffffff;
    *(undefined1 *)(plVar24 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar24 + 0x2c) = 0;
    *(undefined4 *)((longlong)local_358 + 0x3c) = 0x96;
    *(undefined1 *)(local_358 + 3) = 0xb;
    *(undefined4 *)((longlong)local_358 + 0x1c) = 0;
    *(undefined4 *)((longlong)local_358 + 0x2c) = 0;
    FUN_1800079f8(local_2a0,0x1800d8b00,0x29);
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    cVar2 = (char)plVar34[3];
    if ((((cVar2 == '\x1a') || ((byte)(cVar2 - 0x1bU) < 2)) &&
        (*(char *)((longlong)local_2a0 + 0x1c) != '\0')) &&
       (pvVar32 = (LPVOID)local_2a0[4], pvVar32 != (LPVOID)0x0)) {
      pvVar15 = GetProcessHeap();
      HeapFree(pvVar15,0,pvVar32);
    }
    pplVar33 = local_298;
    *(undefined1 *)(local_2a0 + 3) = 0x1a;
    local_2a0[4] = 0;
    *(undefined1 *)((longlong)local_2a0 + 0x1c) = 1;
    FUN_1800079f8((longlong *)local_298,0x1800d8ad0,0x28);
    *(undefined4 *)((longlong)pplVar33 + 0xc) = 1;
    *(undefined4 *)((longlong)pplVar33 + 0x3c) = 300;
    *(undefined1 *)(pplVar33 + 3) = 0xb;
    *(undefined4 *)((longlong)pplVar33 + 0x1c) = 0;
    *(undefined4 *)((longlong)pplVar33 + 0x2c) = 0;
    goto LAB_18005b054;
  }
  if (*(int *)((longlong)param_3 + 0xc) == 0) {
    auVar3._4_4_ = extraout_var;
    auVar3._0_4_ = extraout_s0;
    auVar3._8_8_ = extraout_var_05;
    uVar16 = FUN_1800254e8(auVar3,CONCAT44(uVar41,uVar40),(longlong)param_3,extraout_x1,param_5,
                           param_6,param_7,param_8,param_9,param_10);
    *piVar13 = (int)uVar16;
  }
  if ((*piVar13 != 0) || (iVar11 = (*(code *)param_3[0x1f6])((int)param_3[0x138]), iVar11 == 0))
  goto LAB_18005b054;
  local_340 = (int *)param_3[0xe];
  if ((char)*local_340 == '\0') {
    *(undefined1 *)local_340 = 1;
  }
  local_38f = 0;
  fVar43 = *(float *)((longlong)param_3 + 0xc);
  if (*(int *)local_318 == 1) {
    if ((int)param_3[0x23f] != 0) {
      *(int *)local_318 = 0;
    }
    goto LAB_18005b054;
  }
  if (((int)param_3[0x23f] != 0) && (fVar43 == 0.0)) {
    *(int *)local_318 = 0;
  }
  uVar17 = FUN_180026708((longlong)local_358);
  if ((int)uVar17 == 0) {
    uVar12 = FUN_180026550((longlong)local_350);
    local_340[1] = uVar12;
  }
  else {
    uVar17 = FUN_180026708(extraout_x11);
    if ((int)param_3[0x1b7] == (int)uVar17) {
      local_38f = 1;
      fVar43 = 0.0;
      local_340[1] = (uint)(*(int *)((longlong)param_3 + 0xdbc) == 4);
      if ((int)param_3[0x23f] == 0) {
        *(undefined4 *)((longlong)param_3 + 0x10cc) = 0;
      }
    }
  }
  plVar24 = local_358;
  uVar17 = FUN_180026708((longlong)local_358);
  if ((int)uVar17 == 0) {
LAB_18005991c:
    if (fVar43 == 0.0) goto LAB_180059920;
  }
  else {
    FUN_180026708((longlong)plVar24);
    iVar11 = (*extraout_x11_00)();
    if (iVar11 == local_340[1]) goto LAB_18005991c;
    FUN_180026708((longlong)plVar24);
    (*extraout_x11_01)();
    fVar43 = 0.0;
    if ((int)param_3[0x23f] == 0) {
      *(undefined4 *)((longlong)param_3 + 0x10cc) = 0;
    }
LAB_180059920:
    local_38f = 1;
    *local_1f0 = -NAN;
    *local_240 = 0.0;
    if (local_338 != (int *)0x0) {
      FUN_18004a2d0(local_338);
      (*(code *)param_3[0x224])(2,0);
    }
    auVar45 = FUN_180096150(0x80);
    local_318 = auVar45._0_8_;
    uVar40 = 0x40000000;
    uVar41 = 0;
    local_318[1] = (longlong *)0x0;
    *local_318 = (longlong *)0x0;
    local_318[3] = (longlong *)0x0;
    local_318[2] = (longlong *)0x0;
    local_318[5] = (longlong *)0x0;
    local_318[4] = (longlong *)0x0;
    local_318[7] = (longlong *)0x0;
    local_318[6] = (longlong *)0x0;
    local_318[9] = (longlong *)0x0;
    local_318[8] = (longlong *)0x0;
    local_318[0xb] = (longlong *)0x0;
    local_318[10] = (longlong *)0x0;
    local_318[0xd] = (longlong *)0x0;
    local_318[0xc] = (longlong *)0x0;
    local_318[0xf] = (longlong *)0x0;
    local_318[0xe] = (longlong *)0x0;
    local_338 = FUN_18001cd30(0x3e4ccccd,0x40000000,(undefined4 *)local_318,auVar45._8_8_,1,0);
    if (local_338 == (int *)0x0) goto LAB_18005b054;
    (*(code *)param_3[0x224])(2,local_338);
    if (local_330 != (float *)0x0) {
      FUN_1800966b8(local_330);
      (*(code *)param_3[0x224])(4,0);
    }
    local_318 = (longlong **)FUN_180096150(0x28);
    auVar45 = FUN_1800043e0(0.0,(longlong *)&local_1a0);
    local_330 = auVar45._8_8_;
    local_330[4] = 0.0;
    local_330[5] = 0.0;
    *(undefined8 *)(local_330 + 4) = *auVar45._0_8_;
    *(undefined1 *)(local_330 + 6) = 0;
    *(undefined1 *)(local_330 + 8) = 1;
    local_330[0] = 0.0;
    local_330[1] = 0.0;
    local_330[2] = 0.0;
    local_330[3] = 0.0;
    local_330[7] = 0.0;
    (*(code *)param_3[0x224])(4);
    if (pplVar31 != (longlong **)0x0) {
      FUN_1800966b8(pplVar31);
      (*(code *)param_3[0x224])(5,0);
    }
    pplVar31 = (longlong **)FUN_180096150(0x10);
    local_318 = pplVar31;
    uVar17 = FUN_180026708((longlong)local_320);
    *pplVar31 = (longlong *)0xb0000000a;
    *(int *)(pplVar31 + 1) = (int)uVar17;
    (*(code *)param_3[0x224])(5,pplVar31);
    iVar11 = 0;
    local_360 = (longlong **)CONCAT44(local_360._4_4_,fVar43);
    *local_310 = -2.1474836e+09;
    *local_218 = 2.1474836e+09;
    do {
      uVar12 = 0;
      if (0 < (int)*param_3) {
        do {
          lVar25 = param_3[0xa0];
          if (lVar25 == 0) {
            if ((code *)param_3[0xa2] != (code *)0x0) {
              (*(code *)param_3[0xa2])((int)param_3[0xa3]);
            }
            lVar25 = param_3[0xa0];
            if (lVar25 != 0) goto LAB_180059ac0;
            plVar24 = param_3 + 0xa5;
          }
          else {
LAB_180059ac0:
            iVar1 = (int)param_3[0xa4];
            if (iVar1 == 0) {
              plVar24 = param_3 + 0xa5;
            }
            else {
              iVar29 = iVar11;
              if (iVar1 <= iVar11) {
                iVar29 = iVar1 + -1;
              }
              plVar24 = (longlong *)(lVar25 + (longlong)iVar29 * 0x170);
            }
          }
          lVar25 = plVar24[6];
          if (lVar25 == 0) {
            if ((code *)plVar24[8] != (code *)0x0) {
              (*(code *)plVar24[8])((int)plVar24[9]);
            }
            lVar25 = plVar24[6];
            if (lVar25 != 0) goto LAB_180059b0c;
            puVar21 = (undefined4 *)((longlong)plVar24 + 0x54);
          }
          else {
LAB_180059b0c:
            iVar1 = (int)plVar24[10];
            if (iVar1 == 0) {
              puVar21 = (undefined4 *)((longlong)plVar24 + 0x54);
            }
            else {
              uVar28 = uVar12 & ((int)uVar12 >> 0x1f ^ 0xffffffffU);
              if (iVar1 <= (int)uVar28) {
                uVar28 = iVar1 - 1;
              }
              puVar21 = (undefined4 *)(lVar25 + (longlong)(int)uVar28 * 4);
            }
          }
          *puVar21 = 0;
          uVar12 = uVar12 + 1;
        } while ((int)uVar12 < (int)*param_3);
      }
      plVar24 = local_170;
      pplVar33 = local_298;
      iVar11 = iVar11 + 1;
    } while (iVar11 < 0x3c);
    plVar34 = (longlong *)*local_170;
    fVar43 = (float)local_360;
    if (plVar34 != local_170) {
      do {
        lVar25 = plVar34[2];
        switch(*(undefined1 *)(pplVar33 + 3)) {
        default:
          uVar12 = 0;
          break;
        case 1:
        case 3:
        case 4:
        case 6:
        case 0xb:
        case 0xd:
        case 0xe:
        case 0xf:
        case 0x10:
        case 0x11:
        case 0x13:
        case 0x16:
        case 0x18:
          uVar12 = *(uint *)((longlong)pplVar33 + 0x1c);
          break;
        case 2:
          fVar43 = *(float *)((longlong)pplVar33 + 0x1c);
          if (0.0 <= fVar43) {
            uVar12 = (uint)(fVar43 + 0.5);
          }
          else {
            uVar12 = (uint)(fVar43 - 0.5);
          }
          break;
        case 5:
          uVar12 = (uint)(*(int *)((longlong)pplVar33 + 0x1c) != 0);
          break;
        case 8:
          plVar18 = (longlong *)
                    FUN_1800043e0(*(double *)((longlong)pplVar33 + 0x1c),(longlong *)&local_318);
          uVar12 = (uint)(*plVar18 / 86400000000);
          break;
        case 9:
          plVar18 = (longlong *)
                    FUN_1800043e0(*(double *)((longlong)pplVar33 + 0x1c),(longlong *)&local_288);
          lVar30 = *plVar18 % 86400000000;
          uVar12 = ((int)(lVar30 / 1000000) + (int)(lVar30 >> 0x3f)) -
                   (SUB164(SEXT816(lVar30) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
          if (86399999999 < lVar30) {
            uVar12 = 0;
          }
          break;
        case 0x17:
          dVar44 = *(double *)((longlong)pplVar33 + 0x1c);
          if (0.0 <= dVar44) {
            uVar12 = (uint)(dVar44 + 0.5);
          }
          else {
            uVar12 = (uint)(dVar44 - 0.5);
          }
          break;
        case 0x19:
          plVar18 = (longlong *)
                    FUN_1800043e0(*(double *)((longlong)pplVar33 + 0x1c),(longlong *)&local_298);
          lVar30 = *plVar18 % 86400000000;
          uVar12 = ((int)(lVar30 / 1000000) + (int)(lVar30 >> 0x3f)) -
                   (SUB164(SEXT816(lVar30) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
          if (86399999999 < lVar30) {
            uVar12 = 0;
          }
        }
        FUN_1800067b8((uint *)pplVar31,(int *)param_3,lVar25,uVar12);
        plVar34 = (longlong *)*plVar34;
      } while (plVar34 != plVar24);
      fVar43 = (float)local_360;
    }
    uVar17 = FUN_180026708((longlong)local_358);
    if ((int)uVar17 != 0) {
      uVar17 = FUN_180026708(extraout_x12);
      (*extraout_x11_02)(uVar17,"MGI Proximity");
    }
  }
  if (*(int *)((longlong)param_3 + 0x6c) == 0) {
    if (((local_338 != (int *)0x0) && (local_330 != (float *)0x0)) && (pplVar31 != (longlong **)0x0)
       ) {
      bVar7 = false;
      if ((int)fVar43 < (int)*param_3) {
        bVar7 = false;
        local_350 = (longlong *)CONCAT44(local_350._4_4_,(int)fVar43 + -1);
        local_358 = (longlong *)CONCAT44(local_358._4_4_,(int)fVar43 + -1);
        local_360 = (longlong **)CONCAT44(local_360._4_4_,fVar43);
        do {
          uStack_128 = 0;
          local_130 = 0;
          uStack_118 = 0;
          local_120 = 0;
          uStack_108 = 0;
          local_110 = 0;
          uStack_f8 = 0;
          uStack_100 = 0;
          uStack_e8 = 0;
          local_f0 = 0;
          lStack_d8 = 0;
          local_e0 = 0;
          uStack_c8 = 0;
          local_d0 = 0;
          uStack_b8 = 0;
          uStack_c0 = 0;
          local_b0 = 0;
          uVar12 = FUN_180026550((longlong)local_200);
          FUN_180011fd0(&local_130,(longlong)param_3,(int)fVar43,uVar12);
          fVar38 = *local_1f0;
          if ((fVar38 == fVar43) ||
             (uVar17 = FUN_180012b18((longlong)&local_130), (uVar17 & 1) == 0)) {
            bVar6 = false;
          }
          else {
            bVar6 = true;
          }
          uVar17 = FUN_180012928((longlong)&local_130);
          plVar24 = local_270;
          cVar2 = (char)uVar17;
          if ((uVar17 & 0xff) == 0) {
            fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_00,(longlong)local_270,
                                          (int)fVar43 + -1);
            FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_06,(longlong)plVar24,(int)fVar43);
            plVar24 = local_268;
            fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_07,(longlong)local_268,
                                          (int)fVar43 + -1);
            FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_08,(longlong)plVar24,(int)fVar43);
            plVar24 = local_260;
            fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_09,(longlong)local_260,
                                          (int)fVar43 + -1);
            uVar16 = extraout_x1_10;
          }
          else {
            fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_00,(longlong)local_348,
                                          (int)local_350);
            FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_01,(longlong)local_270,(int)fVar43);
            fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_02,(longlong)local_300,
                                          (int)local_358);
            FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_03,(longlong)local_268,(int)fVar43);
            fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_04,(longlong)local_2f8,
                                          (int)fVar43 + -1);
            uVar16 = extraout_x1_05;
            plVar24 = local_260;
          }
          FUN_1800069e8(fVar36,(int *)pplVar31,uVar16,(longlong)plVar24,(int)fVar43);
          uVar37 = extraout_s0_02;
          uVar39 = extraout_var_02;
          uVar16 = extraout_var_08;
          if (bVar6) {
            FUN_18001cec0(local_338,extraout_x1_11,0);
            uVar37 = extraout_s0_03;
            uVar39 = extraout_var_03;
            uVar16 = extraout_var_09;
          }
          plVar24 = local_348;
          auVar4._4_4_ = uVar39;
          auVar4._0_4_ = uVar37;
          auVar4._8_8_ = uVar16;
          FUN_18001cf60(auVar4,CONCAT44(uVar41,uVar40),local_338,(int *)param_3,(uint)fVar43,
                        (longlong)local_348,(longlong)local_300,(longlong)local_2f8,0,0,'\0',0);
          lVar25 = plVar24[6];
          uVar16 = extraout_x1_12;
          if (lVar25 == 0) {
            if ((code *)plVar24[8] != (code *)0x0) {
              (*(code *)plVar24[8])((int)plVar24[9]);
              uVar16 = extraout_x1_13;
            }
            lVar25 = local_348[6];
            if (lVar25 != 0) goto LAB_180059f0c;
            pfVar19 = (float *)((longlong)local_348 + 0x54);
          }
          else {
LAB_180059f0c:
            iVar11 = (int)local_348[10];
            if (iVar11 == 0) {
              pfVar19 = (float *)((longlong)local_348 + 0x54);
            }
            else {
              uVar12 = (uint)fVar43 & ((int)fVar43 >> 0x1f ^ 0xffffffffU);
              if (iVar11 <= (int)uVar12) {
                uVar12 = iVar11 - 1;
              }
              pfVar19 = (float *)(lVar25 + (longlong)(int)uVar12 * 4);
            }
          }
          FUN_1800069e8(*pfVar19,(int *)pplVar31,uVar16,(longlong)local_348,(int)fVar43);
          plVar24 = local_300;
          pfVar19 = (float *)FUN_18000f448((longlong)local_300,(int)fVar43);
          FUN_1800069e8(*pfVar19,(int *)pplVar31,extraout_x1_14,(longlong)plVar24,(int)fVar43);
          plVar24 = local_2f8;
          pfVar19 = (float *)FUN_18000f448((longlong)local_2f8,(int)fVar43);
          FUN_1800069e8(*pfVar19,(int *)pplVar31,extraout_x1_15,(longlong)plVar24,(int)fVar43);
          pfVar19 = local_238;
          plVar24 = local_278;
          if (fVar43 == 0.0) {
            *local_1e8 = 0;
          }
          if (bVar6) {
            pfVar19 = (float *)FUN_180005d08(param_3 + 0x14c,(int)fVar43);
            *local_238 = *pfVar19;
            pfVar19 = (float *)FUN_180005d08(param_3 + 0x151,(int)fVar43);
            lVar25 = local_120;
            *local_230 = *pfVar19;
            *local_1e8 = local_120 + 108000000000;
            FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_17,(longlong)local_278,(int)fVar43);
            FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_18,(longlong)local_2f0,(int)fVar43);
            FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_19,(longlong)local_2d8,(int)fVar43);
            FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_20,(longlong)local_2d0,(int)fVar43);
            FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_21,(longlong)local_2c8,(int)fVar43);
            FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_22,(longlong)local_2c0,(int)fVar43);
            FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_23,(longlong)local_2b8,(int)fVar43);
            FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_24,(longlong)local_2b0,(int)fVar43);
            FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_25,(longlong)local_2a8,(int)fVar43);
            FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_26,(longlong)local_2e8,(int)fVar43);
            FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_27,(longlong)local_2e0,(int)fVar43);
            *local_330 = 0.0;
            local_330[1] = 0.0;
            local_330[2] = 0.0;
            local_330[7] = 0.0;
            local_330[3] = 0.0;
            pfVar19 = (float *)FUN_180005d08(param_3 + 0x147,(int)*local_240);
            FUN_1800069e8(*pfVar19,(int *)pplVar31,extraout_x1_28,(longlong)local_248,(int)fVar43);
            pfVar19 = (float *)FUN_180005d08(param_3 + 0x147,(int)fVar43);
            FUN_1800069e8(*pfVar19,(int *)pplVar31,extraout_x1_29,(longlong)local_328,(int)fVar43);
          }
          else {
            if (local_120 == *local_1e8) {
              FUN_1800069e8(*local_238,(int *)pplVar31,extraout_x1_16,(longlong)local_278,
                            (int)fVar43);
              pfVar20 = local_230;
              plVar34 = local_2f0;
              FUN_1800069e8(*local_230,(int *)pplVar31,extraout_x1_30,(longlong)local_2f0,
                            (int)fVar43);
              FUN_1800069e8((*pfVar20 + *pfVar19) * 0.5,(int *)pplVar31,extraout_x1_31,
                            (longlong)local_2d8,(int)fVar43);
              fVar42 = *pfVar19 - *pfVar20;
              pfVar19 = (float *)FUN_18000f448((longlong)plVar24,(int)fVar43);
              fVar36 = FUN_180011838(fVar42 * 0.5 + *pfVar19,(longlong)param_3);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_32,(longlong)local_2d0,(int)fVar43);
              pfVar19 = (float *)FUN_18000f448((longlong)plVar24,(int)fVar43);
              fVar36 = FUN_180011838(*pfVar19 + fVar42,(longlong)param_3);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_33,(longlong)local_2c0,(int)fVar43);
              pfVar19 = (float *)FUN_18000f448((longlong)plVar24,(int)fVar43);
              fVar36 = FUN_180011838(fVar42 * 1.5 + *pfVar19,(longlong)param_3);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_34,(longlong)local_2b0,(int)fVar43);
              pfVar19 = (float *)FUN_18000f448((longlong)plVar24,(int)fVar43);
              fVar36 = FUN_180011838(fVar42 + fVar42 + *pfVar19,(longlong)param_3);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_35,(longlong)local_2e8,(int)fVar43);
              pfVar19 = (float *)FUN_18000f448((longlong)plVar34,(int)fVar43);
              fVar36 = FUN_180011838(*pfVar19 - fVar42 * 0.5,(longlong)param_3);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_36,(longlong)local_2c8,(int)fVar43);
              pfVar19 = (float *)FUN_18000f448((longlong)plVar34,(int)fVar43);
              fVar36 = FUN_180011838(*pfVar19 - fVar42,(longlong)param_3);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_37,(longlong)local_2b8,(int)fVar43);
              pfVar19 = (float *)FUN_18000f448((longlong)plVar34,(int)fVar43);
              fVar36 = FUN_180011838(*pfVar19 - fVar42 * 1.5,(longlong)param_3);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_38,(longlong)local_2a8,(int)fVar43);
              pfVar19 = (float *)FUN_18000f448((longlong)plVar34,(int)fVar43);
              fVar36 = FUN_180011838(*pfVar19 - (fVar42 + fVar42),(longlong)param_3);
              uVar16 = extraout_x1_39;
              plVar24 = local_2e0;
            }
            else {
              pfVar19 = (float *)FUN_180005d08(param_3 + 0x14c,(int)fVar43);
              fVar36 = *local_238;
              if (fVar36 <= *pfVar19) {
                pfVar19 = (float *)FUN_180005d08(param_3 + 0x14c,(int)fVar43);
                fVar36 = *pfVar19;
              }
              *local_238 = fVar36;
              pfVar19 = (float *)FUN_180005d08(param_3 + 0x151,(int)fVar43);
              fVar36 = *local_230;
              uVar16 = extraout_x1_40;
              if (*pfVar19 <= fVar36) {
                pfVar19 = (float *)FUN_180005d08(param_3 + 0x151,(int)fVar43);
                fVar36 = *pfVar19;
                uVar16 = extraout_x1_41;
              }
              plVar24 = local_278;
              *local_230 = fVar36;
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,uVar16,(longlong)local_278,
                                            (int)fVar43 + -1);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_42,(longlong)plVar24,(int)fVar43);
              plVar24 = local_2f0;
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_43,(longlong)local_2f0,
                                            (int)fVar43 + -1);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_44,(longlong)plVar24,(int)fVar43);
              plVar24 = local_2d8;
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_45,(longlong)local_2d8,
                                            (int)fVar43 + -1);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_46,(longlong)plVar24,(int)fVar43);
              plVar24 = local_2d0;
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_47,(longlong)local_2d0,
                                            (int)fVar43 + -1);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_48,(longlong)plVar24,(int)fVar43);
              plVar24 = local_2c8;
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_49,(longlong)local_2c8,
                                            (int)fVar43 + -1);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_50,(longlong)plVar24,(int)fVar43);
              plVar24 = local_2c0;
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_51,(longlong)local_2c0,
                                            (int)fVar43 + -1);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_52,(longlong)plVar24,(int)fVar43);
              plVar24 = local_2b8;
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_53,(longlong)local_2b8,
                                            (int)fVar43 + -1);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_54,(longlong)plVar24,(int)fVar43);
              plVar24 = local_2b0;
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_55,(longlong)local_2b0,
                                            (int)fVar43 + -1);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_56,(longlong)plVar24,(int)fVar43);
              plVar24 = local_2a8;
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_57,(longlong)local_2a8,
                                            (int)fVar43 + -1);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_58,(longlong)plVar24,(int)fVar43);
              plVar24 = local_2e8;
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_59,(longlong)local_2e8,
                                            (int)fVar43 + -1);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_60,(longlong)plVar24,(int)fVar43);
              plVar24 = local_2e0;
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_61,(longlong)local_2e0,
                                            (int)fVar43 + -1);
              uVar16 = extraout_x1_62;
            }
            FUN_1800069e8(fVar36,(int *)pplVar31,uVar16,(longlong)plVar24,(int)fVar43);
            pfVar19 = (float *)FUN_180005d08(param_3 + 0x147,(int)*local_240);
            FUN_1800069e8(*pfVar19,(int *)pplVar31,extraout_x1_63,(longlong)local_248,(int)fVar43);
            uVar17 = FUN_180012b18((longlong)&local_130);
            plVar24 = local_328;
            if ((uVar17 & 1) == 0) {
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_64,(longlong)local_328,
                                            (int)fVar43 + -1);
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_65,(longlong)plVar24,(int)fVar43);
            }
            plVar24 = local_328;
            pfVar19 = (float *)FUN_18000f448((longlong)local_328,(int)fVar43);
            plVar34 = local_308;
            pfVar20 = (float *)FUN_18000f448((longlong)local_308,(int)fVar43);
            if (*pfVar19 < *pfVar20) {
              uVar37 = *(undefined4 *)((longlong)plVar24 + 0x1c);
              puVar21 = (undefined4 *)FUN_180005d08(plVar24 + 0xb,(int)fVar43);
              *puVar21 = uVar37;
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_70,(longlong)plVar34,
                                            (int)fVar43);
              plVar34 = local_1e0;
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_71,(longlong)local_1e0,(int)fVar43);
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_72,(longlong)plVar24,
                                            (int)fVar43);
              plVar24 = local_1d0;
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_73,(longlong)local_1d0,(int)fVar43);
              uVar37 = *(undefined4 *)((longlong)plVar34 + 0x1c);
              puVar21 = (undefined4 *)FUN_180005d08(plVar34 + 0xb,(int)fVar43);
              *puVar21 = uVar37;
              uVar37 = *(undefined4 *)((longlong)plVar24 + 0x1c);
            }
            else {
              lVar25 = plVar24[3];
              puVar21 = (undefined4 *)FUN_180005d08(plVar24 + 0xb,(int)fVar43);
              *puVar21 = (int)lVar25;
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_66,(longlong)plVar24,
                                            (int)fVar43);
              plVar18 = local_1e0;
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_67,(longlong)local_1e0,(int)fVar43);
              fVar36 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_68,(longlong)plVar34,
                                            (int)fVar43);
              plVar24 = local_1d0;
              FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_69,(longlong)local_1d0,(int)fVar43);
              lVar25 = plVar18[3];
              puVar21 = (undefined4 *)FUN_180005d08(plVar18 + 0xb,(int)fVar43);
              *puVar21 = (int)lVar25;
              uVar37 = (undefined4)plVar24[3];
            }
            puVar21 = (undefined4 *)FUN_180005d08(plVar24 + 0xb,(int)fVar43);
            *puVar21 = uVar37;
            lVar25 = local_120;
          }
          fVar36 = FUN_180021950(local_330,(int *)param_3,fVar43);
          FUN_1800069e8(fVar36,(int *)pplVar31,extraout_x1_74,(longlong)local_210,(int)fVar43);
          plVar24 = local_308;
          if (cVar2 == '\0') {
            if (fVar38 != fVar43) {
              fVar38 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_75,(longlong)local_308,
                                            (int)fVar43 + -1);
              FUN_1800069e8(fVar38,(int *)pplVar31,extraout_x1_79,(longlong)plVar24,(int)fVar43);
              plVar34 = local_258;
              fVar38 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_80,(longlong)local_258,
                                            (int)fVar43 + -1);
              FUN_1800069e8(fVar38,(int *)pplVar31,extraout_x1_81,(longlong)plVar34,(int)fVar43);
              plVar34 = local_250;
              fVar38 = (float)FUN_180006bb0((int *)pplVar31,extraout_x1_82,(longlong)local_250,
                                            (int)fVar43 + -1);
              FUN_1800069e8(fVar38,(int *)pplVar31,extraout_x1_83,(longlong)plVar34,(int)fVar43);
              pfVar19 = (float *)FUN_18000f448((longlong)local_328,(int)fVar43);
              pfVar20 = (float *)FUN_18000f448((longlong)plVar24,(int)fVar43);
              if (*pfVar19 < *pfVar20) {
                uVar37 = *(undefined4 *)((longlong)plVar24 + 0x1c);
              }
              else {
                uVar37 = (undefined4)plVar24[3];
              }
              puVar21 = (undefined4 *)FUN_180005d08(plVar24 + 0xb,(int)fVar43);
              *puVar21 = uVar37;
              iVar11 = FUN_180004388((int)(lVar25 / 86400000000));
              if (iVar11 == 5) {
                if (local_e0 < lStack_d8) {
                  if ((lStack_d8 <= lVar25) || (bVar10 = false, lVar25 < local_e0))
                  goto LAB_18005a80c;
                }
                else if ((lVar25 < lStack_d8) || (local_e0 <= lVar25)) {
                  bVar10 = false;
                }
                else {
LAB_18005a80c:
                  bVar10 = true;
                }
                if (bVar10) goto LAB_18005a884;
              }
              pfVar19 = (float *)FUN_180005d08(param_3 + 0x14c,(int)fVar43);
              fVar38 = *local_228;
              if (fVar38 <= *pfVar19) {
                pfVar19 = (float *)FUN_180005d08(param_3 + 0x14c,(int)fVar43);
                fVar38 = *pfVar19;
              }
              *local_228 = fVar38;
              pfVar19 = (float *)FUN_180005d08(param_3 + 0x151,(int)fVar43);
              fVar38 = *local_220;
              if (*pfVar19 <= fVar38) {
                pfVar19 = (float *)FUN_180005d08(param_3 + 0x151,(int)fVar43);
                fVar38 = *pfVar19;
              }
              *local_220 = fVar38;
            }
          }
          else {
            pfVar19 = (float *)FUN_180005d08(param_3 + 0x156,(int)fVar43 + -1);
            fVar38 = *pfVar19;
            *local_190 = fVar38;
            FUN_1800069e8(fVar38,(int *)pplVar31,extraout_x1_76,(longlong)local_308,(int)fVar43);
            FUN_1800069e8(*local_228,(int *)pplVar31,extraout_x1_77,(longlong)local_258,(int)fVar43)
            ;
            FUN_1800069e8(*local_220,(int *)pplVar31,extraout_x1_78,(longlong)local_250,(int)fVar43)
            ;
          }
LAB_18005a884:
          if (bVar6) {
            pfVar19 = (float *)FUN_180005d08(param_3 + 0x14c,(int)fVar43);
            *local_228 = *pfVar19;
            pfVar19 = (float *)FUN_180005d08(param_3 + 0x151,(int)fVar43);
            *local_220 = *pfVar19;
            pfVar19 = (float *)FUN_180005d08(param_3 + 0x14c,(int)fVar43);
            *local_310 = *pfVar19;
LAB_18005a928:
            pfVar19 = (float *)FUN_180005d08(param_3 + 0x151,(int)fVar43);
            fVar38 = *pfVar19;
          }
          else {
            pfVar19 = (float *)FUN_180005d08(param_3 + 0x14c,(int)fVar43);
            fVar38 = *local_310;
            if (fVar38 <= *pfVar19) {
              pfVar19 = (float *)FUN_180005d08(param_3 + 0x14c,(int)fVar43);
              fVar38 = *pfVar19;
            }
            *local_310 = fVar38;
            pfVar19 = (float *)FUN_180005d08(param_3 + 0x151,(int)fVar43);
            fVar38 = *local_218;
            if (*pfVar19 <= fVar38) goto LAB_18005a928;
          }
          *local_218 = fVar38;
          fVar38 = FUN_180011838((fVar38 + *local_310) * 0.5,(longlong)param_3);
          FUN_1800069e8(fVar38,(int *)pplVar31,extraout_x1_84,(longlong)local_208,(int)fVar43);
          iVar11 = 0;
          uVar16 = extraout_x1_85;
          do {
            lVar25 = param_3[0xa0];
            if (lVar25 == 0) {
              if ((code *)param_3[0xa2] != (code *)0x0) {
                (*(code *)param_3[0xa2])((int)param_3[0xa3]);
                uVar16 = extraout_x1_86;
              }
              lVar25 = param_3[0xa0];
              if (lVar25 != 0) goto LAB_18005a99c;
              plVar24 = param_3 + 0xa5;
            }
            else {
LAB_18005a99c:
              iVar1 = (int)param_3[0xa4];
              if (iVar1 == 0) {
                plVar24 = param_3 + 0xa5;
              }
              else {
                iVar29 = iVar11;
                if (iVar1 <= iVar11) {
                  iVar29 = iVar1 + -1;
                }
                plVar24 = (longlong *)(lVar25 + (longlong)iVar29 * 0x170);
              }
            }
            uVar12 = *(uint *)pplVar31;
            lVar25 = plVar24[0x10];
            if (lVar25 == 0) {
              if ((code *)plVar24[0x12] != (code *)0x0) {
                (*(code *)plVar24[0x12])((int)plVar24[0x13]);
                uVar16 = extraout_x1_87;
              }
              lVar25 = plVar24[0x10];
              if (lVar25 != 0) goto LAB_18005a9ec;
              plVar24 = plVar24 + 0x15;
            }
            else {
LAB_18005a9ec:
              iVar1 = (int)plVar24[0x14];
              if (iVar1 == 0) {
                plVar24 = plVar24 + 0x15;
              }
              else {
                uVar12 = uVar12 & ((int)uVar12 >> 0x1f ^ 0xffffffffU);
                if (iVar1 <= (int)uVar12) {
                  uVar12 = iVar1 - 1;
                }
                plVar24 = (longlong *)(lVar25 + (longlong)(int)uVar12 * 0x28);
              }
            }
            lVar25 = *plVar24;
            if (lVar25 == 0) {
              if ((code *)plVar24[2] != (code *)0x0) {
                (*(code *)plVar24[2])((int)plVar24[3]);
                uVar16 = extraout_x1_88;
              }
              lVar25 = *plVar24;
              if (lVar25 != 0) goto LAB_18005aa3c;
              pfVar19 = (float *)((longlong)plVar24 + 0x24);
            }
            else {
LAB_18005aa3c:
              iVar1 = (int)plVar24[4];
              if (iVar1 == 0) {
                pfVar19 = (float *)((longlong)plVar24 + 0x24);
              }
              else {
                uVar12 = (uint)fVar43 & ((int)fVar43 >> 0x1f ^ 0xffffffffU);
                if (iVar1 <= (int)uVar12) {
                  uVar12 = iVar1 - 1;
                }
                pfVar19 = (float *)(lVar25 + (longlong)(int)uVar12 * 4);
              }
            }
            if (*pfVar19 == -2.1474836e+09) {
LAB_18005aacc:
              lVar25 = param_3[0xa0];
              if (lVar25 == 0) {
                if ((code *)param_3[0xa2] != (code *)0x0) {
                  (*(code *)param_3[0xa2])((int)param_3[0xa3]);
                  uVar16 = extraout_x1_91;
                }
                lVar25 = param_3[0xa0];
                if (lVar25 != 0) goto LAB_18005aaf4;
                plVar24 = param_3 + 0xa5;
              }
              else {
LAB_18005aaf4:
                iVar1 = (int)param_3[0xa4];
                if (iVar1 == 0) {
                  plVar24 = param_3 + 0xa5;
                }
                else {
                  iVar29 = iVar11;
                  if (iVar1 <= iVar11) {
                    iVar29 = iVar1 + -1;
                  }
                  plVar24 = (longlong *)(lVar25 + (longlong)iVar29 * 0x170);
                }
              }
              FUN_1800069e8(0.0,(int *)pplVar31,uVar16,(longlong)plVar24,(int)fVar43);
              uVar16 = extraout_x1_92;
            }
            else {
              lVar25 = param_3[0xa0];
              if (lVar25 == 0) {
                if ((code *)param_3[0xa2] != (code *)0x0) {
                  (*(code *)param_3[0xa2])((int)param_3[0xa3]);
                  uVar16 = extraout_x1_89;
                }
                lVar25 = param_3[0xa0];
                if (lVar25 != 0) goto LAB_18005aa94;
                plVar24 = param_3 + 0xa5;
              }
              else {
LAB_18005aa94:
                iVar1 = (int)param_3[0xa4];
                if (iVar1 == 0) {
                  plVar24 = param_3 + 0xa5;
                }
                else {
                  iVar29 = iVar11;
                  if (iVar1 <= iVar11) {
                    iVar29 = iVar1 + -1;
                  }
                  plVar24 = (longlong *)(lVar25 + (longlong)iVar29 * 0x170);
                }
              }
              fVar38 = (float)FUN_180006bb0((int *)pplVar31,uVar16,(longlong)plVar24,(int)fVar43);
              uVar16 = extraout_x1_90;
              if (fVar38 == 2.1474836e+09) goto LAB_18005aacc;
            }
            plVar9 = local_270;
            plVar8 = local_2f8;
            plVar18 = local_300;
            plVar34 = local_308;
            plVar24 = local_348;
            iVar11 = iVar11 + 1;
          } while (iVar11 < 0x3c);
          switch((char)local_1a8[3]) {
          default:
            goto switchD_18005ab54_caseD_0;
          case '\x01':
          case '\x03':
          case '\x04':
          case '\x05':
          case '\x06':
          case '\v':
          case '\r':
          case '\x0e':
          case '\x0f':
          case '\x10':
          case '\x11':
          case '\x13':
          case '\x16':
          case '\x18':
            bVar10 = *(int *)((longlong)local_1a8 + 0x1c) == 0;
            break;
          case '\x02':
            bVar10 = *(float *)((longlong)local_1a8 + 0x1c) == 0.0;
            break;
          case '\b':
          case '\t':
          case '\n':
          case '\x17':
          case '\x19':
            bVar10 = false;
            if (!NAN(*(double *)((longlong)local_1a8 + 0x1c))) {
              bVar10 = *(double *)((longlong)local_1a8 + 0x1c) == 0.0;
            }
          }
          if (!bVar10) {
            if (cVar2 != '\0') {
              for (fVar38 = *local_1f8; (int)fVar38 < (int)fVar43; fVar38 = (float)((int)fVar38 + 1)
                  ) {
                local_320 = param_3;
                FUN_1800069e8(0.0,(int *)pplVar31,uVar16,(longlong)plVar9,(int)fVar38);
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_93,(longlong)local_268,(int)fVar38);
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_94,(longlong)local_260,(int)fVar38);
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_95,(longlong)plVar24,(int)fVar38);
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_96,(longlong)plVar18,(int)fVar38);
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_97,(longlong)plVar8,(int)fVar38);
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_98,(longlong)local_258,(int)fVar38);
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_99,(longlong)local_250,(int)fVar38);
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_x00100,(longlong)plVar34,(int)fVar38);
                uVar16 = extraout_x1_x00101;
                param_3 = local_320;
              }
              fVar38 = *local_1f8;
              if ((int)(float)local_360 < (int)*local_1f8) {
                fVar38 = (float)local_360;
              }
              local_360._4_4_ = (undefined4)((ulonglong)local_360 >> 0x20);
              local_360 = (longlong **)CONCAT44(local_360._4_4_,fVar38);
            }
            plVar9 = local_278;
            plVar8 = local_2e0;
            plVar18 = local_2e8;
            plVar34 = local_2f0;
            plVar24 = local_328;
            if (bVar6) {
              for (fVar38 = *local_1d8; (int)fVar38 < (int)fVar43; fVar38 = (float)((int)fVar38 + 1)
                  ) {
                local_320 = param_3;
                FUN_1800069e8(0.0,(int *)pplVar31,uVar16,(longlong)plVar9,(int)fVar38);
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_x00102,(longlong)local_2d8,(int)fVar38
                             );
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_x00103,(longlong)plVar34,(int)fVar38);
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_x00104,(longlong)local_2d0,(int)fVar38
                             );
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_x00105,(longlong)local_2c8,(int)fVar38
                             );
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_x00106,(longlong)local_2c0,(int)fVar38
                             );
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_x00107,(longlong)local_2b8,(int)fVar38
                             );
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_x00108,(longlong)local_2b0,(int)fVar38
                             );
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_x00109,(longlong)local_2a8,(int)fVar38
                             );
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_x00110,(longlong)plVar18,(int)fVar38);
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_x00111,(longlong)plVar8,(int)fVar38);
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_x00112,(longlong)local_210,(int)fVar38
                             );
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_x00113,(longlong)plVar24,(int)fVar38);
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_x00114,(longlong)local_248,(int)fVar38
                             );
                FUN_1800069e8(0.0,(int *)pplVar31,extraout_x1_x00115,(longlong)local_208,(int)fVar38
                             );
                uVar16 = extraout_x1_x00116;
                param_3 = local_320;
              }
              fVar38 = *local_1d8;
              if ((int)(float)local_360 < (int)*local_1d8) {
                fVar38 = (float)local_360;
              }
              local_360._4_4_ = (undefined4)((ulonglong)local_360 >> 0x20);
              local_360 = (longlong **)CONCAT44(local_360._4_4_,fVar38);
            }
          }
switchD_18005ab54_caseD_0:
          plVar24 = local_180;
          if ((local_340[1] == 1) && (fVar43 == (float)((int)*param_3 + -1))) {
            local_318 = &local_288;
            local_1c8 = &local_288;
            local_288 = (longlong *)0x0;
            lStack_280 = 0;
            plVar34 = (longlong *)*local_180;
            local_1c0 = 0;
            puVar14 = (undefined8 *)0x0;
            local_1b8 = (undefined8 *)0x0;
            local_1b0 = (undefined8 *)0x0;
            if (plVar34 != local_180) {
              local_298 = &local_288;
              local_290 = 0;
              puVar14 = (undefined8 *)FUN_180096150(0x18);
              local_1c0 = 1;
              puVar14[2] = plVar34[2];
              puVar26 = puVar14;
              local_1b0 = puVar14;
              lVar25 = local_1c0;
              for (plVar34 = (longlong *)*plVar34; local_290 = 0, local_1c0 = lVar25,
                  local_1b8 = puVar26, plVar34 != plVar24; plVar34 = (longlong *)*plVar34) {
                local_290 = 0;
                puVar22 = (undefined8 *)FUN_180096150(0x18);
                lVar25 = lVar25 + 1;
                puVar22[2] = plVar34[2];
                *puVar26 = puVar22;
                puVar22[1] = puVar26;
                puVar26 = puVar22;
              }
            }
            puVar26 = local_1b8;
            lVar25 = local_1c0;
            local_380 = &local_288;
            uStack_378 = 0;
            local_288 = (longlong *)FUN_180096150(0x18);
            local_1c0 = 0;
            if (lVar25 == 0) {
              *local_288 = (longlong)local_288;
              local_288[1] = (longlong)local_288;
            }
            else {
              *local_288 = (longlong)puVar14;
              local_288[1] = (longlong)puVar26;
              puVar14[1] = local_288;
              *puVar26 = local_288;
            }
            uStack_378 = 0;
            lStack_280 = lVar25;
            uVar12 = FUN_180006c90((uint *)pplVar31,(int *)param_3,&local_288,(int)fVar43,
                                   (uint)local_38f);
            if ((uVar12 & 1) != 0) {
              local_360 = (longlong **)((ulonglong)local_360 & 0xffffffff00000000);
              bVar7 = true;
            }
          }
          if (bVar6) {
            *local_240 = *local_1d8;
            *local_1d8 = fVar43;
          }
          if (cVar2 != '\0') {
            *local_1f8 = fVar43;
          }
          *local_1f0 = fVar43;
          fVar43 = (float)((int)fVar43 + 1);
          local_350 = (longlong *)CONCAT44(local_350._4_4_,(int)local_350 + 1);
          local_358 = (longlong *)CONCAT44(local_358._4_4_,(int)local_358 + 1);
        } while ((int)fVar43 < (int)*param_3);
        fVar43 = (float)local_360;
      }
      if ((int)param_3[0x23f] == 0) {
        *(float *)((longlong)param_3 + 0x10cc) = fVar43;
      }
      if ((bVar7) || (local_38f != 0)) {
        if ((((char)local_2a0[3] != '\x1a') && (1 < (byte)((char)local_2a0[3] - 0x1bU))) ||
           (pcVar23 = (char *)local_2a0[4], pcVar23 == (char *)0x0)) {
          pcVar23 = "Unset";
        }
        local_370 = (undefined1 *)0x0;
        local_368 = 0;
        cVar2 = *pcVar23;
        uStack_378 = 0;
        local_380 = (longlong **)0x0;
        pcVar27 = pcVar23;
        while (cVar2 != '\0') {
          pcVar27 = pcVar27 + 1;
          cVar2 = *pcVar27;
        }
        FUN_18000ddb8(&local_380,(undefined8 *)pcVar23,(longlong)pcVar27 - (longlong)pcVar23);
        auVar5._4_4_ = extraout_var_04;
        auVar5._0_4_ = extraout_s0_04;
        auVar5._8_8_ = extraout_var_10;
        FUN_180025678(auVar5,CONCAT44(uVar41,uVar40),(longlong)param_3,&local_380);
        if (0xf < local_368) {
          FUN_1800966b8(local_380);
        }
        local_380 = (longlong **)((ulonglong)local_380 & 0xffffffffffffff00);
        local_370 = (undefined1 *)0x0;
        local_368 = 0xf;
      }
    }
  }
  else {
    if (local_338 != (int *)0x0) {
      FUN_18004a2d0(local_338);
      (*(code *)param_3[0x224])(2,0);
    }
    if (local_330 != (float *)0x0) {
      FUN_1800966b8(local_330);
      (*(code *)param_3[0x224])(4,0);
    }
    if (pplVar31 != (longlong **)0x0) {
      FUN_1800966b8(pplVar31);
      (*(code *)param_3[0x224])(5,0);
    }
  }
LAB_18005b054:
  *(undefined8 *)local_180[1] = 0;
  puVar14 = (undefined8 *)*local_180;
  while (puVar14 != (undefined8 *)0x0) {
    pvVar32 = (LPVOID)*puVar14;
    FUN_1800966b8(puVar14);
    puVar14 = (undefined8 *)pvVar32;
  }
  FUN_1800966b8(local_180);
  *(undefined8 *)local_170[1] = 0;
  puVar14 = (undefined8 *)*local_170;
  while (puVar14 != (undefined8 *)0x0) {
    pvVar32 = (LPVOID)*puVar14;
    FUN_1800966b8(puVar14);
    puVar14 = (undefined8 *)pvVar32;
  }
  FUN_1800966b8(local_170);
  return;
}

