
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_Dominator(undefined1 param_1 [16],undefined8 param_2,longlong param_3,undefined8 param_4,
                   undefined8 param_5,undefined8 param_6,undefined8 param_7,undefined8 param_8,
                   undefined8 param_9,undefined8 param_10)

{
  int iVar1;
  undefined1 auVar2 [16];
  undefined1 *lpMem;
  HANDLE pvVar3;
  char *pcVar4;
  undefined8 uVar5;
  ulonglong uVar6;
  float *pfVar7;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  undefined8 extraout_x1_10;
  undefined8 extraout_x1_11;
  undefined8 extraout_x1_12;
  undefined8 extraout_x1_13;
  undefined8 extraout_x1_14;
  undefined8 extraout_x1_15;
  undefined8 extraout_x1_16;
  undefined8 extraout_x1_17;
  undefined8 extraout_x1_18;
  undefined8 extraout_x1_19;
  undefined8 extraout_x1_20;
  undefined8 extraout_x1_21;
  undefined8 extraout_x1_22;
  undefined8 extraout_x1_23;
  undefined8 extraout_x1_24;
  undefined8 extraout_x1_25;
  longlong lVar8;
  longlong *plVar9;
  longlong lVar10;
  float *pfVar11;
  undefined4 *puVar12;
  uint uVar13;
  int iVar14;
  uint uVar15;
  int iVar16;
  longlong lVar17;
  float *extraout_x11;
  float *extraout_x11_00;
  float *extraout_x11_01;
  float *extraout_x11_02;
  longlong *plVar18;
  longlong lVar19;
  longlong lVar20;
  char *lpMem_00;
  char *pcVar21;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  undefined4 uVar22;
  float fVar23;
  undefined1 extraout_var [12];
  undefined1 auVar24 [16];
  undefined1 extraout_var_00 [12];
  undefined1 extraout_var_01 [12];
  undefined1 auVar25 [12];
  float fVar26;
  float fVar27;
  longlong local_180;
  longlong local_170;
  char *local_168;
  undefined8 local_160;
  undefined1 *local_158;
  longlong local_150;
  longlong local_148;
  longlong *local_140;
  longlong local_138;
  longlong local_130;
  longlong local_128;
  longlong local_120;
  longlong local_118;
  longlong local_110;
  longlong local_108;
  longlong local_100;
  longlong local_f8;
  longlong local_f0;
  longlong local_e8;
  longlong *local_e0;
  longlong local_d8;
  longlong local_d0;
  longlong local_c8;
  longlong local_c0;
  longlong local_b8;
  longlong local_b0;
  int *local_a8;
  longlong local_a0;
  undefined8 uStack_98;
  undefined1 *local_90;
  undefined8 uStack_88;
  undefined1 *local_80;
  longlong local_70 [2];
  
                    /* 0x3d628  7  scsf_Dominator */
  uStack_98 = 0xfffffffffffffffe;
  local_a0 = param_3;
  local_a8 = (int *)(**(code **)(param_3 + 0x18b8))(param_1._0_4_,0);
  auVar24._4_12_ = extraout_var;
  auVar24._0_4_ = extraout_s0;
  lVar8 = *(longlong *)(param_3 + 0x500);
  uVar5 = extraout_x1;
  if (lVar8 == 0) {
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_00;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003d6ac;
    local_170 = param_3 + 0x528;
LAB_18003d6dc:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_01;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003d708;
    local_138 = param_3 + 0x528;
LAB_18003d73c:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_02;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003d768;
    local_130 = param_3 + 0x528;
LAB_18003d79c:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_03;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003d7c8;
    local_128 = param_3 + 0x528;
LAB_18003d800:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_04;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003d830;
    local_b0 = param_3 + 0x528;
LAB_18003d868:
    local_180 = local_b0;
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_05;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003d894;
    local_f0 = param_3 + 0x528;
LAB_18003d8c8:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_06;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003d8f4;
    local_120 = param_3 + 0x528;
LAB_18003d928:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_07;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003d954;
    local_d8 = param_3 + 0x528;
LAB_18003d988:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_08;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003d9b4;
    local_118 = param_3 + 0x528;
LAB_18003d9e8:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_09;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003da14;
    local_100 = param_3 + 0x528;
LAB_18003da48:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_10;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003da74;
    local_d0 = param_3 + 0x528;
LAB_18003daa8:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_11;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003dad4;
    local_c8 = param_3 + 0x528;
LAB_18003db08:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_12;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003db34;
    local_110 = param_3 + 0x528;
LAB_18003db68:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_13;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003db94;
    local_e8 = param_3 + 0x528;
LAB_18003dbc8:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_14;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003dbf4;
    local_c0 = param_3 + 0x528;
LAB_18003dc28:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_15;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003dc54;
    local_b8 = param_3 + 0x528;
LAB_18003dc88:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_16;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003dcb4;
    local_108 = param_3 + 0x528;
LAB_18003dce8:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_17;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003dd14;
    local_f8 = param_3 + 0x528;
LAB_18003dd48:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_18;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003dd74;
    local_150 = param_3 + 0x528;
LAB_18003dda8:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_19;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003ddd4;
    local_148 = param_3 + 0x528;
LAB_18003de08:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_20;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003de30;
    plVar18 = (longlong *)(param_3 + 0x528);
LAB_18003de60:
    if (*(code **)(param_3 + 0x510) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x510))(*(undefined4 *)(param_3 + 0x518));
      uVar5 = extraout_x1_21;
    }
    lVar8 = *(longlong *)(param_3 + 0x500);
    if (lVar8 != 0) goto LAB_18003de80;
    local_140 = (longlong *)(param_3 + 0x528);
  }
  else {
LAB_18003d6ac:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_170 = param_3 + 0x528;
    }
    else {
      iVar14 = 0;
      if (iVar16 < 1) {
        iVar14 = iVar16 + -1;
      }
      local_170 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003d6dc;
LAB_18003d708:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_138 = param_3 + 0x528;
    }
    else {
      iVar14 = 1;
      if (iVar16 < 2) {
        iVar14 = iVar16 + -1;
      }
      local_138 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003d73c;
LAB_18003d768:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_130 = param_3 + 0x528;
    }
    else {
      iVar14 = 2;
      if (iVar16 < 3) {
        iVar14 = iVar16 + -1;
      }
      local_130 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003d79c;
LAB_18003d7c8:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_128 = param_3 + 0x528;
    }
    else {
      iVar14 = 3;
      if (iVar16 < 4) {
        iVar14 = iVar16 + -1;
      }
      local_128 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003d800;
LAB_18003d830:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_180 = param_3 + 0x528;
    }
    else {
      iVar14 = 4;
      if (iVar16 < 5) {
        iVar14 = iVar16 + -1;
      }
      local_180 = lVar8 + (longlong)iVar14 * 0x170;
    }
    local_b0 = local_180;
    if (lVar8 == 0) goto LAB_18003d868;
LAB_18003d894:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_f0 = param_3 + 0x528;
    }
    else {
      iVar14 = 5;
      if (iVar16 < 6) {
        iVar14 = iVar16 + -1;
      }
      local_f0 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003d8c8;
LAB_18003d8f4:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_120 = param_3 + 0x528;
    }
    else {
      iVar14 = 6;
      if (iVar16 < 7) {
        iVar14 = iVar16 + -1;
      }
      local_120 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003d928;
LAB_18003d954:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_d8 = param_3 + 0x528;
    }
    else {
      iVar14 = 7;
      if (iVar16 < 8) {
        iVar14 = iVar16 + -1;
      }
      local_d8 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003d988;
LAB_18003d9b4:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_118 = param_3 + 0x528;
    }
    else {
      iVar14 = 8;
      if (iVar16 < 9) {
        iVar14 = iVar16 + -1;
      }
      local_118 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003d9e8;
LAB_18003da14:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_100 = param_3 + 0x528;
    }
    else {
      iVar14 = 9;
      if (iVar16 < 10) {
        iVar14 = iVar16 + -1;
      }
      local_100 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003da48;
LAB_18003da74:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_d0 = param_3 + 0x528;
    }
    else {
      iVar14 = 10;
      if (iVar16 < 0xb) {
        iVar14 = iVar16 + -1;
      }
      local_d0 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003daa8;
LAB_18003dad4:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_c8 = param_3 + 0x528;
    }
    else {
      iVar14 = 0xb;
      if (iVar16 < 0xc) {
        iVar14 = iVar16 + -1;
      }
      local_c8 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003db08;
LAB_18003db34:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_110 = param_3 + 0x528;
    }
    else {
      iVar14 = 0xc;
      if (iVar16 < 0xd) {
        iVar14 = iVar16 + -1;
      }
      local_110 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003db68;
LAB_18003db94:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_e8 = param_3 + 0x528;
    }
    else {
      iVar14 = 0xd;
      if (iVar16 < 0xe) {
        iVar14 = iVar16 + -1;
      }
      local_e8 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003dbc8;
LAB_18003dbf4:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_c0 = param_3 + 0x528;
    }
    else {
      iVar14 = 0xe;
      if (iVar16 < 0xf) {
        iVar14 = iVar16 + -1;
      }
      local_c0 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003dc28;
LAB_18003dc54:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_b8 = param_3 + 0x528;
    }
    else {
      iVar14 = 0xf;
      if (iVar16 < 0x10) {
        iVar14 = iVar16 + -1;
      }
      local_b8 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003dc88;
LAB_18003dcb4:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_108 = param_3 + 0x528;
    }
    else {
      iVar14 = 0x10;
      if (iVar16 < 0x11) {
        iVar14 = iVar16 + -1;
      }
      local_108 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003dce8;
LAB_18003dd14:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_f8 = param_3 + 0x528;
    }
    else {
      iVar14 = 0x11;
      if (iVar16 < 0x12) {
        iVar14 = iVar16 + -1;
      }
      local_f8 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003dd48;
LAB_18003dd74:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_150 = param_3 + 0x528;
    }
    else {
      iVar14 = 0x12;
      if (iVar16 < 0x13) {
        iVar14 = iVar16 + -1;
      }
      local_150 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003dda8;
LAB_18003ddd4:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_148 = param_3 + 0x528;
    }
    else {
      iVar14 = 0x13;
      if (iVar16 < 0x14) {
        iVar14 = iVar16 + -1;
      }
      local_148 = lVar8 + (longlong)iVar14 * 0x170;
    }
    if (lVar8 == 0) goto LAB_18003de08;
LAB_18003de30:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      plVar18 = (longlong *)(param_3 + 0x528);
    }
    else {
      iVar14 = 0x14;
      if (iVar16 < 0x15) {
        iVar14 = iVar16 + -1;
      }
      plVar18 = (longlong *)(lVar8 + (longlong)iVar14 * 0x170);
    }
    if (lVar8 == 0) goto LAB_18003de60;
LAB_18003de80:
    iVar16 = *(int *)(param_3 + 0x520);
    if (iVar16 == 0) {
      local_140 = (longlong *)(param_3 + 0x528);
    }
    else {
      iVar14 = 0x15;
      if (iVar16 < 0x16) {
        iVar14 = iVar16 + -1;
      }
      local_140 = (longlong *)(lVar8 + (longlong)iVar14 * 0x170);
    }
  }
  lVar8 = *(longlong *)(param_3 + 0x210);
  if (lVar8 == 0) {
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar5 = extraout_x1_22;
    }
    lVar8 = *(longlong *)(param_3 + 0x210);
    if (lVar8 != 0) goto LAB_18003dedc;
    lVar10 = param_3 + 0x238;
LAB_18003df08:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar5 = extraout_x1_23;
    }
    lVar8 = *(longlong *)(param_3 + 0x210);
    if (lVar8 != 0) goto LAB_18003df30;
    lVar19 = param_3 + 0x238;
LAB_18003df60:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar5 = extraout_x1_24;
    }
    lVar8 = *(longlong *)(param_3 + 0x210);
    if (lVar8 != 0) goto LAB_18003df88;
    lVar20 = param_3 + 0x238;
LAB_18003dfb8:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar24 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar5 = extraout_x1_25;
    }
    lVar8 = *(longlong *)(param_3 + 0x210);
    if (lVar8 != 0) goto LAB_18003dfd8;
    local_e0 = (longlong *)(param_3 + 0x238);
  }
  else {
LAB_18003dedc:
    iVar16 = *(int *)(param_3 + 0x230);
    if (iVar16 == 0) {
      lVar10 = param_3 + 0x238;
    }
    else {
      iVar14 = 0;
      if (iVar16 < 1) {
        iVar14 = iVar16 + -1;
      }
      lVar10 = lVar8 + (longlong)iVar14 * 0x98;
    }
    if (lVar8 == 0) goto LAB_18003df08;
LAB_18003df30:
    iVar16 = *(int *)(param_3 + 0x230);
    if (iVar16 == 0) {
      lVar19 = param_3 + 0x238;
    }
    else {
      iVar14 = 1;
      if (iVar16 < 2) {
        iVar14 = iVar16 + -1;
      }
      lVar19 = lVar8 + (longlong)iVar14 * 0x98;
    }
    if (lVar8 == 0) goto LAB_18003df60;
LAB_18003df88:
    iVar16 = *(int *)(param_3 + 0x230);
    if (iVar16 == 0) {
      lVar20 = param_3 + 0x238;
    }
    else {
      iVar14 = 2;
      if (iVar16 < 3) {
        iVar14 = iVar16 + -1;
      }
      lVar20 = lVar8 + (longlong)iVar14 * 0x98;
    }
    if (lVar8 == 0) goto LAB_18003dfb8;
LAB_18003dfd8:
    iVar16 = *(int *)(param_3 + 0x230);
    if (iVar16 == 0) {
      local_e0 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar14 = 3;
      if (iVar16 < 4) {
        iVar14 = iVar16 + -1;
      }
      local_e0 = (longlong *)(lVar8 + (longlong)iVar14 * 0x98);
    }
  }
  if (*(int *)(param_3 + 0xac) != 0) {
    lpMem_00 = "";
    local_80 = &DAT_1800d4ecd;
    local_90 = &DAT_1800d4ecd;
    uStack_88 = 0;
    FUN_180006050(auVar24,param_2,&local_90,0x1800d7400,param_5,param_6,param_7,param_8,param_9,
                  param_10);
    FUN_1800079f8((longlong *)(param_3 + 0x118),0x1800d7a38,9);
    *(undefined4 *)(param_3 + 0x124) = 1;
    if ((*(char **)(param_3 + 0x338) != (char *)0x0) && (**(char **)(param_3 + 0x338) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0x338),0x1800d4ecd,0);
      *(undefined4 *)(param_3 + 0x344) = 1;
    }
    local_168 = "";
    local_160 = 0;
    local_158 = &DAT_1800d4ecd;
    pvVar3 = GetProcessHeap();
    pcVar4 = (char *)HeapAlloc(pvVar3,0,0x15);
    if (pcVar4 == (char *)0x0) {
      local_168 = "";
      pcVar21 = lpMem_00;
    }
    else {
      param_6 = 0x14;
      pcVar4[8] = '\0';
      pcVar4[9] = '\0';
      pcVar4[10] = '\0';
      pcVar4[0xb] = '\0';
      pcVar4[0xc] = '\0';
      pcVar4[0xd] = '\0';
      pcVar4[0xe] = '\0';
      pcVar4[0xf] = '\0';
      pcVar4[0] = '\0';
      pcVar4[1] = '\0';
      pcVar4[2] = '\0';
      pcVar4[3] = '\0';
      pcVar4[4] = '\0';
      pcVar4[5] = '\0';
      pcVar4[6] = '\0';
      pcVar4[7] = '\0';
      pcVar4[0x10] = '\0';
      pcVar4[0x11] = '\0';
      pcVar4[0x12] = '\0';
      pcVar4[0x13] = '\0';
      pcVar4[0x14] = '\0';
      local_168 = pcVar4;
      FUN_180099d78(pcVar4,0x15,0x1800d6c40,0x14);
      local_160 = 0x100000001;
      pcVar21 = pcVar4;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_168);
    if ((pcVar4 != (char *)0x0) && (pcVar21 != (char *)0x0)) {
      pvVar3 = GetProcessHeap();
      HeapFree(pvVar3,0,pcVar21);
    }
    local_168 = "";
    local_160 = 0;
    local_158 = &DAT_1800d4ecd;
    pvVar3 = GetProcessHeap();
    pcVar4 = (char *)HeapAlloc(pvVar3,0,0x8d);
    if (pcVar4 == (char *)0x0) {
      local_168 = "";
      pcVar21 = lpMem_00;
    }
    else {
      param_6 = 0x8c;
      pcVar4[8] = '\0';
      pcVar4[9] = '\0';
      pcVar4[10] = '\0';
      pcVar4[0xb] = '\0';
      pcVar4[0xc] = '\0';
      pcVar4[0xd] = '\0';
      pcVar4[0xe] = '\0';
      pcVar4[0xf] = '\0';
      pcVar4[0] = '\0';
      pcVar4[1] = '\0';
      pcVar4[2] = '\0';
      pcVar4[3] = '\0';
      pcVar4[4] = '\0';
      pcVar4[5] = '\0';
      pcVar4[6] = '\0';
      pcVar4[7] = '\0';
      pcVar4[0x18] = '\0';
      pcVar4[0x19] = '\0';
      pcVar4[0x1a] = '\0';
      pcVar4[0x1b] = '\0';
      pcVar4[0x1c] = '\0';
      pcVar4[0x1d] = '\0';
      pcVar4[0x1e] = '\0';
      pcVar4[0x1f] = '\0';
      pcVar4[0x10] = '\0';
      pcVar4[0x11] = '\0';
      pcVar4[0x12] = '\0';
      pcVar4[0x13] = '\0';
      pcVar4[0x14] = '\0';
      pcVar4[0x15] = '\0';
      pcVar4[0x16] = '\0';
      pcVar4[0x17] = '\0';
      pcVar4[0x28] = '\0';
      pcVar4[0x29] = '\0';
      pcVar4[0x2a] = '\0';
      pcVar4[0x2b] = '\0';
      pcVar4[0x2c] = '\0';
      pcVar4[0x2d] = '\0';
      pcVar4[0x2e] = '\0';
      pcVar4[0x2f] = '\0';
      pcVar4[0x20] = '\0';
      pcVar4[0x21] = '\0';
      pcVar4[0x22] = '\0';
      pcVar4[0x23] = '\0';
      pcVar4[0x24] = '\0';
      pcVar4[0x25] = '\0';
      pcVar4[0x26] = '\0';
      pcVar4[0x27] = '\0';
      pcVar4[0x38] = '\0';
      pcVar4[0x39] = '\0';
      pcVar4[0x3a] = '\0';
      pcVar4[0x3b] = '\0';
      pcVar4[0x3c] = '\0';
      pcVar4[0x3d] = '\0';
      pcVar4[0x3e] = '\0';
      pcVar4[0x3f] = '\0';
      pcVar4[0x30] = '\0';
      pcVar4[0x31] = '\0';
      pcVar4[0x32] = '\0';
      pcVar4[0x33] = '\0';
      pcVar4[0x34] = '\0';
      pcVar4[0x35] = '\0';
      pcVar4[0x36] = '\0';
      pcVar4[0x37] = '\0';
      pcVar4[0x48] = '\0';
      pcVar4[0x49] = '\0';
      pcVar4[0x4a] = '\0';
      pcVar4[0x4b] = '\0';
      pcVar4[0x4c] = '\0';
      pcVar4[0x4d] = '\0';
      pcVar4[0x4e] = '\0';
      pcVar4[0x4f] = '\0';
      pcVar4[0x40] = '\0';
      pcVar4[0x41] = '\0';
      pcVar4[0x42] = '\0';
      pcVar4[0x43] = '\0';
      pcVar4[0x44] = '\0';
      pcVar4[0x45] = '\0';
      pcVar4[0x46] = '\0';
      pcVar4[0x47] = '\0';
      pcVar4[0x58] = '\0';
      pcVar4[0x59] = '\0';
      pcVar4[0x5a] = '\0';
      pcVar4[0x5b] = '\0';
      pcVar4[0x5c] = '\0';
      pcVar4[0x5d] = '\0';
      pcVar4[0x5e] = '\0';
      pcVar4[0x5f] = '\0';
      pcVar4[0x50] = '\0';
      pcVar4[0x51] = '\0';
      pcVar4[0x52] = '\0';
      pcVar4[0x53] = '\0';
      pcVar4[0x54] = '\0';
      pcVar4[0x55] = '\0';
      pcVar4[0x56] = '\0';
      pcVar4[0x57] = '\0';
      pcVar4[0x68] = '\0';
      pcVar4[0x69] = '\0';
      pcVar4[0x6a] = '\0';
      pcVar4[0x6b] = '\0';
      pcVar4[0x6c] = '\0';
      pcVar4[0x6d] = '\0';
      pcVar4[0x6e] = '\0';
      pcVar4[0x6f] = '\0';
      pcVar4[0x60] = '\0';
      pcVar4[0x61] = '\0';
      pcVar4[0x62] = '\0';
      pcVar4[99] = '\0';
      pcVar4[100] = '\0';
      pcVar4[0x65] = '\0';
      pcVar4[0x66] = '\0';
      pcVar4[0x67] = '\0';
      pcVar4[0x78] = '\0';
      pcVar4[0x79] = '\0';
      pcVar4[0x7a] = '\0';
      pcVar4[0x7b] = '\0';
      pcVar4[0x7c] = '\0';
      pcVar4[0x7d] = '\0';
      pcVar4[0x7e] = '\0';
      pcVar4[0x7f] = '\0';
      pcVar4[0x70] = '\0';
      pcVar4[0x71] = '\0';
      pcVar4[0x72] = '\0';
      pcVar4[0x73] = '\0';
      pcVar4[0x74] = '\0';
      pcVar4[0x75] = '\0';
      pcVar4[0x76] = '\0';
      pcVar4[0x77] = '\0';
      pcVar4[0x80] = '\0';
      pcVar4[0x81] = '\0';
      pcVar4[0x82] = '\0';
      pcVar4[0x83] = '\0';
      pcVar4[0x84] = '\0';
      pcVar4[0x85] = '\0';
      pcVar4[0x86] = '\0';
      pcVar4[0x87] = '\0';
      pcVar4[0x88] = '\0';
      pcVar4[0x89] = '\0';
      pcVar4[0x8a] = '\0';
      pcVar4[0x8b] = '\0';
      pcVar4[0x8c] = '\0';
      local_168 = pcVar4;
      FUN_180099d78(pcVar4,0x8d,0x1800d6bb0,0x8c);
      local_160 = 0x100000001;
      pcVar21 = pcVar4;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_168);
    uVar22 = extraout_s0_00;
    auVar25 = extraout_var_00;
    if ((pcVar4 != (char *)0x0) && (pcVar21 != (char *)0x0)) {
      pvVar3 = GetProcessHeap();
      HeapFree(pvVar3,0,pcVar21);
      local_168 = (char *)0x0;
      local_160 = 0;
      uVar22 = extraout_s0_01;
      auVar25 = extraout_var_01;
    }
    pcVar4 = *(char **)(param_3 + 0x118);
    if (*(char **)(param_3 + 0x118) == (char *)0x0) {
      pcVar4 = lpMem_00;
    }
    auVar2._4_12_ = auVar25;
    auVar2._0_4_ = uVar22;
    FUN_180026368(auVar2,param_2,(undefined8 *)(param_3 + 0x338),0x1800d6c60,pcVar4,param_6,param_7,
                  param_8,param_9,param_10);
    local_168 = "";
    local_160 = 0;
    local_158 = &DAT_1800d4ecd;
    pvVar3 = GetProcessHeap();
    pcVar4 = (char *)HeapAlloc(pvVar3,0,7);
    if (pcVar4 == (char *)0x0) {
      local_168 = "";
    }
    else {
      pcVar4[0] = '\0';
      pcVar4[1] = '\0';
      pcVar4[2] = '\0';
      pcVar4[3] = '\0';
      pcVar4[4] = '\0';
      pcVar4[5] = '\0';
      pcVar4[6] = '\0';
      local_168 = pcVar4;
      FUN_180099d78(pcVar4,7,0x1800d6c58,6);
      local_160 = 0x100000001;
      lpMem_00 = pcVar4;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_168);
    if ((pcVar4 != (char *)0x0) && (lpMem_00 != (char *)0x0)) {
      pvVar3 = GetProcessHeap();
      HeapFree(pvVar3,0,lpMem_00);
      local_168 = (char *)0x0;
      local_160 = 0;
    }
    *(undefined4 *)(param_3 + 4) = 0;
    *(undefined4 *)(param_3 + 0x330) = 2;
    *(undefined4 *)(param_3 + 0x10) = 0;
    *(undefined4 *)(param_3 + 0xcb8) = 0;
    *(undefined4 *)(param_3 + 0x37c) = 1;
    FUN_1800079f8(plVar18,0x1800d7a58,0xe);
    plVar9 = local_140;
    *(undefined4 *)((longlong)plVar18 + 0xc) = 1;
    *(undefined2 *)((longlong)plVar18 + 0x24) = 10;
    *(undefined2 *)(plVar18 + 5) = 3;
    *(undefined4 *)(plVar18 + 3) = 0xf0a60d;
    FUN_1800079f8(local_140,0x1800d7a48,0xf);
    plVar18 = local_e0;
    *(undefined4 *)((longlong)plVar9 + 0xc) = 1;
    *(undefined2 *)((longlong)plVar9 + 0x24) = 0xb;
    *(undefined2 *)(plVar9 + 5) = 3;
    *(undefined4 *)(plVar9 + 3) = 0x221f0;
    *(undefined1 *)(lVar10 + 0x18) = 0xb;
    *(undefined4 *)(lVar10 + 0x1c) = 0x90;
    *(undefined1 *)(lVar19 + 0x18) = 0xb;
    *(undefined4 *)(lVar19 + 0x1c) = 5;
    *(undefined1 *)(lVar20 + 0x18) = 2;
    *(undefined4 *)(lVar20 + 0x1c) = 0x40000000;
    FUN_1800079f8(local_e0,0x1800d7a68,0x15);
    lpMem = local_90;
    *(undefined4 *)((longlong)plVar18 + 0xc) = 1;
    *(undefined1 *)(plVar18 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar18 + 0x1c) = 5;
    if ((int)uStack_88 == 0) {
      return;
    }
    if (local_90 == (undefined1 *)0x0) {
      return;
    }
    pvVar3 = GetProcessHeap();
    HeapFree(pvVar3,0,lpMem);
    return;
  }
  if (*(int *)(param_3 + 900) == 0) {
    uVar5 = FUN_1800254e8(auVar24,param_2,param_3,uVar5,param_5,param_6,param_7,param_8,param_9,
                          param_10);
    *local_a8 = (int)uVar5;
  }
  if (*local_a8 != 0) {
    return;
  }
  if ((*(longlong *)(param_3 + 0x160) == 0) && (*(code **)(param_3 + 0x170) != (code *)0x0)) {
    (**(code **)(param_3 + 0x170))(*(undefined4 *)(param_3 + 0x178));
  }
  (**(code **)(param_3 + 0x12d8))(local_70,*(undefined4 *)(param_3 + 900));
  uVar13 = *(uint *)(param_3 + 900);
  lVar8 = *(longlong *)(param_3 + 0x160);
  uVar15 = uVar13;
  if (lVar8 == 0) {
    if (*(code **)(param_3 + 0x170) != (code *)0x0) {
      (**(code **)(param_3 + 0x170))(*(undefined4 *)(param_3 + 0x178));
      uVar15 = *(uint *)(local_a0 + 900);
    }
    lVar8 = *(longlong *)(param_3 + 0x160);
    if (lVar8 != 0) goto LAB_18003e43c;
    plVar9 = (longlong *)(param_3 + 0x188);
  }
  else {
LAB_18003e43c:
    iVar16 = *(int *)(param_3 + 0x180);
    if (iVar16 == 0) {
      plVar9 = (longlong *)(param_3 + 0x188);
    }
    else {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar16 <= (int)uVar13) {
        uVar13 = iVar16 - 1;
      }
      plVar9 = (longlong *)(lVar8 + (longlong)(int)uVar13 * 8);
    }
  }
  lVar8 = (local_70[0] - *plVar9) + 1;
  lVar17 = (((longlong)((lVar8 >> 0x3f & 0xfffffffffff0bdc0U) + lVar8 + 500000) / 1000000) * 1000000
           ) % 86400000000;
  iVar16 = ((int)(lVar17 / 1000000) + (int)(lVar17 >> 0x3f)) -
           (SUB164(SEXT816(lVar17) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
  if (86399999999 < lVar17) {
    iVar16 = 0;
  }
  lVar8 = (longlong)((lVar8 >> 0x3f & 0xfffffffffffffc18U) + lVar8 + 500) % 86400000000;
  iVar14 = ((int)(lVar8 / 1000) + (int)(lVar8 >> 0x3f)) -
           (SUB164(SEXT816(lVar8) * SEXT816(0x20c49ba5e353f7cf),0xc) >> 0x1f);
  if (86399999999 < lVar8) {
    iVar14 = 0;
  }
  lVar8 = *(longlong *)(local_170 + 0x30);
  if (lVar8 == 0) {
    if (*(code **)(local_170 + 0x40) != (code *)0x0) {
      (**(code **)(local_170 + 0x40))(*(undefined4 *)(local_170 + 0x48));
    }
    lVar8 = *(longlong *)(local_170 + 0x30);
    if (lVar8 != 0) goto LAB_18003e52c;
    pfVar7 = (float *)(local_170 + 0x54);
  }
  else {
LAB_18003e52c:
    iVar1 = *(int *)(local_170 + 0x50);
    if (iVar1 == 0) {
      pfVar7 = (float *)(local_170 + 0x54);
    }
    else {
      uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
      if (iVar1 <= (int)uVar15) {
        uVar15 = iVar1 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar15 * 4);
    }
  }
  *pfVar7 = (float)iVar14 / 1000.0;
  uVar13 = *(uint *)(param_3 + 900);
  lVar8 = *(longlong *)(local_170 + 0x30);
  if (lVar8 == 0) {
    if (*(code **)(local_170 + 0x40) != (code *)0x0) {
      (**(code **)(local_170 + 0x40))(*(undefined4 *)(local_170 + 0x48));
    }
    lVar8 = *(longlong *)(local_170 + 0x30);
    if (lVar8 != 0) goto LAB_18003e58c;
    pfVar7 = (float *)(local_170 + 0x54);
  }
  else {
LAB_18003e58c:
    iVar14 = *(int *)(local_170 + 0x50);
    if (iVar14 == 0) {
      pfVar7 = (float *)(local_170 + 0x54);
    }
    else {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar14 <= (int)uVar13) {
        uVar13 = iVar14 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar13 * 4);
    }
  }
  if (*pfVar7 < 0.0) {
    uVar13 = *(uint *)(param_3 + 900);
    if (lVar8 == 0) {
      if (*(code **)(local_170 + 0x40) != (code *)0x0) {
        (**(code **)(local_170 + 0x40))(*(undefined4 *)(local_170 + 0x48));
      }
      lVar8 = *(longlong *)(local_170 + 0x30);
      if (lVar8 != 0) goto LAB_18003e5f0;
      puVar12 = (undefined4 *)(local_170 + 0x54);
    }
    else {
LAB_18003e5f0:
      iVar14 = *(int *)(local_170 + 0x50);
      if (iVar14 == 0) {
        puVar12 = (undefined4 *)(local_170 + 0x54);
      }
      else {
        uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
        if (iVar14 <= (int)uVar13) {
          uVar13 = iVar14 - 1;
        }
        puVar12 = (undefined4 *)(lVar8 + (longlong)(int)uVar13 * 4);
      }
    }
    *puVar12 = 0;
  }
  uVar13 = *(uint *)(param_3 + 900);
  lVar8 = *(longlong *)(param_3 + 0xbc8);
  if (iVar16 < 1) {
    iVar16 = 1;
  }
  uVar15 = uVar13;
  if (lVar8 == 0) {
    if (*(code **)(param_3 + 0xbd8) != (code *)0x0) {
      (**(code **)(param_3 + 0xbd8))(*(undefined4 *)(param_3 + 0xbe0));
      uVar15 = *(uint *)(param_3 + 900);
    }
    lVar8 = *(longlong *)(param_3 + 0xbc8);
    if (lVar8 != 0) goto LAB_18003e65c;
    pfVar7 = (float *)(param_3 + 0xbec);
  }
  else {
LAB_18003e65c:
    iVar14 = *(int *)(param_3 + 0xbe8);
    if (iVar14 == 0) {
      pfVar7 = (float *)(param_3 + 0xbec);
    }
    else {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar14 <= (int)uVar13) {
        uVar13 = iVar14 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar13 * 4);
    }
  }
  fVar26 = *pfVar7;
  lVar8 = *(longlong *)(local_138 + 0x30);
  if (lVar8 == 0) {
    if (*(code **)(local_138 + 0x40) != (code *)0x0) {
      (**(code **)(local_138 + 0x40))(*(undefined4 *)(local_138 + 0x48));
    }
    lVar8 = *(longlong *)(local_138 + 0x30);
    if (lVar8 != 0) goto LAB_18003e6bc;
    pfVar7 = (float *)(local_138 + 0x54);
  }
  else {
LAB_18003e6bc:
    iVar14 = *(int *)(local_138 + 0x50);
    if (iVar14 == 0) {
      pfVar7 = (float *)(local_138 + 0x54);
    }
    else {
      uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
      if (iVar14 <= (int)uVar15) {
        uVar15 = iVar14 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar15 * 4);
    }
  }
  *pfVar7 = fVar26 / (float)iVar16;
  uVar13 = *(uint *)(param_3 + 900);
  lVar8 = *(longlong *)(param_3 + 0xbf0);
  uVar15 = uVar13;
  if (lVar8 == 0) {
    if (*(code **)(param_3 + 0xc00) != (code *)0x0) {
      (**(code **)(param_3 + 0xc00))(*(undefined4 *)(param_3 + 0xc08));
      uVar15 = *(uint *)(param_3 + 900);
    }
    lVar8 = *(longlong *)(param_3 + 0xbf0);
    if (lVar8 != 0) goto LAB_18003e720;
    pfVar7 = (float *)(param_3 + 0xc14);
  }
  else {
LAB_18003e720:
    iVar14 = *(int *)(param_3 + 0xc10);
    if (iVar14 == 0) {
      pfVar7 = (float *)(param_3 + 0xc14);
    }
    else {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar14 <= (int)uVar13) {
        uVar13 = iVar14 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar13 * 4);
    }
  }
  fVar26 = *pfVar7;
  lVar8 = *(longlong *)(local_130 + 0x30);
  if (lVar8 == 0) {
    if (*(code **)(local_130 + 0x40) != (code *)0x0) {
      (**(code **)(local_130 + 0x40))(*(undefined4 *)(local_130 + 0x48));
    }
    lVar8 = *(longlong *)(local_130 + 0x30);
    if (lVar8 != 0) goto LAB_18003e780;
    pfVar7 = (float *)(local_130 + 0x54);
  }
  else {
LAB_18003e780:
    iVar14 = *(int *)(local_130 + 0x50);
    if (iVar14 == 0) {
      pfVar7 = (float *)(local_130 + 0x54);
    }
    else {
      uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
      if (iVar14 <= (int)uVar15) {
        uVar15 = iVar14 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar15 * 4);
    }
  }
  *pfVar7 = fVar26 / (float)iVar16;
  uVar13 = *(uint *)(param_3 + 900);
  lVar8 = *(longlong *)(param_3 + 0xbc8);
  uVar15 = uVar13;
  if (lVar8 == 0) {
    if (*(code **)(param_3 + 0xbd8) != (code *)0x0) {
      (**(code **)(param_3 + 0xbd8))(*(undefined4 *)(param_3 + 0xbe0));
      uVar15 = *(uint *)(param_3 + 900);
    }
    lVar8 = *(longlong *)(param_3 + 0xbc8);
    if (lVar8 != 0) goto LAB_18003e7e4;
    pfVar7 = (float *)(param_3 + 0xbec);
  }
  else {
LAB_18003e7e4:
    iVar16 = *(int *)(param_3 + 0xbe8);
    if (iVar16 == 0) {
      pfVar7 = (float *)(param_3 + 0xbec);
    }
    else {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar16 <= (int)uVar13) {
        uVar13 = iVar16 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar13 * 4);
    }
  }
  lVar8 = *(longlong *)(param_3 + 0xbf0);
  uVar13 = uVar15;
  if (lVar8 == 0) {
    if (*(code **)(param_3 + 0xc00) != (code *)0x0) {
      (**(code **)(param_3 + 0xc00))(*(undefined4 *)(param_3 + 0xc08));
      uVar13 = *(uint *)(param_3 + 900);
    }
    lVar8 = *(longlong *)(param_3 + 0xbf0);
    if (lVar8 != 0) goto LAB_18003e83c;
    pfVar11 = (float *)(param_3 + 0xc14);
  }
  else {
LAB_18003e83c:
    iVar16 = *(int *)(param_3 + 0xc10);
    if (iVar16 == 0) {
      pfVar11 = (float *)(param_3 + 0xc14);
    }
    else {
      uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
      if (iVar16 <= (int)uVar15) {
        uVar15 = iVar16 - 1;
      }
      pfVar11 = (float *)(lVar8 + (longlong)(int)uVar15 * 4);
    }
  }
  fVar27 = *pfVar11;
  fVar26 = *pfVar7;
  lVar8 = *(longlong *)(local_128 + 0x30);
  if (lVar8 == 0) {
    if (*(code **)(local_128 + 0x40) != (code *)0x0) {
      (**(code **)(local_128 + 0x40))(*(undefined4 *)(local_128 + 0x48));
    }
    lVar8 = *(longlong *)(local_128 + 0x30);
    if (lVar8 != 0) goto LAB_18003e89c;
    pfVar7 = (float *)(local_128 + 0x54);
  }
  else {
LAB_18003e89c:
    iVar16 = *(int *)(local_128 + 0x50);
    if (iVar16 == 0) {
      pfVar7 = (float *)(local_128 + 0x54);
    }
    else {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar16 <= (int)uVar13) {
        uVar13 = iVar16 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar13 * 4);
    }
  }
  *pfVar7 = fVar27 - fVar26;
  uVar6 = FUN_180026708(lVar10);
  lVar8 = local_f0;
  (**(code **)(param_3 + 0x3f8))
            (local_138 + 0x30,local_f0 + 0x30,0,*(undefined4 *)(param_3 + 900),uVar6 & 0xffffffff);
  uVar6 = FUN_180026708(lVar10);
  (**(code **)(param_3 + 0x3f8))
            (local_130 + 0x30,local_120 + 0x30,0,*(undefined4 *)(param_3 + 900),uVar6 & 0xffffffff);
  uVar6 = FUN_180026708(lVar10);
  (**(code **)(param_3 + 0x3f8))
            (local_128 + 0x30,local_d8 + 0x30,0,*(undefined4 *)(param_3 + 900),uVar6 & 0xffffffff);
  uVar13 = *(uint *)(param_3 + 900);
  lVar10 = *(longlong *)(lVar8 + 0x30);
  uVar15 = uVar13;
  if (lVar10 == 0) {
    if (*(code **)(lVar8 + 0x40) != (code *)0x0) {
      (**(code **)(lVar8 + 0x40))(*(undefined4 *)(lVar8 + 0x48));
      uVar15 = *(uint *)(param_3 + 900);
    }
    lVar10 = *(longlong *)(local_f0 + 0x30);
    if (lVar10 != 0) goto LAB_18003e988;
    pfVar7 = (float *)(local_f0 + 0x54);
  }
  else {
LAB_18003e988:
    iVar16 = *(int *)(local_f0 + 0x50);
    if (iVar16 == 0) {
      pfVar7 = (float *)(local_f0 + 0x54);
    }
    else {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar16 <= (int)uVar13) {
        uVar13 = iVar16 - 1;
      }
      pfVar7 = (float *)(lVar10 + (longlong)(int)uVar13 * 4);
    }
  }
  lVar8 = *(longlong *)(local_120 + 0x30);
  uVar13 = uVar15;
  if (lVar8 == 0) {
    if (*(code **)(local_120 + 0x40) != (code *)0x0) {
      (**(code **)(local_120 + 0x40))(*(undefined4 *)(local_120 + 0x48));
      uVar13 = *(uint *)(param_3 + 900);
    }
    lVar8 = *(longlong *)(local_120 + 0x30);
    if (lVar8 != 0) goto LAB_18003e9ec;
    pfVar11 = (float *)(local_120 + 0x54);
  }
  else {
LAB_18003e9ec:
    iVar16 = *(int *)(local_120 + 0x50);
    if (iVar16 == 0) {
      pfVar11 = (float *)(local_120 + 0x54);
    }
    else {
      uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
      if (iVar16 <= (int)uVar15) {
        uVar15 = iVar16 - 1;
      }
      pfVar11 = (float *)(lVar8 + (longlong)(int)uVar15 * 4);
    }
  }
  fVar26 = *pfVar11;
  fVar27 = *pfVar7;
  lVar8 = *(longlong *)(local_180 + 0x30);
  if (lVar8 == 0) {
    if (*(code **)(local_180 + 0x40) != (code *)0x0) {
      (**(code **)(local_180 + 0x40))(*(undefined4 *)(local_180 + 0x48));
    }
    lVar8 = *(longlong *)(local_180 + 0x30);
    if (lVar8 != 0) goto LAB_18003ea50;
    pfVar7 = (float *)(local_180 + 0x54);
  }
  else {
LAB_18003ea50:
    iVar16 = *(int *)(local_180 + 0x50);
    if (iVar16 == 0) {
      pfVar7 = (float *)(local_180 + 0x54);
    }
    else {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar16 <= (int)uVar13) {
        uVar13 = iVar16 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar13 * 4);
    }
  }
  *pfVar7 = -(fVar27 - fVar26);
  fVar26 = ABS(-(fVar27 - fVar26));
  uVar13 = *(uint *)(param_3 + 900);
  lVar8 = *(longlong *)(local_118 + 0x30);
  if (lVar8 == 0) {
    if (*(code **)(local_118 + 0x40) != (code *)0x0) {
      (**(code **)(local_118 + 0x40))(*(undefined4 *)(local_118 + 0x48));
    }
    lVar8 = *(longlong *)(local_118 + 0x30);
    if (lVar8 != 0) goto LAB_18003eab8;
    pfVar7 = (float *)(local_118 + 0x54);
  }
  else {
LAB_18003eab8:
    iVar16 = *(int *)(local_118 + 0x50);
    if (iVar16 == 0) {
      pfVar7 = (float *)(local_118 + 0x54);
    }
    else {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar16 <= (int)uVar13) {
        uVar13 = iVar16 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar13 * 4);
    }
  }
  *pfVar7 = fVar26;
  uVar6 = FUN_180026708(lVar19);
  lVar8 = local_118;
  (**(code **)(param_3 + 0x3f8))
            (local_118 + 0x30,local_100 + 0x30,0,*(undefined4 *)(param_3 + 900),uVar6 & 0xffffffff);
  (**(code **)(param_3 + 0x3f8))(lVar8 + 0x30,local_d0 + 0x30,0,*(undefined4 *)(param_3 + 900),8);
  (**(code **)(param_3 + 0x3f8))(lVar8 + 0x30,local_c8 + 0x30,0,*(undefined4 *)(param_3 + 900),0x15)
  ;
  uVar13 = *(uint *)(param_3 + 900);
  lVar8 = *(longlong *)(local_110 + 0x30);
  if (lVar8 == 0) {
    if (*(code **)(local_110 + 0x40) != (code *)0x0) {
      (**(code **)(local_110 + 0x40))(*(undefined4 *)(local_110 + 0x48));
    }
    lVar8 = *(longlong *)(local_110 + 0x30);
    if (lVar8 != 0) goto LAB_18003eb84;
    pfVar7 = (float *)(local_110 + 0x54);
  }
  else {
LAB_18003eb84:
    iVar16 = *(int *)(local_110 + 0x50);
    if (iVar16 == 0) {
      pfVar7 = (float *)(local_110 + 0x54);
    }
    else {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar16 <= (int)uVar13) {
        uVar13 = iVar16 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar13 * 4);
    }
  }
  *pfVar7 = -fVar26;
  uVar6 = FUN_180026708(lVar19);
  lVar10 = local_e8;
  lVar8 = local_110;
  (**(code **)(param_3 + 0x3f8))
            (local_110 + 0x30,local_e8 + 0x30,0,*(undefined4 *)(param_3 + 900),uVar6 & 0xffffffff);
  (**(code **)(param_3 + 0x3f8))(lVar8 + 0x30,local_c0 + 0x30,0,*(undefined4 *)(param_3 + 900),8);
  (**(code **)(param_3 + 0x3f8))(lVar8 + 0x30,local_b8 + 0x30,0,*(undefined4 *)(param_3 + 900),0x15)
  ;
  (**(code **)(param_3 + 0x3c0))(lVar10 + 0x30,local_108 + 0x30,*(undefined4 *)(param_3 + 900),10);
  uVar13 = *(uint *)(param_3 + 900);
  lVar8 = *(longlong *)(lVar10 + 0x30);
  uVar15 = uVar13;
  if (lVar8 == 0) {
    if (*(code **)(lVar10 + 0x40) != (code *)0x0) {
      (**(code **)(lVar10 + 0x40))(*(undefined4 *)(lVar10 + 0x48));
      uVar15 = *(uint *)(param_3 + 900);
    }
    lVar8 = *(longlong *)(local_e8 + 0x30);
    if (lVar8 != 0) goto LAB_18003ec78;
    pfVar7 = (float *)(local_e8 + 0x54);
  }
  else {
LAB_18003ec78:
    iVar16 = *(int *)(local_e8 + 0x50);
    if (iVar16 == 0) {
      pfVar7 = (float *)(local_e8 + 0x54);
    }
    else {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar16 <= (int)uVar13) {
        uVar13 = iVar16 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar13 * 4);
    }
  }
  if ((*(longlong *)(local_108 + 0x30) == 0) && (*(code **)(local_108 + 0x40) != (code *)0x0)) {
    (**(code **)(local_108 + 0x40))(*(undefined4 *)(local_108 + 0x48));
    uVar15 = *(uint *)(param_3 + 900);
  }
  fVar23 = FUN_180026608(lVar20);
  fVar26 = *extraout_x11;
  fVar27 = *pfVar7;
  lVar8 = *(longlong *)(local_150 + 0x30);
  if (lVar8 == 0) {
    if (*(code **)(local_150 + 0x40) != (code *)0x0) {
      (**(code **)(local_150 + 0x40))(*(undefined4 *)(local_150 + 0x48));
    }
    lVar8 = *(longlong *)(local_150 + 0x30);
    if (lVar8 != 0) goto LAB_18003ed4c;
    pfVar7 = (float *)(local_150 + 0x54);
  }
  else {
LAB_18003ed4c:
    iVar16 = *(int *)(local_150 + 0x50);
    if (iVar16 == 0) {
      pfVar7 = (float *)(local_150 + 0x54);
    }
    else {
      uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
      if (iVar16 <= (int)uVar15) {
        uVar15 = iVar16 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar15 * 4);
    }
  }
  lVar8 = local_100;
  *pfVar7 = fVar27 - fVar23 * fVar26;
  (**(code **)(param_3 + 0x3c0))(local_100 + 0x30,local_f8 + 0x30,*(undefined4 *)(param_3 + 900),10)
  ;
  uVar13 = *(uint *)(param_3 + 900);
  lVar10 = *(longlong *)(lVar8 + 0x30);
  uVar15 = uVar13;
  if (lVar10 == 0) {
    if (*(code **)(lVar8 + 0x40) != (code *)0x0) {
      (**(code **)(lVar8 + 0x40))(*(undefined4 *)(lVar8 + 0x48));
      uVar15 = *(uint *)(param_3 + 900);
    }
    lVar10 = *(longlong *)(local_100 + 0x30);
    if (lVar10 != 0) goto LAB_18003edd0;
    pfVar7 = (float *)(local_100 + 0x54);
  }
  else {
LAB_18003edd0:
    iVar16 = *(int *)(local_100 + 0x50);
    if (iVar16 == 0) {
      pfVar7 = (float *)(local_100 + 0x54);
    }
    else {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar16 <= (int)uVar13) {
        uVar13 = iVar16 - 1;
      }
      pfVar7 = (float *)(lVar10 + (longlong)(int)uVar13 * 4);
    }
  }
  if ((*(longlong *)(local_f8 + 0x30) == 0) && (*(code **)(local_f8 + 0x40) != (code *)0x0)) {
    (**(code **)(local_f8 + 0x40))(*(undefined4 *)(local_f8 + 0x48));
    uVar15 = *(uint *)(param_3 + 900);
  }
  fVar23 = FUN_180026608(lVar20);
  fVar26 = *extraout_x11_00;
  fVar27 = *pfVar7;
  lVar8 = *(longlong *)(local_148 + 0x30);
  if (lVar8 == 0) {
    if (*(code **)(local_148 + 0x40) != (code *)0x0) {
      (**(code **)(local_148 + 0x40))(*(undefined4 *)(local_148 + 0x48));
    }
    lVar8 = *(longlong *)(local_148 + 0x30);
    if (lVar8 != 0) goto LAB_18003eea4;
    pfVar7 = (float *)(local_148 + 0x54);
  }
  else {
LAB_18003eea4:
    iVar16 = *(int *)(local_148 + 0x50);
    if (iVar16 == 0) {
      pfVar7 = (float *)(local_148 + 0x54);
    }
    else {
      uVar15 = uVar15 & ((int)uVar15 >> 0x1f ^ 0xffffffffU);
      if (iVar16 <= (int)uVar15) {
        uVar15 = iVar16 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar15 * 4);
    }
  }
  *pfVar7 = fVar23 * fVar26 + fVar27;
  iVar16 = *(int *)(param_3 + 900);
  uVar13 = iVar16 - 1;
  lVar8 = *(longlong *)(local_180 + 0x30);
  if (lVar8 == 0) {
    if (*(code **)(local_180 + 0x40) != (code *)0x0) {
      (**(code **)(local_180 + 0x40))(*(undefined4 *)(local_180 + 0x48));
      iVar16 = *(int *)(param_3 + 900);
    }
    lVar8 = *(longlong *)(local_180 + 0x30);
    if (lVar8 != 0) goto LAB_18003ef1c;
    pfVar7 = (float *)(local_180 + 0x54);
    plVar9 = (longlong *)(local_b0 + 0x30);
LAB_18003ef58:
    if ((code *)plVar9[2] != (code *)0x0) {
      (*(code *)plVar9[2])((int)plVar9[3]);
    }
    lVar8 = *plVar9;
    if (lVar8 != 0) goto LAB_18003ef78;
    pfVar11 = (float *)((longlong)plVar9 + 0x24);
  }
  else {
LAB_18003ef1c:
    iVar14 = *(int *)(local_180 + 0x50);
    if (iVar14 == 0) {
      pfVar7 = (float *)(local_180 + 0x54);
    }
    else {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar14 <= (int)uVar13) {
        uVar13 = iVar14 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar13 * 4);
    }
    plVar9 = (longlong *)(local_180 + 0x30);
    if (lVar8 == 0) goto LAB_18003ef58;
LAB_18003ef78:
    iVar14 = (int)plVar9[4];
    if (iVar14 == 0) {
      pfVar11 = (float *)((longlong)plVar9 + 0x24);
    }
    else {
      if (iVar16 < 0) {
        iVar16 = 0;
      }
      if (iVar14 <= iVar16) {
        iVar16 = iVar14 + -1;
      }
      pfVar11 = (float *)(lVar8 + (longlong)iVar16 * 4);
    }
  }
  if (*pfVar11 <= *pfVar7) {
LAB_18003f0c0:
    uVar13 = *(uint *)(param_3 + 900);
    lVar8 = plVar18[6];
    if (lVar8 == 0) {
      if ((code *)plVar18[8] != (code *)0x0) {
        (*(code *)plVar18[8])((int)plVar18[9]);
      }
      lVar8 = plVar18[6];
      if (lVar8 != 0) goto LAB_18003f0ec;
      puVar12 = (undefined4 *)((longlong)plVar18 + 0x54);
    }
    else {
LAB_18003f0ec:
      iVar16 = (int)plVar18[10];
      if (iVar16 == 0) {
        puVar12 = (undefined4 *)((longlong)plVar18 + 0x54);
      }
      else {
        uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
        if (iVar16 <= (int)uVar13) {
          uVar13 = iVar16 - 1;
        }
        puVar12 = (undefined4 *)(lVar8 + (longlong)(int)uVar13 * 4);
      }
    }
    *puVar12 = 0;
  }
  else {
    iVar16 = *(int *)(param_3 + 900);
    uVar13 = iVar16 - 1;
    if (lVar8 == 0) {
      if (*(code **)(local_180 + 0x40) != (code *)0x0) {
        (**(code **)(local_180 + 0x40))(*(undefined4 *)(local_180 + 0x48));
        iVar16 = *(int *)(param_3 + 900);
      }
      lVar8 = *(longlong *)(local_180 + 0x30);
      if (lVar8 != 0) goto LAB_18003efec;
      pfVar7 = (float *)(local_180 + 0x54);
    }
    else {
LAB_18003efec:
      iVar14 = *(int *)(local_180 + 0x50);
      if (iVar14 == 0) {
        pfVar7 = (float *)(local_180 + 0x54);
      }
      else {
        uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
        if (iVar14 <= (int)uVar13) {
          uVar13 = iVar14 - 1;
        }
        pfVar7 = (float *)(lVar8 + (longlong)(int)uVar13 * 4);
      }
    }
    lVar8 = *(longlong *)(local_150 + 0x30);
    if (lVar8 == 0) {
      if (*(code **)(local_150 + 0x40) != (code *)0x0) {
        (**(code **)(local_150 + 0x40))(*(undefined4 *)(local_150 + 0x48));
      }
      lVar8 = *(longlong *)(local_150 + 0x30);
      if (lVar8 != 0) goto LAB_18003f048;
      pfVar11 = (float *)(local_150 + 0x54);
    }
    else {
LAB_18003f048:
      iVar14 = *(int *)(local_150 + 0x50);
      if (iVar14 == 0) {
        pfVar11 = (float *)(local_150 + 0x54);
      }
      else {
        uVar13 = iVar16 - 1U & ((int)(iVar16 - 1U) >> 0x1f ^ 0xffffffffU);
        if (iVar14 <= (int)uVar13) {
          uVar13 = iVar14 - 1;
        }
        pfVar11 = (float *)(lVar8 + (longlong)(int)uVar13 * 4);
      }
    }
    if (*pfVar11 <= *pfVar7) goto LAB_18003f0c0;
    FUN_180005d08((longlong *)(param_3 + 0xa88),*(int *)(param_3 + 900));
    uVar6 = FUN_180026708((longlong)local_e0);
    fVar26 = *(float *)(param_3 + 0x698);
    fVar27 = *extraout_x11_01;
    pfVar7 = (float *)FUN_18000f448((longlong)plVar18,*(int *)(param_3 + 900));
    *pfVar7 = fVar27 - (float)(int)uVar6 * fVar26;
  }
  uVar13 = *(uint *)(param_3 + 900);
  lVar8 = *(longlong *)(local_180 + 0x30);
  uVar15 = uVar13;
  if (lVar8 == 0) {
    if (*(code **)(local_180 + 0x40) != (code *)0x0) {
      (**(code **)(local_180 + 0x40))(*(undefined4 *)(local_180 + 0x48));
      uVar15 = *(uint *)(param_3 + 900);
    }
    lVar8 = *(longlong *)(local_180 + 0x30);
    if (lVar8 != 0) goto LAB_18003f164;
    pfVar7 = (float *)(local_180 + 0x54);
    iVar16 = uVar15 - 1;
    plVar18 = (longlong *)(local_b0 + 0x30);
LAB_18003f1a4:
    if ((code *)plVar18[2] != (code *)0x0) {
      (*(code *)plVar18[2])((int)plVar18[3]);
    }
    lVar8 = *plVar18;
    if (lVar8 != 0) goto LAB_18003f1c4;
    pfVar11 = (float *)((longlong)plVar18 + 0x24);
  }
  else {
LAB_18003f164:
    iVar16 = *(int *)(local_180 + 0x50);
    if (iVar16 == 0) {
      pfVar7 = (float *)(local_180 + 0x54);
    }
    else {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar16 <= (int)uVar13) {
        uVar13 = iVar16 - 1;
      }
      pfVar7 = (float *)(lVar8 + (longlong)(int)uVar13 * 4);
    }
    iVar16 = uVar15 - 1;
    plVar18 = (longlong *)(local_180 + 0x30);
    if (lVar8 == 0) goto LAB_18003f1a4;
LAB_18003f1c4:
    iVar14 = (int)plVar18[4];
    if (iVar14 == 0) {
      pfVar11 = (float *)((longlong)plVar18 + 0x24);
    }
    else {
      if (iVar16 < 0) {
        iVar16 = 0;
      }
      if (iVar14 <= iVar16) {
        iVar16 = iVar14 + -1;
      }
      pfVar11 = (float *)(lVar8 + (longlong)iVar16 * 4);
    }
  }
  if (*pfVar7 < *pfVar11) {
    iVar16 = *(int *)(param_3 + 900);
    uVar13 = iVar16 - 1;
    if (lVar8 == 0) {
      if (*(code **)(local_180 + 0x40) != (code *)0x0) {
        (**(code **)(local_180 + 0x40))(*(undefined4 *)(local_180 + 0x48));
        iVar16 = *(int *)(param_3 + 900);
      }
      lVar8 = *(longlong *)(local_180 + 0x30);
      if (lVar8 != 0) goto LAB_18003f238;
      pfVar7 = (float *)(local_180 + 0x54);
    }
    else {
LAB_18003f238:
      iVar14 = *(int *)(local_180 + 0x50);
      if (iVar14 == 0) {
        pfVar7 = (float *)(local_180 + 0x54);
      }
      else {
        uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
        if (iVar14 <= (int)uVar13) {
          uVar13 = iVar14 - 1;
        }
        pfVar7 = (float *)(lVar8 + (longlong)(int)uVar13 * 4);
      }
    }
    lVar8 = *(longlong *)(local_148 + 0x30);
    if (lVar8 == 0) {
      if (*(code **)(local_148 + 0x40) != (code *)0x0) {
        (**(code **)(local_148 + 0x40))(*(undefined4 *)(local_148 + 0x48));
      }
      lVar8 = *(longlong *)(local_148 + 0x30);
      if (lVar8 != 0) goto LAB_18003f294;
      pfVar11 = (float *)(local_148 + 0x54);
    }
    else {
LAB_18003f294:
      iVar14 = *(int *)(local_148 + 0x50);
      if (iVar14 == 0) {
        pfVar11 = (float *)(local_148 + 0x54);
      }
      else {
        uVar13 = iVar16 - 1U & ((int)(iVar16 - 1U) >> 0x1f ^ 0xffffffffU);
        if (iVar14 <= (int)uVar13) {
          uVar13 = iVar14 - 1;
        }
        pfVar11 = (float *)(lVar8 + (longlong)(int)uVar13 * 4);
      }
    }
    if (*pfVar11 < *pfVar7) {
      FUN_180005d08((longlong *)(param_3 + 0xa60),*(int *)(param_3 + 900));
      uVar6 = FUN_180026708((longlong)local_e0);
      fVar26 = *(float *)(param_3 + 0x698);
      fVar27 = *extraout_x11_02;
      pfVar7 = (float *)FUN_18000f448((longlong)local_140,*(int *)(param_3 + 900));
      *pfVar7 = (float)(int)uVar6 * fVar26 + fVar27;
      return;
    }
  }
  uVar13 = *(uint *)(param_3 + 900);
  lVar8 = local_140[6];
  if (lVar8 == 0) {
    if ((code *)local_140[8] != (code *)0x0) {
      (*(code *)local_140[8])((int)local_140[9]);
    }
    lVar8 = local_140[6];
    if (lVar8 != 0) goto LAB_18003f340;
  }
  else {
LAB_18003f340:
    iVar16 = (int)local_140[10];
    if (iVar16 != 0) {
      uVar13 = uVar13 & ((int)uVar13 >> 0x1f ^ 0xffffffffU);
      if (iVar16 <= (int)uVar13) {
        uVar13 = iVar16 - 1;
      }
      puVar12 = (undefined4 *)(lVar8 + (longlong)(int)uVar13 * 4);
      goto LAB_18003f360;
    }
  }
  puVar12 = (undefined4 *)((longlong)local_140 + 0x54);
LAB_18003f360:
  *puVar12 = 0;
  return;
}

