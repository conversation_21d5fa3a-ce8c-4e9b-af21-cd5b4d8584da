
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_ShowHideStudy(undefined1 param_1 [16],undefined8 param_2,longlong param_3,
                       undefined8 param_4,undefined8 param_5,undefined8 param_6,undefined8 param_7,
                       undefined8 param_8,undefined8 param_9,undefined8 param_10)

{
  bool bVar1;
  int iVar2;
  uint uVar3;
  HANDLE pvVar4;
  undefined8 uVar5;
  ulonglong uVar6;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  longlong lVar7;
  int iVar8;
  code *extraout_x11;
  code *extraout_x11_00;
  code *pcVar9;
  code *extraout_x11_01;
  code *extraout_x11_02;
  longlong extraout_x11_03;
  longlong extraout_x11_04;
  longlong extraout_x11_05;
  longlong lVar10;
  longlong extraout_x12;
  code *extraout_x12_00;
  longlong extraout_x13;
  longlong extraout_x14;
  longlong extraout_x14_00;
  longlong extraout_x15;
  longlong *plVar11;
  longlong *plVar12;
  longlong *plVar13;
  longlong *plVar14;
  longlong *plVar15;
  int *lpMem;
  longlong *plVar16;
  char *pcVar17;
  undefined1 extraout_q0 [16];
  undefined1 auVar18 [16];
  undefined1 extraout_q0_00 [16];
  undefined1 extraout_q0_01 [16];
  char local_d0;
  char *local_c8;
  undefined8 local_c0;
  char *local_b8;
  longlong *local_b0;
  int *local_a8;
  char *local_a0;
  undefined8 uStack_98;
  undefined1 *local_90;
  undefined8 uStack_88;
  undefined1 *local_80;
  
                    /* 0x8f928  25  scsf_ShowHideStudy */
  local_a0 = "";
  uStack_98 = 0xfffffffffffffffe;
  local_80 = &DAT_1800d4ecd;
  local_90 = &DAT_1800d4ecd;
  uStack_88 = 0;
  local_a8 = (int *)(**(code **)(param_3 + 0x18b8))(0);
  lVar7 = *(longlong *)(param_3 + 0x210);
  uVar5 = extraout_x1;
  auVar18 = extraout_q0;
  if (lVar7 == 0) {
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar18 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar5 = extraout_x1_00;
    }
    lVar7 = *(longlong *)(param_3 + 0x210);
    if (lVar7 != 0) goto LAB_18008f9bc;
    plVar12 = (longlong *)(param_3 + 0x238);
LAB_18008f9e8:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar18 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar5 = extraout_x1_01;
    }
    lVar7 = *(longlong *)(param_3 + 0x210);
    if (lVar7 != 0) goto LAB_18008fa10;
    plVar13 = (longlong *)(param_3 + 0x238);
LAB_18008fa40:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar18 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar5 = extraout_x1_02;
    }
    lVar7 = *(longlong *)(param_3 + 0x210);
    if (lVar7 != 0) goto LAB_18008fa68;
    plVar14 = (longlong *)(param_3 + 0x238);
LAB_18008fa98:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar18 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar5 = extraout_x1_03;
    }
    lVar7 = *(longlong *)(param_3 + 0x210);
    if (lVar7 != 0) goto LAB_18008fac0;
    plVar15 = (longlong *)(param_3 + 0x238);
LAB_18008faf0:
    if (*(code **)(param_3 + 0x220) != (code *)0x0) {
      auVar18 = (**(code **)(param_3 + 0x220))(*(undefined4 *)(param_3 + 0x228));
      uVar5 = extraout_x1_04;
    }
    lVar7 = *(longlong *)(param_3 + 0x210);
    if (lVar7 != 0) goto LAB_18008fb10;
    plVar11 = (longlong *)(param_3 + 0x238);
  }
  else {
LAB_18008f9bc:
    iVar2 = *(int *)(param_3 + 0x230);
    if (iVar2 == 0) {
      plVar12 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar8 = 0;
      if (iVar2 < 1) {
        iVar8 = iVar2 + -1;
      }
      plVar12 = (longlong *)(lVar7 + (longlong)iVar8 * 0x98);
    }
    if (lVar7 == 0) goto LAB_18008f9e8;
LAB_18008fa10:
    iVar2 = *(int *)(param_3 + 0x230);
    if (iVar2 == 0) {
      plVar13 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar8 = 1;
      if (iVar2 < 2) {
        iVar8 = iVar2 + -1;
      }
      plVar13 = (longlong *)(lVar7 + (longlong)iVar8 * 0x98);
    }
    if (lVar7 == 0) goto LAB_18008fa40;
LAB_18008fa68:
    iVar2 = *(int *)(param_3 + 0x230);
    if (iVar2 == 0) {
      plVar14 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar8 = 2;
      if (iVar2 < 3) {
        iVar8 = iVar2 + -1;
      }
      plVar14 = (longlong *)(lVar7 + (longlong)iVar8 * 0x98);
    }
    if (lVar7 == 0) goto LAB_18008fa98;
LAB_18008fac0:
    iVar2 = *(int *)(param_3 + 0x230);
    if (iVar2 == 0) {
      plVar15 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar8 = 3;
      if (iVar2 < 4) {
        iVar8 = iVar2 + -1;
      }
      plVar15 = (longlong *)(lVar7 + (longlong)iVar8 * 0x98);
    }
    if (lVar7 == 0) goto LAB_18008faf0;
LAB_18008fb10:
    iVar2 = *(int *)(param_3 + 0x230);
    if (iVar2 == 0) {
      plVar11 = (longlong *)(param_3 + 0x238);
    }
    else {
      iVar8 = 4;
      if (iVar2 < 5) {
        iVar8 = iVar2 + -1;
      }
      plVar11 = (longlong *)(lVar7 + (longlong)iVar8 * 0x98);
    }
  }
  local_b0 = plVar12;
  if (*(int *)(param_3 + 0xac) != 0) {
    FUN_1800079f8((longlong *)(param_3 + 0x118),0x1800db920,0xf);
    *(undefined4 *)(param_3 + 0x124) = 1;
    if ((*(char **)(param_3 + 0x338) != (char *)0x0) && (**(char **)(param_3 + 0x338) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0x338),(longlong)local_a0,0);
      *(undefined4 *)(param_3 + 0x344) = 1;
    }
    plVar16 = (longlong *)local_a0;
    local_c8 = local_a0;
    local_c0 = 0;
    local_b8 = local_a0;
    pvVar4 = GetProcessHeap();
    local_b0 = (longlong *)HeapAlloc(pvVar4,0,0x15);
    bVar1 = local_b0 == (longlong *)0x0;
    if (bVar1) {
      local_c8 = (char *)plVar16;
    }
    else {
      param_6 = 0x14;
      *(char *)((longlong)local_b0 + 8) = '\0';
      *(char *)((longlong)local_b0 + 9) = '\0';
      *(char *)((longlong)local_b0 + 10) = '\0';
      *(char *)((longlong)local_b0 + 0xb) = '\0';
      *(char *)((longlong)local_b0 + 0xc) = '\0';
      *(char *)((longlong)local_b0 + 0xd) = '\0';
      *(char *)((longlong)local_b0 + 0xe) = '\0';
      *(char *)((longlong)local_b0 + 0xf) = '\0';
      *(char *)((longlong)local_b0 + 0) = '\0';
      *(char *)((longlong)local_b0 + 1) = '\0';
      *(char *)((longlong)local_b0 + 2) = '\0';
      *(char *)((longlong)local_b0 + 3) = '\0';
      *(char *)((longlong)local_b0 + 4) = '\0';
      *(char *)((longlong)local_b0 + 5) = '\0';
      *(char *)((longlong)local_b0 + 6) = '\0';
      *(char *)((longlong)local_b0 + 7) = '\0';
      *(char *)((longlong)local_b0 + 0x10) = '\0';
      *(char *)((longlong)local_b0 + 0x11) = '\0';
      *(char *)((longlong)local_b0 + 0x12) = '\0';
      *(char *)((longlong)local_b0 + 0x13) = '\0';
      *(char *)((longlong)local_b0 + 0x14) = '\0';
      local_c8 = (char *)local_b0;
      FUN_180099d78((char *)local_b0,0x15,0x1800d6c40,0x14);
      local_c0 = 0x100000001;
      plVar16 = local_b0;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_c8);
    if ((!bVar1) && (plVar16 != (longlong *)0x0)) {
      pvVar4 = GetProcessHeap();
      HeapFree(pvVar4,0,plVar16);
    }
    lpMem = (int *)local_a0;
    local_c8 = local_a0;
    local_c0 = 0;
    local_b8 = local_a0;
    pvVar4 = GetProcessHeap();
    local_a8 = (int *)HeapAlloc(pvVar4,0,0x8d);
    bVar1 = local_a8 == (int *)0x0;
    if (bVar1) {
      local_c8 = (char *)lpMem;
    }
    else {
      param_6 = 0x8c;
      *(char *)((longlong)local_a8 + 8) = '\0';
      *(char *)((longlong)local_a8 + 9) = '\0';
      *(char *)((longlong)local_a8 + 10) = '\0';
      *(char *)((longlong)local_a8 + 0xb) = '\0';
      *(char *)((longlong)local_a8 + 0xc) = '\0';
      *(char *)((longlong)local_a8 + 0xd) = '\0';
      *(char *)((longlong)local_a8 + 0xe) = '\0';
      *(char *)((longlong)local_a8 + 0xf) = '\0';
      *(char *)((longlong)local_a8 + 0) = '\0';
      *(char *)((longlong)local_a8 + 1) = '\0';
      *(char *)((longlong)local_a8 + 2) = '\0';
      *(char *)((longlong)local_a8 + 3) = '\0';
      *(char *)((longlong)local_a8 + 4) = '\0';
      *(char *)((longlong)local_a8 + 5) = '\0';
      *(char *)((longlong)local_a8 + 6) = '\0';
      *(char *)((longlong)local_a8 + 7) = '\0';
      *(char *)((longlong)local_a8 + 0x18) = '\0';
      *(char *)((longlong)local_a8 + 0x19) = '\0';
      *(char *)((longlong)local_a8 + 0x1a) = '\0';
      *(char *)((longlong)local_a8 + 0x1b) = '\0';
      *(char *)((longlong)local_a8 + 0x1c) = '\0';
      *(char *)((longlong)local_a8 + 0x1d) = '\0';
      *(char *)((longlong)local_a8 + 0x1e) = '\0';
      *(char *)((longlong)local_a8 + 0x1f) = '\0';
      *(char *)((longlong)local_a8 + 0x10) = '\0';
      *(char *)((longlong)local_a8 + 0x11) = '\0';
      *(char *)((longlong)local_a8 + 0x12) = '\0';
      *(char *)((longlong)local_a8 + 0x13) = '\0';
      *(char *)((longlong)local_a8 + 0x14) = '\0';
      *(char *)((longlong)local_a8 + 0x15) = '\0';
      *(char *)((longlong)local_a8 + 0x16) = '\0';
      *(char *)((longlong)local_a8 + 0x17) = '\0';
      *(char *)((longlong)local_a8 + 0x28) = '\0';
      *(char *)((longlong)local_a8 + 0x29) = '\0';
      *(char *)((longlong)local_a8 + 0x2a) = '\0';
      *(char *)((longlong)local_a8 + 0x2b) = '\0';
      *(char *)((longlong)local_a8 + 0x2c) = '\0';
      *(char *)((longlong)local_a8 + 0x2d) = '\0';
      *(char *)((longlong)local_a8 + 0x2e) = '\0';
      *(char *)((longlong)local_a8 + 0x2f) = '\0';
      *(char *)((longlong)local_a8 + 0x20) = '\0';
      *(char *)((longlong)local_a8 + 0x21) = '\0';
      *(char *)((longlong)local_a8 + 0x22) = '\0';
      *(char *)((longlong)local_a8 + 0x23) = '\0';
      *(char *)((longlong)local_a8 + 0x24) = '\0';
      *(char *)((longlong)local_a8 + 0x25) = '\0';
      *(char *)((longlong)local_a8 + 0x26) = '\0';
      *(char *)((longlong)local_a8 + 0x27) = '\0';
      *(char *)((longlong)local_a8 + 0x38) = '\0';
      *(char *)((longlong)local_a8 + 0x39) = '\0';
      *(char *)((longlong)local_a8 + 0x3a) = '\0';
      *(char *)((longlong)local_a8 + 0x3b) = '\0';
      *(char *)((longlong)local_a8 + 0x3c) = '\0';
      *(char *)((longlong)local_a8 + 0x3d) = '\0';
      *(char *)((longlong)local_a8 + 0x3e) = '\0';
      *(char *)((longlong)local_a8 + 0x3f) = '\0';
      *(char *)((longlong)local_a8 + 0x30) = '\0';
      *(char *)((longlong)local_a8 + 0x31) = '\0';
      *(char *)((longlong)local_a8 + 0x32) = '\0';
      *(char *)((longlong)local_a8 + 0x33) = '\0';
      *(char *)((longlong)local_a8 + 0x34) = '\0';
      *(char *)((longlong)local_a8 + 0x35) = '\0';
      *(char *)((longlong)local_a8 + 0x36) = '\0';
      *(char *)((longlong)local_a8 + 0x37) = '\0';
      *(char *)((longlong)local_a8 + 0x48) = '\0';
      *(char *)((longlong)local_a8 + 0x49) = '\0';
      *(char *)((longlong)local_a8 + 0x4a) = '\0';
      *(char *)((longlong)local_a8 + 0x4b) = '\0';
      *(char *)((longlong)local_a8 + 0x4c) = '\0';
      *(char *)((longlong)local_a8 + 0x4d) = '\0';
      *(char *)((longlong)local_a8 + 0x4e) = '\0';
      *(char *)((longlong)local_a8 + 0x4f) = '\0';
      *(char *)((longlong)local_a8 + 0x40) = '\0';
      *(char *)((longlong)local_a8 + 0x41) = '\0';
      *(char *)((longlong)local_a8 + 0x42) = '\0';
      *(char *)((longlong)local_a8 + 0x43) = '\0';
      *(char *)((longlong)local_a8 + 0x44) = '\0';
      *(char *)((longlong)local_a8 + 0x45) = '\0';
      *(char *)((longlong)local_a8 + 0x46) = '\0';
      *(char *)((longlong)local_a8 + 0x47) = '\0';
      *(char *)((longlong)local_a8 + 0x58) = '\0';
      *(char *)((longlong)local_a8 + 0x59) = '\0';
      *(char *)((longlong)local_a8 + 0x5a) = '\0';
      *(char *)((longlong)local_a8 + 0x5b) = '\0';
      *(char *)((longlong)local_a8 + 0x5c) = '\0';
      *(char *)((longlong)local_a8 + 0x5d) = '\0';
      *(char *)((longlong)local_a8 + 0x5e) = '\0';
      *(char *)((longlong)local_a8 + 0x5f) = '\0';
      *(char *)((longlong)local_a8 + 0x50) = '\0';
      *(char *)((longlong)local_a8 + 0x51) = '\0';
      *(char *)((longlong)local_a8 + 0x52) = '\0';
      *(char *)((longlong)local_a8 + 0x53) = '\0';
      *(char *)((longlong)local_a8 + 0x54) = '\0';
      *(char *)((longlong)local_a8 + 0x55) = '\0';
      *(char *)((longlong)local_a8 + 0x56) = '\0';
      *(char *)((longlong)local_a8 + 0x57) = '\0';
      *(char *)((longlong)local_a8 + 0x68) = '\0';
      *(char *)((longlong)local_a8 + 0x69) = '\0';
      *(char *)((longlong)local_a8 + 0x6a) = '\0';
      *(char *)((longlong)local_a8 + 0x6b) = '\0';
      *(char *)((longlong)local_a8 + 0x6c) = '\0';
      *(char *)((longlong)local_a8 + 0x6d) = '\0';
      *(char *)((longlong)local_a8 + 0x6e) = '\0';
      *(char *)((longlong)local_a8 + 0x6f) = '\0';
      *(char *)((longlong)local_a8 + 0x60) = '\0';
      *(char *)((longlong)local_a8 + 0x61) = '\0';
      *(char *)((longlong)local_a8 + 0x62) = '\0';
      *(char *)((longlong)local_a8 + 99) = '\0';
      *(char *)((longlong)local_a8 + 100) = '\0';
      *(char *)((longlong)local_a8 + 0x65) = '\0';
      *(char *)((longlong)local_a8 + 0x66) = '\0';
      *(char *)((longlong)local_a8 + 0x67) = '\0';
      *(char *)((longlong)local_a8 + 0x78) = '\0';
      *(char *)((longlong)local_a8 + 0x79) = '\0';
      *(char *)((longlong)local_a8 + 0x7a) = '\0';
      *(char *)((longlong)local_a8 + 0x7b) = '\0';
      *(char *)((longlong)local_a8 + 0x7c) = '\0';
      *(char *)((longlong)local_a8 + 0x7d) = '\0';
      *(char *)((longlong)local_a8 + 0x7e) = '\0';
      *(char *)((longlong)local_a8 + 0x7f) = '\0';
      *(char *)((longlong)local_a8 + 0x70) = '\0';
      *(char *)((longlong)local_a8 + 0x71) = '\0';
      *(char *)((longlong)local_a8 + 0x72) = '\0';
      *(char *)((longlong)local_a8 + 0x73) = '\0';
      *(char *)((longlong)local_a8 + 0x74) = '\0';
      *(char *)((longlong)local_a8 + 0x75) = '\0';
      *(char *)((longlong)local_a8 + 0x76) = '\0';
      *(char *)((longlong)local_a8 + 0x77) = '\0';
      *(char *)((longlong)local_a8 + 0x80) = '\0';
      *(char *)((longlong)local_a8 + 0x81) = '\0';
      *(char *)((longlong)local_a8 + 0x82) = '\0';
      *(char *)((longlong)local_a8 + 0x83) = '\0';
      *(char *)((longlong)local_a8 + 0x84) = '\0';
      *(char *)((longlong)local_a8 + 0x85) = '\0';
      *(char *)((longlong)local_a8 + 0x86) = '\0';
      *(char *)((longlong)local_a8 + 0x87) = '\0';
      *(char *)((longlong)local_a8 + 0x88) = '\0';
      *(char *)((longlong)local_a8 + 0x89) = '\0';
      *(char *)((longlong)local_a8 + 0x8a) = '\0';
      *(char *)((longlong)local_a8 + 0x8b) = '\0';
      *(char *)((longlong)local_a8 + 0x8c) = '\0';
      local_c8 = (char *)local_a8;
      FUN_180099d78((char *)local_a8,0x8d,0x1800d6bb0,0x8c);
      local_c0 = 0x100000001;
      lpMem = local_a8;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_c8);
    auVar18 = extraout_q0_00;
    if ((!bVar1) && (lpMem != (int *)0x0)) {
      pvVar4 = GetProcessHeap();
      HeapFree(pvVar4,0,lpMem);
      local_c8 = (char *)0x0;
      local_c0 = 0;
      auVar18 = extraout_q0_01;
    }
    plVar16 = (longlong *)local_a0;
    pcVar17 = *(char **)(param_3 + 0x118);
    if (*(char **)(param_3 + 0x118) == (char *)0x0) {
      pcVar17 = local_a0;
    }
    FUN_180026368(auVar18,param_2,(undefined8 *)(param_3 + 0x338),0x1800db8a0,pcVar17,param_6,
                  param_7,param_8,param_9,param_10);
    local_c8 = (char *)plVar16;
    local_c0 = 0;
    local_b8 = (char *)plVar16;
    pvVar4 = GetProcessHeap();
    local_b0 = (longlong *)HeapAlloc(pvVar4,0,7);
    bVar1 = local_b0 == (longlong *)0x0;
    if (bVar1) {
      local_c8 = (char *)plVar16;
    }
    else {
      *(char *)((longlong)local_b0 + 0) = '\0';
      *(char *)((longlong)local_b0 + 1) = '\0';
      *(char *)((longlong)local_b0 + 2) = '\0';
      *(char *)((longlong)local_b0 + 3) = '\0';
      *(char *)((longlong)local_b0 + 4) = '\0';
      *(char *)((longlong)local_b0 + 5) = '\0';
      *(char *)((longlong)local_b0 + 6) = '\0';
      local_c8 = (char *)local_b0;
      FUN_180099d78((char *)local_b0,7,0x1800d6c58,6);
      local_c0 = 0x100000001;
      plVar16 = local_b0;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0x338),(longlong *)&local_c8);
    if ((!bVar1) && (plVar16 != (longlong *)0x0)) {
      pvVar4 = GetProcessHeap();
      HeapFree(pvVar4,0,plVar16);
      local_c8 = (char *)0x0;
      local_c0 = 0;
    }
    *(undefined4 *)(param_3 + 4) = 0;
    *(undefined4 *)(param_3 + 0x10) = 0;
    *(undefined4 *)(param_3 + 0x37c) = 1;
    *(undefined4 *)(param_3 + 0xcb8) = 1;
    *(undefined4 *)(param_3 + 0xb0) = 1;
    FUN_1800079f8(plVar12,0x1800db908,0x14);
    *(undefined4 *)((longlong)plVar12 + 0xc) = 1;
    *(undefined1 *)(plVar12 + 3) = 0xd;
    *(undefined4 *)((longlong)plVar12 + 0x1c) = 0;
    FUN_1800079f8(plVar13,0x1800db988,9);
    *(undefined4 *)((longlong)plVar13 + 0xc) = 1;
    *(undefined1 *)(plVar13 + 3) = 9;
    *(undefined8 *)((longlong)plVar13 + 0x1c) = 0x3fd1555555555555;
    FUN_1800079f8(plVar14,0x1800db998,10);
    *(undefined4 *)((longlong)plVar14 + 0xc) = 1;
    *(undefined1 *)(plVar14 + 3) = 9;
    *(undefined8 *)((longlong)plVar14 + 0x1c) = 0x3fe2aaaaaaaaaaab;
    FUN_1800079f8(plVar15,0x1800db930,0x1c);
    *(undefined4 *)((longlong)plVar15 + 0xc) = 1;
    *(undefined1 *)(plVar15 + 3) = 5;
    *(undefined4 *)((longlong)plVar15 + 0x1c) = 0;
    FUN_1800079f8(plVar11,0x1800db950,0x35);
    *(undefined4 *)((longlong)plVar11 + 0xc) = 1;
    *(undefined1 *)(plVar11 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar11 + 0x3c) = 0x96;
    *(undefined4 *)((longlong)plVar11 + 0x1c) = 0;
    *(undefined4 *)((longlong)plVar11 + 0x2c) = 0;
    return;
  }
  if (*(int *)(param_3 + 900) == 0) {
    uVar5 = FUN_1800254e8(auVar18,param_2,param_3,uVar5,param_5,param_6,param_7,param_8,param_9,
                          param_10);
    *local_a8 = (int)uVar5;
  }
  if (*local_a8 != 0) {
    return;
  }
  iVar2 = (**(code **)(param_3 + 0xfb0))(*(undefined4 *)(param_3 + 0x9c0));
  if (iVar2 == 0) {
    return;
  }
  pcVar17 = *(char **)(param_3 + 0x70);
  local_d0 = pcVar17[1];
  if ((*(int *)(param_3 + 0x11f8) != 0) && (*(int *)(param_3 + 900) == 0)) {
    if (*pcVar17 == '\0') {
      *pcVar17 = '\x01';
      FUN_180026708((longlong)plVar11);
      iVar2 = (*extraout_x11)();
      local_d0 = iVar2 != 0;
      pcVar17[1] = local_d0;
    }
    uVar6 = FUN_180026708((longlong)plVar11);
    (*extraout_x11_00)(uVar6,"Show/Hide Study");
    pcVar9 = *(code **)(param_3 + 0xf38);
    if (((char)plVar12[3] - 0x12U & 0xfc) == 0 && (char)plVar12[3] != '\x13') {
      uVar3 = *(uint *)(plVar12 + 4);
    }
    else {
      uVar3 = FUN_180026550((longlong)plVar12);
      pcVar9 = extraout_x11_01;
    }
    (*pcVar9)(uVar3);
    FUN_180026708((longlong)plVar11);
    (*extraout_x11_02)();
  }
  if (*(int *)(param_3 + 0x6f0) != 0) {
    local_b0 = plVar12;
  }
  iVar2 = FUN_18008f870((longlong)plVar13);
  lVar7 = (longlong)iVar2 * 1000000 + (extraout_x14 / 86400000000) * 86400000000;
  FUN_18008f870((longlong)plVar14);
  bVar1 = FUN_180026690((longlong)plVar15);
  if (bVar1) {
    iVar2 = ((int)(extraout_x12 / 1000000) + (int)(extraout_x12 >> 0x3f)) -
            (SUB164(SEXT816(extraout_x12) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
    if (extraout_x13 <= extraout_x12) {
      iVar2 = 0;
    }
    bVar1 = (iVar2 % 0x3c) % 10 < 5;
    lVar10 = extraout_x11_03;
  }
  else {
    uVar6 = FUN_180026708((longlong)plVar11);
    if ((int)uVar6 == 0) {
      lVar10 = extraout_x11_04;
      if (extraout_x15 < lVar7) {
        bVar1 = lVar7 <= extraout_x14_00 || extraout_x14_00 < extraout_x15;
      }
      else {
        bVar1 = lVar7 <= extraout_x14_00 && extraout_x14_00 < extraout_x15;
      }
    }
    else {
      uVar6 = FUN_180026708((longlong)plVar11);
      lVar10 = extraout_x11_05;
      if (*(int *)(param_3 + 0xdb8) != (int)uVar6) goto LAB_1800900a4;
      bVar1 = *(int *)(param_3 + 0xdbc) == 4;
    }
  }
  pcVar17[1] = bVar1;
LAB_1800900a4:
  if (local_d0 != pcVar17[1]) {
    pcVar9 = *(code **)(param_3 + 0xf38);
    if (((char)local_b0[3] - 0x12U & 0xfc) == 0 && (char)local_b0[3] != '\x13') {
      uVar3 = *(uint *)(local_b0 + 4);
    }
    else {
      uVar3 = FUN_180026550(lVar10);
      pcVar9 = extraout_x12_00;
    }
    (*pcVar9)(uVar3);
    (**(code **)(param_3 + 0x1650))(*(undefined4 *)(param_3 + 0x24));
  }
  return;
}

