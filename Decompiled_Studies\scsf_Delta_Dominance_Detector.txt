
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_Delta_Dominance_Detector
               (undefined1 param_1 [16],undefined8 param_2,int *param_3,undefined8 param_4,
               undefined8 param_5,undefined8 param_6,undefined8 param_7,undefined8 param_8,
               undefined8 param_9,undefined8 param_10)

{
  int iVar1;
  undefined1 auVar2 [16];
  float fVar3;
  undefined1 *puVar4;
  int iVar5;
  uint uVar6;
  HANDLE pvVar7;
  char *pcVar8;
  undefined8 uVar9;
  undefined4 *puVar10;
  int *piVar11;
  float *pfVar12;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  uint uVar13;
  longlong lVar14;
  longlong lVar15;
  longlong *plVar16;
  int iVar17;
  int *piVar18;
  float *extraout_x11;
  float *extraout_x11_00;
  float *extraout_x11_01;
  float *extraout_x11_02;
  int iVar19;
  int iVar20;
  ulonglong uVar21;
  char *lpMem;
  longlong *plVar22;
  int iVar23;
  int iVar24;
  char *pcVar25;
  longlong *plVar26;
  longlong *plVar27;
  longlong *plVar28;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  undefined4 uVar29;
  undefined4 extraout_s0_02;
  float fVar30;
  undefined1 extraout_var [12];
  undefined1 auVar31 [16];
  undefined1 extraout_var_00 [12];
  undefined1 extraout_var_01 [12];
  undefined1 auVar32 [12];
  undefined1 extraout_var_02 [12];
  longlong *local_f0;
  char *local_e8;
  undefined8 local_e0;
  undefined1 *local_d8;
  int *local_d0;
  int *local_c8;
  longlong *local_c0;
  longlong *local_b8;
  int *local_b0;
  int *local_a8;
  int *local_a0;
  undefined8 local_98;
  undefined1 *local_90;
  undefined8 uStack_88;
  undefined1 *local_80;
  
                    /* 0x349d0  6  scsf_Delta_Dominance_Detector */
  local_98 = 0xfffffffffffffffe;
  local_a8 = param_3;
  local_a0 = (int *)(**(code **)(param_3 + 0x62e))(param_1._0_4_,0);
  auVar31._4_12_ = extraout_var;
  auVar31._0_4_ = extraout_s0;
  lVar15 = *(longlong *)(param_3 + 0x140);
  uVar9 = extraout_x1;
  if (lVar15 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      auVar31 = (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar9 = extraout_x1_00;
    }
    lVar15 = *(longlong *)(param_3 + 0x140);
    if (lVar15 != 0) goto LAB_180034a54;
    plVar22 = (longlong *)(param_3 + 0x14a);
LAB_180034a84:
    local_b8 = plVar22;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      auVar31 = (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar9 = extraout_x1_01;
    }
    lVar15 = *(longlong *)(param_3 + 0x140);
    if (lVar15 != 0) goto LAB_180034aac;
    plVar27 = (longlong *)(param_3 + 0x14a);
LAB_180034adc:
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      auVar31 = (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar9 = extraout_x1_02;
    }
    lVar15 = *(longlong *)(param_3 + 0x140);
    if (lVar15 != 0) goto LAB_180034afc;
    plVar28 = (longlong *)(param_3 + 0x14a);
  }
  else {
LAB_180034a54:
    iVar5 = param_3[0x148];
    if (iVar5 == 0) {
      plVar22 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar17 = 0;
      if (iVar5 < 1) {
        iVar17 = iVar5 + -1;
      }
      plVar22 = (longlong *)(lVar15 + (longlong)iVar17 * 0x170);
    }
    local_b8 = plVar22;
    if (lVar15 == 0) goto LAB_180034a84;
LAB_180034aac:
    iVar5 = param_3[0x148];
    if (iVar5 == 0) {
      plVar27 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar17 = 1;
      if (iVar5 < 2) {
        iVar17 = iVar5 + -1;
      }
      plVar27 = (longlong *)(lVar15 + (longlong)iVar17 * 0x170);
    }
    if (lVar15 == 0) goto LAB_180034adc;
LAB_180034afc:
    iVar5 = param_3[0x148];
    if (iVar5 == 0) {
      plVar28 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar17 = 2;
      if (iVar5 < 3) {
        iVar17 = iVar5 + -1;
      }
      plVar28 = (longlong *)(lVar15 + (longlong)iVar17 * 0x170);
    }
  }
  lVar15 = *(longlong *)(param_3 + 0x84);
  if (lVar15 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar31 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar9 = extraout_x1_03;
    }
    lVar15 = *(longlong *)(param_3 + 0x84);
    if (lVar15 != 0) goto LAB_180034b54;
    piVar11 = param_3 + 0x8e;
LAB_180034b88:
    local_d0 = piVar11;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar31 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar9 = extraout_x1_04;
    }
    lVar15 = *(longlong *)(param_3 + 0x84);
    if (lVar15 != 0) goto LAB_180034bb8;
    local_c8 = param_3 + 0x8e;
LAB_180034bf4:
    local_f0 = (longlong *)local_c8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar31 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar9 = extraout_x1_05;
    }
    lVar15 = *(longlong *)(param_3 + 0x84);
    if (lVar15 != 0) goto LAB_180034c20;
    piVar18 = param_3 + 0x8e;
LAB_180034c5c:
    local_b0 = piVar18;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar31 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar9 = extraout_x1_06;
    }
    lVar15 = *(longlong *)(param_3 + 0x84);
    if (lVar15 != 0) goto LAB_180034c7c;
    local_c0 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_180034b54:
    iVar5 = param_3[0x8c];
    if (iVar5 == 0) {
      piVar11 = param_3 + 0x8e;
    }
    else {
      iVar17 = 0;
      if (iVar5 < 1) {
        iVar17 = iVar5 + -1;
      }
      piVar11 = (int *)(lVar15 + (longlong)iVar17 * 0x98);
    }
    local_d0 = piVar11;
    if (lVar15 == 0) goto LAB_180034b88;
LAB_180034bb8:
    iVar5 = param_3[0x8c];
    if (iVar5 == 0) {
      local_f0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar17 = 1;
      if (iVar5 < 2) {
        iVar17 = iVar5 + -1;
      }
      local_f0 = (longlong *)(lVar15 + (longlong)iVar17 * 0x98);
    }
    local_c8 = (int *)local_f0;
    if (lVar15 == 0) goto LAB_180034bf4;
LAB_180034c20:
    iVar5 = param_3[0x8c];
    if (iVar5 == 0) {
      piVar18 = param_3 + 0x8e;
    }
    else {
      iVar17 = 2;
      if (iVar5 < 3) {
        iVar17 = iVar5 + -1;
      }
      piVar18 = (int *)(lVar15 + (longlong)iVar17 * 0x98);
    }
    local_b0 = piVar18;
    if (lVar15 == 0) goto LAB_180034c5c;
LAB_180034c7c:
    iVar5 = param_3[0x8c];
    if (iVar5 == 0) {
      local_c0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar17 = 3;
      if (iVar5 < 4) {
        iVar17 = iVar5 + -1;
      }
      local_c0 = (longlong *)(lVar15 + (longlong)iVar17 * 0x98);
    }
  }
  if (param_3[0x2b] != 0) {
    lpMem = "";
    local_80 = &DAT_1800d4ecd;
    local_90 = &DAT_1800d4ecd;
    uStack_88 = 0;
    FUN_180006050(auVar31,param_2,&local_90,0x1800d7400,param_5,param_6,param_7,param_8,param_9,
                  param_10);
    FUN_1800079f8((longlong *)(param_3 + 0x46),0x1800d7420,0x18);
    param_3[0x49] = 1;
    if ((*(char **)(param_3 + 0xce) != (char *)0x0) && (**(char **)(param_3 + 0xce) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0xce),0x1800d4ecd,0);
      param_3[0xd1] = 1;
    }
    local_e8 = "";
    local_e0 = 0;
    local_d8 = &DAT_1800d4ecd;
    pvVar7 = GetProcessHeap();
    pcVar8 = (char *)HeapAlloc(pvVar7,0,0x15);
    if (pcVar8 == (char *)0x0) {
      local_e8 = "";
      pcVar25 = lpMem;
    }
    else {
      param_6 = 0x14;
      pcVar8[8] = '\0';
      pcVar8[9] = '\0';
      pcVar8[10] = '\0';
      pcVar8[0xb] = '\0';
      pcVar8[0xc] = '\0';
      pcVar8[0xd] = '\0';
      pcVar8[0xe] = '\0';
      pcVar8[0xf] = '\0';
      pcVar8[0] = '\0';
      pcVar8[1] = '\0';
      pcVar8[2] = '\0';
      pcVar8[3] = '\0';
      pcVar8[4] = '\0';
      pcVar8[5] = '\0';
      pcVar8[6] = '\0';
      pcVar8[7] = '\0';
      pcVar8[0x10] = '\0';
      pcVar8[0x11] = '\0';
      pcVar8[0x12] = '\0';
      pcVar8[0x13] = '\0';
      pcVar8[0x14] = '\0';
      local_e8 = pcVar8;
      FUN_180099d78(pcVar8,0x15,0x1800d6c40,0x14);
      local_e0 = 0x100000001;
      pcVar25 = pcVar8;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_e8);
    if ((pcVar8 != (char *)0x0) && (pcVar25 != (char *)0x0)) {
      pvVar7 = GetProcessHeap();
      HeapFree(pvVar7,0,pcVar25);
    }
    local_e8 = "";
    local_e0 = 0;
    local_d8 = &DAT_1800d4ecd;
    pvVar7 = GetProcessHeap();
    pcVar8 = (char *)HeapAlloc(pvVar7,0,0x8d);
    if (pcVar8 == (char *)0x0) {
      local_e8 = "";
      pcVar25 = lpMem;
    }
    else {
      param_6 = 0x8c;
      pcVar8[8] = '\0';
      pcVar8[9] = '\0';
      pcVar8[10] = '\0';
      pcVar8[0xb] = '\0';
      pcVar8[0xc] = '\0';
      pcVar8[0xd] = '\0';
      pcVar8[0xe] = '\0';
      pcVar8[0xf] = '\0';
      pcVar8[0] = '\0';
      pcVar8[1] = '\0';
      pcVar8[2] = '\0';
      pcVar8[3] = '\0';
      pcVar8[4] = '\0';
      pcVar8[5] = '\0';
      pcVar8[6] = '\0';
      pcVar8[7] = '\0';
      pcVar8[0x18] = '\0';
      pcVar8[0x19] = '\0';
      pcVar8[0x1a] = '\0';
      pcVar8[0x1b] = '\0';
      pcVar8[0x1c] = '\0';
      pcVar8[0x1d] = '\0';
      pcVar8[0x1e] = '\0';
      pcVar8[0x1f] = '\0';
      pcVar8[0x10] = '\0';
      pcVar8[0x11] = '\0';
      pcVar8[0x12] = '\0';
      pcVar8[0x13] = '\0';
      pcVar8[0x14] = '\0';
      pcVar8[0x15] = '\0';
      pcVar8[0x16] = '\0';
      pcVar8[0x17] = '\0';
      pcVar8[0x28] = '\0';
      pcVar8[0x29] = '\0';
      pcVar8[0x2a] = '\0';
      pcVar8[0x2b] = '\0';
      pcVar8[0x2c] = '\0';
      pcVar8[0x2d] = '\0';
      pcVar8[0x2e] = '\0';
      pcVar8[0x2f] = '\0';
      pcVar8[0x20] = '\0';
      pcVar8[0x21] = '\0';
      pcVar8[0x22] = '\0';
      pcVar8[0x23] = '\0';
      pcVar8[0x24] = '\0';
      pcVar8[0x25] = '\0';
      pcVar8[0x26] = '\0';
      pcVar8[0x27] = '\0';
      pcVar8[0x38] = '\0';
      pcVar8[0x39] = '\0';
      pcVar8[0x3a] = '\0';
      pcVar8[0x3b] = '\0';
      pcVar8[0x3c] = '\0';
      pcVar8[0x3d] = '\0';
      pcVar8[0x3e] = '\0';
      pcVar8[0x3f] = '\0';
      pcVar8[0x30] = '\0';
      pcVar8[0x31] = '\0';
      pcVar8[0x32] = '\0';
      pcVar8[0x33] = '\0';
      pcVar8[0x34] = '\0';
      pcVar8[0x35] = '\0';
      pcVar8[0x36] = '\0';
      pcVar8[0x37] = '\0';
      pcVar8[0x48] = '\0';
      pcVar8[0x49] = '\0';
      pcVar8[0x4a] = '\0';
      pcVar8[0x4b] = '\0';
      pcVar8[0x4c] = '\0';
      pcVar8[0x4d] = '\0';
      pcVar8[0x4e] = '\0';
      pcVar8[0x4f] = '\0';
      pcVar8[0x40] = '\0';
      pcVar8[0x41] = '\0';
      pcVar8[0x42] = '\0';
      pcVar8[0x43] = '\0';
      pcVar8[0x44] = '\0';
      pcVar8[0x45] = '\0';
      pcVar8[0x46] = '\0';
      pcVar8[0x47] = '\0';
      pcVar8[0x58] = '\0';
      pcVar8[0x59] = '\0';
      pcVar8[0x5a] = '\0';
      pcVar8[0x5b] = '\0';
      pcVar8[0x5c] = '\0';
      pcVar8[0x5d] = '\0';
      pcVar8[0x5e] = '\0';
      pcVar8[0x5f] = '\0';
      pcVar8[0x50] = '\0';
      pcVar8[0x51] = '\0';
      pcVar8[0x52] = '\0';
      pcVar8[0x53] = '\0';
      pcVar8[0x54] = '\0';
      pcVar8[0x55] = '\0';
      pcVar8[0x56] = '\0';
      pcVar8[0x57] = '\0';
      pcVar8[0x68] = '\0';
      pcVar8[0x69] = '\0';
      pcVar8[0x6a] = '\0';
      pcVar8[0x6b] = '\0';
      pcVar8[0x6c] = '\0';
      pcVar8[0x6d] = '\0';
      pcVar8[0x6e] = '\0';
      pcVar8[0x6f] = '\0';
      pcVar8[0x60] = '\0';
      pcVar8[0x61] = '\0';
      pcVar8[0x62] = '\0';
      pcVar8[99] = '\0';
      pcVar8[100] = '\0';
      pcVar8[0x65] = '\0';
      pcVar8[0x66] = '\0';
      pcVar8[0x67] = '\0';
      pcVar8[0x78] = '\0';
      pcVar8[0x79] = '\0';
      pcVar8[0x7a] = '\0';
      pcVar8[0x7b] = '\0';
      pcVar8[0x7c] = '\0';
      pcVar8[0x7d] = '\0';
      pcVar8[0x7e] = '\0';
      pcVar8[0x7f] = '\0';
      pcVar8[0x70] = '\0';
      pcVar8[0x71] = '\0';
      pcVar8[0x72] = '\0';
      pcVar8[0x73] = '\0';
      pcVar8[0x74] = '\0';
      pcVar8[0x75] = '\0';
      pcVar8[0x76] = '\0';
      pcVar8[0x77] = '\0';
      pcVar8[0x80] = '\0';
      pcVar8[0x81] = '\0';
      pcVar8[0x82] = '\0';
      pcVar8[0x83] = '\0';
      pcVar8[0x84] = '\0';
      pcVar8[0x85] = '\0';
      pcVar8[0x86] = '\0';
      pcVar8[0x87] = '\0';
      pcVar8[0x88] = '\0';
      pcVar8[0x89] = '\0';
      pcVar8[0x8a] = '\0';
      pcVar8[0x8b] = '\0';
      pcVar8[0x8c] = '\0';
      local_e8 = pcVar8;
      FUN_180099d78(pcVar8,0x8d,0x1800d6bb0,0x8c);
      local_e0 = 0x100000001;
      pcVar25 = pcVar8;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_e8);
    uVar29 = extraout_s0_00;
    auVar32 = extraout_var_00;
    if ((pcVar8 != (char *)0x0) && (pcVar25 != (char *)0x0)) {
      pvVar7 = GetProcessHeap();
      HeapFree(pvVar7,0,pcVar25);
      local_e8 = (char *)0x0;
      local_e0 = 0;
      uVar29 = extraout_s0_01;
      auVar32 = extraout_var_01;
    }
    pcVar8 = *(char **)(param_3 + 0x46);
    if (*(char **)(param_3 + 0x46) == (char *)0x0) {
      pcVar8 = lpMem;
    }
    auVar2._4_12_ = auVar32;
    auVar2._0_4_ = uVar29;
    FUN_180026368(auVar2,param_2,(undefined8 *)(param_3 + 0xce),0x1800d6c60,pcVar8,param_6,param_7,
                  param_8,param_9,param_10);
    local_e8 = "";
    local_e0 = 0;
    local_d8 = &DAT_1800d4ecd;
    pvVar7 = GetProcessHeap();
    pcVar8 = (char *)HeapAlloc(pvVar7,0,7);
    if (pcVar8 == (char *)0x0) {
      local_e8 = "";
    }
    else {
      pcVar8[0] = '\0';
      pcVar8[1] = '\0';
      pcVar8[2] = '\0';
      pcVar8[3] = '\0';
      pcVar8[4] = '\0';
      pcVar8[5] = '\0';
      pcVar8[6] = '\0';
      local_e8 = pcVar8;
      FUN_180099d78(pcVar8,7,0x1800d6c58,6);
      local_e0 = 0x100000001;
      lpMem = pcVar8;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_e8);
    if ((pcVar8 != (char *)0x0) && (lpMem != (char *)0x0)) {
      pvVar7 = GetProcessHeap();
      HeapFree(pvVar7,0,lpMem);
      local_e8 = (char *)0x0;
      local_e0 = 0;
    }
    param_3[1] = 0;
    param_3[4] = 0;
    param_3[0x32e] = 0;
    param_3[0xdf] = 1;
    param_3[0x276] = 1;
    FUN_1800079f8(plVar22,0x1800d7420,0x18);
    *(int *)((longlong)local_b8 + 0xc) = 1;
    *(undefined2 *)((longlong)local_b8 + 0x24) = 0x12;
    local_b8[3] = 0xff8f20000000ff;
    *(int *)(local_b8 + 4) = 1;
    FUN_1800079f8(plVar27,0x1800d7410,0xf);
    *(undefined4 *)((longlong)plVar27 + 0xc) = 1;
    *(undefined4 *)(plVar27 + 3) = 0xf0a60d;
    *(undefined2 *)((longlong)plVar27 + 0x24) = 5;
    *(undefined2 *)(plVar27 + 5) = 1;
    FUN_1800079f8(plVar28,0x1800d7478,0x10);
    *(undefined4 *)((longlong)plVar28 + 0xc) = 1;
    *(undefined4 *)(plVar28 + 3) = 0x221f0;
    *(undefined2 *)((longlong)plVar28 + 0x24) = 5;
    *(undefined2 *)(plVar28 + 5) = 1;
    local_d0[7] = 3;
    *(undefined1 *)(local_d0 + 6) = 0xb;
    *(undefined1 *)((longlong)local_f0 + 0x18) = 2;
    *(int *)((longlong)local_f0 + 0x1c) = 0x40000000;
    if (*(code **)(piVar18 + 0x14) != (code *)0x0) {
      (**(code **)(piVar18 + 0x14))
                (piVar18[0x13],"First Trigger/Remains On;Recalculate Each Tick/Dynamic");
    }
    plVar22 = local_c0;
    piVar18[7] = 0;
    *(undefined1 *)(piVar18 + 6) = 0x16;
    FUN_1800079f8(local_c0,0x1800d74a8,0x12);
    puVar4 = local_90;
    *(undefined4 *)((longlong)plVar22 + 0xc) = 1;
    *(undefined1 *)(plVar22 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar22 + 0x1c) = 0;
    if ((int)uStack_88 == 0) {
      return;
    }
    if (local_90 == (undefined1 *)0x0) {
      return;
    }
    pvVar7 = GetProcessHeap();
    HeapFree(pvVar7,0,puVar4);
    return;
  }
  if (param_3[0xe1] == 0) {
    uVar9 = FUN_1800254e8(auVar31,param_2,(longlong)param_3,uVar9,param_5,param_6,param_7,param_8,
                          param_9,param_10);
    auVar31._4_12_ = extraout_var_02;
    auVar31._0_4_ = extraout_s0_02;
    *local_a0 = (int)uVar9;
  }
  if (*local_a0 != 0) {
    return;
  }
  if ((*(longlong *)(param_3 + 0x58) == 0) && (*(code **)(param_3 + 0x5c) != (code *)0x0)) {
    auVar31 = (**(code **)(param_3 + 0x5c))(param_3[0x5e]);
  }
  if ((param_3[0x47e] != 0) && (param_3[0xe1] == 0)) {
    local_80 = &DAT_1800d4ecd;
    local_90 = &DAT_1800d4ecd;
    uStack_88 = 0;
    FUN_180006050(auVar31,param_2,&local_90,0x1800d7490,param_5,param_6,param_7,param_8,param_9,
                  param_10);
    puVar4 = local_90;
    if (((int)uStack_88 != 0) && (local_90 != (undefined1 *)0x0)) {
      pvVar7 = GetProcessHeap();
      HeapFree(pvVar7,0,puVar4);
      local_90 = (undefined1 *)0x0;
      uStack_88 = 0;
    }
  }
  if ((int)(*(longlong **)(param_3 + 0x3a6))[1] < *param_3) {
    return;
  }
  iVar5 = FUN_1800064f0(*(longlong **)(param_3 + 0x3a6),param_3[0xe1]);
  if (iVar5 == 0) {
    return;
  }
  iVar20 = 0;
  iVar23 = 0;
  iVar17 = 0;
  if (0 < iVar5) {
    plVar16 = *(longlong **)(param_3 + 0x3a6);
    iVar20 = 0;
    uVar6 = param_3[0xe1];
    lVar15 = 0;
    iVar24 = 0;
    do {
      lVar14 = 0;
      if (uVar6 < *(uint *)(plVar16 + 1)) {
        if (*plVar16 == 0) {
          uVar21 = (ulonglong)*(uint *)(plVar16 + 3);
        }
        else {
          uVar21 = (ulonglong)*(uint *)(*plVar16 + (ulonglong)uVar6 * 4);
        }
      }
      else {
        uVar21 = (ulonglong)*(uint *)(plVar16 + 3);
      }
      if (uVar6 + 1 < *(uint *)(plVar16 + 1)) {
        if (*plVar16 == 0) {
          uVar13 = *(uint *)(plVar16 + 3);
        }
        else {
          uVar13 = *(uint *)(*plVar16 + (ulonglong)(uVar6 + 1) * 4);
        }
      }
      else {
        uVar13 = *(uint *)(plVar16 + 3);
      }
      iVar23 = iVar24;
      if ((uint)uVar21 < uVar13) {
        iVar19 = (uVar13 - (uint)uVar21) + -1;
        iVar1 = iVar17;
        if (iVar19 < iVar17) {
          iVar1 = iVar19;
        }
        lVar14 = plVar16[2] + (uVar21 + (longlong)iVar1) * 0x14;
        if (lVar14 != 0) {
          iVar23 = *(int *)(lVar14 + 0xc);
          if (iVar5 == 1) {
            iVar20 = *(int *)(lVar14 + 8);
          }
          else if (iVar17 == 0) {
            iVar23 = iVar23 + iVar24;
          }
          else {
            iVar1 = iVar23 - *(int *)(lVar15 + 8);
            if (iVar17 == iVar5 + -1) {
              if (iVar1 < 0) {
                iVar23 = -iVar1;
                if (-1 < iVar1) {
                  iVar23 = iVar1;
                }
                iVar20 = iVar23 + iVar20;
                iVar19 = *(int *)(lVar14 + 8);
                iVar23 = iVar24;
              }
              else {
                iVar19 = *(int *)(lVar14 + 8);
                iVar23 = iVar1 + iVar24;
              }
            }
            else {
              if (-1 < iVar1) {
                iVar23 = iVar1 + iVar24;
                goto LAB_180035284;
              }
              iVar19 = -iVar1;
              iVar23 = iVar24;
              if (-1 < iVar1) {
                iVar19 = iVar1;
              }
            }
            iVar20 = iVar19 + iVar20;
          }
        }
      }
LAB_180035284:
      iVar17 = iVar17 + 1;
      lVar15 = lVar14;
      iVar24 = iVar23;
    } while (iVar17 < iVar5);
  }
  lVar15 = plVar22[0x10];
  if (lVar15 == 0) {
    if ((code *)plVar22[0x12] != (code *)0x0) {
      (*(code *)plVar22[0x12])((int)plVar22[0x13]);
    }
    lVar15 = plVar22[0x10];
    if (lVar15 != 0) goto LAB_1800352c4;
    plVar16 = plVar22 + 0x15;
  }
  else {
LAB_1800352c4:
    iVar5 = (int)plVar22[0x14];
    if (iVar5 == 0) {
      plVar16 = plVar22 + 0x15;
    }
    else {
      iVar17 = 3;
      if (iVar5 < 4) {
        iVar17 = iVar5 + -1;
      }
      plVar16 = (longlong *)(lVar15 + (longlong)iVar17 * 0x28);
    }
  }
  lVar15 = *plVar16;
  local_d0 = (int *)CONCAT44(local_d0._4_4_,param_3[0xe1]);
  if (lVar15 == 0) {
    if ((code *)plVar16[2] != (code *)0x0) {
      (*(code *)plVar16[2])((int)plVar16[3]);
    }
    lVar15 = *plVar16;
    if (lVar15 != 0) goto LAB_180035318;
    pfVar12 = (float *)((longlong)plVar16 + 0x24);
  }
  else {
LAB_180035318:
    iVar5 = (int)plVar16[4];
    if (iVar5 == 0) {
      pfVar12 = (float *)((longlong)plVar16 + 0x24);
    }
    else {
      uVar6 = (uint)local_d0 & ((int)(uint)local_d0 >> 0x1f ^ 0xffffffffU);
      if (iVar5 <= (int)uVar6) {
        uVar6 = iVar5 - 1;
      }
      pfVar12 = (float *)(lVar15 + (longlong)(int)uVar6 * 4);
    }
  }
  *pfVar12 = (float)iVar23;
  lVar15 = plVar22[0x10];
  if (lVar15 == 0) {
    if ((code *)plVar22[0x12] != (code *)0x0) {
      (*(code *)plVar22[0x12])((int)plVar22[0x13]);
    }
    lVar15 = plVar22[0x10];
    if (lVar15 != 0) goto LAB_180035370;
    plVar16 = plVar22 + 0x15;
  }
  else {
LAB_180035370:
    iVar5 = (int)plVar22[0x14];
    if (iVar5 == 0) {
      plVar16 = plVar22 + 0x15;
    }
    else {
      iVar17 = 4;
      if (iVar5 < 5) {
        iVar17 = iVar5 + -1;
      }
      plVar16 = (longlong *)(lVar15 + (longlong)iVar17 * 0x28);
    }
  }
  uVar6 = param_3[0xe1];
  lVar15 = *plVar16;
  if (lVar15 == 0) {
    if ((code *)plVar16[2] != (code *)0x0) {
      (*(code *)plVar16[2])((int)plVar16[3]);
    }
    lVar15 = *plVar16;
    if (lVar15 != 0) goto LAB_1800353c0;
    pfVar12 = (float *)((longlong)plVar16 + 0x24);
  }
  else {
LAB_1800353c0:
    iVar5 = (int)plVar16[4];
    if (iVar5 == 0) {
      pfVar12 = (float *)((longlong)plVar16 + 0x24);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar5 <= (int)uVar6) {
        uVar6 = iVar5 - 1;
      }
      pfVar12 = (float *)(lVar15 + (longlong)(int)uVar6 * 4);
    }
  }
  *pfVar12 = (float)iVar20;
  uVar21 = FUN_180026708((longlong)piVar11);
  lVar15 = plVar22[0x10];
  local_d0 = (int *)CONCAT44(local_d0._4_4_,(int)uVar21);
  if (lVar15 == 0) {
    if ((code *)plVar22[0x12] != (code *)0x0) {
      (*(code *)plVar22[0x12])((int)plVar22[0x13]);
    }
    lVar15 = plVar22[0x10];
    if (lVar15 != 0) goto LAB_180035430;
    plVar16 = plVar22 + 0x15;
LAB_180035460:
    local_f0 = plVar22 + 0x10;
    if ((code *)plVar22[0x12] != (code *)0x0) {
      (*(code *)plVar22[0x12])((int)plVar22[0x13]);
    }
    lVar15 = *local_f0;
    if (lVar15 != 0) goto LAB_180035484;
    plVar26 = plVar22 + 0x15;
  }
  else {
LAB_180035430:
    iVar5 = (int)plVar22[0x14];
    if (iVar5 == 0) {
      plVar16 = plVar22 + 0x15;
    }
    else {
      iVar17 = 0;
      if (iVar5 < 1) {
        iVar17 = iVar5 + -1;
      }
      plVar16 = (longlong *)(lVar15 + (longlong)iVar17 * 0x28);
    }
    if (lVar15 == 0) goto LAB_180035460;
LAB_180035484:
    iVar5 = (int)plVar22[0x14];
    if (iVar5 == 0) {
      plVar26 = plVar22 + 0x15;
    }
    else {
      iVar17 = 3;
      if (iVar5 < 4) {
        iVar17 = iVar5 + -1;
      }
      plVar26 = (longlong *)(lVar15 + (longlong)iVar17 * 0x28);
    }
  }
  (**(code **)(param_3 + 0xe4))(plVar26,plVar16,param_3[0xe1],(ulonglong)local_d0 & 0xffffffff);
  uVar21 = FUN_180026708((longlong)piVar11);
  lVar15 = plVar22[0x10];
  local_d0 = (int *)CONCAT44(local_d0._4_4_,(int)uVar21);
  if (lVar15 == 0) {
    if ((code *)plVar22[0x12] != (code *)0x0) {
      (*(code *)plVar22[0x12])((int)plVar22[0x13]);
    }
    lVar15 = plVar22[0x10];
    if (lVar15 != 0) goto LAB_1800354fc;
    plVar16 = plVar22 + 0x15;
LAB_18003552c:
    if ((code *)plVar22[0x12] != (code *)0x0) {
      (*(code *)plVar22[0x12])((int)plVar22[0x13]);
    }
    lVar15 = plVar22[0x10];
    if (lVar15 != 0) goto LAB_18003554c;
    plVar26 = plVar22 + 0x15;
  }
  else {
LAB_1800354fc:
    iVar5 = (int)plVar22[0x14];
    if (iVar5 == 0) {
      plVar16 = plVar22 + 0x15;
    }
    else {
      iVar17 = 1;
      if (iVar5 < 2) {
        iVar17 = iVar5 + -1;
      }
      plVar16 = (longlong *)(lVar15 + (longlong)iVar17 * 0x28);
    }
    if (lVar15 == 0) goto LAB_18003552c;
LAB_18003554c:
    iVar5 = (int)plVar22[0x14];
    if (iVar5 == 0) {
      plVar26 = plVar22 + 0x15;
    }
    else {
      iVar17 = 4;
      if (iVar5 < 5) {
        iVar17 = iVar5 + -1;
      }
      plVar26 = (longlong *)(lVar15 + (longlong)iVar17 * 0x28);
    }
  }
  (**(code **)(param_3 + 0xe4))(plVar26,plVar16,param_3[0xe1],(ulonglong)local_d0 & 0xffffffff);
  lVar15 = plVar22[0x10];
  if (lVar15 == 0) {
    if ((code *)plVar22[0x12] != (code *)0x0) {
      (*(code *)plVar22[0x12])((int)plVar22[0x13]);
    }
    lVar15 = plVar22[0x10];
    if (lVar15 != 0) goto LAB_1800355bc;
    uVar6 = param_3[0xe1];
    plVar16 = plVar22 + 0x15;
LAB_1800355f0:
    if ((code *)plVar22[0x12] != (code *)0x0) {
      (*(code *)plVar22[0x12])((int)plVar22[0x13]);
      uVar6 = local_a8[0xe1];
    }
    lVar15 = plVar22[0x10];
    if (lVar15 != 0) goto LAB_180035618;
    plVar26 = plVar22 + 0x15;
  }
  else {
LAB_1800355bc:
    iVar5 = (int)plVar22[0x14];
    if (iVar5 == 0) {
      plVar16 = plVar22 + 0x15;
    }
    else {
      iVar17 = 0;
      if (iVar5 < 1) {
        iVar17 = iVar5 + -1;
      }
      plVar16 = (longlong *)(lVar15 + (longlong)iVar17 * 0x28);
    }
    uVar6 = param_3[0xe1];
    if (lVar15 == 0) goto LAB_1800355f0;
LAB_180035618:
    iVar5 = (int)plVar22[0x14];
    if (iVar5 == 0) {
      plVar26 = plVar22 + 0x15;
    }
    else {
      iVar17 = 1;
      if (iVar5 < 2) {
        iVar17 = iVar5 + -1;
      }
      plVar26 = (longlong *)(lVar15 + (longlong)iVar17 * 0x28);
    }
  }
  lVar15 = *plVar26;
  if (lVar15 == 0) {
    if ((code *)plVar26[2] != (code *)0x0) {
      (*(code *)plVar26[2])((int)plVar26[3]);
    }
    lVar15 = *plVar26;
    if (lVar15 != 0) goto LAB_180035668;
    pfVar12 = (float *)((longlong)plVar26 + 0x24);
  }
  else {
LAB_180035668:
    iVar5 = (int)plVar26[4];
    if (iVar5 == 0) {
      pfVar12 = (float *)((longlong)plVar26 + 0x24);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar5 <= (int)uVar6) {
        uVar6 = iVar5 - 1;
      }
      pfVar12 = (float *)(lVar15 + (longlong)(int)uVar6 * 4);
    }
  }
  if ((*plVar16 == 0) && ((code *)plVar16[2] != (code *)0x0)) {
    (*(code *)plVar16[2])((int)plVar16[3]);
  }
  fVar30 = FUN_180026608((longlong)local_c8);
  if (*extraout_x11 <= fVar30 * *pfVar12) {
    lVar15 = plVar22[0x10];
    if (lVar15 == 0) {
      if ((code *)plVar22[0x12] != (code *)0x0) {
        (*(code *)plVar22[0x12])((int)plVar22[0x13]);
      }
      lVar15 = plVar22[0x10];
      if (lVar15 != 0) goto LAB_180035900;
      uVar6 = param_3[0xe1];
      plVar16 = plVar22 + 0x15;
LAB_180035938:
      if ((code *)plVar22[0x12] != (code *)0x0) {
        (*(code *)plVar22[0x12])((int)plVar22[0x13]);
        uVar6 = local_a8[0xe1];
      }
      lVar15 = plVar22[0x10];
      if (lVar15 != 0) goto LAB_180035960;
      plVar26 = plVar22 + 0x15;
    }
    else {
LAB_180035900:
      iVar5 = (int)plVar22[0x14];
      if (iVar5 == 0) {
        plVar16 = plVar22 + 0x15;
      }
      else {
        iVar17 = 1;
        if (iVar5 < 2) {
          iVar17 = iVar5 + -1;
        }
        plVar16 = (longlong *)(lVar15 + (longlong)iVar17 * 0x28);
      }
      uVar6 = param_3[0xe1];
      if (lVar15 == 0) goto LAB_180035938;
LAB_180035960:
      iVar5 = (int)plVar22[0x14];
      if (iVar5 == 0) {
        plVar26 = plVar22 + 0x15;
      }
      else {
        iVar17 = 0;
        if (iVar5 < 1) {
          iVar17 = iVar5 + -1;
        }
        plVar26 = (longlong *)(lVar15 + (longlong)iVar17 * 0x28);
      }
    }
    lVar15 = *plVar26;
    if (lVar15 == 0) {
      if ((code *)plVar26[2] != (code *)0x0) {
        (*(code *)plVar26[2])((int)plVar26[3]);
      }
      lVar15 = *plVar26;
      if (lVar15 == 0) {
        pfVar12 = (float *)((longlong)plVar26 + 0x24);
        goto LAB_1800359d0;
      }
    }
    iVar5 = (int)plVar26[4];
    if (iVar5 == 0) {
      pfVar12 = (float *)((longlong)plVar26 + 0x24);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar5 <= (int)uVar6) {
        uVar6 = iVar5 - 1;
      }
      pfVar12 = (float *)(lVar15 + (longlong)(int)uVar6 * 4);
    }
LAB_1800359d0:
    if ((*plVar16 == 0) && ((code *)plVar16[2] != (code *)0x0)) {
      (*(code *)plVar16[2])((int)plVar16[3]);
    }
    fVar30 = FUN_180026608((longlong)local_c8);
    if (fVar30 * *pfVar12 < *extraout_x11_01) {
      puVar10 = (undefined4 *)FUN_18000f448((longlong)plVar22,param_3[0xe1]);
      *puVar10 = 0xbf800000;
      iVar5 = *(int *)((longlong)local_b8 + 0x1c);
      piVar11 = (int *)FUN_180005d08(plVar22 + 0xb,param_3[0xe1]);
      *piVar11 = iVar5;
      puVar10 = (undefined4 *)FUN_18000f448((longlong)plVar27,param_3[0xe1]);
      *puVar10 = 0;
      FUN_180005d08((longlong *)(param_3 + 0x298),param_3[0xe1]);
      uVar21 = FUN_180026708((longlong)local_c0);
      fVar30 = (float)param_3[0x1a6];
      fVar3 = *extraout_x11_02;
      pfVar12 = (float *)FUN_18000f448((longlong)plVar28,param_3[0xe1]);
      *pfVar12 = (float)(int)uVar21 * fVar30 + fVar3;
      return;
    }
    uVar6 = FUN_180026550((longlong)local_b0);
    if (uVar6 != 1) {
      return;
    }
    puVar10 = (undefined4 *)FUN_18000f448((longlong)plVar22,param_3[0xe1]);
    *puVar10 = 0;
    puVar10 = (undefined4 *)FUN_18000f448((longlong)plVar27,param_3[0xe1]);
    *puVar10 = 0;
    puVar10 = (undefined4 *)FUN_18000f448((longlong)plVar28,param_3[0xe1]);
    *puVar10 = 0;
    return;
  }
  uVar6 = param_3[0xe1];
  lVar15 = plVar22[6];
  if (lVar15 == 0) {
    if ((code *)plVar22[8] != (code *)0x0) {
      (*(code *)plVar22[8])((int)plVar22[9]);
    }
    lVar15 = plVar22[6];
    if (lVar15 != 0) goto LAB_180035724;
    puVar10 = (undefined4 *)((longlong)plVar22 + 0x54);
  }
  else {
LAB_180035724:
    iVar5 = (int)plVar22[10];
    if (iVar5 == 0) {
      puVar10 = (undefined4 *)((longlong)plVar22 + 0x54);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar5 <= (int)uVar6) {
        uVar6 = iVar5 - 1;
      }
      puVar10 = (undefined4 *)(lVar15 + (longlong)(int)uVar6 * 4);
    }
  }
  *puVar10 = 0x3f800000;
  uVar6 = param_3[0xe1];
  lVar15 = local_b8[3];
  lVar14 = plVar22[0xb];
  if (lVar14 == 0) {
    if ((code *)plVar22[0xd] != (code *)0x0) {
      (*(code *)plVar22[0xd])((int)plVar22[0xe]);
    }
    lVar14 = plVar22[0xb];
    if (lVar14 != 0) goto LAB_180035784;
    piVar11 = (int *)((longlong)plVar22 + 0x7c);
  }
  else {
LAB_180035784:
    iVar5 = (int)plVar22[0xf];
    if (iVar5 == 0) {
      piVar11 = (int *)((longlong)plVar22 + 0x7c);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar5 <= (int)uVar6) {
        uVar6 = iVar5 - 1;
      }
      piVar11 = (int *)(lVar14 + (longlong)(int)uVar6 * 4);
    }
  }
  *piVar11 = (int)lVar15;
  uVar6 = param_3[0xe1];
  if ((*(longlong *)(param_3 + 0x2a2) == 0) && (*(code **)(param_3 + 0x2a6) != (code *)0x0)) {
    (**(code **)(param_3 + 0x2a6))(param_3[0x2a8]);
    uVar6 = local_a8[0xe1];
  }
  uVar21 = FUN_180026708((longlong)local_c0);
  fVar30 = (float)param_3[0x1a6];
  lVar15 = plVar27[6];
  fVar3 = *extraout_x11_00;
  if (lVar15 == 0) {
    if ((code *)plVar27[8] != (code *)0x0) {
      (*(code *)plVar27[8])((int)plVar27[9]);
    }
    lVar15 = plVar27[6];
    if (lVar15 != 0) goto LAB_18003584c;
    pfVar12 = (float *)((longlong)plVar27 + 0x54);
  }
  else {
LAB_18003584c:
    iVar5 = (int)plVar27[10];
    if (iVar5 == 0) {
      pfVar12 = (float *)((longlong)plVar27 + 0x54);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar5 <= (int)uVar6) {
        uVar6 = iVar5 - 1;
      }
      pfVar12 = (float *)(lVar15 + (longlong)(int)uVar6 * 4);
    }
  }
  *pfVar12 = fVar3 - (float)(int)uVar21 * fVar30;
  uVar6 = param_3[0xe1];
  lVar15 = plVar28[6];
  if (lVar15 == 0) {
    if ((code *)plVar28[8] != (code *)0x0) {
      (*(code *)plVar28[8])((int)plVar28[9]);
    }
    lVar15 = plVar28[6];
    if (lVar15 == 0) goto LAB_180035898;
  }
  iVar5 = (int)plVar28[10];
  if (iVar5 != 0) {
    uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
    if (iVar5 <= (int)uVar6) {
      uVar6 = iVar5 - 1;
    }
    *(undefined4 *)(lVar15 + (longlong)(int)uVar6 * 4) = 0;
    return;
  }
LAB_180035898:
  *(undefined4 *)((longlong)plVar28 + 0x54) = 0;
  return;
}

