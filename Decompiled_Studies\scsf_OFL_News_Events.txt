
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */
/* WARNING: Globals starting with '_' overlap smaller symbols at the same address */

byte **** scsf_OFL_News_Events
                    (undefined1 param_1 [16],undefined8 param_2,int *param_3,undefined8 param_4,
                    undefined8 param_5,undefined *param_6,undefined *param_7,undefined8 param_8,
                    undefined8 param_9,undefined8 param_10)

{
  undefined1 *puVar1;
  char cVar2;
  code cVar3;
  LPVOID pvVar4;
  byte ******lpMem;
  bool bVar5;
  int iVar6;
  undefined4 uVar7;
  uint uVar8;
  int iVar9;
  uint uVar10;
  undefined8 *puVar11;
  byte ******ppppppbVar12;
  HANDLE pvVar13;
  byte *******pppppppbVar14;
  ulonglong uVar15;
  longlong *plVar16;
  undefined8 uVar17;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  undefined8 extraout_x1_10;
  undefined8 extraout_x1_11;
  undefined8 extraout_x1_12;
  undefined8 extraout_x1_13;
  undefined8 extraout_x1_14;
  undefined8 extraout_x1_15;
  undefined8 extraout_x1_16;
  undefined8 extraout_x1_17;
  undefined8 extraout_x1_18;
  undefined8 extraout_x1_19;
  undefined8 extraout_x1_20;
  undefined8 extraout_x1_21;
  undefined8 extraout_x1_22;
  undefined8 extraout_x1_23;
  undefined8 extraout_x1_24;
  undefined8 extraout_x1_25;
  undefined8 extraout_x1_26;
  undefined8 extraout_x1_27;
  undefined8 extraout_x1_28;
  undefined8 extraout_x1_29;
  undefined8 extraout_x1_30;
  char *pcVar18;
  undefined8 extraout_x1_31;
  undefined8 extraout_x1_32;
  byte *******pppppppbVar19;
  byte *******lpMem_00;
  LPVOID *ppvVar20;
  code *pcVar21;
  LPVOID *ppvVar22;
  code *pcVar23;
  longlong lVar24;
  int *piVar25;
  char *pcVar26;
  byte *******pppppppbVar27;
  longlong extraout_x11;
  longlong *plVar28;
  byte *******lpMem_01;
  undefined1 extraout_q0 [16];
  undefined1 auVar29 [16];
  undefined1 extraout_q0_00 [16];
  undefined1 extraout_q0_01 [16];
  undefined1 extraout_q0_02 [16];
  undefined1 extraout_q0_03 [16];
  undefined1 extraout_q0_04 [16];
  undefined1 extraout_q0_05 [16];
  undefined1 extraout_q0_06 [16];
  undefined1 extraout_q0_07 [16];
  undefined1 extraout_q0_08 [16];
  undefined1 extraout_q0_09 [16];
  undefined1 extraout_q0_10 [16];
  undefined1 extraout_q0_11 [16];
  undefined1 extraout_q0_12 [16];
  undefined1 extraout_q0_13 [16];
  undefined1 extraout_q0_14 [16];
  undefined1 extraout_q0_15 [16];
  undefined1 extraout_q0_16 [16];
  undefined1 extraout_q0_17 [16];
  undefined1 extraout_q0_18 [16];
  undefined1 extraout_q0_19 [16];
  undefined1 extraout_q0_20 [16];
  undefined1 extraout_q0_21 [16];
  undefined1 extraout_q0_22 [16];
  undefined1 extraout_q0_23 [16];
  undefined1 extraout_q0_24 [16];
  undefined1 extraout_q0_25 [16];
  undefined1 extraout_q0_26 [16];
  undefined1 extraout_q0_27 [16];
  undefined1 extraout_q0_28 [16];
  undefined1 extraout_q0_29 [16];
  undefined1 extraout_q0_30 [16];
  undefined1 extraout_q0_31 [16];
  undefined1 extraout_q0_32 [16];
  undefined1 extraout_q0_33 [16];
  undefined1 extraout_q0_34 [16];
  undefined1 extraout_q0_35 [16];
  byte *****local_588;
  longlong *local_580;
  byte ******local_578;
  longlong *local_570;
  longlong *local_568;
  longlong *local_560;
  longlong *local_558;
  longlong *local_550;
  longlong *local_548;
  longlong *local_540;
  longlong *local_538;
  longlong *local_530;
  longlong *local_528;
  longlong *local_520;
  longlong *local_518;
  longlong *local_510;
  longlong *local_508;
  longlong *local_500;
  longlong *local_4f8;
  byte *****local_4f0;
  longlong *local_4e8;
  longlong *local_4e0;
  longlong *local_4d8;
  longlong *local_4d0;
  longlong *local_4c8;
  byte ******local_4c0;
  byte *****local_4b8;
  longlong *local_4b0;
  longlong *local_4a8;
  int *local_4a0;
  int *local_498;
  int *local_490;
  longlong *local_488;
  uint *local_480;
  LPVOID local_478;
  undefined8 local_470;
  longlong *local_460;
  longlong *local_458;
  uint *local_450;
  longlong *local_448;
  longlong *local_440;
  longlong *local_438;
  longlong *local_430;
  longlong *local_428;
  longlong *local_420;
  longlong *local_418;
  longlong *local_410;
  longlong *local_408;
  longlong *local_400;
  longlong *local_3f8;
  longlong *local_3f0;
  longlong *local_3e8;
  longlong *local_3e0;
  longlong *local_3d8;
  longlong *local_3d0;
  longlong *local_3c8;
  longlong *local_3c0;
  longlong *local_3b8;
  longlong *local_3b0;
  longlong *local_3a8;
  longlong *local_3a0;
  longlong *local_398;
  undefined4 local_390;
  LPVOID local_388;
  undefined8 local_380;
  undefined8 local_370;
  byte ******local_360;
  undefined8 uStack_358;
  undefined1 *local_350;
  byte ******local_340 [2];
  byte ******local_330;
  undefined8 uStack_328;
  undefined1 *local_320;
  byte *****local_310;
  longlong local_308;
  byte ******local_300;
  undefined8 uStack_2f8;
  undefined8 local_2f0;
  ulonglong local_2e8;
  longlong *local_2e0 [2];
  LPVOID local_2d0;
  undefined8 uStack_2c8;
  undefined8 local_2c0;
  LPVOID local_2b0;
  undefined8 uStack_2a8;
  undefined8 local_2a0;
  byte ******local_290;
  undefined8 uStack_288;
  undefined1 *local_280;
  LPVOID local_270;
  undefined8 uStack_268;
  undefined8 local_260;
  LPVOID local_250;
  undefined8 uStack_248;
  undefined8 local_240;
  LPVOID local_230;
  undefined8 uStack_228;
  undefined8 local_220;
  longlong *local_210 [2];
  char *local_200;
  undefined8 uStack_1f8;
  char *local_1f0;
  char *local_1e0;
  undefined8 uStack_1d8;
  char *local_1d0;
  longlong *local_1c0 [2];
  byte ******local_1b0;
  undefined8 uStack_1a8;
  undefined1 *local_1a0;
  byte ******local_190;
  undefined8 uStack_188;
  undefined1 *local_180;
  LPVOID local_170;
  undefined8 uStack_168;
  undefined8 local_160;
  ulonglong local_158;
  LPVOID local_150;
  undefined8 uStack_148;
  undefined8 local_140;
  ulonglong local_138;
  longlong lStack_130;
  undefined4 local_124;
  longlong lStack_118;
  undefined4 local_10c;
  longlong lStack_100;
  undefined4 local_f4;
  longlong alStack_e8 [3];
  longlong lStack_d0;
  undefined4 local_c4;
  longlong alStack_b8 [3];
  longlong lStack_a0;
  undefined4 local_94;
  longlong alStack_88 [5];
  
                    /* 0x62fd0  18  scsf_OFL_News_Events */
  local_390 = 0;
  lpMem_01 = (byte *******)&DAT_1800d4ecd;
  local_370 = 0xfffffffffffffffe;
  local_340[0] = (byte ******)&DAT_1800d4ecd;
  local_320 = &DAT_1800d4ecd;
  local_330 = (byte ******)&DAT_1800d4ecd;
  uStack_328 = 0;
  local_2e0[0] = (longlong *)(**(code **)(param_3 + 0x62e))(0);
  local_480 = (uint *)(**(code **)(param_3 + 0x62e))(1);
  local_490 = (int *)(**(code **)(param_3 + 0x62e))(2);
  local_4a0 = (int *)(**(code **)(param_3 + 0x62e))(3);
  local_450 = (uint *)(**(code **)(param_3 + 0x62e))(4);
  puVar11 = (undefined8 *)(**(code **)(param_3 + 0x446))(4);
  plVar28 = (longlong *)*puVar11;
  local_460 = (longlong *)(**(code **)(param_3 + 0x444))(5);
  local_458 = (longlong *)(**(code **)(param_3 + 0x444))(6);
  lVar24 = *(longlong *)(param_3 + 0x140);
  uVar17 = extraout_x1;
  auVar29 = extraout_q0;
  if (lVar24 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar17 = extraout_x1_00;
    }
    lVar24 = *(longlong *)(param_3 + 0x140);
    if (lVar24 != 0) goto LAB_1800630d4;
    local_498 = param_3 + 0x14a;
    pcVar21 = (code *)param_6;
    pcVar23 = (code *)param_7;
  }
  else {
LAB_1800630d4:
    iVar6 = param_3[0x148];
    pcVar21 = (code *)param_6;
    pcVar23 = (code *)param_7;
    if (iVar6 == 0) {
      local_498 = param_3 + 0x14a;
    }
    else {
      iVar9 = 0;
      if (iVar6 < 1) {
        iVar9 = iVar6 + -1;
      }
      local_498 = (int *)(lVar24 + (longlong)iVar9 * 0x170);
    }
  }
  lVar24 = *(longlong *)(param_3 + 0x84);
  if (lVar24 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_01;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_18006313c;
    local_440 = (longlong *)(param_3 + 0x8e);
LAB_180063174:
    local_4a8 = local_440;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_02;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_1800631a4;
    local_438 = (longlong *)(param_3 + 0x8e);
LAB_1800631d8:
    local_580 = local_438;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_03;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063208;
    local_430 = (longlong *)(param_3 + 0x8e);
LAB_180063240:
    local_488 = local_430;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_04;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063270;
    local_428 = (longlong *)(param_3 + 0x8e);
LAB_1800632a8:
    local_570 = local_428;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_05;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_1800632e0;
    local_420 = (longlong *)(param_3 + 0x8e);
LAB_180063320:
    local_568 = local_420;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_06;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063350;
    local_418 = (longlong *)(param_3 + 0x8e);
LAB_180063388:
    local_560 = local_418;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_07;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_1800633b8;
    local_410 = (longlong *)(param_3 + 0x8e);
LAB_1800633f0:
    local_558 = local_410;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_08;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063420;
    local_408 = (longlong *)(param_3 + 0x8e);
LAB_180063458:
    local_550 = local_408;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_09;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063488;
    local_400 = (longlong *)(param_3 + 0x8e);
LAB_1800634c0:
    local_548 = local_400;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_10;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_1800634f0;
    local_3f8 = (longlong *)(param_3 + 0x8e);
LAB_180063528:
    local_540 = local_3f8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_11;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063558;
    local_3f0 = (longlong *)(param_3 + 0x8e);
LAB_180063590:
    local_538 = local_3f0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_12;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_1800635c0;
    local_3e8 = (longlong *)(param_3 + 0x8e);
LAB_1800635f8:
    local_530 = local_3e8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_13;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063628;
    local_3e0 = (longlong *)(param_3 + 0x8e);
LAB_180063660:
    local_528 = local_3e0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_14;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063698;
    local_3d8 = (longlong *)(param_3 + 0x8e);
LAB_1800636d8:
    local_520 = local_3d8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_15;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063708;
    local_3d0 = (longlong *)(param_3 + 0x8e);
LAB_180063740:
    local_518 = local_3d0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_16;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063770;
    local_3c8 = (longlong *)(param_3 + 0x8e);
LAB_1800637a8:
    local_510 = local_3c8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_17;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_1800637d8;
    local_3c0 = (longlong *)(param_3 + 0x8e);
LAB_180063810:
    local_508 = local_3c0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_18;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063840;
    local_3b8 = (longlong *)(param_3 + 0x8e);
LAB_180063878:
    local_500 = local_3b8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_19;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_1800638a8;
    local_3b0 = (longlong *)(param_3 + 0x8e);
LAB_1800638e0:
    local_4f8 = local_3b0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      auVar29 = (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_20;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063910;
    ppppppbVar12 = (byte ******)(param_3 + 0x8e);
LAB_180063948:
    local_4f0 = (byte *****)ppppppbVar12;
    local_310 = (byte *****)ppppppbVar12;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      ppppppbVar12 = (byte ******)(**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_21;
      auVar29 = extraout_q0_00;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063978;
    local_3a8 = (longlong *)(param_3 + 0x8e);
LAB_1800639b0:
    local_4e8 = local_3a8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      ppppppbVar12 = (byte ******)(**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_22;
      auVar29 = extraout_q0_01;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_1800639e0;
    local_3a0 = (longlong *)(param_3 + 0x8e);
LAB_180063a18:
    local_4e0 = local_3a0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      ppppppbVar12 = (byte ******)(**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_23;
      auVar29 = extraout_q0_02;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063a48;
    local_398 = (longlong *)(param_3 + 0x8e);
LAB_180063a80:
    local_4d8 = local_398;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      ppppppbVar12 = (byte ******)(**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_24;
      auVar29 = extraout_q0_03;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063ab0;
    local_448 = (longlong *)(param_3 + 0x8e);
LAB_180063ae8:
    local_4d0 = local_448;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      ppppppbVar12 = (byte ******)(**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_25;
      auVar29 = extraout_q0_04;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063b18;
    local_1c0[0] = (longlong *)(param_3 + 0x8e);
LAB_180063b50:
    local_4c8 = local_1c0[0];
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      ppppppbVar12 = (byte ******)(**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_26;
      auVar29 = extraout_q0_05;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063b80;
    local_4c0 = (byte ******)(param_3 + 0x8e);
LAB_180063bb8:
    local_578 = local_4c0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      ppppppbVar12 = (byte ******)(**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_27;
      auVar29 = extraout_q0_06;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063be8;
    local_4b8 = (byte *****)(param_3 + 0x8e);
LAB_180063c20:
    local_588 = local_4b8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      ppppppbVar12 = (byte ******)(**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_28;
      auVar29 = extraout_q0_07;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063c50;
    local_210[0] = (longlong *)(param_3 + 0x8e);
LAB_180063c88:
    local_4b0 = local_210[0];
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      ppppppbVar12 = (byte ******)(**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_29;
      auVar29 = extraout_q0_08;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063cb0;
    plVar16 = (longlong *)(param_3 + 0x8e);
LAB_180063ce0:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      ppppppbVar12 = (byte ******)(**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar17 = extraout_x1_30;
      auVar29 = extraout_q0_09;
    }
    lVar24 = *(longlong *)(param_3 + 0x84);
    if (lVar24 != 0) goto LAB_180063d00;
    piVar25 = param_3 + 0x8e;
  }
  else {
LAB_18006313c:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_4a8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0;
      if (iVar6 < 1) {
        iVar9 = iVar6 + -1;
      }
      local_4a8 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_440 = local_4a8;
    if (lVar24 == 0) goto LAB_180063174;
LAB_1800631a4:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_580 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 1;
      if (iVar6 + -1 == 0 || iVar6 < 1) {
        iVar9 = iVar6 + -1;
      }
      local_580 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_438 = local_580;
    if (lVar24 == 0) goto LAB_1800631d8;
LAB_180063208:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_488 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 2;
      if (iVar6 < 3) {
        iVar9 = iVar6 + -1;
      }
      local_488 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_430 = local_488;
    if (lVar24 == 0) goto LAB_180063240;
LAB_180063270:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_570 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 3;
      if (iVar6 < 4) {
        iVar9 = iVar6 + -1;
      }
      local_570 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_428 = local_570;
    if (lVar24 == 0) goto LAB_1800632a8;
LAB_1800632e0:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_568 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 4;
      if (iVar6 < 5) {
        iVar9 = iVar6 + -1;
      }
      local_568 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_420 = local_568;
    if (lVar24 == 0) goto LAB_180063320;
LAB_180063350:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_560 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 5;
      if (iVar6 < 6) {
        iVar9 = iVar6 + -1;
      }
      local_560 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_418 = local_560;
    if (lVar24 == 0) goto LAB_180063388;
LAB_1800633b8:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_558 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 6;
      if (iVar6 < 7) {
        iVar9 = iVar6 + -1;
      }
      local_558 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_410 = local_558;
    if (lVar24 == 0) goto LAB_1800633f0;
LAB_180063420:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_550 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 7;
      if (iVar6 < 8) {
        iVar9 = iVar6 + -1;
      }
      local_550 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_408 = local_550;
    if (lVar24 == 0) goto LAB_180063458;
LAB_180063488:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_548 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 8;
      if (iVar6 < 9) {
        iVar9 = iVar6 + -1;
      }
      local_548 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_400 = local_548;
    if (lVar24 == 0) goto LAB_1800634c0;
LAB_1800634f0:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_540 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 9;
      if (iVar6 < 10) {
        iVar9 = iVar6 + -1;
      }
      local_540 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_3f8 = local_540;
    if (lVar24 == 0) goto LAB_180063528;
LAB_180063558:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_538 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 10;
      if (iVar6 < 0xb) {
        iVar9 = iVar6 + -1;
      }
      local_538 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_3f0 = local_538;
    if (lVar24 == 0) goto LAB_180063590;
LAB_1800635c0:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_530 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0xb;
      if (iVar6 < 0xc) {
        iVar9 = iVar6 + -1;
      }
      local_530 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_3e8 = local_530;
    if (lVar24 == 0) goto LAB_1800635f8;
LAB_180063628:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_528 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0xc;
      if (iVar6 < 0xd) {
        iVar9 = iVar6 + -1;
      }
      local_528 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_3e0 = local_528;
    if (lVar24 == 0) goto LAB_180063660;
LAB_180063698:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_520 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0xd;
      if (iVar6 < 0xe) {
        iVar9 = iVar6 + -1;
      }
      local_520 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_3d8 = local_520;
    if (lVar24 == 0) goto LAB_1800636d8;
LAB_180063708:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_518 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0xe;
      if (iVar6 < 0xf) {
        iVar9 = iVar6 + -1;
      }
      local_518 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_3d0 = local_518;
    if (lVar24 == 0) goto LAB_180063740;
LAB_180063770:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_510 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0xf;
      if (iVar6 < 0x10) {
        iVar9 = iVar6 + -1;
      }
      local_510 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_3c8 = local_510;
    if (lVar24 == 0) goto LAB_1800637a8;
LAB_1800637d8:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_508 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0x10;
      if (iVar6 < 0x11) {
        iVar9 = iVar6 + -1;
      }
      local_508 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_3c0 = local_508;
    if (lVar24 == 0) goto LAB_180063810;
LAB_180063840:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_500 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0x11;
      if (iVar6 < 0x12) {
        iVar9 = iVar6 + -1;
      }
      local_500 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_3b8 = local_500;
    if (lVar24 == 0) goto LAB_180063878;
LAB_1800638a8:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_4f8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0x12;
      if (iVar6 < 0x13) {
        iVar9 = iVar6 + -1;
      }
      local_4f8 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_3b0 = local_4f8;
    if (lVar24 == 0) goto LAB_1800638e0;
LAB_180063910:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      ppppppbVar12 = (byte ******)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0x13;
      if (iVar6 < 0x14) {
        iVar9 = iVar6 + -1;
      }
      ppppppbVar12 = (byte ******)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_4f0 = (byte *****)ppppppbVar12;
    local_310 = (byte *****)ppppppbVar12;
    if (lVar24 == 0) goto LAB_180063948;
LAB_180063978:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_4e8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0x14;
      if (iVar6 < 0x15) {
        iVar9 = iVar6 + -1;
      }
      local_4e8 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_3a8 = local_4e8;
    if (lVar24 == 0) goto LAB_1800639b0;
LAB_1800639e0:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_4e0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0x15;
      if (iVar6 < 0x16) {
        iVar9 = iVar6 + -1;
      }
      local_4e0 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_3a0 = local_4e0;
    if (lVar24 == 0) goto LAB_180063a18;
LAB_180063a48:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_4d8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0x16;
      if (iVar6 < 0x17) {
        iVar9 = iVar6 + -1;
      }
      local_4d8 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_398 = local_4d8;
    if (lVar24 == 0) goto LAB_180063a80;
LAB_180063ab0:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_4d0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0x17;
      if (iVar6 < 0x18) {
        iVar9 = iVar6 + -1;
      }
      local_4d0 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_448 = local_4d0;
    if (lVar24 == 0) goto LAB_180063ae8;
LAB_180063b18:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_4c8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0x18;
      if (iVar6 < 0x19) {
        iVar9 = iVar6 + -1;
      }
      local_4c8 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_1c0[0] = local_4c8;
    if (lVar24 == 0) goto LAB_180063b50;
LAB_180063b80:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_578 = (byte ******)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0x19;
      if (iVar6 < 0x1a) {
        iVar9 = iVar6 + -1;
      }
      local_578 = (byte ******)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_4c0 = local_578;
    if (lVar24 == 0) goto LAB_180063bb8;
LAB_180063be8:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_588 = (byte *****)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0x1a;
      if (iVar6 < 0x1b) {
        iVar9 = iVar6 + -1;
      }
      local_588 = (byte *****)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_4b8 = local_588;
    if (lVar24 == 0) goto LAB_180063c20;
LAB_180063c50:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      local_4b0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0x1b;
      if (iVar6 < 0x1c) {
        iVar9 = iVar6 + -1;
      }
      local_4b0 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    local_210[0] = local_4b0;
    if (lVar24 == 0) goto LAB_180063c88;
LAB_180063cb0:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      plVar16 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar9 = 0x1c;
      if (iVar6 < 0x1d) {
        iVar9 = iVar6 + -1;
      }
      plVar16 = (longlong *)(lVar24 + (longlong)iVar9 * 0x98);
    }
    if (lVar24 == 0) goto LAB_180063ce0;
LAB_180063d00:
    iVar6 = param_3[0x8c];
    if (iVar6 == 0) {
      piVar25 = param_3 + 0x8e;
    }
    else {
      iVar9 = 0x1d;
      if (iVar6 < 0x1e) {
        iVar9 = iVar6 + -1;
      }
      piVar25 = (int *)(lVar24 + (longlong)iVar9 * 0x98);
    }
  }
  if (param_3[0x2b] != 0) {
    FUN_1800079f8((longlong *)(param_3 + 0x46),0x1800d91f0,0xf);
    param_3[0x49] = 1;
    if ((*(char **)(param_3 + 0xce) != (char *)0x0) && (**(char **)(param_3 + 0xce) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0xce),0x1800d4ecd,0);
      param_3[0xd1] = 1;
    }
    local_360 = (byte ******)&DAT_1800d4ecd;
    uStack_358 = 0;
    local_350 = &DAT_1800d4ecd;
    pvVar13 = GetProcessHeap();
    pppppppbVar14 = (byte *******)HeapAlloc(pvVar13,0,0x15);
    if (pppppppbVar14 == (byte *******)0x0) {
      local_360 = (byte ******)&DAT_1800d4ecd;
      pppppppbVar19 = lpMem_01;
    }
    else {
      pppppppbVar14[1] = (byte ******)0x0;
      *pppppppbVar14 = (byte ******)0x0;
      *(undefined4 *)(pppppppbVar14 + 2) = 0;
      *(code *)((longlong)pppppppbVar14 + 0x14) = (code)0x0;
      local_360 = (byte ******)pppppppbVar14;
      FUN_180099d78((char *)pppppppbVar14,0x15,0x1800d6c40,0x14);
      uStack_358 = 0x100000001;
      pppppppbVar19 = pppppppbVar14;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_360);
    if ((pppppppbVar14 != (byte *******)0x0) && (pppppppbVar19 != (byte *******)0x0)) {
      pvVar13 = GetProcessHeap();
      HeapFree(pvVar13,0,pppppppbVar19);
    }
    local_360 = (byte ******)&DAT_1800d4ecd;
    uStack_358 = 0;
    local_350 = &DAT_1800d4ecd;
    pvVar13 = GetProcessHeap();
    pppppppbVar14 = (byte *******)HeapAlloc(pvVar13,0,0x8d);
    if (pppppppbVar14 == (byte *******)0x0) {
      local_360 = (byte ******)&DAT_1800d4ecd;
      pppppppbVar19 = lpMem_01;
    }
    else {
      pppppppbVar14[1] = (byte ******)0x0;
      *pppppppbVar14 = (byte ******)0x0;
      pppppppbVar14[3] = (byte ******)0x0;
      pppppppbVar14[2] = (byte ******)0x0;
      pppppppbVar14[5] = (byte ******)0x0;
      pppppppbVar14[4] = (byte ******)0x0;
      pppppppbVar14[7] = (byte ******)0x0;
      pppppppbVar14[6] = (byte ******)0x0;
      pppppppbVar14[9] = (byte ******)0x0;
      pppppppbVar14[8] = (byte ******)0x0;
      pppppppbVar14[0xb] = (byte ******)0x0;
      pppppppbVar14[10] = (byte ******)0x0;
      pppppppbVar14[0xd] = (byte ******)0x0;
      pppppppbVar14[0xc] = (byte ******)0x0;
      pppppppbVar14[0xf] = (byte ******)0x0;
      pppppppbVar14[0xe] = (byte ******)0x0;
      pppppppbVar14[0x10] = (byte ******)0x0;
      *(undefined4 *)(pppppppbVar14 + 0x11) = 0;
      *(code *)((longlong)pppppppbVar14 + 0x8c) = (code)0x0;
      local_360 = (byte ******)pppppppbVar14;
      FUN_180099d78((char *)pppppppbVar14,0x8d,0x1800d6bb0,0x8c);
      uStack_358 = 0x100000001;
      pppppppbVar19 = pppppppbVar14;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_360);
    if ((pppppppbVar14 != (byte *******)0x0) && (pppppppbVar19 != (byte *******)0x0)) {
      pvVar13 = GetProcessHeap();
      HeapFree(pvVar13,0,pppppppbVar19);
    }
    local_360 = (byte ******)&DAT_1800d4ecd;
    uStack_358 = 0;
    local_350 = &DAT_1800d4ecd;
    pvVar13 = GetProcessHeap();
    pppppppbVar14 = (byte *******)HeapAlloc(pvVar13,0,7);
    if (pppppppbVar14 == (byte *******)0x0) {
      local_360 = (byte ******)&DAT_1800d4ecd;
    }
    else {
      *(undefined4 *)pppppppbVar14 = 0;
      *(undefined2 *)((longlong)pppppppbVar14 + 4) = 0;
      *(code *)((longlong)pppppppbVar14 + 6) = (code)0x0;
      local_360 = (byte ******)pppppppbVar14;
      FUN_180099d78((char *)pppppppbVar14,7,0x1800d6c58,6);
      uStack_358 = 0x100000001;
      lpMem_01 = pppppppbVar14;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_360);
    if ((pppppppbVar14 != (byte *******)0x0) && (lpMem_01 != (byte *******)0x0)) {
      pvVar13 = GetProcessHeap();
      HeapFree(pvVar13,0,lpMem_01);
      local_360 = (byte ******)0x0;
      uStack_358 = 0;
    }
    param_3[0xdf] = 0;
    param_3[0xcc] = 2;
    param_3[1] = 0;
    param_3[4] = 0;
    param_3[0x32e] = 0;
    param_3[0x2c] = 1;
    param_3[0x276] = 0;
    FUN_1800079f8(local_4a8,0x1800d9258,0xc);
    *(int *)((longlong)local_440 + 0xc) = 1;
    *(undefined1 *)(local_440 + 3) = 0xb;
    *(int *)((longlong)local_440 + 0x3c) = 7;
    *(int *)((longlong)local_440 + 0x1c) = 1;
    *(int *)((longlong)local_440 + 0x2c) = 1;
    FUN_1800079f8(local_580,0x1800d9268,0x19);
    *(int *)((longlong)local_438 + 0xc) = 1;
    *(undefined1 *)(local_438 + 3) = 0xb;
    *(int *)((longlong)local_438 + 0x1c) = 10;
    *(int *)((longlong)local_438 + 0x2c) = 10;
    *(int *)((longlong)local_438 + 0x3c) = 0x3c;
    FUN_1800079f8(local_488,0x1800d9220,0x17);
    *(int *)((longlong)local_430 + 0xc) = 1;
    *(undefined1 *)(local_430 + 3) = 5;
    *(int *)((longlong)local_430 + 0x1c) = 1;
    FUN_1800079f8(local_570,0x1800d9238,0x19);
    *(int *)((longlong)local_428 + 0xc) = 1;
    *(undefined1 *)(local_428 + 3) = 5;
    *(int *)((longlong)local_428 + 0x1c) = 1;
    FUN_1800079f8(local_568,0x1800d92b8,0x16);
    *(int *)((longlong)local_420 + 0xc) = 1;
    *(undefined1 *)(local_420 + 3) = 5;
    *(int *)((longlong)local_420 + 0x1c) = 1;
    FUN_1800079f8(local_560,0x1800d92d0,0x15);
    *(int *)((longlong)local_418 + 0xc) = 1;
    *(undefined1 *)(local_418 + 3) = 5;
    *(int *)((longlong)local_418 + 0x1c) = 1;
    FUN_1800079f8(local_558,0x1800d9288,0x11);
    *(int *)((longlong)local_410 + 0xc) = 1;
    *(undefined1 *)(local_410 + 3) = 0xe;
    *(int *)((longlong)local_410 + 0x1c) = 0x4242ff;
    FUN_1800079f8(local_550,0x1800d92a0,0x13);
    *(int *)((longlong)local_408 + 0xc) = 1;
    *(int *)((longlong)local_408 + 0x1c) = 0x4080ff;
    *(undefined1 *)(local_408 + 3) = 0xe;
    FUN_1800079f8(local_548,0x1800d9328,0x10);
    *(int *)((longlong)local_400 + 0xc) = 1;
    *(int *)((longlong)local_400 + 0x1c) = 0x8fff20;
    *(undefined1 *)(local_400 + 3) = 0xe;
    FUN_1800079f8(local_540,0x1800d9340,0x10);
    *(int *)((longlong)local_3f8 + 0xc) = 1;
    *(int *)((longlong)local_3f8 + 0x1c) = 0xc0c0c0;
    *(undefined1 *)(local_3f8 + 3) = 0xe;
    FUN_1800079f8(local_538,0x1800d92e8,0x1b);
    *(int *)((longlong)local_3f0 + 0xc) = 1;
    *(int *)((longlong)local_3f0 + 0x1c) = 0x2b1920;
    *(undefined1 *)(local_3f0 + 3) = 0xe;
    FUN_1800079f8(local_530,0x1800d9308,0x19);
    *(int *)((longlong)local_3e8 + 0xc) = 1;
    *(undefined1 *)(local_3e8 + 3) = 0xe;
    *(int *)((longlong)local_3e8 + 0x1c) = 0xff80ff;
    FUN_1800079f8(local_528,0x1800d9390,0x1f);
    *(int *)((longlong)local_3e0 + 0xc) = 1;
    *(undefined1 *)(local_3e0 + 3) = 0xe;
    *(int *)((longlong)local_3e0 + 0x1c) = 0x2b2320;
    FUN_1800079f8(local_520,0x1800d93b0,0x18);
    *(int *)((longlong)local_3d8 + 0xc) = 1;
    *(undefined1 *)(local_3d8 + 3) = 0xe;
    *(int *)((longlong)local_3d8 + 0x1c) = 0;
    FUN_1800079f8(local_518,0x1800d9358,0x1e);
    *(int *)((longlong)local_3d0 + 0xc) = 1;
    *(undefined1 *)(local_3d0 + 3) = 0xe;
    *(int *)((longlong)local_3d0 + 0x1c) = 0xff80ff;
    FUN_1800079f8(local_510,0x1800d9378,0x15);
    *(int *)((longlong)local_3c8 + 0xc) = 1;
    *(int *)((longlong)local_3c8 + 0x1c) = 0x694536;
    *(undefined1 *)(local_3c8 + 3) = 0xe;
    FUN_1800079f8(local_508,0x1800d93f8,0x1b);
    *(int *)((longlong)local_3c0 + 0xc) = 1;
    *(undefined1 *)(local_3c0 + 3) = 0xe;
    *(int *)((longlong)local_3c0 + 0x1c) = 0x2b2320;
    FUN_1800079f8(local_500,0x1800d9418,0x11);
    *(int *)((longlong)local_3b8 + 0xc) = 1;
    *(int *)((longlong)local_3b8 + 0x1c) = 0xeccaa8;
    *(undefined1 *)(local_3b8 + 3) = 0xe;
    FUN_1800079f8(local_4f8,0x1800d93d0,0x1c);
    *(int *)((longlong)local_3b0 + 0xc) = 1;
    *(undefined1 *)(local_3b0 + 3) = 0xe;
    *(int *)((longlong)local_3b0 + 0x1c) = 0x2b2320;
    FUN_1800079f8((longlong *)local_4f0,0x1800d93f0,4);
    *(int *)((longlong)local_310 + 0xc) = 1;
    if ((byte *****)local_310[10] != (byte *****)0x0) {
      (*(code *)local_310[10])
                (*(int *)((longlong)local_310 + 0x4c),"Arial;Consolas;Segoe UI;Tahoma");
    }
    *(undefined1 *)(local_310 + 3) = 0x16;
    *(int *)((longlong)local_310 + 0x1c) = 1;
    FUN_1800079f8(local_4e8,0x1800d9490,9);
    *(int *)((longlong)local_3a8 + 0xc) = 1;
    *(undefined1 *)(local_3a8 + 3) = 0xb;
    *(int *)((longlong)local_3a8 + 0x1c) = 9;
    *(int *)((longlong)local_3a8 + 0x2c) = 5;
    *(int *)((longlong)local_3a8 + 0x3c) = 0x1e;
    FUN_1800079f8(local_4e0,0x1800d9430,0x1b);
    *(int *)((longlong)local_3a0 + 0xc) = 1;
    *(int *)((longlong)local_3a0 + 0x1c) = 0x1e;
    *(int *)((longlong)local_3a0 + 0x3c) = 0x7fffffff;
    *(undefined1 *)(local_3a0 + 3) = 0xb;
    *(int *)((longlong)local_3a0 + 0x2c) = 0;
    FUN_1800079f8(local_4d8,0x1800d9450,0x1a);
    *(int *)((longlong)local_398 + 0xc) = 1;
    *(undefined1 *)(local_398 + 3) = 0xb;
    *(int *)((longlong)local_398 + 0x1c) = 0xe1;
    *(int *)((longlong)local_398 + 0x2c) = 0;
    *(int *)((longlong)local_398 + 0x3c) = 0x7fffffff;
    FUN_1800079f8(local_4d0,0x1800d94e0,0x2b);
    *(int *)((longlong)local_448 + 0xc) = 1;
    *(undefined1 *)(local_448 + 3) = 0xb;
    *(int *)((longlong)local_448 + 0x1c) = 10;
    *(int *)((longlong)local_448 + 0x2c) = 1;
    *(int *)((longlong)local_448 + 0x3c) = 0x3c;
    FUN_1800079f8(local_4c8,0x1800d9510,0x2a);
    *(int *)((longlong)local_1c0[0] + 0xc) = 1;
    *(undefined1 *)(local_1c0[0] + 3) = 0xb;
    *(int *)((longlong)local_1c0[0] + 0x1c) = 1;
    *(int *)((longlong)local_1c0[0] + 0x2c) = 1;
    *(int *)((longlong)local_1c0[0] + 0x3c) = 0x3c;
    FUN_1800079f8((longlong *)local_4c0,0x1800d94a0,0x19);
    *(undefined4 *)((longlong)local_578 + 0xc) = 1;
    *(code *)(local_578 + 3) = (code)0x5;
    *(undefined4 *)((longlong)local_578 + 0x1c) = 0;
    FUN_1800079f8((longlong *)local_4b8,0x1800d94c0,0x19);
    *(int *)((longlong)local_588 + 0xc) = 1;
    *(undefined1 *)(local_588 + 3) = 5;
    *(int *)((longlong)local_588 + 0x1c) = 0;
    FUN_1800079f8(local_4b0,0x1800d9570,0x10);
    *(int *)((longlong)local_210[0] + 0xc) = 1;
    *(undefined1 *)(local_210[0] + 3) = 5;
    *(int *)((longlong)local_210[0] + 0x1c) = 0;
    ppppppbVar12 = (byte ******)FUN_1800079f8(plVar16,0x1800d9588,0x1e);
    *(undefined4 *)((longlong)plVar16 + 0xc) = 1;
    *(undefined1 *)(plVar16 + 3) = 5;
    *(undefined4 *)((longlong)plVar16 + 0x1c) = 1;
    *(undefined1 *)(piVar25 + 6) = 5;
    piVar25[7] = 0;
    goto LAB_18006567c;
  }
  if (param_3[3] == 0) {
    ppppppbVar12 = (byte ******)
                   FUN_1800254e8(auVar29,param_2,(longlong)param_3,uVar17,param_5,pcVar21,pcVar23,
                                 param_8,param_9,param_10);
    *(int *)local_2e0[0] = (int)ppppppbVar12;
  }
  if ((int)*local_2e0[0] != 0) goto LAB_18006567c;
  if (param_3[0x1b] != 0) {
    if (plVar28 != (longlong *)0x0) {
      FUN_180060a70(plVar28);
      FUN_1800966b8(plVar28);
      ppppppbVar12 = (byte ******)(**(code **)(param_3 + 0x448))(4,0);
    }
    goto LAB_18006567c;
  }
  ppppppbVar12 = (byte ******)(**(code **)(param_3 + 0x3ec))(param_3[0x270]);
  iVar6 = (int)ppppppbVar12;
  if (iVar6 == 0) {
    *local_480 = 0;
    goto LAB_18006567c;
  }
  if (*local_480 == 0) {
    uVar10 = 0;
  }
  else {
    uVar10 = param_3[3];
  }
  if (plVar28 == (longlong *)0x0) {
    plVar28 = (longlong *)FUN_180096150(0x108);
    plVar28[1] = 0;
    *plVar28 = 0;
    plVar28[3] = 0;
    plVar28[2] = 0;
    plVar28[5] = 0;
    plVar28[4] = 0;
    plVar28[7] = 0;
    plVar28[6] = 0;
    plVar28[9] = 0;
    plVar28[8] = 0;
    plVar28[0xb] = 0;
    plVar28[10] = 0;
    plVar28[0xd] = 0;
    plVar28[0xc] = 0;
    plVar28[0xf] = 0;
    plVar28[0xe] = 0;
    plVar28[0x11] = 0;
    plVar28[0x10] = 0;
    plVar28[0x13] = 0;
    plVar28[0x12] = 0;
    plVar28[0x15] = 0;
    plVar28[0x14] = 0;
    plVar28[0x17] = 0;
    plVar28[0x16] = 0;
    plVar28[0x19] = 0;
    plVar28[0x18] = 0;
    plVar28[0x1b] = 0;
    plVar28[0x1a] = 0;
    plVar28[0x1d] = 0;
    plVar28[0x1c] = 0;
    plVar28[0x1f] = 0;
    plVar28[0x1e] = 0;
    plVar28[0x20] = 0;
    *plVar28 = 0;
    plVar28[1] = 0;
    plVar28[2] = 0;
    *(undefined1 *)(plVar28 + 3) = 0;
    plVar28[6] = (longlong)&DAT_1800d4ecd;
    plVar28[4] = (longlong)&DAT_1800d4ecd;
    plVar28[5] = 0;
    plVar28[9] = (longlong)&DAT_1800d4ecd;
    plVar28[7] = (longlong)&DAT_1800d4ecd;
    plVar28[8] = 0;
    plVar28[0xc] = (longlong)&DAT_1800d4ecd;
    plVar28[10] = (longlong)&DAT_1800d4ecd;
    plVar28[0xb] = 0;
    *(int *)(plVar28 + 0xd) = 0x1010101;
    plVar28[0xf] = 0;
    plVar28[0xe] = 0;
    plVar28[0x10] = 0;
    plVar28[0x11] = 0;
    local_2e0[0] = plVar28;
    FUN_18000ddb8(plVar28 + 0xe,(undefined8 *)&DAT_1800d953c,3);
    plVar28[0x12] = 0;
    plVar28[0x13] = 0;
    plVar28[0x14] = 0;
    plVar28[0x15] = 0;
    plVar28[0x16] = 0;
    plVar28[0x17] = 0;
    *(int *)(plVar28 + 0x18) = 0;
    plVar28[0x1a] = 0;
    plVar28[0x19] = 0;
    plVar28[0x1b] = 0;
    plVar28[0x1c] = 0;
    FUN_18000ddb8(plVar28 + 0x19,(undefined8 *)"Arial",5);
    plVar28[0x1f] = 1;
    *(undefined1 *)(plVar28 + 0x20) = 0;
    plVar28[0x1e] = 0xa0000000a;
    plVar28[0x1d] = 0xa0000000a;
    ppppppbVar12 = (byte ******)(**(code **)(param_3 + 0x448))(4,plVar28);
  }
  if (uVar10 == 0) {
    *local_480 = (uint)(iVar6 != 0);
    *local_490 = 0;
    *local_450 = 0x7fffffff;
LAB_180064688:
    *(undefined1 *)(plVar28 + 3) = 1;
    bVar5 = FUN_180026690((longlong)local_488);
    *(bool *)(plVar28 + 0xd) = bVar5;
    bVar5 = FUN_180026690((longlong)local_570);
    *(bool *)((longlong)plVar28 + 0x69) = bVar5;
    bVar5 = FUN_180026690((longlong)local_568);
    *(bool *)((longlong)plVar28 + 0x6a) = bVar5;
    bVar5 = FUN_180026690((longlong)local_560);
    *(bool *)((longlong)plVar28 + 0x6b) = bVar5;
    FUN_18001a120(plVar28 + 0xe,(undefined8 *)&DAT_1800d953c,3);
    uVar7 = FUN_180026888((longlong)local_558);
    *(undefined4 *)(plVar28 + 0x12) = uVar7;
    uVar7 = FUN_180026888((longlong)local_550);
    *(undefined4 *)((longlong)plVar28 + 0x94) = uVar7;
    uVar7 = FUN_180026888((longlong)local_548);
    *(undefined4 *)(plVar28 + 0x13) = uVar7;
    uVar7 = FUN_180026888((longlong)local_540);
    *(undefined4 *)((longlong)plVar28 + 0x9c) = uVar7;
    uVar7 = FUN_180026888((longlong)local_538);
    *(undefined4 *)(plVar28 + 0x14) = uVar7;
    uVar7 = FUN_180026888((longlong)local_530);
    *(undefined4 *)((longlong)plVar28 + 0xa4) = uVar7;
    uVar7 = FUN_180026888((longlong)local_528);
    *(undefined4 *)(plVar28 + 0x15) = uVar7;
    uVar7 = FUN_180026888((longlong)local_520);
    *(undefined4 *)((longlong)plVar28 + 0xac) = uVar7;
    uVar7 = FUN_180026888((longlong)local_518);
    *(undefined4 *)(plVar28 + 0x16) = uVar7;
    uVar7 = FUN_180026888((longlong)local_510);
    *(undefined4 *)((longlong)plVar28 + 0xb4) = uVar7;
    uVar7 = FUN_180026888((longlong)local_508);
    *(undefined4 *)(plVar28 + 0x17) = uVar7;
    uVar7 = FUN_180026888((longlong)local_500);
    *(undefined4 *)((longlong)plVar28 + 0xbc) = uVar7;
    uVar7 = FUN_180026888((longlong)local_4f8);
    *(undefined4 *)(plVar28 + 0x18) = uVar7;
    uVar8 = FUN_180026550((longlong)local_4f0);
    switch(uVar8) {
    default:
      pcVar18 = "Arial";
      break;
    case 1:
      pcVar18 = "Consolas";
      break;
    case 2:
      pcVar18 = "Segoe UI";
      break;
    case 3:
      pcVar18 = "Tahoma";
    }
    cVar2 = *pcVar18;
    pcVar26 = pcVar18;
    while (cVar2 != '\0') {
      pcVar26 = pcVar26 + 1;
      cVar2 = *pcVar26;
    }
    FUN_18001a120(plVar28 + 0x19,(undefined8 *)pcVar18,(longlong)pcVar26 - (longlong)pcVar18);
    uVar15 = FUN_180026708((longlong)local_4e8);
    *(int *)(plVar28 + 0x1d) = (int)uVar15;
    uVar15 = FUN_180026708((longlong)local_4e0);
    *(int *)((longlong)plVar28 + 0xec) = (int)uVar15;
    uVar15 = FUN_180026708((longlong)local_4d8);
    *(int *)(plVar28 + 0x1e) = (int)uVar15;
    uVar15 = FUN_180026708((longlong)local_4d0);
    *(int *)((longlong)plVar28 + 0xf4) = (int)uVar15;
    uVar15 = FUN_180026708((longlong)local_4c8);
    *(int *)(plVar28 + 0x1f) = (int)uVar15;
    bVar5 = FUN_180026690((longlong)local_4c0);
    *(bool *)((longlong)plVar28 + 0xfc) = bVar5;
    bVar5 = FUN_180026690((longlong)local_4b8);
    *(bool *)((longlong)plVar28 + 0xfd) = bVar5;
    bVar5 = FUN_180026690((longlong)local_4b0);
    *(bool *)((longlong)plVar28 + 0xfe) = bVar5;
    bVar5 = FUN_180026690((longlong)plVar16);
    *(bool *)((longlong)plVar28 + 0xff) = bVar5;
    bVar5 = FUN_180026690((longlong)piVar25);
    *(bool *)(plVar28 + 0x20) = bVar5;
    (**(code **)(param_3 + 0x718))(plVar28 + 4);
    plVar16 = FUN_18005f738(&local_478,plVar28 + 4);
    FUN_180007920(plVar28 + 7,plVar16);
    auVar29 = FUN_180004590(&local_478);
    pcVar21 = (code *)plVar28[7];
    if ((byte *******)plVar28[7] == (byte *******)0x0) {
      pcVar21 = (code *)lpMem_01;
    }
    pppppppbVar14 = (byte *******)plVar28[4];
    if ((byte *******)plVar28[4] == (byte *******)0x0) {
      pppppppbVar14 = lpMem_01;
    }
    FUN_180006050(auVar29,param_2,&local_330,0x1800d9540,pppppppbVar14,pcVar21,pcVar23,param_8,
                  param_9,param_10);
    plVar16 = FUN_18005f9a0(&local_478);
    FUN_180007920(plVar28 + 10,plVar16);
    auVar29 = FUN_180004590(&local_478);
    pppppppbVar14 = (byte *******)plVar28[10];
    if ((byte *******)plVar28[10] == (byte *******)0x0) {
      pppppppbVar14 = lpMem_01;
    }
    ppppppbVar12 = (byte ******)
                   FUN_180006050(auVar29,param_2,&local_330,0x1800d9630,pppppppbVar14,pcVar21,
                                 pcVar23,param_8,param_9,param_10);
    local_340[0] = local_330;
  }
  else if ((char)plVar28[3] == '\0') goto LAB_180064688;
  if (param_3[0x1bc] == 0) {
    local_308 = *(longlong *)(param_3 + 0x13c);
  }
  else {
    local_308 = *(longlong *)(param_3 + 0x4ac);
  }
  if ((int)(*local_460 / 86400000000) < 1) {
    *local_460 = local_308;
  }
  if ((int)(*local_458 / 86400000000) < 1) {
    *local_458 = local_308;
  }
  pppppppbVar14 = (byte *******)local_340[0];
  if (*param_3 <= (int)uVar10) {
LAB_18006565c:
    local_340[0] = (byte ******)pppppppbVar14;
    *local_480 = (uint)(iVar6 != 0);
    *(code **)(param_3 + 1000) = FUN_180061720;
LAB_18006567c:
    lpMem = local_340[0];
    if (((int)uStack_328 != 0) && ((byte *******)local_340[0] != (byte *******)0x0)) {
      pvVar13 = GetProcessHeap();
      uVar10 = HeapFree(pvVar13,0,lpMem);
      ppppppbVar12 = (byte ******)(ulonglong)uVar10;
    }
    return (byte ****)ppppppbVar12;
  }
LAB_180064998:
  if (param_3[0x47e] != 0) goto LAB_180065648;
  (**(code **)(param_3 + 0x4d4))(local_1c0,&local_308,"EST-05EDT+01,M3.2.0/02:00,M11.1.0/02:00");
  local_2e0[0] = (longlong *)((local_308 / 86400000000) * 86400000000);
  uVar15 = FUN_180026708((longlong)local_4a8);
  local_210[0] = (longlong *)(extraout_x11 + (longlong)(int)uVar15 * 86400000000 + -1000000);
  (**(code **)(param_3 + 0x4d4))(local_340,local_2e0,"UTC+00");
  auVar29 = (**(code **)(param_3 + 0x4d4))(&local_310,local_210,"UTC+00");
  uStack_2a8 = 0;
  local_2b0 = (LPVOID)0x0;
  local_2a0 = 0;
  FUN_1800936d8(auVar29,param_2,&local_2b0,(longlong *)local_340,'\x01');
  uStack_2c8 = 0;
  local_2d0 = (LPVOID)0x0;
  local_2c0 = 0;
  FUN_1800936d8(extraout_q0_10,param_2,&local_2d0,(longlong *)&local_310,'\x01');
  uStack_228 = 0;
  local_230 = (LPVOID)0x0;
  local_220 = 0;
  FUN_1800936d8(extraout_q0_11,param_2,&local_230,&local_308,'\0');
  plVar16 = local_460;
  uStack_248 = 0;
  local_250 = (LPVOID)0x0;
  local_240 = 0;
  FUN_1800936d8(extraout_q0_12,param_2,&local_250,local_460,'\0');
  pppppppbVar19 = (byte *******)0x0;
  uStack_268 = 0;
  local_270 = (LPVOID)0x0;
  local_260 = 0;
  ppppppbVar12 = (byte ******)FUN_1800936d8(extraout_q0_13,param_2,&local_270,local_458,'\0');
  if ((uVar10 == 0) || ((0 < (int)(local_308 / 86400000000) && (*plVar16 <= local_308)))) {
    if (*local_490 != 0) {
      ppppppbVar12 = (byte ******)
                     FUN_180006050(extraout_q0_14,param_2,&local_330,0x1800d96d0,pppppppbVar19,
                                   pcVar21,pcVar23,param_8,param_9,param_10);
      auVar29 = extraout_q0_23;
LAB_180064ed8:
      pppppppbVar14 = (byte *******)local_330;
      if ((*local_490 == 0) && (*local_4a0 == 0)) {
        puVar11 = FUN_1800936d8(auVar29,param_2,&local_388,&local_308,'\0');
        pppppppbVar14 = (byte *******)*puVar11;
        if ((byte *******)*puVar11 == (byte *******)0x0) {
          pppppppbVar14 = lpMem_01;
        }
        FUN_180006050(extraout_q0_24,param_2,&local_330,0x1800d96f8,pppppppbVar14,pcVar21,pcVar23,
                      param_8,param_9,param_10);
        pvVar4 = local_388;
        if (((int)local_380 != 0) && (local_388 != (LPVOID)0x0)) {
          pvVar13 = GetProcessHeap();
          HeapFree(pvVar13,0,pvVar4);
          local_388 = (LPVOID)0x0;
          local_380 = 0;
        }
        pppppppbVar14 = (byte *******)local_330;
        pppppppbVar19 = (byte *******)local_330;
        if ((byte *******)local_330 == (byte *******)0x0) {
          pppppppbVar19 = lpMem_01;
        }
        (**(code **)(param_3 + 0x14))(pppppppbVar19,0);
        pcVar23 = FUN_180065850;
        pcVar21 = FUN_180065820;
        FUN_180096870(&lStack_130,0x30,4);
        FUN_1800079f8(&lStack_130,0x1800d96a0,0x1b);
        local_124 = 1;
        FUN_1800079f8(&lStack_118,0x1800d96c0,10);
        local_10c = 1;
        FUN_1800079f8(&lStack_100,0x1800d9780,7);
        local_f4 = 1;
        puVar11 = (undefined8 *)(**(code **)(param_3 + 0x102))(&local_478);
        pppppppbVar19 = (byte *******)*puVar11;
        if ((byte *******)*puVar11 == (byte *******)0x0) {
          pppppppbVar19 = lpMem_01;
        }
        FUN_18000bbb8(alStack_e8,(char *)pppppppbVar19);
        pvVar4 = local_478;
        if (((int)local_470 != 0) && (local_478 != (LPVOID)0x0)) {
          pvVar13 = GetProcessHeap();
          HeapFree(pvVar13,0,pvVar4);
          local_478 = (LPVOID)0x0;
          local_470 = 0;
        }
        FUN_1800079f8(&lStack_d0,0x1800d9788,0x11);
        local_c4 = 1;
        FUN_180007920(alStack_b8,(longlong *)&local_2b0);
        FUN_1800079f8(&lStack_a0,0x1800d9718,0xf);
        local_94 = 1;
        FUN_180007920(alStack_88,(longlong *)&local_2d0);
        if ((*(char **)(param_3 + 0x27e) != (char *)0x0) && (**(char **)(param_3 + 0x27e) != '\0'))
        {
          FUN_1800079f8((longlong *)(param_3 + 0x27e),0x1800d4ecd,0);
          param_3[0x281] = 1;
        }
        local_1d0 = "";
        local_1e0 = "";
        uStack_1d8 = 0;
        local_1e0 = FUN_180004620(0x4e);
        if (local_1e0 == (char *)0x0) {
          local_1e0 = local_1d0;
        }
        else {
          pcVar21 = (code *)0x4d;
          FUN_180099d78(local_1e0,0x4e,0x1800d9730,0x4d);
          uStack_1d8 = 0x100000001;
        }
        local_350 = &DAT_1800d4ecd;
        local_360 = (byte ******)&DAT_1800d4ecd;
        uStack_358 = 0;
        pppppppbVar19 = (byte *******)FUN_180004620(0x23);
        if (pppppppbVar19 == (byte *******)0x0) {
          local_360 = (byte ******)&DAT_1800d4ecd;
          pppppppbVar27 = lpMem_01;
        }
        else {
          pcVar21 = (code *)0x22;
          local_360 = (byte ******)pppppppbVar19;
          FUN_180099d78((char *)pppppppbVar19,0x23,0x1800d97c0,0x22);
          uStack_358 = 0x100000001;
          pppppppbVar27 = pppppppbVar19;
        }
        local_1f0 = "";
        local_200 = "";
        uStack_1f8 = 0;
        local_200 = FUN_180004620(0x38);
        if (local_200 == (char *)0x0) {
          local_200 = local_1f0;
        }
        else {
          pcVar21 = (code *)0x37;
          FUN_180099d78(local_200,0x38,0x1800d97e8,0x37);
          uStack_1f8 = 0x100000001;
        }
        iVar9 = (**(code **)(param_3 + 0x27c))(&local_200);
        plVar16 = local_460;
        if (iVar9 == 0) {
          (**(code **)(param_3 + 0x14))("Error making HTTP request.",0);
          pcVar18 = local_200;
          if (((int)uStack_1f8 != 0) && (local_200 != (char *)0x0)) {
            pvVar13 = GetProcessHeap();
            HeapFree(pvVar13,0,pcVar18);
            local_200 = (char *)0x0;
            uStack_1f8 = 0;
          }
          if ((pppppppbVar19 != (byte *******)0x0) && (pppppppbVar27 != (byte *******)0x0)) {
            pvVar13 = GetProcessHeap();
            HeapFree(pvVar13,0,pppppppbVar27);
            local_360 = (byte ******)0x0;
            uStack_358 = 0;
          }
          pcVar18 = local_1e0;
          if (((int)uStack_1d8 != 0) && (local_1e0 != (char *)0x0)) {
            pvVar13 = GetProcessHeap();
            HeapFree(pvVar13,0,pcVar18);
            local_1e0 = (char *)0x0;
            uStack_1d8 = 0;
          }
          pcVar21 = FUN_180065850;
          pppppppbVar19 = (byte *******)0x4;
          ppppppbVar12 = (byte ******)FUN_180096738(&lStack_130,0x30,4);
          goto LAB_180065288;
        }
        *local_490 = 1;
        puVar11 = FUN_1800936d8(extraout_q0_25,param_2,&local_478,local_460,'\0');
        pppppppbVar14 = (byte *******)*puVar11;
        if ((byte *******)*puVar11 == (byte *******)0x0) {
          pppppppbVar14 = lpMem_01;
        }
        FUN_180006050(extraout_q0_33,param_2,&local_330,0x1800d9870,pppppppbVar14,pcVar21,pcVar23,
                      param_8,param_9,param_10);
        FUN_180004590(&local_478);
        uVar15 = FUN_180026708((longlong)local_580);
        *plVar16 = *plVar16 + (longlong)(int)uVar15 * 60000000;
        puVar11 = FUN_1800936d8(extraout_q0_34,param_2,&local_478,plVar16,'\0');
        pppppppbVar14 = (byte *******)*puVar11;
        if ((byte *******)*puVar11 == (byte *******)0x0) {
          pppppppbVar14 = lpMem_01;
        }
        FUN_180006050(extraout_q0_35,param_2,&local_330,0x1800d9890,pppppppbVar14,pcVar21,pcVar23,
                      param_8,param_9,param_10);
        FUN_180004590(&local_478);
        FUN_180004590(&local_200);
        FUN_180004590(&local_360);
        FUN_180004590(&local_1e0);
        FUN_180096738(&lStack_130,0x30,4);
        FUN_180004590(&local_270);
        FUN_180004590(&local_250);
        FUN_180004590(&local_230);
        FUN_180004590(&local_2d0);
        ppppppbVar12 = (byte ******)FUN_180004590(&local_2b0);
        local_340[0] = local_330;
        goto LAB_18006567c;
      }
      goto LAB_180065288;
    }
    local_280 = &DAT_1800d4ecd;
    local_290 = (byte ******)&DAT_1800d4ecd;
    uStack_288 = 0;
    puVar1 = _DAT_1800ffc38;
    if (DAT_1800ffc50 < 0x10) {
      puVar1 = &DAT_1800ffc38;
    }
    pppppppbVar19 = (byte *******)plVar28[10];
    if ((byte *******)plVar28[10] == (byte *******)0x0) {
      pppppppbVar19 = lpMem_01;
    }
    FUN_180006050(extraout_q0_14,param_2,&local_290,0x1800d912c,pppppppbVar19,puVar1,pcVar23,param_8
                  ,param_9,param_10);
    lpMem_00 = (byte *******)local_290;
    local_2f0 = 0;
    local_2e8 = 0;
    uStack_2f8 = 0;
    local_300 = (byte ******)0x0;
    pppppppbVar27 = (byte *******)local_290;
    if ((byte *******)local_290 == (byte *******)0x0) {
      pppppppbVar27 = lpMem_01;
    }
    cVar3 = *(code *)pppppppbVar27;
    pppppppbVar19 = pppppppbVar27;
    while (cVar3 != (code)0x0) {
      pppppppbVar19 = (byte *******)((longlong)pppppppbVar19 + 1);
      cVar3 = *(code *)pppppppbVar19;
    }
    pppppppbVar19 = (byte *******)((longlong)pppppppbVar19 - (longlong)pppppppbVar27);
    uVar17 = FUN_18000ddb8(&local_300,pppppppbVar27,(ulonglong)pppppppbVar19);
    plVar16 = local_458;
    auVar29 = extraout_q0_15;
    if (0 < (int)(*local_458 / 86400000000) && *local_458 <= local_308) {
      pppppppbVar19 = &local_300;
      uVar8 = FUN_1800606a0(uVar17,extraout_x1_31,(LPCSTR)pppppppbVar19);
      *local_450 = uVar8;
      *plVar16 = *plVar16 + 30000000;
      auVar29 = extraout_q0_16;
    }
    pcVar21 = (code *)(ulonglong)*local_450;
    if (*local_450 == 0x7fffffff) {
LAB_180064e50:
      ppppppbVar12 = (byte ******)
                     FUN_180006050(auVar29,param_2,&local_330,0x1800d95f0,pppppppbVar19,pcVar21,
                                   pcVar23,param_8,param_9,param_10);
      *local_4a0 = 0;
      auVar29 = extraout_q0_20;
      if (0xf < local_2e8) {
        ppppppbVar12 = (byte ******)FUN_1800966b8(local_300);
        auVar29 = extraout_q0_21;
      }
      local_300 = (byte ******)((ulonglong)local_300 & 0xffffffffffffff00);
      local_2f0 = 0;
      local_2e8 = 0xf;
      if (((int)uStack_288 != 0) && (lpMem_00 != (byte *******)0x0)) {
        pvVar13 = GetProcessHeap();
        uVar8 = HeapFree(pvVar13,0,lpMem_00);
        ppppppbVar12 = (byte ******)(ulonglong)uVar8;
        local_290 = (byte ******)0x0;
        uStack_288 = 0;
        pppppppbVar19 = lpMem_00;
        auVar29 = extraout_q0_22;
      }
      goto LAB_180064ed8;
    }
    ppppppbVar12 = (byte ******)FUN_180026708((longlong)local_580);
    if ((int)ppppppbVar12 <= (int)pcVar21) {
      pppppppbVar19 = (byte *******)local_300;
      if (local_2e8 < 0x10) {
        pppppppbVar19 = &local_300;
      }
      FUN_180006050(extraout_q0_17,param_2,&local_330,0x1800d95a8,pppppppbVar19,pcVar21,pcVar23,
                    param_8,param_9,param_10);
      *local_4a0 = 0;
      auVar29 = extraout_q0_19;
      goto LAB_180064e50;
    }
    if (*plVar28 != plVar28[1]) {
      *local_4a0 = 1;
      if (0xf < local_2e8) {
        ppppppbVar12 = (byte ******)FUN_1800966b8(local_300);
      }
      local_300 = (byte ******)((ulonglong)local_300 & 0xffffffffffffff00);
      local_2f0 = 0;
      local_2e8 = 0xf;
      if (((int)uStack_288 != 0) && (lpMem_00 != (byte *******)0x0)) {
        pvVar13 = GetProcessHeap();
        uVar8 = HeapFree(pvVar13,0,lpMem_00);
        ppppppbVar12 = (byte ******)(ulonglong)uVar8;
        local_290 = (byte ******)0x0;
        uStack_288 = 0;
      }
      goto LAB_180065530;
    }
    pppppppbVar14 = (byte *******)local_300;
    if (local_2e8 < 0x10) {
      pppppppbVar14 = &local_300;
    }
    FUN_180006050(extraout_q0_17,param_2,&local_330,0x1800d9660,pppppppbVar14,pcVar21,pcVar23,
                  param_8,param_9,param_10);
    pcVar21 = (code *)&local_588;
    local_588 = local_310;
    local_578 = local_340[0];
    ppppppbVar12 = (byte ******)
                   FUN_180062278(extraout_q0_18,param_2,(longlong)param_3,plVar28,
                                 (longlong *)&local_578,(longlong *)pcVar21,pcVar23,param_8,param_9,
                                 param_10);
    *local_4a0 = 1;
    if (0xf < local_2e8) {
      ppppppbVar12 = (byte ******)FUN_1800966b8(local_300);
    }
    local_300 = (byte ******)((ulonglong)local_300 & 0xffffffffffffff00);
    local_2f0 = 0;
    local_2e8 = 0xf;
    if (((int)uStack_288 != 0) && (lpMem_00 != (byte *******)0x0)) {
      pvVar13 = GetProcessHeap();
      uVar8 = HeapFree(pvVar13,0,lpMem_00);
      ppppppbVar12 = (byte ******)(ulonglong)uVar8;
      local_290 = (byte ******)0x0;
      uStack_288 = 0;
    }
    pvVar4 = local_270;
    if (((int)uStack_268 != 0) && (local_270 != (LPVOID)0x0)) {
      pvVar13 = GetProcessHeap();
      uVar8 = HeapFree(pvVar13,0,pvVar4);
      ppppppbVar12 = (byte ******)(ulonglong)uVar8;
      local_270 = (LPVOID)0x0;
      uStack_268 = 0;
    }
    pvVar4 = local_250;
    if (((int)uStack_248 != 0) && (local_250 != (LPVOID)0x0)) {
      pvVar13 = GetProcessHeap();
      uVar8 = HeapFree(pvVar13,0,pvVar4);
      ppppppbVar12 = (byte ******)(ulonglong)uVar8;
      local_250 = (LPVOID)0x0;
      uStack_248 = 0;
    }
    pvVar4 = local_230;
    if (((int)uStack_228 != 0) && (local_230 != (LPVOID)0x0)) {
      pvVar13 = GetProcessHeap();
      uVar8 = HeapFree(pvVar13,0,pvVar4);
      ppppppbVar12 = (byte ******)(ulonglong)uVar8;
      local_230 = (LPVOID)0x0;
      uStack_228 = 0;
    }
    pvVar4 = local_2d0;
    if (((int)uStack_2c8 != 0) && (local_2d0 != (LPVOID)0x0)) {
      pvVar13 = GetProcessHeap();
      uVar8 = HeapFree(pvVar13,0,pvVar4);
      ppppppbVar12 = (byte ******)(ulonglong)uVar8;
      local_2d0 = (LPVOID)0x0;
      uStack_2c8 = 0;
    }
    pvVar4 = local_2b0;
    pppppppbVar14 = (byte *******)local_330;
    if (((int)uStack_2a8 == 0) || (local_2b0 == (LPVOID)0x0)) goto LAB_180065648;
    pvVar13 = GetProcessHeap();
    uVar8 = HeapFree(pvVar13,0,pvVar4);
    pppppppbVar14 = (byte *******)local_330;
  }
  else {
LAB_180065288:
    piVar25 = local_490;
    if (*local_490 == 1) {
      pppppppbVar27 = *(byte ********)(param_3 + 0x27e);
      if (*(byte ********)(param_3 + 0x27e) == (byte *******)0x0) {
        pppppppbVar27 = lpMem_01;
      }
      uVar8 = FUN_1800a7250(&DAT_1800d4ecd,(byte *)pppppppbVar27);
      ppppppbVar12 = (byte ******)(ulonglong)uVar8;
      if (uVar8 != 0) {
        *piVar25 = 0;
        pppppppbVar27 = *(byte ********)(param_3 + 0x27e);
        if (*(byte ********)(param_3 + 0x27e) == (byte *******)0x0) {
          pppppppbVar27 = lpMem_01;
        }
        iVar9 = FUN_1800a7250((byte *)"HTTP_REQUEST_ERROR",(byte *)pppppppbVar27);
        if (iVar9 == 0) {
          FUN_180006050(extraout_q0_26,param_2,&local_330,0x1800d9820,pppppppbVar19,pcVar21,pcVar23,
                        param_8,param_9,param_10);
          pppppppbVar14 = (byte *******)local_330;
          auVar29 = extraout_q0_27;
        }
        else {
          local_180 = &DAT_1800d4ecd;
          local_190 = (byte ******)&DAT_1800d4ecd;
          uStack_188 = 0;
          local_140 = 0;
          local_138 = 0;
          uStack_148 = 0;
          local_150 = (LPVOID)0x0;
          pppppppbVar19 = *(byte ********)(param_3 + 0x27e);
          if (*(byte ********)(param_3 + 0x27e) == (byte *******)0x0) {
            pppppppbVar19 = lpMem_01;
          }
          cVar3 = *(code *)pppppppbVar19;
          pppppppbVar27 = pppppppbVar19;
          while (cVar3 != (code)0x0) {
            pppppppbVar27 = (byte *******)((longlong)pppppppbVar27 + 1);
            cVar3 = *(code *)pppppppbVar27;
          }
          auVar29 = FUN_18000ddb8(&local_150,pppppppbVar19,
                                  (longlong)pppppppbVar27 - (longlong)pppppppbVar19);
          local_1a0 = &DAT_1800d4ecd;
          local_1b0 = (byte ******)&DAT_1800d4ecd;
          uStack_1a8 = 0;
          puVar1 = _DAT_1800ffc38;
          if (DAT_1800ffc50 < 0x10) {
            puVar1 = &DAT_1800ffc38;
          }
          pppppppbVar19 = (byte *******)plVar28[10];
          if ((byte *******)plVar28[10] == (byte *******)0x0) {
            pppppppbVar19 = lpMem_01;
          }
          FUN_180006050(auVar29,param_2,&local_1b0,0x1800d912c,pppppppbVar19,puVar1,pcVar23,param_8,
                        param_9,param_10);
          ppppppbVar12 = local_1b0;
          local_160 = 0;
          local_158 = 0;
          uStack_168 = 0;
          local_170 = (LPVOID)0x0;
          pppppppbVar19 = (byte *******)local_1b0;
          if ((byte *******)local_1b0 == (byte *******)0x0) {
            pppppppbVar19 = lpMem_01;
          }
          cVar3 = *(code *)pppppppbVar19;
          pppppppbVar27 = pppppppbVar19;
          while (cVar3 != (code)0x0) {
            pppppppbVar27 = (byte *******)((longlong)pppppppbVar27 + 1);
            cVar3 = *(code *)pppppppbVar27;
          }
          uVar17 = FUN_18000ddb8(&local_170,pppppppbVar19,
                                 (longlong)pppppppbVar27 - (longlong)pppppppbVar19);
          ppvVar22 = &local_150;
          ppvVar20 = &local_170;
          uVar15 = FUN_18005fc40(extraout_q0_28,param_2,uVar17,extraout_x1_32,(LPCSTR)ppvVar20,
                                 (longlong *)ppvVar22,pcVar23,param_8,param_9,param_10);
          pppppppbVar19 = lpMem_01;
          auVar29 = extraout_q0_29;
          if ((uVar15 & 1) == 0) {
            FUN_180006050(extraout_q0_29,param_2,&local_190,0x1800d9850,ppvVar20,ppvVar22,pcVar23,
                          param_8,param_9,param_10);
            pppppppbVar19 = (byte *******)local_190;
            auVar29 = extraout_q0_30;
          }
          if (0xf < local_158) {
            auVar29 = FUN_1800966b8(local_170);
          }
          local_170 = (LPVOID)((ulonglong)local_170 & 0xffffffffffffff00);
          local_160 = 0;
          local_158 = 0xf;
          if (((int)uStack_1a8 != 0) && ((byte *******)ppppppbVar12 != (byte *******)0x0)) {
            pvVar13 = GetProcessHeap();
            HeapFree(pvVar13,0,ppppppbVar12);
            local_1b0 = (byte ******)0x0;
            uStack_1a8 = 0;
            auVar29 = extraout_q0_31;
          }
          if (0xf < local_138) {
            auVar29 = FUN_1800966b8(local_150);
          }
          local_150 = (LPVOID)((ulonglong)local_150 & 0xffffffffffffff00);
          local_140 = 0;
          local_138 = 0xf;
          if (((int)uStack_188 != 0) && (pppppppbVar19 != (byte *******)0x0)) {
            pvVar13 = GetProcessHeap();
            HeapFree(pvVar13,0,pppppppbVar19);
            local_190 = (byte ******)0x0;
            uStack_188 = 0;
            auVar29 = extraout_q0_32;
          }
        }
        pcVar21 = (code *)&local_588;
        local_588 = local_310;
        local_578 = local_340[0];
        ppppppbVar12 = (byte ******)
                       FUN_180062278(auVar29,param_2,(longlong)param_3,plVar28,
                                     (longlong *)&local_578,(longlong *)pcVar21,pcVar23,param_8,
                                     param_9,param_10);
      }
    }
    lVar24 = *(longlong *)(local_498 + 0xc);
    if (lVar24 == 0) {
      if (*(code **)(local_498 + 0x10) != (code *)0x0) {
        ppppppbVar12 = (byte ******)(**(code **)(local_498 + 0x10))(local_498[0x12]);
      }
      lVar24 = *(longlong *)(local_498 + 0xc);
      if (lVar24 != 0) goto LAB_180065504;
      piVar25 = local_498 + 0x15;
    }
    else {
LAB_180065504:
      iVar9 = local_498[0x14];
      if (iVar9 == 0) {
        piVar25 = local_498 + 0x15;
      }
      else {
        uVar8 = uVar10 & ((int)uVar10 >> 0x1f ^ 0xffffffffU);
        if (iVar9 <= (int)uVar8) {
          uVar8 = iVar9 - 1;
        }
        piVar25 = (int *)(lVar24 + (longlong)(int)uVar8 * 4);
      }
    }
    *piVar25 = 0;
LAB_180065530:
    pvVar4 = local_270;
    if (((int)uStack_268 != 0) && (local_270 != (LPVOID)0x0)) {
      pvVar13 = GetProcessHeap();
      uVar8 = HeapFree(pvVar13,0,pvVar4);
      ppppppbVar12 = (byte ******)(ulonglong)uVar8;
      local_270 = (LPVOID)0x0;
      uStack_268 = 0;
    }
    pvVar4 = local_250;
    if (((int)uStack_248 != 0) && (local_250 != (LPVOID)0x0)) {
      pvVar13 = GetProcessHeap();
      uVar8 = HeapFree(pvVar13,0,pvVar4);
      ppppppbVar12 = (byte ******)(ulonglong)uVar8;
      local_250 = (LPVOID)0x0;
      uStack_248 = 0;
    }
    pvVar4 = local_230;
    if (((int)uStack_228 != 0) && (local_230 != (LPVOID)0x0)) {
      pvVar13 = GetProcessHeap();
      uVar8 = HeapFree(pvVar13,0,pvVar4);
      ppppppbVar12 = (byte ******)(ulonglong)uVar8;
      local_230 = (LPVOID)0x0;
      uStack_228 = 0;
    }
    pvVar4 = local_2d0;
    if (((int)uStack_2c8 != 0) && (local_2d0 != (LPVOID)0x0)) {
      pvVar13 = GetProcessHeap();
      uVar8 = HeapFree(pvVar13,0,pvVar4);
      ppppppbVar12 = (byte ******)(ulonglong)uVar8;
      local_2d0 = (LPVOID)0x0;
      uStack_2c8 = 0;
    }
    pvVar4 = local_2b0;
    if (((int)uStack_2a8 == 0) || (local_2b0 == (LPVOID)0x0)) goto LAB_180065648;
    pvVar13 = GetProcessHeap();
    uVar8 = HeapFree(pvVar13,0,pvVar4);
  }
  ppppppbVar12 = (byte ******)(ulonglong)uVar8;
  local_2b0 = (LPVOID)0x0;
  uStack_2a8 = 0;
LAB_180065648:
  uVar10 = uVar10 + 1;
  if (*param_3 <= (int)uVar10) goto LAB_18006565c;
  goto LAB_180064998;
}

