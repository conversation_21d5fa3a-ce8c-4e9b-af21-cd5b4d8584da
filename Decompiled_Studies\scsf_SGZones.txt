
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_SGZones(undefined1 param_1 [16],undefined1 param_2 [16],int *param_3,undefined8 param_4,
                 undefined8 param_5,undefined8 param_6,ulonglong param_7,ulonglong param_8,
                 ulonglong param_9,undefined8 *param_10)

{
  int iVar1;
  uint uVar2;
  char cVar3;
  undefined1 auVar4 [16];
  undefined1 auVar5 [16];
  undefined1 auVar6 [16];
  undefined1 auVar7 [16];
  undefined1 auVar8 [16];
  longlong *plVar9;
  bool bVar10;
  bool bVar11;
  uint uVar12;
  undefined4 uVar13;
  int iVar14;
  uint uVar15;
  int *piVar16;
  undefined8 *puVar17;
  HANDLE pvVar18;
  char *pcVar19;
  undefined8 uVar20;
  longlong **pplVar21;
  ulonglong uVar22;
  ulonglong uVar23;
  ulonglong uVar24;
  ulonglong uVar25;
  longlong *plVar26;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  undefined8 extraout_x1_10;
  undefined8 extraout_x1_11;
  undefined8 extraout_x1_12;
  undefined8 extraout_x1_13;
  undefined8 extraout_x1_14;
  undefined8 extraout_x1_15;
  undefined8 extraout_x1_16;
  undefined8 extraout_x1_17;
  char *pcVar27;
  uint uVar28;
  longlong lVar29;
  uint *puVar30;
  float *pfVar31;
  longlong extraout_x11;
  longlong extraout_x11_00;
  longlong extraout_x11_01;
  code *extraout_x12;
  code *extraout_x12_00;
  code *extraout_x12_01;
  code *extraout_x12_02;
  code *extraout_x12_03;
  code *extraout_x12_04;
  code *pcVar32;
  longlong *plVar33;
  char **ppcVar34;
  char *pcVar35;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  undefined4 extraout_s0_02;
  undefined4 extraout_s0_03;
  undefined4 extraout_s0_04;
  undefined4 extraout_s0_05;
  undefined4 extraout_s0_06;
  undefined4 extraout_s0_07;
  undefined4 extraout_s0_08;
  undefined4 extraout_s0_09;
  undefined4 extraout_s0_10;
  undefined4 extraout_s0_11;
  undefined4 extraout_s0_12;
  undefined4 extraout_s0_13;
  undefined4 extraout_s0_14;
  undefined4 extraout_s0_15;
  undefined4 extraout_s0_16;
  undefined4 extraout_s0_17;
  undefined4 extraout_s0_18;
  undefined4 extraout_s0_19;
  undefined4 extraout_s0_20;
  undefined4 extraout_s0_21;
  undefined4 extraout_s0_22;
  undefined4 extraout_s0_23;
  undefined4 extraout_var;
  undefined4 extraout_var_00;
  undefined4 extraout_var_01;
  undefined4 extraout_var_02;
  undefined4 extraout_var_03;
  undefined4 extraout_var_04;
  undefined4 extraout_var_05;
  undefined4 extraout_var_06;
  undefined4 extraout_var_07;
  undefined4 extraout_var_08;
  undefined4 extraout_var_09;
  undefined4 extraout_var_10;
  undefined4 extraout_var_11;
  undefined4 extraout_var_12;
  undefined4 extraout_var_13;
  undefined4 extraout_var_14;
  undefined4 extraout_var_15;
  undefined4 extraout_var_16;
  undefined4 extraout_var_17;
  undefined4 extraout_var_18;
  undefined4 extraout_var_19;
  undefined4 extraout_var_20;
  undefined4 extraout_var_21;
  undefined4 extraout_var_22;
  undefined4 extraout_var_23;
  undefined4 uVar36;
  undefined8 extraout_var_24;
  undefined8 extraout_var_25;
  undefined8 extraout_var_26;
  undefined8 extraout_var_27;
  undefined8 extraout_var_28;
  undefined8 extraout_var_29;
  undefined8 extraout_var_30;
  undefined8 extraout_var_31;
  undefined8 extraout_var_32;
  undefined8 extraout_var_33;
  undefined8 extraout_var_34;
  undefined8 extraout_var_35;
  undefined8 extraout_var_36;
  undefined8 extraout_var_37;
  undefined8 extraout_var_38;
  undefined8 extraout_var_39;
  undefined8 extraout_var_40;
  undefined8 extraout_var_41;
  undefined8 extraout_var_42;
  undefined8 uVar37;
  undefined8 extraout_var_43;
  undefined8 extraout_var_44;
  undefined8 extraout_var_45;
  undefined8 extraout_var_46;
  undefined8 extraout_var_47;
  undefined8 extraout_var_48;
  undefined4 uVar38;
  undefined8 uVar39;
  uint7 in_stack_fffffffffffffd29;
  char *local_2b0;
  undefined8 uStack_2a8;
  undefined1 *local_2a0;
  ulonglong local_298;
  uint local_290;
  longlong **local_280;
  longlong *local_278;
  longlong *local_270;
  longlong *local_268;
  longlong **local_260;
  longlong *local_258;
  longlong *local_250;
  longlong *local_248;
  longlong *local_240;
  longlong *local_238;
  longlong *local_230;
  longlong *local_228;
  longlong *local_220;
  longlong *local_218;
  longlong *local_210;
  longlong *local_208;
  longlong *local_200;
  longlong *local_1f8;
  longlong *local_1f0;
  longlong *local_1e8;
  longlong *local_1e0;
  longlong *local_1d8;
  longlong *local_1d0;
  longlong *local_1c8;
  longlong *local_1c0;
  undefined8 uStack_1b8;
  longlong *local_1b0;
  longlong *local_1a8;
  longlong *local_1a0;
  longlong *local_198;
  longlong *local_190;
  undefined **local_188;
  longlong *local_180;
  longlong *plStack_178;
  longlong *plStack_170;
  longlong *plStack_168;
  uint local_160;
  undefined8 local_150;
  longlong **local_148;
  undefined4 local_140;
  undefined **local_138;
  undefined **local_130;
  undefined **local_128;
  undefined **local_120;
  undefined **local_118;
  char *local_110;
  undefined8 uStack_108;
  undefined1 *local_100;
  longlong local_f0;
  undefined8 local_e8;
  code *local_e0;
  ulonglong local_d8;
  undefined8 local_d0;
  longlong local_c8;
  undefined8 local_c0;
  code *local_b8;
  ulonglong local_b0;
  undefined8 local_a8;
  longlong local_a0;
  undefined8 uStack_98;
  code *local_90;
  ulonglong local_88;
  undefined8 local_80;
  undefined8 local_78;
  undefined1 auStack_70 [16];
  
                    /* 0x8e128  24  scsf_SGZones */
  uVar15 = param_2._0_4_;
  uVar38 = param_2._4_4_;
  uVar39 = param_2._8_8_;
  local_150 = 0xfffffffffffffffe;
  piVar16 = (int *)(**(code **)(param_3 + 0x62e))(param_1._0_4_,0);
  (**(code **)(param_3 + 0x62e))(1);
  puVar17 = (undefined8 *)(**(code **)(param_3 + 0x446))(2);
  lVar29 = *(longlong *)(param_3 + 0x84);
  local_280 = (longlong **)*puVar17;
  uVar20 = extraout_x1;
  uVar13 = extraout_s0;
  uVar36 = extraout_var;
  uVar37 = extraout_var_24;
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_00;
      uVar13 = extraout_s0_00;
      uVar36 = extraout_var_00;
      uVar37 = extraout_var_25;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e1cc;
    local_1e8 = (longlong *)(param_3 + 0x8e);
LAB_18008e200:
    local_228 = local_1e8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_01;
      uVar13 = extraout_s0_01;
      uVar36 = extraout_var_01;
      uVar37 = extraout_var_26;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e230;
    local_1e0 = (longlong *)(param_3 + 0x8e);
LAB_18008e264:
    local_220 = local_1e0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_02;
      uVar13 = extraout_s0_02;
      uVar36 = extraout_var_02;
      uVar37 = extraout_var_27;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e294;
    local_1d8 = (longlong *)(param_3 + 0x8e);
LAB_18008e2cc:
    local_218 = local_1d8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_03;
      uVar13 = extraout_s0_03;
      uVar36 = extraout_var_03;
      uVar37 = extraout_var_28;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e2fc;
    local_1b0 = (longlong *)(param_3 + 0x8e);
LAB_18008e334:
    local_208 = local_1b0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_04;
      uVar13 = extraout_s0_04;
      uVar36 = extraout_var_04;
      uVar37 = extraout_var_29;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e364;
    local_1a8 = (longlong *)(param_3 + 0x8e);
LAB_18008e39c:
    local_210 = local_1a8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_05;
      uVar13 = extraout_s0_05;
      uVar36 = extraout_var_05;
      uVar37 = extraout_var_30;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e3cc;
    local_1a0 = (longlong *)(param_3 + 0x8e);
LAB_18008e404:
    local_278 = local_1a0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_06;
      uVar13 = extraout_s0_06;
      uVar36 = extraout_var_06;
      uVar37 = extraout_var_31;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e434;
    local_198 = (longlong *)(param_3 + 0x8e);
LAB_18008e46c:
    local_270 = local_198;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_07;
      uVar13 = extraout_s0_07;
      uVar36 = extraout_var_07;
      uVar37 = extraout_var_32;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e494;
    ppcVar34 = (char **)(param_3 + 0x8e);
LAB_18008e4c4:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_08;
      uVar13 = extraout_s0_08;
      uVar36 = extraout_var_08;
      uVar37 = extraout_var_33;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e4f4;
    local_190 = (longlong *)(param_3 + 0x8e);
LAB_18008e52c:
    local_230 = local_190;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_09;
      uVar13 = extraout_s0_09;
      uVar36 = extraout_var_09;
      uVar37 = extraout_var_34;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e55c;
    local_1c8 = (longlong *)(param_3 + 0x8e);
LAB_18008e594:
    local_238 = local_1c8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_10;
      uVar13 = extraout_s0_10;
      uVar36 = extraout_var_10;
      uVar37 = extraout_var_35;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e5c4;
    local_200 = (longlong *)(param_3 + 0x8e);
LAB_18008e5fc:
    local_240 = local_200;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_11;
      uVar13 = extraout_s0_11;
      uVar36 = extraout_var_11;
      uVar37 = extraout_var_36;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e62c;
    local_1c0 = (longlong *)(param_3 + 0x8e);
LAB_18008e664:
    local_248 = local_1c0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_12;
      uVar13 = extraout_s0_12;
      uVar36 = extraout_var_12;
      uVar37 = extraout_var_37;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e694;
    local_1f8 = (longlong *)(param_3 + 0x8e);
LAB_18008e6cc:
    local_250 = local_1f8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_13;
      uVar13 = extraout_s0_13;
      uVar36 = extraout_var_13;
      uVar37 = extraout_var_38;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e6fc;
    local_1f0 = (longlong *)(param_3 + 0x8e);
LAB_18008e734:
    local_258 = local_1f0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_14;
      uVar13 = extraout_s0_14;
      uVar36 = extraout_var_14;
      uVar37 = extraout_var_39;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e764;
    local_1d0 = (longlong *)(param_3 + 0x8e);
LAB_18008e79c:
    local_268 = local_1d0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_15;
      uVar13 = extraout_s0_15;
      uVar36 = extraout_var_15;
      uVar37 = extraout_var_40;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e7cc;
    local_148 = (longlong **)(param_3 + 0x8e);
LAB_18008e804:
    local_260 = local_148;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_16;
      uVar13 = extraout_s0_16;
      uVar36 = extraout_var_16;
      uVar37 = extraout_var_41;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e82c;
    plVar26 = (longlong *)(param_3 + 0x8e);
LAB_18008e85c:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar20 = extraout_x1_17;
      uVar13 = extraout_s0_17;
      uVar36 = extraout_var_17;
      uVar37 = extraout_var_42;
    }
    lVar29 = *(longlong *)(param_3 + 0x84);
    if (lVar29 != 0) goto LAB_18008e87c;
    plVar33 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_18008e1cc:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      local_228 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 0;
      if (iVar14 < 1) {
        iVar1 = iVar14 + -1;
      }
      local_228 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
    local_1e8 = local_228;
    if (lVar29 == 0) goto LAB_18008e200;
LAB_18008e230:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      local_220 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 1;
      if (iVar14 + -1 == 0 || iVar14 < 1) {
        iVar1 = iVar14 + -1;
      }
      local_220 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
    local_1e0 = local_220;
    if (lVar29 == 0) goto LAB_18008e264;
LAB_18008e294:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      local_218 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 2;
      if (iVar14 < 3) {
        iVar1 = iVar14 + -1;
      }
      local_218 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
    local_1d8 = local_218;
    if (lVar29 == 0) goto LAB_18008e2cc;
LAB_18008e2fc:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      local_208 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 3;
      if (iVar14 < 4) {
        iVar1 = iVar14 + -1;
      }
      local_208 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
    local_1b0 = local_208;
    if (lVar29 == 0) goto LAB_18008e334;
LAB_18008e364:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      local_210 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 4;
      if (iVar14 < 5) {
        iVar1 = iVar14 + -1;
      }
      local_210 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
    local_1a8 = local_210;
    if (lVar29 == 0) goto LAB_18008e39c;
LAB_18008e3cc:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      local_278 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 5;
      if (iVar14 < 6) {
        iVar1 = iVar14 + -1;
      }
      local_278 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
    local_1a0 = local_278;
    if (lVar29 == 0) goto LAB_18008e404;
LAB_18008e434:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      local_270 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 6;
      if (iVar14 < 7) {
        iVar1 = iVar14 + -1;
      }
      local_270 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
    local_198 = local_270;
    if (lVar29 == 0) goto LAB_18008e46c;
LAB_18008e494:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      ppcVar34 = (char **)(param_3 + 0x8e);
    }
    else {
      iVar1 = 7;
      if (iVar14 < 8) {
        iVar1 = iVar14 + -1;
      }
      ppcVar34 = (char **)(lVar29 + (longlong)iVar1 * 0x98);
    }
    if (lVar29 == 0) goto LAB_18008e4c4;
LAB_18008e4f4:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      local_230 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 8;
      if (iVar14 < 9) {
        iVar1 = iVar14 + -1;
      }
      local_230 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
    local_190 = local_230;
    if (lVar29 == 0) goto LAB_18008e52c;
LAB_18008e55c:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      local_238 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 9;
      if (iVar14 < 10) {
        iVar1 = iVar14 + -1;
      }
      local_238 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
    local_1c8 = local_238;
    if (lVar29 == 0) goto LAB_18008e594;
LAB_18008e5c4:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      local_240 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 10;
      if (iVar14 < 0xb) {
        iVar1 = iVar14 + -1;
      }
      local_240 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
    local_200 = local_240;
    if (lVar29 == 0) goto LAB_18008e5fc;
LAB_18008e62c:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      local_248 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 0xb;
      if (iVar14 < 0xc) {
        iVar1 = iVar14 + -1;
      }
      local_248 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
    local_1c0 = local_248;
    if (lVar29 == 0) goto LAB_18008e664;
LAB_18008e694:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      local_250 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 0xc;
      if (iVar14 < 0xd) {
        iVar1 = iVar14 + -1;
      }
      local_250 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
    local_1f8 = local_250;
    if (lVar29 == 0) goto LAB_18008e6cc;
LAB_18008e6fc:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      local_258 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 0xd;
      if (iVar14 < 0xe) {
        iVar1 = iVar14 + -1;
      }
      local_258 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
    local_1f0 = local_258;
    if (lVar29 == 0) goto LAB_18008e734;
LAB_18008e764:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      local_268 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 0xe;
      if (iVar14 < 0xf) {
        iVar1 = iVar14 + -1;
      }
      local_268 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
    local_1d0 = local_268;
    if (lVar29 == 0) goto LAB_18008e79c;
LAB_18008e7cc:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      local_260 = (longlong **)(param_3 + 0x8e);
    }
    else {
      iVar1 = 0xf;
      if (iVar14 < 0x10) {
        iVar1 = iVar14 + -1;
      }
      local_260 = (longlong **)(lVar29 + (longlong)iVar1 * 0x98);
    }
    local_148 = local_260;
    if (lVar29 == 0) goto LAB_18008e804;
LAB_18008e82c:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      plVar26 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 0x10;
      if (iVar14 < 0x11) {
        iVar1 = iVar14 + -1;
      }
      plVar26 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
    if (lVar29 == 0) goto LAB_18008e85c;
LAB_18008e87c:
    iVar14 = param_3[0x8c];
    if (iVar14 == 0) {
      plVar33 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar1 = 0x11;
      if (iVar14 < 0x12) {
        iVar1 = iVar14 + -1;
      }
      plVar33 = (longlong *)(lVar29 + (longlong)iVar1 * 0x98);
    }
  }
  local_128 = LineStyles::vftable;
  local_130 = ErasureModes::vftable;
  local_188 = PitSessions::vftable;
  local_138 = PricePositions::vftable;
  local_118 = OverlapModes::vftable;
  local_120 = ShowZonePrices::vftable;
  if (param_3[0x2b] != 0) {
    FUN_1800079f8((longlong *)(param_3 + 0x46),0x1800db810,8);
    pcVar27 = "";
    param_3[0x49] = 1;
    if ((*(char **)(param_3 + 0xce) != (char *)0x0) && (**(char **)(param_3 + 0xce) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0xce),0x1800d4ecd,0);
      param_3[0xd1] = 1;
    }
    local_2b0 = "";
    uStack_2a8 = 0;
    local_2a0 = &DAT_1800d4ecd;
    pvVar18 = GetProcessHeap();
    pcVar19 = (char *)HeapAlloc(pvVar18,0,0x15);
    if (pcVar19 == (char *)0x0) {
      local_2b0 = "";
      pcVar35 = pcVar27;
    }
    else {
      param_6 = 0x14;
      pcVar19[8] = '\0';
      pcVar19[9] = '\0';
      pcVar19[10] = '\0';
      pcVar19[0xb] = '\0';
      pcVar19[0xc] = '\0';
      pcVar19[0xd] = '\0';
      pcVar19[0xe] = '\0';
      pcVar19[0xf] = '\0';
      pcVar19[0] = '\0';
      pcVar19[1] = '\0';
      pcVar19[2] = '\0';
      pcVar19[3] = '\0';
      pcVar19[4] = '\0';
      pcVar19[5] = '\0';
      pcVar19[6] = '\0';
      pcVar19[7] = '\0';
      pcVar19[0x10] = '\0';
      pcVar19[0x11] = '\0';
      pcVar19[0x12] = '\0';
      pcVar19[0x13] = '\0';
      pcVar19[0x14] = '\0';
      local_2b0 = pcVar19;
      FUN_180099d78(pcVar19,0x15,0x1800d6c40,0x14);
      uStack_2a8 = 0x100000001;
      pcVar35 = pcVar19;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_2b0);
    if ((pcVar19 != (char *)0x0) && (pcVar35 != (char *)0x0)) {
      pvVar18 = GetProcessHeap();
      HeapFree(pvVar18,0,pcVar35);
    }
    local_2b0 = "";
    uStack_2a8 = 0;
    local_2a0 = &DAT_1800d4ecd;
    pvVar18 = GetProcessHeap();
    pcVar19 = (char *)HeapAlloc(pvVar18,0,0x8d);
    if (pcVar19 == (char *)0x0) {
      local_2b0 = "";
      pcVar35 = pcVar27;
    }
    else {
      param_6 = 0x8c;
      pcVar19[8] = '\0';
      pcVar19[9] = '\0';
      pcVar19[10] = '\0';
      pcVar19[0xb] = '\0';
      pcVar19[0xc] = '\0';
      pcVar19[0xd] = '\0';
      pcVar19[0xe] = '\0';
      pcVar19[0xf] = '\0';
      pcVar19[0] = '\0';
      pcVar19[1] = '\0';
      pcVar19[2] = '\0';
      pcVar19[3] = '\0';
      pcVar19[4] = '\0';
      pcVar19[5] = '\0';
      pcVar19[6] = '\0';
      pcVar19[7] = '\0';
      pcVar19[0x18] = '\0';
      pcVar19[0x19] = '\0';
      pcVar19[0x1a] = '\0';
      pcVar19[0x1b] = '\0';
      pcVar19[0x1c] = '\0';
      pcVar19[0x1d] = '\0';
      pcVar19[0x1e] = '\0';
      pcVar19[0x1f] = '\0';
      pcVar19[0x10] = '\0';
      pcVar19[0x11] = '\0';
      pcVar19[0x12] = '\0';
      pcVar19[0x13] = '\0';
      pcVar19[0x14] = '\0';
      pcVar19[0x15] = '\0';
      pcVar19[0x16] = '\0';
      pcVar19[0x17] = '\0';
      pcVar19[0x28] = '\0';
      pcVar19[0x29] = '\0';
      pcVar19[0x2a] = '\0';
      pcVar19[0x2b] = '\0';
      pcVar19[0x2c] = '\0';
      pcVar19[0x2d] = '\0';
      pcVar19[0x2e] = '\0';
      pcVar19[0x2f] = '\0';
      pcVar19[0x20] = '\0';
      pcVar19[0x21] = '\0';
      pcVar19[0x22] = '\0';
      pcVar19[0x23] = '\0';
      pcVar19[0x24] = '\0';
      pcVar19[0x25] = '\0';
      pcVar19[0x26] = '\0';
      pcVar19[0x27] = '\0';
      pcVar19[0x38] = '\0';
      pcVar19[0x39] = '\0';
      pcVar19[0x3a] = '\0';
      pcVar19[0x3b] = '\0';
      pcVar19[0x3c] = '\0';
      pcVar19[0x3d] = '\0';
      pcVar19[0x3e] = '\0';
      pcVar19[0x3f] = '\0';
      pcVar19[0x30] = '\0';
      pcVar19[0x31] = '\0';
      pcVar19[0x32] = '\0';
      pcVar19[0x33] = '\0';
      pcVar19[0x34] = '\0';
      pcVar19[0x35] = '\0';
      pcVar19[0x36] = '\0';
      pcVar19[0x37] = '\0';
      pcVar19[0x48] = '\0';
      pcVar19[0x49] = '\0';
      pcVar19[0x4a] = '\0';
      pcVar19[0x4b] = '\0';
      pcVar19[0x4c] = '\0';
      pcVar19[0x4d] = '\0';
      pcVar19[0x4e] = '\0';
      pcVar19[0x4f] = '\0';
      pcVar19[0x40] = '\0';
      pcVar19[0x41] = '\0';
      pcVar19[0x42] = '\0';
      pcVar19[0x43] = '\0';
      pcVar19[0x44] = '\0';
      pcVar19[0x45] = '\0';
      pcVar19[0x46] = '\0';
      pcVar19[0x47] = '\0';
      pcVar19[0x58] = '\0';
      pcVar19[0x59] = '\0';
      pcVar19[0x5a] = '\0';
      pcVar19[0x5b] = '\0';
      pcVar19[0x5c] = '\0';
      pcVar19[0x5d] = '\0';
      pcVar19[0x5e] = '\0';
      pcVar19[0x5f] = '\0';
      pcVar19[0x50] = '\0';
      pcVar19[0x51] = '\0';
      pcVar19[0x52] = '\0';
      pcVar19[0x53] = '\0';
      pcVar19[0x54] = '\0';
      pcVar19[0x55] = '\0';
      pcVar19[0x56] = '\0';
      pcVar19[0x57] = '\0';
      pcVar19[0x68] = '\0';
      pcVar19[0x69] = '\0';
      pcVar19[0x6a] = '\0';
      pcVar19[0x6b] = '\0';
      pcVar19[0x6c] = '\0';
      pcVar19[0x6d] = '\0';
      pcVar19[0x6e] = '\0';
      pcVar19[0x6f] = '\0';
      pcVar19[0x60] = '\0';
      pcVar19[0x61] = '\0';
      pcVar19[0x62] = '\0';
      pcVar19[99] = '\0';
      pcVar19[100] = '\0';
      pcVar19[0x65] = '\0';
      pcVar19[0x66] = '\0';
      pcVar19[0x67] = '\0';
      pcVar19[0x78] = '\0';
      pcVar19[0x79] = '\0';
      pcVar19[0x7a] = '\0';
      pcVar19[0x7b] = '\0';
      pcVar19[0x7c] = '\0';
      pcVar19[0x7d] = '\0';
      pcVar19[0x7e] = '\0';
      pcVar19[0x7f] = '\0';
      pcVar19[0x70] = '\0';
      pcVar19[0x71] = '\0';
      pcVar19[0x72] = '\0';
      pcVar19[0x73] = '\0';
      pcVar19[0x74] = '\0';
      pcVar19[0x75] = '\0';
      pcVar19[0x76] = '\0';
      pcVar19[0x77] = '\0';
      pcVar19[0x80] = '\0';
      pcVar19[0x81] = '\0';
      pcVar19[0x82] = '\0';
      pcVar19[0x83] = '\0';
      pcVar19[0x84] = '\0';
      pcVar19[0x85] = '\0';
      pcVar19[0x86] = '\0';
      pcVar19[0x87] = '\0';
      pcVar19[0x88] = '\0';
      pcVar19[0x89] = '\0';
      pcVar19[0x8a] = '\0';
      pcVar19[0x8b] = '\0';
      pcVar19[0x8c] = '\0';
      local_2b0 = pcVar19;
      FUN_180099d78(pcVar19,0x8d,0x1800d6bb0,0x8c);
      uStack_2a8 = 0x100000001;
      pcVar35 = pcVar19;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_2b0);
    uVar13 = extraout_s0_18;
    uVar36 = extraout_var_18;
    uVar20 = extraout_var_43;
    if ((pcVar19 != (char *)0x0) && (pcVar35 != (char *)0x0)) {
      pvVar18 = GetProcessHeap();
      HeapFree(pvVar18,0,pcVar35);
      local_2b0 = (char *)0x0;
      uStack_2a8 = 0;
      uVar13 = extraout_s0_19;
      uVar36 = extraout_var_19;
      uVar20 = extraout_var_44;
    }
    pcVar19 = *(char **)(param_3 + 0x46);
    if (*(char **)(param_3 + 0x46) == (char *)0x0) {
      pcVar19 = pcVar27;
    }
    auVar6._4_4_ = uVar36;
    auVar6._0_4_ = uVar13;
    auVar6._8_8_ = uVar20;
    FUN_180026368(auVar6,CONCAT44(uVar38,uVar15),(undefined8 *)(param_3 + 0xce),0x1800d6c60,pcVar19,
                  param_6,param_7,param_8,param_9,param_10);
    local_2b0 = "";
    uStack_2a8 = 0;
    local_2a0 = &DAT_1800d4ecd;
    pvVar18 = GetProcessHeap();
    pcVar19 = (char *)HeapAlloc(pvVar18,0,7);
    if (pcVar19 == (char *)0x0) {
      local_2b0 = "";
    }
    else {
      pcVar19[0] = '\0';
      pcVar19[1] = '\0';
      pcVar19[2] = '\0';
      pcVar19[3] = '\0';
      pcVar19[4] = '\0';
      pcVar19[5] = '\0';
      param_6 = 6;
      pcVar19[6] = '\0';
      local_2b0 = pcVar19;
      FUN_180099d78(pcVar19,7,0x1800d6c58,6);
      uStack_2a8 = 0x100000001;
      pcVar27 = pcVar19;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_2b0);
    if ((pcVar19 != (char *)0x0) && (pcVar27 != (char *)0x0)) {
      pvVar18 = GetProcessHeap();
      HeapFree(pvVar18,0,pcVar27);
      local_2b0 = (char *)0x0;
      uStack_2a8 = 0;
    }
    param_3[4] = 0;
    param_3[0xdf] = 1;
    FUN_1800079f8(local_228,0x1800d7d38,10);
    *(int *)((longlong)local_1e8 + 0xc) = 1;
    local_1e8[4] = 0x400000001;
    *(undefined1 *)(local_1e8 + 3) = 0x14;
    FUN_1800079f8(local_220,0x1800db800,7);
    *(int *)((longlong)local_1e0 + 0xc) = 1;
    local_1e0[4] = 0x100000000;
    *(undefined1 *)(local_1e0 + 3) = 0x14;
    FUN_1800079f8(local_218,0x1800db808,6);
    *(int *)((longlong)local_1d8 + 0xc) = 1;
    local_1d8[4] = 0x200000000;
    *(undefined1 *)(local_1d8 + 3) = 0x14;
    FUN_1800079f8(local_208,0x1800db5e0,10);
    *(int *)((longlong)local_1b0 + 0xc) = 1;
    *(undefined1 *)(local_1b0 + 3) = 0xe;
    *(int *)((longlong)local_1b0 + 0x1c) = 0xf0a60d;
    FUN_1800079f8(local_210,0x1800db5a0,0x11);
    *(int *)((longlong)local_1a8 + 0xc) = 1;
    *(undefined1 *)(local_1a8 + 3) = 0xe;
    *(int *)((longlong)local_1a8 + 0x1c) = 0xf0a60d;
    FUN_1800079f8(local_278,0x1800db860,0x12);
    *(int *)((longlong)local_1a0 + 0xc) = 1;
    *(undefined1 *)(local_1a0 + 3) = 0xe;
    *(int *)((longlong)local_1a0 + 0x1c) = 0x808080;
    FUN_1800079f8(local_270,0x1800db878,0x19);
    *(int *)((longlong)local_198 + 0xc) = 1;
    *(undefined1 *)(local_198 + 3) = 0xe;
    *(int *)((longlong)local_198 + 0x1c) = 0xd3d3d3;
    local_100 = &DAT_1800d4ecd;
    local_110 = "";
    uStack_108 = 0;
    auVar7._4_4_ = extraout_var_20;
    auVar7._0_4_ = extraout_s0_20;
    auVar7._8_8_ = extraout_var_45;
    FUN_180006050(auVar7,CONCAT44(uVar38,uVar15),&local_110,0x1800d8410,500,param_6,param_7,param_8,
                  param_9,param_10);
    pcVar27 = local_110;
    if ((&local_110 != ppcVar34) &&
       (((local_110 != (char *)0x0 && (*local_110 != '\0')) ||
        ((*ppcVar34 != (char *)0x0 && (**ppcVar34 != '\0')))))) {
      if (local_110 == (char *)0x0) {
        if ((*(int *)(ppcVar34 + 1) != 0) && (pcVar19 = *ppcVar34, pcVar19 != (char *)0x0)) {
          pvVar18 = GetProcessHeap();
          HeapFree(pvVar18,0,pcVar19);
        }
        *(undefined4 *)(ppcVar34 + 1) = 0;
        *ppcVar34 = ppcVar34[2];
      }
      else {
        cVar3 = *local_110;
        pcVar19 = local_110;
        while (cVar3 != '\0') {
          pcVar19 = pcVar19 + 1;
          cVar3 = *pcVar19;
        }
        iVar14 = (int)((longlong)pcVar19 - (longlong)local_110);
        if (0x7ffffffe < (ulonglong)((longlong)pcVar19 - (longlong)local_110)) {
          iVar14 = 0x7ffffffe;
        }
        FUN_1800079f8((longlong *)ppcVar34,(longlong)local_110,iVar14);
      }
      *(undefined4 *)((longlong)ppcVar34 + 0xc) = 1;
    }
    *(undefined4 *)((longlong)ppcVar34 + 0x1c) = 0x14;
    *(undefined4 *)((longlong)ppcVar34 + 0x3c) = 500;
    *(undefined4 *)((longlong)ppcVar34 + 0x2c) = 0;
    *(undefined1 *)(ppcVar34 + 3) = 0xb;
    FUN_1800079f8(local_230,0x1800d83f8,0x11);
    *(int *)((longlong)local_190 + 0xc) = 1;
    *(undefined1 *)(local_190 + 3) = 0xb;
    *(int *)((longlong)local_190 + 0x1c) = 1;
    FUN_1800079f8(local_238,0x1800d8448,0x10);
    *(int *)((longlong)local_1c8 + 0xc) = 1;
    puVar17 = FUN_180029680((longlong *)&local_120,&local_2b0);
    plVar9 = local_1c8;
    if (0xf < (ulonglong)puVar17[3]) {
      puVar17 = (undefined8 *)*puVar17;
    }
    if ((code *)local_1c8[10] != (code *)0x0) {
      (*(code *)local_1c8[10])(*(int *)((longlong)local_1c8 + 0x4c),puVar17);
      *(undefined1 *)(plVar9 + 3) = 0x16;
    }
    if (0xf < local_298) {
      FUN_1800966b8(local_2b0);
    }
    local_2b0 = (char *)((ulonglong)local_2b0 & 0xffffffffffffff00);
    local_2a0 = (undefined1 *)0x0;
    local_298 = 0xf;
    *(undefined1 *)(local_1c8 + 3) = 0x16;
    *(int *)((longlong)local_1c8 + 0x1c) = 1;
    FUN_1800079f8(local_240,0x1800d8430,0x13);
    *(int *)((longlong)local_200 + 0xc) = 1;
    puVar17 = FUN_180029680((longlong *)&local_138,&local_2b0);
    plVar9 = local_200;
    if (0xf < (ulonglong)puVar17[3]) {
      puVar17 = (undefined8 *)*puVar17;
    }
    if ((code *)local_200[10] != (code *)0x0) {
      (*(code *)local_200[10])(*(int *)((longlong)local_200 + 0x4c),puVar17);
      *(undefined1 *)(plVar9 + 3) = 0x16;
    }
    if (0xf < local_298) {
      FUN_1800966b8(local_2b0);
    }
    local_2b0 = (char *)((ulonglong)local_2b0 & 0xffffffffffffff00);
    local_2a0 = (undefined1 *)0x0;
    local_298 = 0xf;
    *(undefined1 *)(local_200 + 3) = 0x16;
    *(int *)((longlong)local_200 + 0x1c) = 1;
    FUN_1800079f8(local_248,0x1800d7210,0x12);
    *(int *)((longlong)local_1c0 + 0xc) = 1;
    *(undefined1 *)(local_1c0 + 3) = 0xb;
    *(int *)((longlong)local_1c0 + 0x1c) = 0x5a;
    *(int *)((longlong)local_1c0 + 0x3c) = 100;
    *(int *)((longlong)local_1c0 + 0x2c) = 0;
    FUN_1800079f8(local_250,0x1800d71f8,0x14);
    *(int *)((longlong)local_1f8 + 0xc) = 1;
    puVar17 = FUN_180029680((longlong *)&local_130,&local_2b0);
    plVar9 = local_1f8;
    if (0xf < (ulonglong)puVar17[3]) {
      puVar17 = (undefined8 *)*puVar17;
    }
    if ((code *)local_1f8[10] != (code *)0x0) {
      (*(code *)local_1f8[10])(*(int *)((longlong)local_1f8 + 0x4c),puVar17);
      *(undefined1 *)(plVar9 + 3) = 0x16;
    }
    if (0xf < local_298) {
      FUN_1800966b8(local_2b0);
    }
    local_2b0 = (char *)((ulonglong)local_2b0 & 0xffffffffffffff00);
    local_2a0 = (undefined1 *)0x0;
    local_298 = 0xf;
    *(undefined1 *)(local_1f8 + 3) = 0x16;
    *(int *)((longlong)local_1f8 + 0x1c) = 1;
    FUN_1800079f8(local_258,0x1800d8480,0x14);
    *(int *)((longlong)local_1f0 + 0xc) = 1;
    puVar17 = FUN_180029680((longlong *)&local_118,&local_2b0);
    plVar9 = local_1f0;
    if (0xf < (ulonglong)puVar17[3]) {
      puVar17 = (undefined8 *)*puVar17;
    }
    if ((code *)local_1f0[10] != (code *)0x0) {
      (*(code *)local_1f0[10])(*(int *)((longlong)local_1f0 + 0x4c),puVar17);
      *(undefined1 *)(plVar9 + 3) = 0x16;
    }
    if (0xf < local_298) {
      FUN_1800966b8(local_2b0);
    }
    local_2b0 = (char *)((ulonglong)local_2b0 & 0xffffffffffffff00);
    local_2a0 = (undefined1 *)0x0;
    local_298 = 0xf;
    *(undefined1 *)(local_1f0 + 3) = 0x16;
    *(int *)((longlong)local_1f0 + 0x1c) = 0;
    FUN_1800079f8(local_268,0x1800d8460,0x19);
    *(int *)((longlong)local_1d0 + 0xc) = 1;
    *(undefined1 *)(local_1d0 + 3) = 5;
    *(int *)((longlong)local_1d0 + 0x1c) = 1;
    FUN_1800079f8((longlong *)local_260,0x1800d73a0,0xc);
    *(uint *)((longlong)local_148 + 0xc) = 1;
    *(undefined1 *)(local_148 + 3) = 5;
    *(uint *)((longlong)local_148 + 0x1c) = 0;
    FUN_1800079f8(plVar26,0x1800d7388,0x11);
    *(undefined4 *)((longlong)plVar26 + 0xc) = 1;
    puVar17 = FUN_180029680((longlong *)&local_128,&local_2b0);
    if (0xf < (ulonglong)puVar17[3]) {
      puVar17 = (undefined8 *)*puVar17;
    }
    if ((code *)plVar26[10] != (code *)0x0) {
      (*(code *)plVar26[10])(*(undefined4 *)((longlong)plVar26 + 0x4c),puVar17);
      *(undefined1 *)(plVar26 + 3) = 0x16;
    }
    if (0xf < local_298) {
      FUN_1800966b8(local_2b0);
    }
    local_2b0 = (char *)((ulonglong)local_2b0 & 0xffffffffffffff00);
    local_2a0 = (undefined1 *)0x0;
    local_298 = 0xf;
    *(undefined1 *)(plVar26 + 3) = 0x16;
    *(undefined4 *)((longlong)plVar26 + 0x1c) = 0;
    FUN_1800079f8(plVar33,0x1800db820,0x11);
    *(undefined4 *)((longlong)plVar33 + 0xc) = 1;
    if ((code *)plVar33[10] != (code *)0x0) {
      (*(code *)plVar33[10])
                (*(undefined4 *)((longlong)plVar33 + 0x4c),"Cross Both;Cross Top;Cross Bottom");
    }
    *(undefined1 *)(plVar33 + 3) = 0x16;
    *(undefined4 *)((longlong)plVar33 + 0x1c) = 0;
    if ((int)uStack_108 == 0) {
      return;
    }
    if (pcVar27 == (char *)0x0) {
      return;
    }
    pvVar18 = GetProcessHeap();
    HeapFree(pvVar18,0,pcVar27);
    return;
  }
  if (param_3[0xe1] == 0) {
    auVar4._4_4_ = uVar36;
    auVar4._0_4_ = uVar13;
    auVar4._8_8_ = uVar37;
    uVar20 = FUN_1800254e8(auVar4,CONCAT44(uVar38,uVar15),(longlong)param_3,uVar20,param_5,param_6,
                           param_7,param_8,param_9,param_10);
    *piVar16 = (int)uVar20;
  }
  if (*piVar16 != 0) {
    return;
  }
  uVar12 = param_3[0xe1];
  lVar29 = *(longlong *)(param_3 + 0x58);
  if (lVar29 == 0) {
    if (*(code **)(param_3 + 0x5c) != (code *)0x0) {
      (**(code **)(param_3 + 0x5c))(param_3[0x5e]);
    }
    lVar29 = *(longlong *)(param_3 + 0x58);
    if (lVar29 == 0) {
      piVar16 = param_3 + 0x62;
      goto LAB_18008f248;
    }
  }
  iVar14 = param_3[0x60];
  if (iVar14 == 0) {
    piVar16 = param_3 + 0x62;
  }
  else {
    uVar12 = uVar12 & ((int)uVar12 >> 0x1f ^ 0xffffffffU);
    if (iVar14 <= (int)uVar12) {
      uVar12 = iVar14 - 1;
    }
    piVar16 = (int *)(lVar29 + (longlong)(int)uVar12 * 8);
  }
LAB_18008f248:
  local_78 = *(undefined8 *)piVar16;
  pcVar27 = "PST-08PDT+01,M3.2.0/02:00,M11.1.0/02:00";
  (**(code **)(param_3 + 0x4d4))(auStack_70,&local_78);
  pplVar21 = local_280;
  if ((param_3[0x47e] != 0) && (param_3[0xe1] == 0)) {
    if (local_280 != (longlong **)0x0) {
      FUN_180022590((int *)local_280,(longlong)param_3,pcVar27);
      FUN_18000bcc0(pplVar21);
      (**(code **)(param_3 + 0x448))(2,0);
    }
    uStack_2a8 = 0;
    local_2b0 = (char *)0x0;
    local_298 = 0;
    local_2a0 = (undefined1 *)0x0;
    local_290 = 0;
    plStack_178 = (longlong *)0x0;
    local_180 = (longlong *)0x0;
    plStack_168 = (longlong *)0x0;
    plStack_170 = (longlong *)0x0;
    local_160 = 0;
    pplVar21 = (longlong **)FUN_180096150(0xa8);
    pplVar21[1] = (longlong *)0x0;
    *pplVar21 = (longlong *)0x0;
    pplVar21[3] = (longlong *)0x0;
    pplVar21[2] = (longlong *)0x0;
    pplVar21[5] = (longlong *)0x0;
    pplVar21[4] = (longlong *)0x0;
    pplVar21[7] = (longlong *)0x0;
    pplVar21[6] = (longlong *)0x0;
    pplVar21[9] = (longlong *)0x0;
    pplVar21[8] = (longlong *)0x0;
    pplVar21[0xb] = (longlong *)0x0;
    pplVar21[10] = (longlong *)0x0;
    pplVar21[0xd] = (longlong *)0x0;
    pplVar21[0xc] = (longlong *)0x0;
    pplVar21[0xf] = (longlong *)0x0;
    pplVar21[0xe] = (longlong *)0x0;
    pplVar21[0x11] = (longlong *)0x0;
    pplVar21[0x10] = (longlong *)0x0;
    pplVar21[0x13] = (longlong *)0x0;
    pplVar21[0x12] = (longlong *)0x0;
    pplVar21[0x14] = (longlong *)0x0;
    local_148 = pplVar21;
    bVar10 = FUN_180026690((longlong)local_260);
    uVar12 = FUN_180026550((longlong)plVar26);
    local_280 = (longlong **)CONCAT44(local_280._4_4_,uVar12);
    bVar11 = FUN_180026690((longlong)local_268);
    uVar13 = FUN_180026888((longlong)local_270);
    local_268 = (longlong *)CONCAT44(local_268._4_4_,uVar13);
    uVar13 = FUN_180026888((longlong)local_278);
    local_270 = (longlong *)CONCAT44(local_270._4_4_,uVar13);
    uVar22 = FUN_180026708((longlong)local_258);
    local_260 = (longlong **)CONCAT44(local_260._4_4_,(int)uVar22);
    uVar22 = FUN_180026708((longlong)local_250);
    local_278 = (longlong *)CONCAT44(local_278._4_4_,(int)uVar22);
    uVar22 = FUN_180026708((longlong)local_248);
    uVar12 = FUN_180026550((longlong)local_240);
    uVar23 = FUN_180026708((longlong)local_238);
    uVar24 = FUN_180026708((longlong)local_230);
    uVar25 = FUN_180026708((longlong)ppcVar34);
    pplVar21[0x12] = (longlong *)0x0;
    pplVar21[0x13] = (longlong *)0x0;
    plVar26 = (longlong *)FUN_180096150(0x98);
    *plVar26 = (longlong)plVar26;
    plVar26[1] = (longlong)plVar26;
    pplVar21[0x12] = plVar26;
    *(uint *)pplVar21 = (uint)uVar25;
    *(uint *)((longlong)pplVar21 + 4) = (uint)uVar24;
    *(uint *)(pplVar21 + 1) = (uint)uVar23;
    *(uint *)((longlong)pplVar21 + 0xc) = uVar12;
    *(uint *)(pplVar21 + 2) = (uint)uVar22;
    *(uint *)((longlong)pplVar21 + 0x14) = (uint)local_278;
    *(uint *)(pplVar21 + 6) = (uint)local_270;
    *(uint *)((longlong)pplVar21 + 0x34) = (uint)local_268;
    pplVar21[5] = (longlong *)0x80808000808080;
    pplVar21[4] = (longlong *)0x80808000808080;
    *(bool *)(pplVar21 + 7) = bVar11;
    *(undefined8 *)((longlong)pplVar21 + 0x44) = uStack_2a8;
    *(char **)((longlong)pplVar21 + 0x3c) = local_2b0;
    *(ulonglong *)((longlong)pplVar21 + 0x54) = local_298;
    *(undefined1 **)((longlong)pplVar21 + 0x4c) = local_2a0;
    *(undefined1 *)(pplVar21 + 0x11) = 0;
    *(undefined2 *)((longlong)pplVar21 + 0x8a) = 0;
    *(uint *)((longlong)pplVar21 + 0x8c) = 0;
    *(uint *)((longlong)pplVar21 + 0x5c) = local_290;
    pplVar21[0xd] = plStack_178;
    pplVar21[0xc] = local_180;
    pplVar21[0xf] = plStack_168;
    pplVar21[0xe] = plStack_170;
    *(uint *)(pplVar21 + 0x10) = local_160;
    *(uint *)((longlong)pplVar21 + 0x84) = 0;
    *(uint *)(pplVar21 + 3) = (uint)local_260;
    *(uint *)((longlong)pplVar21 + 0x1c) = (uint)local_280;
    *(bool *)((longlong)pplVar21 + 0x89) = bVar10;
    plVar26 = (longlong *)FUN_180096150(0x18);
    plVar26[1] = 0;
    *plVar26 = 0;
    plVar26[1] = 0;
    plVar26[2] = 0;
    local_1d0 = plVar26;
    lVar29 = FUN_180096150(0x120);
    *(longlong *)lVar29 = lVar29;
    *(longlong *)(lVar29 + 8) = lVar29;
    plVar26[1] = lVar29;
    *(undefined1 *)plVar26 = 1;
    FUN_18000af28((char *)plVar26,(longlong)param_3);
    pplVar21[0x14] = plVar26;
    FUN_180022590((int *)pplVar21,(longlong)param_3,pcVar27);
    local_280 = pplVar21;
    (**(code **)(param_3 + 0x448))(2,pplVar21);
  }
  pplVar21 = local_280;
  if (local_280 != (longlong **)0x0) {
    if (param_3[0x1b] == 0) {
      iVar14 = (**(code **)(param_3 + 0x3ec))(param_3[0x270]);
      if (iVar14 == 0) {
        FUN_180022590((int *)pplVar21,(longlong)param_3,pcVar27);
      }
      else {
        local_a0 = 0;
        uStack_98 = 0;
        local_80 = 0;
        local_90 = (code *)0x0;
        local_88 = 0;
        local_f0 = 0;
        local_e8 = 0;
        local_d0 = 0;
        local_e0 = (code *)0x0;
        local_d8 = 0;
        local_c8 = 0;
        local_c0 = 0;
        local_a8 = 0;
        local_b8 = (code *)0x0;
        local_b0 = 0;
        FUN_180085f18((longlong)local_228);
        if (((char)local_1e8[3] == '\x12') || ((byte)((char)local_1e8[3] - 0x14U) < 2)) {
          uVar12 = *(uint *)(local_1e8 + 4);
          pcVar32 = extraout_x12;
        }
        else {
          uVar12 = FUN_180026550(extraout_x11);
          pcVar32 = extraout_x12_00;
        }
        (*pcVar32)(uVar12);
        FUN_180085f18((longlong)local_220);
        if (((char)local_1e0[3] == '\x12') || ((byte)((char)local_1e0[3] - 0x14U) < 2)) {
          uVar12 = *(uint *)(local_1e0 + 4);
          pcVar32 = extraout_x12_01;
        }
        else {
          uVar12 = FUN_180026550(extraout_x11_00);
          pcVar32 = extraout_x12_02;
        }
        (*pcVar32)(uVar12);
        FUN_180085f18((longlong)local_218);
        if (((char)local_1d8[3] == '\x12') || ((byte)((char)local_1d8[3] - 0x14U) < 2)) {
          uVar12 = *(uint *)(local_1d8 + 4);
          pcVar32 = extraout_x12_03;
        }
        else {
          uVar12 = FUN_180026550(extraout_x11_01);
          pcVar32 = extraout_x12_04;
        }
        (*pcVar32)(uVar12);
        if (((int)local_e8 != 0) && ((int)local_c0 != 0)) {
          uVar13 = extraout_s0_21;
          uVar36 = extraout_var_21;
          uVar20 = extraout_var_46;
          if ((param_3[0x2b] == 0) && (uVar12 = param_3[0xe1], uVar12 != *param_3 - 1U)) {
            if ((local_a0 == 0) &&
               ((local_90 == (code *)0x0 ||
                ((*local_90)(local_88 & 0xffffffff), uVar13 = extraout_s0_22,
                uVar36 = extraout_var_22, uVar20 = extraout_var_47, local_a0 == 0)))) {
              pfVar31 = (float *)((longlong)&local_80 + 4);
            }
            else if ((int)local_80 == 0) {
              pfVar31 = (float *)((longlong)&local_80 + 4);
            }
            else {
              uVar12 = uVar12 & ((int)uVar12 >> 0x1f ^ 0xffffffffU);
              if ((int)local_80 <= (int)uVar12) {
                uVar12 = (int)local_80 - 1;
              }
              pfVar31 = (float *)(local_a0 + (longlong)(int)uVar12 * 4);
            }
            if (*pfVar31 != 0.0) {
              uVar15 = FUN_180026550((longlong)plVar33);
              if (uVar15 == 0) {
                uVar12 = 0xb;
              }
              else if (uVar15 == 1) {
                uVar12 = 5;
              }
              else {
                uVar12 = 0xb;
                if (uVar15 == 2) {
                  uVar12 = 6;
                }
              }
              local_148 = &local_1c0;
              local_1c0 = (longlong *)0x0;
              uStack_1b8 = 0;
              local_1c0 = (longlong *)FUN_180096150(0x120);
              *local_1c0 = (longlong)local_1c0;
              local_1c0[1] = (longlong)local_1c0;
              param_10 = (undefined8 *)FUN_1800043e0(0.0,(longlong *)&local_1d0);
              uVar15 = FUN_180026888((longlong)local_210);
              param_8 = (ulonglong)uVar15;
              uVar15 = FUN_180026888((longlong)local_208);
              uVar2 = param_3[0xe1];
              param_7 = (ulonglong)uVar15;
              if ((local_f0 == 0) &&
                 ((local_e0 == (code *)0x0 || ((*local_e0)(local_d8 & 0xffffffff), local_f0 == 0))))
              {
                puVar30 = (uint *)((longlong)&local_d0 + 4);
              }
              else if ((int)local_d0 == 0) {
                puVar30 = (uint *)((longlong)&local_d0 + 4);
              }
              else {
                uVar15 = uVar2 & ((int)uVar2 >> 0x1f ^ 0xffffffffU);
                if ((int)local_d0 <= (int)uVar15) {
                  uVar15 = (int)local_d0 - 1;
                }
                puVar30 = (uint *)(local_f0 + (longlong)(int)uVar15 * 4);
              }
              uVar15 = *puVar30;
              uVar28 = param_3[0xe1];
              if (((local_c8 == 0) &&
                  ((local_b8 == (code *)0x0 || ((*local_b8)(local_b0 & 0xffffffff), local_c8 == 0)))
                  ) || ((int)local_a8 == 0)) {
                pfVar31 = (float *)((longlong)&local_a8 + 4);
              }
              else {
                uVar28 = uVar28 & ((int)uVar28 >> 0x1f ^ 0xffffffffU);
                if ((int)local_a8 <= (int)uVar28) {
                  uVar28 = (int)local_a8 - 1;
                }
                pfVar31 = (float *)(local_c8 + (longlong)(int)uVar28 * 4);
              }
              uVar38 = 0;
              uVar39 = 0;
              param_9 = (ulonglong)uVar12;
              FUN_180023270(*pfVar31,ZEXT416(uVar15),(uint *)local_280,(longlong)param_3,
                            (ulonglong)(uint)param_3[0xe1],(ulonglong)uVar2,param_7,param_8,param_9,
                            param_10,0,'\0',0,0,0,(ulonglong)in_stack_fffffffffffffd29 << 8,
                            &local_1c0);
              uVar13 = extraout_s0_23;
              uVar36 = extraout_var_23;
              uVar20 = extraout_var_48;
            }
          }
          local_148 = (longlong **)0x0;
          local_140 = 0;
          auVar5._4_4_ = uVar36;
          auVar5._0_4_ = uVar13;
          auVar5._8_8_ = uVar20;
          auVar8._4_4_ = uVar38;
          auVar8._0_4_ = uVar15;
          auVar8._8_8_ = uVar39;
          FUN_180023e50(auVar5,auVar8,(int *)local_280,(longlong)param_3,
                        (ulonglong)(uint)param_3[0xe1],(longlong)&local_148,param_7,param_8,param_9,
                        param_10);
        }
      }
    }
    else {
      FUN_180022590((int *)local_280,(longlong)param_3,pcVar27);
      FUN_18000bcc0(pplVar21);
      (**(code **)(param_3 + 0x448))(2,0);
    }
  }
  return;
}

