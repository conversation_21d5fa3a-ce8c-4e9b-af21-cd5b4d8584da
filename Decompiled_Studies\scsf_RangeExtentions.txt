
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_RangeExtensions
               (undefined1 param_1 [16],undefined1 param_2 [16],int *param_3,undefined8 param_4,
               undefined8 param_5,undefined8 param_6,ulonglong param_7,ulonglong param_8,
               undefined8 param_9,undefined8 param_10)

{
  int iVar1;
  byte bVar2;
  undefined1 auVar3 [16];
  undefined1 auVar4 [16];
  undefined1 auVar5 [16];
  undefined1 auVar6 [16];
  undefined1 auVar7 [16];
  uint *puVar8;
  bool bVar9;
  bool bVar10;
  bool bVar11;
  int iVar12;
  uint uVar13;
  uint uVar14;
  uint uVar15;
  float *pfVar16;
  float *pfVar17;
  undefined8 *puVar18;
  HAND<PERSON> pvVar19;
  char *pcVar20;
  undefined8 uVar21;
  ulonglong uVar22;
  ulonglong uVar23;
  ulonglong uVar24;
  longlong **pplVar25;
  longlong *plVar26;
  float *pfVar27;
  undefined4 *puVar28;
  float *pfVar29;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  undefined8 extraout_x1_10;
  undefined8 extraout_x1_11;
  undefined8 extraout_x1_12;
  undefined8 extraout_x1_13;
  undefined8 extraout_x1_14;
  undefined8 extraout_x1_15;
  undefined8 extraout_x1_16;
  undefined8 extraout_x1_17;
  undefined8 extraout_x1_18;
  undefined8 extraout_x1_19;
  undefined8 extraout_x1_20;
  undefined8 extraout_x1_21;
  undefined8 extraout_x1_22;
  undefined8 extraout_x1_23;
  undefined8 extraout_x1_24;
  undefined8 extraout_x1_25;
  undefined8 extraout_x1_26;
  undefined8 extraout_x1_27;
  int iVar30;
  uint uVar31;
  longlong *plVar32;
  longlong *plVar33;
  uint *puVar34;
  longlong lVar35;
  uint extraout_w11;
  uint extraout_w11_00;
  code *extraout_x11;
  code *pcVar36;
  code *extraout_x12;
  code *extraout_x12_00;
  code *extraout_x12_01;
  code *extraout_x12_02;
  code *extraout_x12_03;
  code *pcVar37;
  longlong extraout_x13;
  int extraout_w14;
  longlong extraout_x15;
  longlong lVar38;
  char *pcVar39;
  uint uVar40;
  longlong *plVar41;
  int *piVar42;
  longlong *plVar43;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  undefined4 extraout_s0_02;
  undefined4 extraout_s0_03;
  undefined4 extraout_s0_04;
  undefined4 extraout_s0_05;
  undefined4 extraout_s0_06;
  undefined4 extraout_s0_07;
  undefined4 extraout_s0_08;
  undefined4 extraout_s0_09;
  undefined4 extraout_s0_10;
  undefined4 extraout_s0_11;
  undefined4 extraout_s0_12;
  undefined4 extraout_s0_13;
  undefined4 extraout_s0_14;
  undefined4 extraout_s0_15;
  undefined4 extraout_s0_16;
  undefined4 extraout_s0_17;
  undefined4 extraout_s0_18;
  undefined4 extraout_s0_19;
  undefined4 extraout_s0_20;
  undefined4 extraout_s0_21;
  undefined4 extraout_s0_22;
  undefined4 extraout_s0_23;
  undefined4 extraout_s0_24;
  undefined4 extraout_s0_25;
  undefined4 extraout_s0_26;
  undefined4 extraout_s0_27;
  undefined4 extraout_s0_28;
  undefined4 extraout_s0_29;
  undefined4 extraout_s0_30;
  float extraout_s0_31;
  float extraout_s0_32;
  float extraout_s0_33;
  float extraout_s0_34;
  float extraout_s0_35;
  float extraout_s0_36;
  float extraout_s0_37;
  float extraout_s0_38;
  float extraout_s0_39;
  float extraout_s0_40;
  float extraout_s0_41;
  float extraout_s0_42;
  float extraout_s0_43;
  float extraout_s0_44;
  float extraout_s0_45;
  float extraout_s0_46;
  float extraout_s0_47;
  float fVar44;
  float extraout_s0_48;
  undefined4 extraout_var;
  undefined4 extraout_var_00;
  undefined4 extraout_var_01;
  undefined4 extraout_var_02;
  undefined4 extraout_var_03;
  undefined4 extraout_var_04;
  undefined4 extraout_var_05;
  undefined4 extraout_var_06;
  undefined4 extraout_var_07;
  undefined4 extraout_var_08;
  undefined4 extraout_var_09;
  undefined4 extraout_var_10;
  undefined4 extraout_var_11;
  undefined4 extraout_var_12;
  undefined4 extraout_var_13;
  undefined4 extraout_var_14;
  undefined4 extraout_var_15;
  undefined4 extraout_var_16;
  undefined4 extraout_var_17;
  undefined4 extraout_var_18;
  undefined4 extraout_var_19;
  undefined4 extraout_var_20;
  undefined4 extraout_var_21;
  undefined4 extraout_var_22;
  undefined4 extraout_var_23;
  undefined4 extraout_var_24;
  undefined4 extraout_var_25;
  undefined4 extraout_var_26;
  undefined4 extraout_var_27;
  undefined4 extraout_var_28;
  undefined4 extraout_var_29;
  undefined4 uVar45;
  undefined4 extraout_var_30;
  undefined4 extraout_var_31;
  undefined4 extraout_var_32;
  undefined4 extraout_var_33;
  undefined4 extraout_var_34;
  undefined4 extraout_var_35;
  undefined4 extraout_var_36;
  undefined4 extraout_var_37;
  undefined4 extraout_var_38;
  undefined4 extraout_var_39;
  undefined4 extraout_var_40;
  undefined4 extraout_var_41;
  undefined4 extraout_var_42;
  undefined4 extraout_var_43;
  undefined4 extraout_var_44;
  undefined4 extraout_var_45;
  undefined4 extraout_var_46;
  undefined4 extraout_var_47;
  undefined4 extraout_var_48;
  undefined4 extraout_var_49;
  undefined8 extraout_var_50;
  undefined8 extraout_var_51;
  undefined8 extraout_var_52;
  undefined8 extraout_var_53;
  undefined8 extraout_var_54;
  undefined8 extraout_var_55;
  undefined8 extraout_var_56;
  undefined8 extraout_var_57;
  undefined8 extraout_var_58;
  undefined8 extraout_var_59;
  undefined8 extraout_var_60;
  undefined8 extraout_var_61;
  undefined8 extraout_var_62;
  undefined8 extraout_var_63;
  undefined8 extraout_var_64;
  undefined8 extraout_var_65;
  undefined8 extraout_var_66;
  undefined8 extraout_var_67;
  undefined8 extraout_var_68;
  undefined8 extraout_var_69;
  undefined8 extraout_var_70;
  undefined8 extraout_var_71;
  undefined8 extraout_var_72;
  undefined8 extraout_var_73;
  undefined8 extraout_var_74;
  undefined8 extraout_var_75;
  undefined8 extraout_var_76;
  undefined8 extraout_var_77;
  undefined8 extraout_var_78;
  undefined8 uVar46;
  undefined8 extraout_var_79;
  undefined8 extraout_var_80;
  undefined8 extraout_var_81;
  undefined8 extraout_var_82;
  undefined8 extraout_var_83;
  undefined8 extraout_var_84;
  undefined8 extraout_var_85;
  undefined8 extraout_var_86;
  undefined8 extraout_var_87;
  undefined8 extraout_var_88;
  undefined8 extraout_var_89;
  undefined8 extraout_var_90;
  undefined8 extraout_var_91;
  undefined8 extraout_var_92;
  undefined8 extraout_var_93;
  undefined8 extraout_var_94;
  undefined8 extraout_var_95;
  undefined8 extraout_var_96;
  undefined8 extraout_var_97;
  undefined8 extraout_var_98;
  undefined8 extraout_var_99;
  undefined8 extraout_var_x00100;
  undefined4 uVar47;
  undefined8 uVar48;
  float fVar49;
  int iVar50;
  undefined4 uVar51;
  float fVar52;
  uint7 in_stack_fffffffffffffc89;
  uint *local_358;
  char *local_348;
  longlong *local_338;
  longlong *local_330;
  longlong *local_328;
  longlong *local_320;
  longlong *local_310;
  longlong *local_300;
  longlong *local_2f8;
  longlong *local_2f0;
  uint *local_2e8;
  uint *local_2d8;
  longlong *local_2d0;
  longlong *local_2c8;
  longlong *local_2c0;
  longlong *local_2b8;
  longlong *local_2b0;
  uint *local_2a8;
  longlong *local_2a0;
  longlong *local_298;
  longlong *local_290;
  longlong *local_288;
  longlong *local_280;
  longlong *local_278;
  longlong *local_270;
  longlong **local_268;
  longlong *local_260;
  undefined8 uStack_258;
  undefined8 local_250;
  ulonglong local_248;
  uint local_240;
  longlong *local_230;
  uint *local_228;
  longlong *local_220;
  longlong **local_218;
  longlong *local_210;
  longlong *local_208;
  longlong *local_200;
  longlong *local_1f8;
  longlong *local_1f0;
  longlong *local_1e8;
  longlong *local_1e0;
  longlong *local_1d8;
  longlong *local_1d0;
  longlong *local_1c8;
  longlong *local_1c0;
  longlong *local_1b8;
  longlong *local_1b0;
  longlong *local_1a8;
  longlong *local_1a0;
  int *local_198;
  longlong *local_190;
  undefined **local_188;
  undefined **local_180;
  undefined **local_178;
  undefined **local_170;
  undefined8 local_168;
  longlong local_160;
  undefined8 local_158;
  code *local_150;
  ulonglong local_148;
  undefined8 local_140;
  longlong local_138;
  undefined8 local_130;
  code *local_128;
  ulonglong local_120;
  undefined8 local_118;
  char *local_110;
  undefined8 uStack_108;
  undefined1 *local_100;
  undefined8 uStack_f8;
  uint local_f0;
  uint *local_e0;
  uint *local_d8;
  undefined **local_d0;
  longlong *local_c8;
  undefined4 local_c0;
  undefined **local_b8;
  undefined1 *local_b0;
  undefined8 uStack_a8;
  undefined1 *local_a0;
  
                    /* 0x85f68  21  scsf_RangeExtensions */
  uVar15 = param_2._0_4_;
  uVar47 = param_2._4_4_;
  uVar48 = param_2._8_8_;
  local_168 = 0xfffffffffffffffe;
  local_348 = "";
  local_a0 = &DAT_1800d4ecd;
  local_b0 = &DAT_1800d4ecd;
  uStack_a8 = 0;
  local_2d8 = (uint *)(**(code **)(param_3 + 0x62e))(param_1._0_4_,0);
  local_2a8 = (uint *)(**(code **)(param_3 + 0x62e))(1);
  local_d8 = (uint *)(**(code **)(param_3 + 0x62e))(2);
  local_228 = (uint *)(**(code **)(param_3 + 0x62e))(3);
  pfVar16 = (float *)(**(code **)(param_3 + 0x630))(0);
  pfVar17 = (float *)(**(code **)(param_3 + 0x630))(1);
  puVar18 = (undefined8 *)(**(code **)(param_3 + 0x446))(2);
  lVar35 = *(longlong *)(param_3 + 0x140);
  local_358 = (uint *)*puVar18;
  uVar21 = extraout_x1;
  uVar51 = extraout_s0;
  uVar45 = extraout_var;
  uVar46 = extraout_var_50;
  if (lVar35 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar21 = extraout_x1_00;
      uVar51 = extraout_s0_00;
      uVar45 = extraout_var_00;
      uVar46 = extraout_var_51;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_180086088;
    local_190 = (longlong *)(param_3 + 0x14a);
LAB_1800860c4:
    local_300 = local_190;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar21 = extraout_x1_01;
      uVar51 = extraout_s0_01;
      uVar45 = extraout_var_01;
      uVar46 = extraout_var_52;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_1800860f4;
    local_208 = (longlong *)(param_3 + 0x14a);
LAB_180086128:
    local_280 = local_208;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar21 = extraout_x1_02;
      uVar51 = extraout_s0_02;
      uVar45 = extraout_var_02;
      uVar46 = extraout_var_53;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_180086160;
    local_200 = (longlong *)(param_3 + 0x14a);
LAB_1800861a0:
    local_278 = local_200;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar21 = extraout_x1_03;
      uVar51 = extraout_s0_03;
      uVar45 = extraout_var_03;
      uVar46 = extraout_var_54;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_1800861d0;
    local_1f8 = (longlong *)(param_3 + 0x14a);
LAB_180086208:
    local_270 = local_1f8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar21 = extraout_x1_04;
      uVar51 = extraout_s0_04;
      uVar45 = extraout_var_04;
      uVar46 = extraout_var_55;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_180086238;
    local_1f0 = (longlong *)(param_3 + 0x14a);
LAB_180086270:
    local_338 = local_1f0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar21 = extraout_x1_05;
      uVar51 = extraout_s0_05;
      uVar45 = extraout_var_05;
      uVar46 = extraout_var_56;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_1800862a0;
    local_1e8 = (longlong *)(param_3 + 0x14a);
LAB_1800862d8:
    local_330 = local_1e8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar21 = extraout_x1_06;
      uVar51 = extraout_s0_06;
      uVar45 = extraout_var_06;
      uVar46 = extraout_var_57;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_180086308;
    local_1e0 = (longlong *)(param_3 + 0x14a);
LAB_180086340:
    local_2a0 = local_1e0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar21 = extraout_x1_07;
      uVar51 = extraout_s0_07;
      uVar45 = extraout_var_07;
      uVar46 = extraout_var_58;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_180086370;
    local_1d8 = (longlong *)(param_3 + 0x14a);
LAB_1800863a8:
    local_328 = local_1d8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar21 = extraout_x1_08;
      uVar51 = extraout_s0_08;
      uVar45 = extraout_var_08;
      uVar46 = extraout_var_59;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_1800863d8;
    local_1d0 = (longlong *)(param_3 + 0x14a);
LAB_180086410:
    local_298 = local_1d0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar21 = extraout_x1_09;
      uVar51 = extraout_s0_09;
      uVar45 = extraout_var_09;
      uVar46 = extraout_var_60;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_180086440;
    local_1c8 = (longlong *)(param_3 + 0x14a);
LAB_180086478:
    local_290 = local_1c8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar21 = extraout_x1_10;
      uVar51 = extraout_s0_10;
      uVar45 = extraout_var_10;
      uVar46 = extraout_var_61;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_1800864a8;
    local_1c0 = (longlong *)(param_3 + 0x14a);
LAB_1800864e0:
    local_288 = local_1c0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar21 = extraout_x1_11;
      uVar51 = extraout_s0_11;
      uVar45 = extraout_var_11;
      uVar46 = extraout_var_62;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_180086510;
    local_1b8 = (longlong *)(param_3 + 0x14a);
LAB_180086548:
    local_320 = local_1b8;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar21 = extraout_x1_12;
      uVar51 = extraout_s0_12;
      uVar45 = extraout_var_12;
      uVar46 = extraout_var_63;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_180086578;
    local_1b0 = (longlong *)(param_3 + 0x14a);
LAB_1800865b0:
    local_310 = local_1b0;
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar21 = extraout_x1_13;
      uVar51 = extraout_s0_13;
      uVar45 = extraout_var_13;
      uVar46 = extraout_var_64;
    }
    lVar35 = *(longlong *)(param_3 + 0x140);
    if (lVar35 != 0) goto LAB_1800865d0;
    plVar32 = (longlong *)(param_3 + 0x14a);
  }
  else {
LAB_180086088:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_300 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar50 = 0;
      if (iVar12 < 1) {
        iVar50 = iVar12 + -1;
      }
      local_300 = (longlong *)(lVar35 + (longlong)iVar50 * 0x170);
    }
    local_190 = local_300;
    if (lVar35 == 0) goto LAB_1800860c4;
LAB_1800860f4:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_280 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar50 = 1;
      if (iVar12 + -1 == 0 || iVar12 < 1) {
        iVar50 = iVar12 + -1;
      }
      local_280 = (longlong *)(lVar35 + (longlong)iVar50 * 0x170);
    }
    local_208 = local_280;
    if (lVar35 == 0) goto LAB_180086128;
LAB_180086160:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_278 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar50 = 2;
      if (iVar12 < 3) {
        iVar50 = iVar12 + -1;
      }
      local_278 = (longlong *)(lVar35 + (longlong)iVar50 * 0x170);
    }
    local_200 = local_278;
    if (lVar35 == 0) goto LAB_1800861a0;
LAB_1800861d0:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_270 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar50 = 3;
      if (iVar12 < 4) {
        iVar50 = iVar12 + -1;
      }
      local_270 = (longlong *)(lVar35 + (longlong)iVar50 * 0x170);
    }
    local_1f8 = local_270;
    if (lVar35 == 0) goto LAB_180086208;
LAB_180086238:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_338 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar50 = 4;
      if (iVar12 < 5) {
        iVar50 = iVar12 + -1;
      }
      local_338 = (longlong *)(lVar35 + (longlong)iVar50 * 0x170);
    }
    local_1f0 = local_338;
    if (lVar35 == 0) goto LAB_180086270;
LAB_1800862a0:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_330 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar50 = 5;
      if (iVar12 < 6) {
        iVar50 = iVar12 + -1;
      }
      local_330 = (longlong *)(lVar35 + (longlong)iVar50 * 0x170);
    }
    local_1e8 = local_330;
    if (lVar35 == 0) goto LAB_1800862d8;
LAB_180086308:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_2a0 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar50 = 6;
      if (iVar12 < 7) {
        iVar50 = iVar12 + -1;
      }
      local_2a0 = (longlong *)(lVar35 + (longlong)iVar50 * 0x170);
    }
    local_1e0 = local_2a0;
    if (lVar35 == 0) goto LAB_180086340;
LAB_180086370:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_328 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar50 = 7;
      if (iVar12 < 8) {
        iVar50 = iVar12 + -1;
      }
      local_328 = (longlong *)(lVar35 + (longlong)iVar50 * 0x170);
    }
    local_1d8 = local_328;
    if (lVar35 == 0) goto LAB_1800863a8;
LAB_1800863d8:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_298 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar50 = 8;
      if (iVar12 < 9) {
        iVar50 = iVar12 + -1;
      }
      local_298 = (longlong *)(lVar35 + (longlong)iVar50 * 0x170);
    }
    local_1d0 = local_298;
    if (lVar35 == 0) goto LAB_180086410;
LAB_180086440:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_290 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar50 = 9;
      if (iVar12 < 10) {
        iVar50 = iVar12 + -1;
      }
      local_290 = (longlong *)(lVar35 + (longlong)iVar50 * 0x170);
    }
    local_1c8 = local_290;
    if (lVar35 == 0) goto LAB_180086478;
LAB_1800864a8:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_288 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar50 = 10;
      if (iVar12 < 0xb) {
        iVar50 = iVar12 + -1;
      }
      local_288 = (longlong *)(lVar35 + (longlong)iVar50 * 0x170);
    }
    local_1c0 = local_288;
    if (lVar35 == 0) goto LAB_1800864e0;
LAB_180086510:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_320 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar50 = 0xb;
      if (iVar12 < 0xc) {
        iVar50 = iVar12 + -1;
      }
      local_320 = (longlong *)(lVar35 + (longlong)iVar50 * 0x170);
    }
    local_1b8 = local_320;
    if (lVar35 == 0) goto LAB_180086548;
LAB_180086578:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      local_310 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar50 = 0xc;
      if (iVar12 < 0xd) {
        iVar50 = iVar12 + -1;
      }
      local_310 = (longlong *)(lVar35 + (longlong)iVar50 * 0x170);
    }
    local_1b0 = local_310;
    if (lVar35 == 0) goto LAB_1800865b0;
LAB_1800865d0:
    iVar12 = param_3[0x148];
    if (iVar12 == 0) {
      plVar32 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar50 = 0xd;
      if (iVar12 < 0xe) {
        iVar50 = iVar12 + -1;
      }
      plVar32 = (longlong *)(lVar35 + (longlong)iVar50 * 0x170);
    }
  }
  lVar35 = *(longlong *)(param_3 + 0x84);
  if (lVar35 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar21 = extraout_x1_14;
      uVar51 = extraout_s0_14;
      uVar45 = extraout_var_14;
      uVar46 = extraout_var_65;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_180086634;
    local_210 = (longlong *)(param_3 + 0x8e);
LAB_180086668:
    local_2f8 = local_210;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar21 = extraout_x1_15;
      uVar51 = extraout_s0_15;
      uVar45 = extraout_var_15;
      uVar46 = extraout_var_66;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_180086698;
    local_230 = (longlong *)(param_3 + 0x8e);
LAB_1800866cc:
    local_2f0 = local_230;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar21 = extraout_x1_16;
      uVar51 = extraout_s0_16;
      uVar45 = extraout_var_16;
      uVar46 = extraout_var_67;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_1800866fc;
    local_1a8 = (longlong *)(param_3 + 0x8e);
LAB_180086734:
    local_2b0 = local_1a8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar21 = extraout_x1_17;
      uVar51 = extraout_s0_17;
      uVar45 = extraout_var_17;
      uVar46 = extraout_var_68;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_180086764;
    local_1a0 = (longlong *)(param_3 + 0x8e);
LAB_18008679c:
    local_2b8 = local_1a0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar21 = extraout_x1_18;
      uVar51 = extraout_s0_18;
      uVar45 = extraout_var_18;
      uVar46 = extraout_var_69;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_1800867c8;
    piVar42 = param_3 + 0x8e;
LAB_1800867fc:
    local_198 = piVar42;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar21 = extraout_x1_19;
      uVar51 = extraout_s0_19;
      uVar45 = extraout_var_19;
      uVar46 = extraout_var_70;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_18008682c;
    local_260 = (longlong *)(param_3 + 0x8e);
LAB_180086864:
    local_2c0 = local_260;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar21 = extraout_x1_20;
      uVar51 = extraout_s0_20;
      uVar45 = extraout_var_20;
      uVar46 = extraout_var_71;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_180086894;
    local_c8 = (longlong *)(param_3 + 0x8e);
LAB_1800868cc:
    local_2c8 = local_c8;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar21 = extraout_x1_21;
      uVar51 = extraout_s0_21;
      uVar45 = extraout_var_21;
      uVar46 = extraout_var_72;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_1800868fc;
    local_220 = (longlong *)(param_3 + 0x8e);
LAB_180086934:
    local_2d0 = local_220;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar21 = extraout_x1_22;
      uVar51 = extraout_s0_22;
      uVar45 = extraout_var_22;
      uVar46 = extraout_var_73;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_180086964;
    local_218 = (longlong **)(param_3 + 0x8e);
LAB_18008699c:
    local_268 = local_218;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar21 = extraout_x1_23;
      uVar51 = extraout_s0_23;
      uVar45 = extraout_var_23;
      uVar46 = extraout_var_74;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_1800869c4;
    plVar26 = (longlong *)(param_3 + 0x8e);
LAB_1800869f4:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar21 = extraout_x1_24;
      uVar51 = extraout_s0_24;
      uVar45 = extraout_var_24;
      uVar46 = extraout_var_75;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_180086a1c;
    plVar43 = (longlong *)(param_3 + 0x8e);
LAB_180086a4c:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar21 = extraout_x1_25;
      uVar51 = extraout_s0_25;
      uVar45 = extraout_var_25;
      uVar46 = extraout_var_76;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_180086a74;
    plVar41 = (longlong *)(param_3 + 0x8e);
LAB_180086aa4:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar21 = extraout_x1_26;
      uVar51 = extraout_s0_26;
      uVar45 = extraout_var_26;
      uVar46 = extraout_var_77;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 != 0) goto LAB_180086ad4;
    local_e0 = (uint *)(param_3 + 0x8e);
LAB_180086b10:
    local_2e8 = local_e0;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar21 = extraout_x1_27;
      uVar51 = extraout_s0_27;
      uVar45 = extraout_var_27;
      uVar46 = extraout_var_78;
    }
    lVar35 = *(longlong *)(param_3 + 0x84);
    if (lVar35 == 0) {
      plVar33 = (longlong *)(param_3 + 0x8e);
      goto LAB_180086b54;
    }
  }
  else {
LAB_180086634:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_2f8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar50 = 0;
      if (iVar12 < 1) {
        iVar50 = iVar12 + -1;
      }
      local_2f8 = (longlong *)(lVar35 + (longlong)iVar50 * 0x98);
    }
    local_210 = local_2f8;
    if (lVar35 == 0) goto LAB_180086668;
LAB_180086698:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_2f0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar50 = 1;
      if (iVar12 + -1 == 0 || iVar12 < 1) {
        iVar50 = iVar12 + -1;
      }
      local_2f0 = (longlong *)(lVar35 + (longlong)iVar50 * 0x98);
    }
    local_230 = local_2f0;
    if (lVar35 == 0) goto LAB_1800866cc;
LAB_1800866fc:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_2b0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar50 = 2;
      if (iVar12 < 3) {
        iVar50 = iVar12 + -1;
      }
      local_2b0 = (longlong *)(lVar35 + (longlong)iVar50 * 0x98);
    }
    local_1a8 = local_2b0;
    if (lVar35 == 0) goto LAB_180086734;
LAB_180086764:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_2b8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar50 = 3;
      if (iVar12 < 4) {
        iVar50 = iVar12 + -1;
      }
      local_2b8 = (longlong *)(lVar35 + (longlong)iVar50 * 0x98);
    }
    local_1a0 = local_2b8;
    if (lVar35 == 0) goto LAB_18008679c;
LAB_1800867c8:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      piVar42 = param_3 + 0x8e;
    }
    else {
      iVar50 = 4;
      if (iVar12 < 5) {
        iVar50 = iVar12 + -1;
      }
      piVar42 = (int *)(lVar35 + (longlong)iVar50 * 0x98);
    }
    local_198 = piVar42;
    if (lVar35 == 0) goto LAB_1800867fc;
LAB_18008682c:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_2c0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar50 = 5;
      if (iVar12 < 6) {
        iVar50 = iVar12 + -1;
      }
      local_2c0 = (longlong *)(lVar35 + (longlong)iVar50 * 0x98);
    }
    local_260 = local_2c0;
    if (lVar35 == 0) goto LAB_180086864;
LAB_180086894:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_2c8 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar50 = 6;
      if (iVar12 < 7) {
        iVar50 = iVar12 + -1;
      }
      local_2c8 = (longlong *)(lVar35 + (longlong)iVar50 * 0x98);
    }
    local_c8 = local_2c8;
    if (lVar35 == 0) goto LAB_1800868cc;
LAB_1800868fc:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_2d0 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar50 = 7;
      if (iVar12 < 8) {
        iVar50 = iVar12 + -1;
      }
      local_2d0 = (longlong *)(lVar35 + (longlong)iVar50 * 0x98);
    }
    local_220 = local_2d0;
    if (lVar35 == 0) goto LAB_180086934;
LAB_180086964:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_268 = (longlong **)(param_3 + 0x8e);
    }
    else {
      iVar50 = 8;
      if (iVar12 < 9) {
        iVar50 = iVar12 + -1;
      }
      local_268 = (longlong **)(lVar35 + (longlong)iVar50 * 0x98);
    }
    local_218 = local_268;
    if (lVar35 == 0) goto LAB_18008699c;
LAB_1800869c4:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      plVar26 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar50 = 9;
      if (iVar12 < 10) {
        iVar50 = iVar12 + -1;
      }
      plVar26 = (longlong *)(lVar35 + (longlong)iVar50 * 0x98);
    }
    if (lVar35 == 0) goto LAB_1800869f4;
LAB_180086a1c:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      plVar43 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar50 = 10;
      if (iVar12 < 0xb) {
        iVar50 = iVar12 + -1;
      }
      plVar43 = (longlong *)(lVar35 + (longlong)iVar50 * 0x98);
    }
    if (lVar35 == 0) goto LAB_180086a4c;
LAB_180086a74:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      plVar41 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar50 = 0xb;
      if (iVar12 < 0xc) {
        iVar50 = iVar12 + -1;
      }
      plVar41 = (longlong *)(lVar35 + (longlong)iVar50 * 0x98);
    }
    if (lVar35 == 0) goto LAB_180086aa4;
LAB_180086ad4:
    iVar12 = param_3[0x8c];
    if (iVar12 == 0) {
      local_2e8 = (uint *)(param_3 + 0x8e);
    }
    else {
      iVar50 = 0xc;
      if (iVar12 < 0xd) {
        iVar50 = iVar12 + -1;
      }
      local_2e8 = (uint *)(lVar35 + (longlong)iVar50 * 0x98);
    }
    local_e0 = local_2e8;
    if (lVar35 == 0) goto LAB_180086b10;
  }
  iVar12 = param_3[0x8c];
  if (iVar12 == 0) {
    plVar33 = (longlong *)(param_3 + 0x8e);
  }
  else {
    iVar50 = 0xd;
    if (iVar12 < 0xe) {
      iVar50 = iVar12 + -1;
    }
    plVar33 = (longlong *)(lVar35 + (longlong)iVar50 * 0x98);
  }
LAB_180086b54:
  local_b8 = LineStyles::vftable;
  local_170 = ErasureModes::vftable;
  local_178 = PitSessions::vftable;
  local_d0 = PricePositions::vftable;
  local_180 = OverlapModes::vftable;
  local_188 = ShowZonePrices::vftable;
  if (param_3[0x2b] == 0) {
    if (param_3[3] == 0) {
      auVar3._4_4_ = uVar45;
      auVar3._0_4_ = uVar51;
      auVar3._8_8_ = uVar46;
      uVar21 = FUN_1800254e8(auVar3,CONCAT44(uVar47,uVar15),(longlong)param_3,uVar21,param_5,param_6
                             ,param_7,param_8,param_9,param_10);
      *local_2d8 = (uint)uVar21;
    }
    if (*local_2d8 == 0) {
      iVar12 = (**(code **)(param_3 + 0x3ec))(param_3[0x270]);
      if (param_3[3] == 0) {
        *local_228 = (uint)(iVar12 != 0);
        if (local_358 != (uint *)0x0) {
          FUN_180022590((int *)local_358,(longlong)param_3,param_5);
          FUN_18000bcc0(local_358);
          (**(code **)(param_3 + 0x448))(2,0);
        }
        uStack_108 = 0;
        local_110 = (char *)0x0;
        uStack_f8 = 0;
        local_100 = (undefined1 *)0x0;
        local_f0 = 0;
        uStack_258 = 0;
        local_260 = (longlong *)0x0;
        local_248 = 0;
        local_250 = 0;
        local_240 = 0;
        local_358 = (uint *)FUN_180096150(0xa8);
        local_358[2] = 0;
        local_358[3] = 0;
        local_358[0] = 0;
        local_358[1] = 0;
        local_358[6] = 0;
        local_358[7] = 0;
        local_358[4] = 0;
        local_358[5] = 0;
        local_358[10] = 0;
        local_358[0xb] = 0;
        local_358[8] = 0;
        local_358[9] = 0;
        local_358[0xe] = 0;
        local_358[0xf] = 0;
        local_358[0xc] = 0;
        local_358[0xd] = 0;
        local_358[0x12] = 0;
        local_358[0x13] = 0;
        local_358[0x10] = 0;
        local_358[0x11] = 0;
        local_358[0x16] = 0;
        local_358[0x17] = 0;
        local_358[0x14] = 0;
        local_358[0x15] = 0;
        local_358[0x1a] = 0;
        local_358[0x1b] = 0;
        local_358[0x18] = 0;
        local_358[0x19] = 0;
        local_358[0x1e] = 0;
        local_358[0x1f] = 0;
        local_358[0x1c] = 0;
        local_358[0x1d] = 0;
        local_358[0x22] = 0;
        local_358[0x23] = 0;
        local_358[0x20] = 0;
        local_358[0x21] = 0;
        local_358[0x26] = 0;
        local_358[0x27] = 0;
        local_358[0x24] = 0;
        local_358[0x25] = 0;
        local_358[0x28] = 0;
        local_358[0x29] = 0;
        local_2d8 = local_358;
        local_e0 = local_358;
        bVar9 = FUN_180026690((longlong)plVar43);
        uVar13 = FUN_180026550((longlong)plVar41);
        bVar10 = FUN_180026690((longlong)plVar26);
        uVar22 = FUN_180026708((longlong)local_268);
        uVar14 = FUN_180026550((longlong)local_2d0);
        bVar11 = FUN_180026690((longlong)local_2c8);
        uVar40 = 0;
        if (!bVar11) {
          uVar40 = 3;
        }
        uVar23 = FUN_180026708((longlong)local_2c0);
        uVar24 = FUN_180026708((longlong)piVar42);
        local_358[0x24] = 0;
        local_358[0x25] = 0;
        local_358[0x26] = 0;
        local_358[0x27] = 0;
        lVar35 = FUN_180096150(0x98);
        *(longlong *)lVar35 = lVar35;
        *(longlong *)(lVar35 + 8) = lVar35;
        *(longlong *)(local_358 + 0x24) = lVar35;
        *local_358 = (uint)uVar24;
        local_358[1] = (uint)uVar23;
        local_358[2] = uVar40;
        local_358[3] = uVar14;
        local_358[4] = (uint)uVar22;
        *(bool *)(local_358 + 0xe) = bVar10;
        *(undefined8 *)(local_358 + 0x11) = uStack_108;
        *(char **)(local_358 + 0xf) = local_110;
        *(undefined8 *)(local_358 + 0x15) = uStack_f8;
        *(undefined1 **)(local_358 + 0x13) = local_100;
        local_358[5] = 0;
        local_358[6] = 0;
        local_358[8] = 0;
        local_358[9] = 0;
        local_358[10] = 0;
        local_358[0xb] = 0;
        local_358[0x17] = local_f0;
        *(undefined8 *)(local_358 + 0x1a) = uStack_258;
        *(longlong **)(local_358 + 0x18) = local_260;
        *(ulonglong *)(local_358 + 0x1e) = local_248;
        *(undefined8 *)(local_358 + 0x1c) = local_250;
        local_358[0xc] = 0;
        local_358[0xd] = 0;
        local_358[7] = uVar13;
        local_358[0x20] = local_240;
        local_358[0x21] = 0;
        *(undefined1 *)(local_358 + 0x22) = 0;
        *(undefined2 *)((longlong)local_358 + 0x8a) = 0;
        local_358[0x23] = 0;
        *(bool *)((longlong)local_358 + 0x89) = bVar9;
        pplVar25 = (longlong **)FUN_180096150(0x18);
        pplVar25[1] = (longlong *)0x0;
        *pplVar25 = (longlong *)0x0;
        pplVar25[1] = (longlong *)0x0;
        pplVar25[2] = (longlong *)0x0;
        local_218 = pplVar25;
        plVar26 = (longlong *)FUN_180096150(0x120);
        *plVar26 = (longlong)plVar26;
        plVar26[1] = (longlong)plVar26;
        pplVar25[1] = plVar26;
        *(undefined1 *)pplVar25 = 1;
        FUN_18000af28((char *)pplVar25,(longlong)param_3);
        *(longlong ***)(local_358 + 0x28) = pplVar25;
        FUN_180022590((int *)local_358,(longlong)param_3,param_5);
        (**(code **)(param_3 + 0x448))(2,local_358);
        *local_d8 = 0;
        *local_2a8 = 0;
      }
      puVar8 = local_d8;
      if (param_3[0x1b] == 0) {
        if (local_358 != (uint *)0x0) {
          if (iVar12 == 0) {
            FUN_180022590((int *)local_358,(longlong)param_3,param_5);
            *local_228 = 0;
          }
          else {
            if (*local_228 == 0) {
              uVar40 = 0;
            }
            else {
              uVar40 = param_3[3];
            }
            if ((int)uVar40 < *param_3) {
              do {
                lVar35 = *(longlong *)(param_3 + 0x58);
                uVar13 = (int)uVar40 >> 0x1f;
                if (lVar35 == 0) {
                  if (*(code **)(param_3 + 0x5c) != (code *)0x0) {
                    (**(code **)(param_3 + 0x5c))(param_3[0x5e]);
                  }
                  lVar35 = *(longlong *)(param_3 + 0x58);
                  if (lVar35 != 0) goto LAB_1800877ac;
                  piVar42 = param_3 + 0x62;
                }
                else {
LAB_1800877ac:
                  iVar50 = param_3[0x60];
                  if (iVar50 == 0) {
                    piVar42 = param_3 + 0x62;
                  }
                  else {
                    uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                    if (iVar50 <= (int)uVar14) {
                      uVar14 = iVar50 - 1;
                    }
                    piVar42 = (int *)(lVar35 + (longlong)(int)uVar14 * 8);
                  }
                }
                local_d8 = *(uint **)piVar42;
                (**(code **)(param_3 + 0x4d4))
                          (&local_e0,&local_d8,"PST-08PDT+01,M3.2.0/02:00,M11.1.0/02:00");
                local_160 = 0;
                local_158 = 0;
                pcVar36 = *(code **)(param_3 + 0x1c4);
                local_140 = 0;
                local_150 = (code *)0x0;
                local_148 = 0;
                local_138 = 0;
                local_130 = 0;
                bVar2 = *(byte *)(local_2f8 + 3);
                uVar14 = (uint)bVar2;
                local_118 = 0;
                local_128 = (code *)0x0;
                local_120 = 0;
                if (bVar2 != 0x12 && bVar2 != 0x14) {
                  FUN_180026550((longlong)local_2f8);
                  pcVar36 = extraout_x12;
                  uVar14 = extraout_w11;
                }
                if ((uVar14 - 0x12 & 0xfc) == 0 && uVar14 != 0x13) {
                  uVar14 = *(uint *)(local_210 + 4);
                }
                else {
                  uVar14 = FUN_180026550((longlong)local_2f8);
                  pcVar36 = extraout_x12_00;
                }
                (*pcVar36)(uVar14);
                pcVar36 = *(code **)(param_3 + 0x1c4);
                bVar2 = *(byte *)(local_2f0 + 3);
                uVar14 = (uint)bVar2;
                if (bVar2 != 0x12 && bVar2 != 0x14) {
                  FUN_180026550((longlong)local_2f0);
                  pcVar36 = extraout_x12_01;
                  uVar14 = extraout_w11_00;
                }
                if ((uVar14 - 0x12 & 0xfc) == 0 && uVar14 != 0x13) {
                  uVar14 = *(uint *)(local_230 + 4);
                }
                else {
                  uVar14 = FUN_180026550((longlong)local_2f0);
                  pcVar36 = extraout_x12_02;
                }
                plVar26 = &local_138;
                (*pcVar36)(uVar14);
                if ((int)local_158 == 0) {
                  return;
                }
                if ((int)local_130 == 0) {
                  return;
                }
                bVar10 = false;
                bVar9 = false;
                fVar44 = extraout_s0_31;
                uVar51 = extraout_var_31;
                uVar21 = extraout_var_82;
                if ((local_160 == 0) &&
                   ((local_150 == (code *)0x0 ||
                    ((*local_150)(local_148 & 0xffffffff), fVar44 = extraout_s0_32,
                    uVar51 = extraout_var_32, uVar21 = extraout_var_83, local_160 == 0)))) {
                  pfVar27 = (float *)((longlong)&local_140 + 4);
                }
                else if ((int)local_140 == 0) {
                  pfVar27 = (float *)((longlong)&local_140 + 4);
                }
                else {
                  uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                  if ((int)local_140 <= (int)uVar14) {
                    uVar14 = (int)local_140 - 1;
                  }
                  pfVar27 = (float *)(local_160 + (longlong)(int)uVar14 * 4);
                }
                if (*pfVar27 == 0.0) {
                  uVar22 = local_118 & 0xffffffff;
LAB_180087f88:
                  uVar14 = FUN_180026550((longlong)local_2e8);
                  pcVar36 = extraout_x11;
                  pcVar37 = extraout_x12_03;
                  lVar35 = extraout_x13;
                  lVar38 = extraout_x15;
                  fVar44 = extraout_s0_45;
                  uVar51 = extraout_var_45;
                  uVar21 = extraout_var_96;
                  iVar50 = extraout_w14;
                  if (uVar14 == 0) {
                    *puVar8 = uVar40;
                    *local_2a8 = uVar40;
                  }
                }
                else {
                  iVar50 = (int)local_140;
                  if (local_138 == 0) {
                    if (local_128 != (code *)0x0) {
                      (*local_128)(local_120 & 0xffffffff);
                      fVar44 = extraout_s0_33;
                      uVar51 = extraout_var_33;
                      uVar21 = extraout_var_84;
                      iVar50 = (int)local_140;
                      if (local_138 != 0) goto LAB_180087990;
                    }
                    pfVar27 = (float *)((longlong)&local_118 + 4);
                  }
                  else {
LAB_180087990:
                    if ((uint)local_118 == 0) {
                      pfVar27 = (float *)((longlong)&local_118 + 4);
                    }
                    else {
                      uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                      if ((int)(uint)local_118 <= (int)uVar14) {
                        uVar14 = (uint)local_118 - 1;
                      }
                      pfVar27 = (float *)(local_138 + (longlong)(int)uVar14 * 4);
                    }
                  }
                  uVar22 = (ulonglong)(uint)local_118;
                  if (*pfVar27 == 0.0) goto LAB_180087f88;
                  if (*puVar8 == uVar40) goto LAB_180087c34;
                  if (local_160 == 0) {
                    if (local_150 != (code *)0x0) {
                      (*local_150)(local_148 & 0xffffffff);
                      uVar22 = local_118 & 0xffffffff;
                      fVar44 = extraout_s0_34;
                      uVar51 = extraout_var_34;
                      uVar21 = extraout_var_85;
                      iVar50 = (int)local_140;
                      if (local_160 != 0) goto LAB_180087a04;
                    }
                    pfVar27 = (float *)((longlong)&local_140 + 4);
                  }
                  else {
LAB_180087a04:
                    if (iVar50 == 0) {
                      pfVar27 = (float *)((longlong)&local_140 + 4);
                    }
                    else {
                      uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                      if (iVar50 <= (int)uVar14) {
                        uVar14 = iVar50 - 1;
                      }
                      pfVar27 = (float *)(local_160 + (longlong)(int)uVar14 * 4);
                    }
                  }
                  if (local_160 == 0) {
                    if (local_150 != (code *)0x0) {
                      (*local_150)(local_148 & 0xffffffff);
                      uVar22 = local_118 & 0xffffffff;
                      fVar44 = extraout_s0_35;
                      uVar51 = extraout_var_35;
                      uVar21 = extraout_var_86;
                      iVar50 = (int)local_140;
                      if (local_160 != 0) goto LAB_180087a5c;
                    }
                    pfVar29 = (float *)((longlong)&local_140 + 4);
                  }
                  else {
LAB_180087a5c:
                    if (iVar50 == 0) {
                      pfVar29 = (float *)((longlong)&local_140 + 4);
                    }
                    else {
                      uVar14 = uVar40 - 1 & ((int)(uVar40 - 1) >> 0x1f ^ 0xffffffffU);
                      if (iVar50 <= (int)uVar14) {
                        uVar14 = iVar50 - 1;
                      }
                      pfVar29 = (float *)(local_160 + (longlong)(int)uVar14 * 4);
                    }
                  }
                  if (*pfVar27 == *pfVar29) {
LAB_180087b54:
                    uVar14 = *puVar8;
                    if (uVar14 == uVar40) {
LAB_180087c34:
                      bVar10 = true;
                      goto LAB_180087c38;
                    }
                    if (local_160 == 0) {
                      if (local_150 != (code *)0x0) {
                        (*local_150)(local_148 & 0xffffffff);
                        uVar22 = local_118 & 0xffffffff;
                        fVar44 = extraout_s0_38;
                        uVar51 = extraout_var_38;
                        uVar21 = extraout_var_89;
                        iVar50 = (int)local_140;
                        if (local_160 != 0) goto LAB_180087b94;
                      }
                      pfVar27 = (float *)((longlong)&local_140 + 4);
                    }
                    else {
LAB_180087b94:
                      if (iVar50 == 0) {
                        pfVar27 = (float *)((longlong)&local_140 + 4);
                      }
                      else {
                        uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
                        if (iVar50 <= (int)uVar14) {
                          uVar14 = iVar50 - 1;
                        }
                        pfVar27 = (float *)(local_160 + (longlong)(int)uVar14 * 4);
                      }
                    }
                    if (*pfVar16 < *pfVar27) goto LAB_180087c34;
                    uVar14 = *puVar8;
                    if (local_138 == 0) {
                      if (local_128 != (code *)0x0) {
                        (*local_128)(local_120 & 0xffffffff);
                        uVar22 = local_118 & 0xffffffff;
                        fVar44 = extraout_s0_39;
                        uVar51 = extraout_var_39;
                        uVar21 = extraout_var_90;
                        iVar50 = (int)local_140;
                        if (local_138 != 0) goto LAB_180087c00;
                      }
                      pfVar27 = (float *)((longlong)&local_118 + 4);
                    }
                    else {
LAB_180087c00:
                      iVar30 = (int)uVar22;
                      if (iVar30 == 0) {
                        pfVar27 = (float *)((longlong)&local_118 + 4);
                      }
                      else {
                        uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
                        if (iVar30 <= (int)uVar14) {
                          uVar14 = iVar30 - 1;
                        }
                        pfVar27 = (float *)(local_138 + (longlong)(int)uVar14 * 4);
                      }
                    }
                    pcVar36 = local_150;
                    pcVar37 = local_128;
                    lVar35 = local_160;
                    lVar38 = local_138;
                    if (*pfVar27 < *pfVar17) goto LAB_180087c34;
                  }
                  else {
                    if (local_138 == 0) {
                      if (local_128 != (code *)0x0) {
                        (*local_128)(local_120 & 0xffffffff);
                        uVar22 = local_118 & 0xffffffff;
                        fVar44 = extraout_s0_36;
                        uVar51 = extraout_var_36;
                        uVar21 = extraout_var_87;
                        iVar50 = (int)local_140;
                        if (local_138 != 0) goto LAB_180087ac4;
                      }
                      pfVar27 = (float *)((longlong)&local_118 + 4);
LAB_180087ae8:
                      if (local_128 != (code *)0x0) {
                        (*local_128)(local_120 & 0xffffffff);
                        uVar22 = local_118 & 0xffffffff;
                        fVar44 = extraout_s0_37;
                        uVar51 = extraout_var_37;
                        uVar21 = extraout_var_88;
                        iVar50 = (int)local_140;
                        if (local_138 != 0) goto LAB_180087b18;
                      }
                      pfVar29 = (float *)((longlong)&local_118 + 4);
                    }
                    else {
LAB_180087ac4:
                      iVar30 = (int)uVar22;
                      if (iVar30 == 0) {
                        pfVar27 = (float *)((longlong)&local_118 + 4);
                      }
                      else {
                        uVar14 = uVar40 - 1 & ((int)(uVar40 - 1) >> 0x1f ^ 0xffffffffU);
                        if (iVar30 <= (int)uVar14) {
                          uVar14 = iVar30 - 1;
                        }
                        pfVar27 = (float *)(local_138 + (longlong)(int)uVar14 * 4);
                      }
                      if (local_138 == 0) goto LAB_180087ae8;
LAB_180087b18:
                      iVar30 = (int)uVar22;
                      if (iVar30 == 0) {
                        pfVar29 = (float *)((longlong)&local_118 + 4);
                      }
                      else {
                        uVar14 = uVar40;
                        if ((int)uVar40 < 0) {
                          uVar14 = 0;
                        }
                        if (iVar30 <= (int)uVar14) {
                          uVar14 = iVar30 - 1;
                        }
                        pfVar29 = (float *)(local_138 + (longlong)(int)uVar14 * 4);
                      }
                    }
                    if (*pfVar29 == *pfVar27) goto LAB_180087b54;
                    bVar9 = true;
LAB_180087c38:
                    if ((*puVar8 == 0) && (*local_2a8 == 0)) {
                      *puVar8 = uVar40;
                      *local_2a8 = uVar40;
                    }
                    if (bVar9) {
                      uVar14 = FUN_180026550((longlong)local_2e8);
                      uVar15 = uVar40;
                      if (uVar14 != 0) {
                        uVar15 = *puVar8;
                      }
                      *local_2a8 = uVar15;
                    }
                    FUN_180022590((int *)local_358,(longlong)param_3,plVar26);
                    local_260 = (longlong *)0x0;
                    uStack_258 = 0;
                    param_3[0x433] = *puVar8;
                    local_218 = &local_260;
                    local_260 = (longlong *)FUN_180096150(0x120);
                    *local_260 = (longlong)local_260;
                    local_260[1] = (longlong)local_260;
                    local_2d8 = (uint *)0x0;
                    uVar15 = FUN_180026888((longlong)local_2b8);
                    param_8 = (ulonglong)uVar15;
                    uVar15 = FUN_180026888((longlong)local_2b0);
                    param_7 = (ulonglong)uVar15;
                    uVar14 = *local_2a8;
                    if ((local_160 == 0) &&
                       ((local_150 == (code *)0x0 ||
                        ((*local_150)(local_148 & 0xffffffff), local_160 == 0)))) {
                      puVar34 = (uint *)((longlong)&local_140 + 4);
                    }
                    else if ((int)local_140 == 0) {
                      puVar34 = (uint *)((longlong)&local_140 + 4);
                    }
                    else {
                      uVar15 = uVar40 & (uVar13 ^ 0xffffffff);
                      if ((int)local_140 <= (int)uVar15) {
                        uVar15 = (int)local_140 - 1;
                      }
                      puVar34 = (uint *)(local_160 + (longlong)(int)uVar15 * 4);
                    }
                    uVar15 = *puVar34;
                    if ((local_138 == 0) &&
                       ((local_128 == (code *)0x0 ||
                        ((*local_128)(local_120 & 0xffffffff), local_138 == 0)))) {
                      pfVar27 = (float *)((longlong)&local_118 + 4);
                    }
                    else if ((uint)local_118 == 0) {
                      pfVar27 = (float *)((longlong)&local_118 + 4);
                    }
                    else {
                      uVar31 = uVar40 & (uVar13 ^ 0xffffffff);
                      if ((int)(uint)local_118 <= (int)uVar31) {
                        uVar31 = (uint)local_118 - 1;
                      }
                      pfVar27 = (float *)(local_138 + (longlong)(int)uVar31 * 4);
                    }
                    uVar47 = 0;
                    uVar48 = 0;
                    param_9 = 1;
                    FUN_180023270(*pfVar27,ZEXT416(uVar15),local_358,(longlong)param_3,
                                  (ulonglong)uVar40,(ulonglong)uVar14,param_7,param_8,1,&local_2d8,0
                                  ,'\0',0,0,0,(ulonglong)in_stack_fffffffffffffc89 << 8,&local_260);
                    fVar44 = extraout_s0_40;
                    uVar51 = extraout_var_40;
                    uVar21 = extraout_var_91;
                    if (bVar9) {
                      *puVar8 = uVar40;
                      if ((local_160 == 0) &&
                         ((local_150 == (code *)0x0 ||
                          ((*local_150)(local_148 & 0xffffffff), fVar44 = extraout_s0_41,
                          uVar51 = extraout_var_41, uVar21 = extraout_var_92, local_160 == 0)))) {
                        pfVar27 = (float *)((longlong)&local_140 + 4);
                      }
                      else if ((int)local_140 == 0) {
                        pfVar27 = (float *)((longlong)&local_140 + 4);
                      }
                      else {
                        uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                        if ((int)local_140 <= (int)uVar14) {
                          uVar14 = (int)local_140 - 1;
                        }
                        pfVar27 = (float *)(local_160 + (longlong)(int)uVar14 * 4);
                      }
                      *pfVar16 = *pfVar27;
                      uVar14 = *puVar8;
                      iVar50 = (int)local_140;
                      if (local_138 == 0) {
                        if (local_128 != (code *)0x0) {
                          (*local_128)(local_120 & 0xffffffff);
                          fVar44 = extraout_s0_42;
                          uVar51 = extraout_var_42;
                          uVar21 = extraout_var_93;
                          iVar50 = (int)local_140;
                          if (local_138 != 0) goto LAB_180087e6c;
                        }
LAB_180087e54:
                        uVar22 = local_118 & 0xffffffff;
                        *pfVar17 = local_118._4_4_;
                        pcVar36 = local_150;
                        pcVar37 = local_128;
                        lVar35 = local_160;
                        lVar38 = local_138;
                      }
                      else {
LAB_180087e6c:
                        uVar22 = local_118 & 0xffffffff;
                        pcVar36 = local_150;
                        pcVar37 = local_128;
                        lVar35 = local_160;
                        lVar38 = local_138;
                        if ((uint)local_118 == 0) {
                          *pfVar17 = local_118._4_4_;
                        }
                        else {
                          uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
                          if ((int)(uint)local_118 <= (int)uVar14) {
                            uVar14 = (uint)local_118 - 1;
                          }
                          *pfVar17 = *(float *)(local_138 + (longlong)(int)uVar14 * 4);
                        }
                      }
                    }
                    else {
                      if (bVar10) {
                        uVar14 = *puVar8;
                        if ((local_160 == 0) &&
                           ((local_150 == (code *)0x0 ||
                            ((*local_150)(local_148 & 0xffffffff), fVar44 = extraout_s0_43,
                            uVar51 = extraout_var_43, uVar21 = extraout_var_94, local_160 == 0)))) {
                          pfVar27 = (float *)((longlong)&local_140 + 4);
                        }
                        else if ((int)local_140 == 0) {
                          pfVar27 = (float *)((longlong)&local_140 + 4);
                        }
                        else {
                          uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
                          if ((int)local_140 <= (int)uVar14) {
                            uVar14 = (int)local_140 - 1;
                          }
                          pfVar27 = (float *)(local_160 + (longlong)(int)uVar14 * 4);
                        }
                        *pfVar16 = *pfVar27;
                        uVar14 = *puVar8;
                        iVar50 = (int)local_140;
                        if (local_138 == 0) {
                          if (local_128 == (code *)0x0) goto LAB_180087e54;
                          (*local_128)(local_120 & 0xffffffff);
                          fVar44 = extraout_s0_44;
                          uVar51 = extraout_var_44;
                          uVar21 = extraout_var_95;
                          iVar50 = (int)local_140;
                          if (local_138 == 0) {
                            uVar22 = local_118 & 0xffffffff;
                            *pfVar17 = local_118._4_4_;
                            pcVar36 = local_150;
                            pcVar37 = local_128;
                            lVar35 = local_160;
                            lVar38 = local_138;
                            goto LAB_180087fa0;
                          }
                        }
                        goto LAB_180087e6c;
                      }
                      uVar22 = local_118 & 0xffffffff;
                      pcVar36 = local_150;
                      pcVar37 = local_128;
                      lVar35 = local_160;
                      lVar38 = local_138;
                      iVar50 = (int)local_140;
                    }
                  }
                }
LAB_180087fa0:
                if (lVar35 == 0) {
                  if (pcVar36 != (code *)0x0) {
                    (*pcVar36)(local_148 & 0xffffffff);
                    uVar22 = local_118 & 0xffffffff;
                    pcVar36 = local_150;
                    pcVar37 = local_128;
                    lVar35 = local_160;
                    lVar38 = local_138;
                    fVar44 = extraout_s0_46;
                    uVar51 = extraout_var_46;
                    uVar21 = extraout_var_97;
                    iVar50 = (int)local_140;
                    if (local_160 != 0) goto LAB_180087fd4;
                  }
                  pfVar27 = (float *)((longlong)&local_140 + 4);
                }
                else {
LAB_180087fd4:
                  if (iVar50 == 0) {
                    pfVar27 = (float *)((longlong)&local_140 + 4);
                  }
                  else {
                    uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                    if (iVar50 <= (int)uVar14) {
                      uVar14 = iVar50 - 1;
                    }
                    pfVar27 = (float *)(lVar35 + (longlong)(int)uVar14 * 4);
                  }
                }
                if (*pfVar27 != 0.0) {
                  if (lVar38 == 0) {
                    if (pcVar37 != (code *)0x0) {
                      (*pcVar37)(local_120 & 0xffffffff);
                      uVar22 = local_118 & 0xffffffff;
                      pcVar36 = local_150;
                      pcVar37 = local_128;
                      lVar35 = local_160;
                      lVar38 = local_138;
                      fVar44 = extraout_s0_47;
                      uVar51 = extraout_var_47;
                      uVar21 = extraout_var_98;
                      iVar50 = (int)local_140;
                      if (local_138 != 0) goto LAB_180088034;
                    }
                    pfVar27 = (float *)((longlong)&local_118 + 4);
                  }
                  else {
LAB_180088034:
                    iVar30 = (int)uVar22;
                    if (iVar30 == 0) {
                      pfVar27 = (float *)((longlong)&local_118 + 4);
                    }
                    else {
                      uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                      if (iVar30 <= (int)uVar14) {
                        uVar14 = iVar30 - 1;
                      }
                      pfVar27 = (float *)(lVar38 + (longlong)(int)uVar14 * 4);
                    }
                  }
                  if (*pfVar27 != 0.0) {
                    uVar14 = *puVar8;
                    if (lVar35 == 0) {
                      if (pcVar36 != (code *)0x0) {
                        (*pcVar36)(local_148 & 0xffffffff);
                        uVar22 = local_118 & 0xffffffff;
                        pcVar37 = local_128;
                        lVar38 = local_138;
                        if (local_160 != 0) {
                          lVar35 = local_160;
                          iVar50 = (int)local_140;
                          goto LAB_18008808c;
                        }
                      }
LAB_180088090:
                      pfVar27 = (float *)((longlong)&local_140 + 4);
                    }
                    else {
LAB_18008808c:
                      if (iVar50 == 0) goto LAB_180088090;
                      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
                      if (iVar50 <= (int)uVar14) {
                        uVar14 = iVar50 - 1;
                      }
                      pfVar27 = (float *)(lVar35 + (longlong)(int)uVar14 * 4);
                    }
                    uVar14 = *puVar8;
                    if (lVar38 == 0) {
                      if ((pcVar37 != (code *)0x0) &&
                         ((*pcVar37)(local_120 & 0xffffffff), local_138 != 0)) {
                        uVar22 = local_118 & 0xffffffff;
                        lVar38 = local_138;
                        goto LAB_1800880cc;
                      }
LAB_1800880d0:
                      pfVar29 = (float *)((longlong)&local_118 + 4);
                    }
                    else {
LAB_1800880cc:
                      iVar50 = (int)uVar22;
                      if (iVar50 == 0) goto LAB_1800880d0;
                      uVar14 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
                      if (iVar50 <= (int)uVar14) {
                        uVar14 = iVar50 - 1;
                      }
                      pfVar29 = (float *)(lVar38 + (longlong)(int)uVar14 * 4);
                    }
                    fVar49 = *pfVar27 - *pfVar29;
                    fVar44 = FUN_180026608((longlong)plVar33);
                    fVar52 = *pfVar16;
                    lVar35 = local_338[6];
                    if (lVar35 == 0) {
                      if ((code *)local_338[8] != (code *)0x0) {
                        (*(code *)local_338[8])((int)local_338[9]);
                      }
                      lVar35 = local_338[6];
                      if (lVar35 != 0) goto LAB_180088140;
                      pfVar27 = (float *)((longlong)local_338 + 0x54);
                    }
                    else {
LAB_180088140:
                      iVar50 = (int)local_338[10];
                      if (iVar50 == 0) {
                        pfVar27 = (float *)((longlong)local_338 + 0x54);
                      }
                      else {
                        uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                        if (iVar50 <= (int)uVar14) {
                          uVar14 = iVar50 - 1;
                        }
                        pfVar27 = (float *)(lVar35 + (longlong)(int)uVar14 * 4);
                      }
                    }
                    *pfVar27 = fVar44 * fVar49 + fVar52;
                    fVar44 = FUN_180026608((longlong)plVar33);
                    fVar52 = *pfVar17;
                    lVar35 = local_330[6];
                    if (lVar35 == 0) {
                      if ((code *)local_330[8] != (code *)0x0) {
                        (*(code *)local_330[8])((int)local_330[9]);
                      }
                      lVar35 = local_330[6];
                      if (lVar35 != 0) goto LAB_1800881b4;
                      pfVar27 = (float *)((longlong)local_330 + 0x54);
                    }
                    else {
LAB_1800881b4:
                      iVar50 = (int)local_330[10];
                      if (iVar50 == 0) {
                        pfVar27 = (float *)((longlong)local_330 + 0x54);
                      }
                      else {
                        uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                        if (iVar50 <= (int)uVar14) {
                          uVar14 = iVar50 - 1;
                        }
                        pfVar27 = (float *)(lVar35 + (longlong)(int)uVar14 * 4);
                      }
                    }
                    *pfVar27 = fVar52 - fVar44 * fVar49;
                    fVar44 = FUN_180026608((longlong)plVar33);
                    fVar52 = *pfVar16;
                    pfVar27 = (float *)FUN_18000f448((longlong)local_2a0,uVar40);
                    *pfVar27 = fVar44 * fVar49 + fVar44 * fVar49 + fVar52;
                    fVar44 = FUN_180026608((longlong)plVar33);
                    fVar52 = *pfVar17;
                    lVar35 = local_328[6];
                    if (lVar35 == 0) {
                      if ((code *)local_328[8] != (code *)0x0) {
                        (*(code *)local_328[8])((int)local_328[9]);
                      }
                      lVar35 = local_328[6];
                      if (lVar35 != 0) goto LAB_18008825c;
                      pfVar27 = (float *)((longlong)local_328 + 0x54);
                    }
                    else {
LAB_18008825c:
                      iVar50 = (int)local_328[10];
                      if (iVar50 == 0) {
                        pfVar27 = (float *)((longlong)local_328 + 0x54);
                      }
                      else {
                        uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                        if (iVar50 <= (int)uVar14) {
                          uVar14 = iVar50 - 1;
                        }
                        pfVar27 = (float *)(lVar35 + (longlong)(int)uVar14 * 4);
                      }
                    }
                    *pfVar27 = fVar52 - (fVar44 * fVar49 + fVar44 * fVar49);
                    fVar44 = FUN_180026608((longlong)plVar33);
                    fVar52 = *pfVar16;
                    pfVar27 = (float *)FUN_18000f448((longlong)local_298,uVar40);
                    *pfVar27 = fVar44 * fVar49 * 3.0 + fVar52;
                    fVar44 = FUN_180026608((longlong)plVar33);
                    fVar52 = *pfVar17;
                    pfVar27 = (float *)FUN_18000f448((longlong)local_290,uVar40);
                    *pfVar27 = fVar52 - fVar44 * fVar49 * 3.0;
                    fVar44 = FUN_180026608((longlong)plVar33);
                    fVar52 = *pfVar16;
                    pfVar27 = (float *)FUN_18000f448((longlong)local_288,uVar40);
                    *pfVar27 = fVar44 * fVar49 * 4.0 + fVar52;
                    fVar44 = FUN_180026608((longlong)plVar33);
                    lVar35 = local_320[6];
                    fVar52 = *pfVar17;
                    if (lVar35 == 0) {
                      if ((code *)local_320[8] != (code *)0x0) {
                        (*(code *)local_320[8])((int)local_320[9]);
                      }
                      lVar35 = local_320[6];
                      if (lVar35 != 0) goto LAB_180088354;
                      pfVar27 = (float *)((longlong)local_320 + 0x54);
                    }
                    else {
LAB_180088354:
                      iVar50 = (int)local_320[10];
                      if (iVar50 == 0) {
                        pfVar27 = (float *)((longlong)local_320 + 0x54);
                      }
                      else {
                        uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                        if (iVar50 <= (int)uVar14) {
                          uVar14 = iVar50 - 1;
                        }
                        pfVar27 = (float *)(lVar35 + (longlong)(int)uVar14 * 4);
                      }
                    }
                    *pfVar27 = fVar52 - fVar44 * fVar49 * 4.0;
                    fVar44 = FUN_180026608((longlong)plVar33);
                    fVar52 = *pfVar16;
                    lVar35 = local_310[6];
                    if (lVar35 == 0) {
                      if ((code *)local_310[8] != (code *)0x0) {
                        (*(code *)local_310[8])((int)local_310[9]);
                      }
                      lVar35 = local_310[6];
                      if (lVar35 != 0) goto LAB_1800883c8;
                      pfVar27 = (float *)((longlong)local_310 + 0x54);
                    }
                    else {
LAB_1800883c8:
                      iVar50 = (int)local_310[10];
                      if (iVar50 == 0) {
                        pfVar27 = (float *)((longlong)local_310 + 0x54);
                      }
                      else {
                        uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                        if (iVar50 <= (int)uVar14) {
                          uVar14 = iVar50 - 1;
                        }
                        pfVar27 = (float *)(lVar35 + (longlong)(int)uVar14 * 4);
                      }
                    }
                    *pfVar27 = fVar44 * fVar49 * 5.0 + fVar52;
                    fVar44 = FUN_180026608((longlong)plVar33);
                    fVar49 = fVar44 * fVar49;
                    fVar52 = *pfVar17;
                    lVar35 = plVar32[6];
                    uVar51 = extraout_var_48;
                    uVar21 = extraout_var_99;
                    if (lVar35 == 0) {
                      if ((code *)plVar32[8] != (code *)0x0) {
                        (*(code *)plVar32[8])((int)plVar32[9]);
                        fVar44 = extraout_s0_48;
                        uVar51 = extraout_var_49;
                        uVar21 = extraout_var_x00100;
                      }
                      lVar35 = plVar32[6];
                      if (lVar35 != 0) goto LAB_180088440;
                      pfVar27 = (float *)((longlong)plVar32 + 0x54);
                    }
                    else {
LAB_180088440:
                      iVar50 = (int)plVar32[10];
                      if (iVar50 == 0) {
                        pfVar27 = (float *)((longlong)plVar32 + 0x54);
                      }
                      else {
                        uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                        if (iVar50 <= (int)uVar14) {
                          uVar14 = iVar50 - 1;
                        }
                        pfVar27 = (float *)(lVar35 + (longlong)(int)uVar14 * 4);
                      }
                    }
                    *pfVar27 = fVar52 - fVar49 * 5.0;
                  }
                }
                local_c8 = (longlong *)0x0;
                local_c0 = 0;
                auVar4._4_4_ = uVar51;
                auVar4._0_4_ = fVar44;
                auVar4._8_8_ = uVar21;
                auVar7._4_4_ = uVar47;
                auVar7._0_4_ = uVar15;
                auVar7._8_8_ = uVar48;
                FUN_180023e50(auVar4,auVar7,(int *)local_358,(longlong)param_3,(ulonglong)uVar40,
                              (longlong)&local_c8,param_7,param_8,param_9,uVar22);
                lVar35 = *(longlong *)(param_3 + 0x2ac);
                if (lVar35 == 0) {
                  if (*(code **)(param_3 + 0x2b0) != (code *)0x0) {
                    (**(code **)(param_3 + 0x2b0))(param_3[0x2b2]);
                  }
                  lVar35 = *(longlong *)(param_3 + 0x2ac);
                  if (lVar35 != 0) goto LAB_1800884b4;
                  pfVar27 = (float *)(param_3 + 0x2b5);
                }
                else {
LAB_1800884b4:
                  iVar50 = param_3[0x2b4];
                  if (iVar50 == 0) {
                    pfVar27 = (float *)(param_3 + 0x2b5);
                  }
                  else {
                    uVar14 = uVar40 - 1 & ((int)(uVar40 - 1) >> 0x1f ^ 0xffffffffU);
                    if (iVar50 <= (int)uVar14) {
                      uVar14 = iVar50 - 1;
                    }
                    pfVar27 = (float *)(lVar35 + (longlong)(int)uVar14 * 4);
                  }
                }
                if ((local_160 == 0) &&
                   ((local_150 == (code *)0x0 ||
                    ((*local_150)(local_148 & 0xffffffff), local_160 == 0)))) {
                  pfVar29 = (float *)((longlong)&local_140 + 4);
                  iVar50 = (int)local_140;
                }
                else {
                  iVar50 = (int)local_140;
                  if ((int)local_140 == 0) {
                    pfVar29 = (float *)((longlong)&local_140 + 4);
                  }
                  else {
                    uVar14 = uVar40 - 1 & ((int)(uVar40 - 1) >> 0x1f ^ 0xffffffffU);
                    if ((int)local_140 <= (int)uVar14) {
                      uVar14 = (int)local_140 - 1;
                    }
                    pfVar29 = (float *)(local_160 + (longlong)(int)uVar14 * 4);
                  }
                }
                if (*pfVar27 <= *pfVar29) {
LAB_1800886a8:
                  if (local_138 == 0) {
                    if (local_128 != (code *)0x0) {
                      (*local_128)(local_120 & 0xffffffff);
                      iVar50 = (int)local_140;
                      if (local_138 != 0) goto LAB_1800886e8;
                    }
                    pfVar27 = (float *)((longlong)&local_118 + 4);
                  }
                  else {
LAB_1800886e8:
                    if ((uint)local_118 == 0) {
                      pfVar27 = (float *)((longlong)&local_118 + 4);
                    }
                    else {
                      uVar14 = uVar40 - 1 & ((int)(uVar40 - 1) >> 0x1f ^ 0xffffffffU);
                      if ((int)(uint)local_118 <= (int)uVar14) {
                        uVar14 = (uint)local_118 - 1;
                      }
                      pfVar27 = (float *)(local_138 + (longlong)(int)uVar14 * 4);
                    }
                  }
                  lVar35 = *(longlong *)(param_3 + 0x2ac);
                  iVar30 = (uint)local_118;
                  if (lVar35 == 0) {
                    if (*(code **)(param_3 + 0x2b0) != (code *)0x0) {
                      (**(code **)(param_3 + 0x2b0))(param_3[0x2b2]);
                      iVar50 = (int)local_140;
                      iVar30 = (uint)local_118;
                    }
                    lVar35 = *(longlong *)(param_3 + 0x2ac);
                    if (lVar35 != 0) goto LAB_180088754;
                    pfVar29 = (float *)(param_3 + 0x2b5);
                  }
                  else {
LAB_180088754:
                    iVar1 = param_3[0x2b4];
                    if (iVar1 == 0) {
                      pfVar29 = (float *)(param_3 + 0x2b5);
                    }
                    else {
                      uVar14 = uVar40 - 1 & ((int)(uVar40 - 1) >> 0x1f ^ 0xffffffffU);
                      if (iVar1 <= (int)uVar14) {
                        uVar14 = iVar1 - 1;
                      }
                      pfVar29 = (float *)(lVar35 + (longlong)(int)uVar14 * 4);
                    }
                  }
                  if (*pfVar27 <= *pfVar29) {
LAB_180088868:
                    if (local_160 == 0) {
                      if (local_150 != (code *)0x0) {
                        (*local_150)(local_148 & 0xffffffff);
                        iVar50 = (int)local_140;
                        iVar30 = (uint)local_118;
                        if (local_160 != 0) goto LAB_1800888a0;
                      }
                      pfVar27 = (float *)((longlong)&local_140 + 4);
                    }
                    else {
LAB_1800888a0:
                      if (iVar50 == 0) {
                        pfVar27 = (float *)((longlong)&local_140 + 4);
                      }
                      else {
                        uVar14 = uVar40 - 1 & ((int)(uVar40 - 1) >> 0x1f ^ 0xffffffffU);
                        if (iVar50 <= (int)uVar14) {
                          uVar14 = iVar50 - 1;
                        }
                        pfVar27 = (float *)(local_160 + (longlong)(int)uVar14 * 4);
                      }
                    }
                    lVar35 = *(longlong *)(param_3 + 0x2ac);
                    if (lVar35 == 0) {
                      if (*(code **)(param_3 + 0x2b0) != (code *)0x0) {
                        (**(code **)(param_3 + 0x2b0))(param_3[0x2b2]);
                        iVar50 = (int)local_140;
                        iVar30 = (uint)local_118;
                      }
                      lVar35 = *(longlong *)(param_3 + 0x2ac);
                      if (lVar35 != 0) goto LAB_180088904;
                      pfVar29 = (float *)(param_3 + 0x2b5);
                    }
                    else {
LAB_180088904:
                      iVar1 = param_3[0x2b4];
                      if (iVar1 == 0) {
                        pfVar29 = (float *)(param_3 + 0x2b5);
                      }
                      else {
                        uVar14 = uVar40 - 1 & ((int)(uVar40 - 1) >> 0x1f ^ 0xffffffffU);
                        if (iVar1 <= (int)uVar14) {
                          uVar14 = iVar1 - 1;
                        }
                        pfVar29 = (float *)(lVar35 + (longlong)(int)uVar14 * 4);
                      }
                    }
                    if (*pfVar29 <= *pfVar27) {
                      if (local_160 == 0) {
                        if (local_150 != (code *)0x0) {
                          (*local_150)(local_148 & 0xffffffff);
                          iVar30 = (uint)local_118;
                          if (local_160 != 0) {
                            iVar50 = (int)local_140;
                            goto LAB_180088960;
                          }
                        }
LAB_180088964:
                        pfVar27 = (float *)((longlong)&local_140 + 4);
                      }
                      else {
LAB_180088960:
                        if (iVar50 == 0) goto LAB_180088964;
                        uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                        if (iVar50 <= (int)uVar14) {
                          uVar14 = iVar50 - 1;
                        }
                        pfVar27 = (float *)(local_160 + (longlong)(int)uVar14 * 4);
                      }
                      lVar35 = *(longlong *)(param_3 + 0x2ac);
                      if (lVar35 == 0) {
                        if (*(code **)(param_3 + 0x2b0) != (code *)0x0) {
                          (**(code **)(param_3 + 0x2b0))(param_3[0x2b2]);
                          iVar30 = (uint)local_118;
                        }
                        lVar35 = *(longlong *)(param_3 + 0x2ac);
                        if (lVar35 != 0) goto LAB_1800889b4;
                        pfVar29 = (float *)(param_3 + 0x2b5);
                      }
                      else {
LAB_1800889b4:
                        iVar50 = param_3[0x2b4];
                        if (iVar50 == 0) {
                          pfVar29 = (float *)(param_3 + 0x2b5);
                        }
                        else {
                          uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                          if (iVar50 <= (int)uVar14) {
                            uVar14 = iVar50 - 1;
                          }
                          pfVar29 = (float *)(lVar35 + (longlong)(int)uVar14 * 4);
                        }
                      }
                      if (*pfVar27 < *pfVar29) {
                        puVar28 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x2ac),uVar40);
                        uVar51 = *puVar28;
                        plVar26 = local_278;
                        goto LAB_180088b24;
                      }
                    }
                    if (local_138 == 0) {
                      if (local_128 != (code *)0x0) {
                        (*local_128)(local_120 & 0xffffffff);
                        iVar30 = (uint)local_118;
                        if (local_138 != 0) goto LAB_180088a2c;
                      }
                      pfVar27 = (float *)((longlong)&local_118 + 4);
                    }
                    else {
LAB_180088a2c:
                      if (iVar30 == 0) {
                        pfVar27 = (float *)((longlong)&local_118 + 4);
                      }
                      else {
                        uVar14 = uVar40 - 1 & ((int)(uVar40 - 1) >> 0x1f ^ 0xffffffffU);
                        if (iVar30 <= (int)uVar14) {
                          uVar14 = iVar30 - 1;
                        }
                        pfVar27 = (float *)(local_138 + (longlong)(int)uVar14 * 4);
                      }
                    }
                    lVar35 = *(longlong *)(param_3 + 0x2ac);
                    if (lVar35 == 0) {
                      if (*(code **)(param_3 + 0x2b0) != (code *)0x0) {
                        (**(code **)(param_3 + 0x2b0))(param_3[0x2b2]);
                        iVar30 = (uint)local_118;
                      }
                      lVar35 = *(longlong *)(param_3 + 0x2ac);
                      if (lVar35 != 0) goto LAB_180088a84;
                      pfVar29 = (float *)(param_3 + 0x2b5);
                    }
                    else {
LAB_180088a84:
                      iVar50 = param_3[0x2b4];
                      if (iVar50 == 0) {
                        pfVar29 = (float *)(param_3 + 0x2b5);
                      }
                      else {
                        uVar14 = uVar40 - 1 & ((int)(uVar40 - 1) >> 0x1f ^ 0xffffffffU);
                        if (iVar50 <= (int)uVar14) {
                          uVar14 = iVar50 - 1;
                        }
                        pfVar29 = (float *)(lVar35 + (longlong)(int)uVar14 * 4);
                      }
                    }
                    if (*pfVar29 < *pfVar27) goto LAB_180088b30;
                    if (local_138 == 0) {
                      if ((local_128 != (code *)0x0) &&
                         ((*local_128)(local_120 & 0xffffffff), local_138 != 0)) {
                        iVar30 = (uint)local_118;
                        goto LAB_180088ad4;
                      }
LAB_180088ad8:
                      pfVar27 = (float *)((longlong)&local_118 + 4);
                    }
                    else {
LAB_180088ad4:
                      if (iVar30 == 0) goto LAB_180088ad8;
                      uVar13 = uVar40 & (uVar13 ^ 0xffffffff);
                      if (iVar30 <= (int)uVar13) {
                        uVar13 = iVar30 - 1;
                      }
                      pfVar27 = (float *)(local_138 + (longlong)(int)uVar13 * 4);
                    }
                    pfVar29 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2ac),uVar40);
                    if (*pfVar27 <= *pfVar29) goto LAB_180088b30;
                    puVar28 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x2ac),uVar40);
                    uVar51 = *puVar28;
                    plVar26 = local_270;
                  }
                  else {
                    if (local_138 == 0) {
                      if (local_128 != (code *)0x0) {
                        (*local_128)(local_120 & 0xffffffff);
                        iVar50 = (int)local_140;
                        iVar30 = (uint)local_118;
                        if (local_138 != 0) goto LAB_1800887bc;
                      }
                      pfVar27 = (float *)((longlong)&local_118 + 4);
                    }
                    else {
LAB_1800887bc:
                      if (iVar30 == 0) {
                        pfVar27 = (float *)((longlong)&local_118 + 4);
                      }
                      else {
                        uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                        if (iVar30 <= (int)uVar14) {
                          uVar14 = iVar30 - 1;
                        }
                        pfVar27 = (float *)(local_138 + (longlong)(int)uVar14 * 4);
                      }
                    }
                    lVar35 = *(longlong *)(param_3 + 0x2ac);
                    if (lVar35 == 0) {
                      if (*(code **)(param_3 + 0x2b0) != (code *)0x0) {
                        (**(code **)(param_3 + 0x2b0))(param_3[0x2b2]);
                        iVar50 = (int)local_140;
                        iVar30 = (uint)local_118;
                      }
                      lVar35 = *(longlong *)(param_3 + 0x2ac);
                      if (lVar35 != 0) goto LAB_18008881c;
                      pfVar29 = (float *)(param_3 + 0x2b5);
                    }
                    else {
LAB_18008881c:
                      iVar1 = param_3[0x2b4];
                      if (iVar1 == 0) {
                        pfVar29 = (float *)(param_3 + 0x2b5);
                      }
                      else {
                        uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                        if (iVar1 <= (int)uVar14) {
                          uVar14 = iVar1 - 1;
                        }
                        pfVar29 = (float *)(lVar35 + (longlong)(int)uVar14 * 4);
                      }
                    }
                    if (*pfVar29 < *pfVar27) goto LAB_180088868;
                    puVar28 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x2ac),uVar40);
                    uVar51 = *puVar28;
                    plVar26 = local_280;
                  }
LAB_180088b24:
                  puVar28 = (undefined4 *)FUN_18000f448((longlong)plVar26,uVar40);
                  *puVar28 = uVar51;
                }
                else {
                  if (local_160 == 0) {
                    if (local_150 != (code *)0x0) {
                      (*local_150)(local_148 & 0xffffffff);
                      iVar50 = (int)local_140;
                      if (local_160 != 0) goto LAB_18008856c;
                    }
                    pfVar27 = (float *)((longlong)&local_140 + 4);
                  }
                  else {
LAB_18008856c:
                    if (iVar50 == 0) {
                      pfVar27 = (float *)((longlong)&local_140 + 4);
                    }
                    else {
                      uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                      if (iVar50 <= (int)uVar14) {
                        uVar14 = iVar50 - 1;
                      }
                      pfVar27 = (float *)(local_160 + (longlong)(int)uVar14 * 4);
                    }
                  }
                  lVar35 = *(longlong *)(param_3 + 0x2ac);
                  if (lVar35 == 0) {
                    if (*(code **)(param_3 + 0x2b0) != (code *)0x0) {
                      (**(code **)(param_3 + 0x2b0))(param_3[0x2b2]);
                      iVar50 = (int)local_140;
                    }
                    lVar35 = *(longlong *)(param_3 + 0x2ac);
                    if (lVar35 != 0) goto LAB_1800885c0;
                    pfVar29 = (float *)(param_3 + 0x2b5);
                  }
                  else {
LAB_1800885c0:
                    iVar30 = param_3[0x2b4];
                    if (iVar30 == 0) {
                      pfVar29 = (float *)(param_3 + 0x2b5);
                    }
                    else {
                      uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                      if (iVar30 <= (int)uVar14) {
                        uVar14 = iVar30 - 1;
                      }
                      pfVar29 = (float *)(lVar35 + (longlong)(int)uVar14 * 4);
                    }
                  }
                  if (*pfVar27 < *pfVar29) goto LAB_1800886a8;
                  if (lVar35 == 0) {
                    if (*(code **)(param_3 + 0x2b0) != (code *)0x0) {
                      (**(code **)(param_3 + 0x2b0))(param_3[0x2b2]);
                    }
                    lVar35 = *(longlong *)(param_3 + 0x2ac);
                    if (lVar35 != 0) goto LAB_180088618;
                    piVar42 = param_3 + 0x2b5;
                  }
                  else {
LAB_180088618:
                    iVar50 = param_3[0x2b4];
                    if (iVar50 == 0) {
                      piVar42 = param_3 + 0x2b5;
                    }
                    else {
                      uVar14 = uVar40 & (uVar13 ^ 0xffffffff);
                      if (iVar50 <= (int)uVar14) {
                        uVar14 = iVar50 - 1;
                      }
                      piVar42 = (int *)(lVar35 + (longlong)(int)uVar14 * 4);
                    }
                  }
                  iVar50 = *piVar42;
                  lVar35 = local_300[6];
                  if (lVar35 == 0) {
                    if ((code *)local_300[8] != (code *)0x0) {
                      (*(code *)local_300[8])((int)local_300[9]);
                    }
                    lVar35 = local_300[6];
                    if (lVar35 != 0) goto LAB_180088674;
                    *(int *)((longlong)local_300 + 0x54) = iVar50;
                  }
                  else {
LAB_180088674:
                    iVar30 = (int)local_300[10];
                    if (iVar30 == 0) {
                      *(int *)((longlong)local_300 + 0x54) = iVar50;
                    }
                    else {
                      uVar13 = uVar40 & (uVar13 ^ 0xffffffff);
                      if (iVar30 <= (int)uVar13) {
                        uVar13 = iVar30 - 1;
                      }
                      *(int *)(lVar35 + (longlong)(int)uVar13 * 4) = iVar50;
                    }
                  }
                }
LAB_180088b30:
                uVar40 = uVar40 + 1;
              } while ((int)uVar40 < *param_3);
            }
            *local_228 = (uint)(iVar12 != 0);
          }
        }
      }
      else if (local_358 != (uint *)0x0) {
        FUN_180022590((int *)local_358,(longlong)param_3,param_5);
        FUN_18000bcc0(local_358);
        (**(code **)(param_3 + 0x448))(2,0);
      }
    }
  }
  else {
    FUN_1800079f8((longlong *)(param_3 + 0x46),0x1800db528,0x10);
    param_3[0x49] = 1;
    if ((*(char **)(param_3 + 0xce) != (char *)0x0) && (**(char **)(param_3 + 0xce) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0xce),0x1800d4ecd,0);
      param_3[0xd1] = 1;
    }
    local_110 = "";
    uStack_108 = 0;
    local_100 = &DAT_1800d4ecd;
    pvVar19 = GetProcessHeap();
    pcVar20 = (char *)HeapAlloc(pvVar19,0,0x15);
    if (pcVar20 == (char *)0x0) {
      local_110 = "";
      pcVar39 = local_348;
    }
    else {
      param_6 = 0x14;
      pcVar20[8] = '\0';
      pcVar20[9] = '\0';
      pcVar20[10] = '\0';
      pcVar20[0xb] = '\0';
      pcVar20[0xc] = '\0';
      pcVar20[0xd] = '\0';
      pcVar20[0xe] = '\0';
      pcVar20[0xf] = '\0';
      pcVar20[0] = '\0';
      pcVar20[1] = '\0';
      pcVar20[2] = '\0';
      pcVar20[3] = '\0';
      pcVar20[4] = '\0';
      pcVar20[5] = '\0';
      pcVar20[6] = '\0';
      pcVar20[7] = '\0';
      pcVar20[0x10] = '\0';
      pcVar20[0x11] = '\0';
      pcVar20[0x12] = '\0';
      pcVar20[0x13] = '\0';
      pcVar20[0x14] = '\0';
      local_110 = pcVar20;
      FUN_180099d78(pcVar20,0x15,0x1800d6c40,0x14);
      uStack_108 = 0x100000001;
      pcVar39 = pcVar20;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_110);
    if ((pcVar20 != (char *)0x0) && (pcVar39 != (char *)0x0)) {
      pvVar19 = GetProcessHeap();
      HeapFree(pvVar19,0,pcVar39);
    }
    local_110 = "";
    uStack_108 = 0;
    local_100 = &DAT_1800d4ecd;
    pvVar19 = GetProcessHeap();
    pcVar20 = (char *)HeapAlloc(pvVar19,0,0x8d);
    if (pcVar20 == (char *)0x0) {
      local_110 = "";
    }
    else {
      param_6 = 0x8c;
      pcVar20[8] = '\0';
      pcVar20[9] = '\0';
      pcVar20[10] = '\0';
      pcVar20[0xb] = '\0';
      pcVar20[0xc] = '\0';
      pcVar20[0xd] = '\0';
      pcVar20[0xe] = '\0';
      pcVar20[0xf] = '\0';
      pcVar20[0] = '\0';
      pcVar20[1] = '\0';
      pcVar20[2] = '\0';
      pcVar20[3] = '\0';
      pcVar20[4] = '\0';
      pcVar20[5] = '\0';
      pcVar20[6] = '\0';
      pcVar20[7] = '\0';
      pcVar20[0x18] = '\0';
      pcVar20[0x19] = '\0';
      pcVar20[0x1a] = '\0';
      pcVar20[0x1b] = '\0';
      pcVar20[0x1c] = '\0';
      pcVar20[0x1d] = '\0';
      pcVar20[0x1e] = '\0';
      pcVar20[0x1f] = '\0';
      pcVar20[0x10] = '\0';
      pcVar20[0x11] = '\0';
      pcVar20[0x12] = '\0';
      pcVar20[0x13] = '\0';
      pcVar20[0x14] = '\0';
      pcVar20[0x15] = '\0';
      pcVar20[0x16] = '\0';
      pcVar20[0x17] = '\0';
      pcVar20[0x28] = '\0';
      pcVar20[0x29] = '\0';
      pcVar20[0x2a] = '\0';
      pcVar20[0x2b] = '\0';
      pcVar20[0x2c] = '\0';
      pcVar20[0x2d] = '\0';
      pcVar20[0x2e] = '\0';
      pcVar20[0x2f] = '\0';
      pcVar20[0x20] = '\0';
      pcVar20[0x21] = '\0';
      pcVar20[0x22] = '\0';
      pcVar20[0x23] = '\0';
      pcVar20[0x24] = '\0';
      pcVar20[0x25] = '\0';
      pcVar20[0x26] = '\0';
      pcVar20[0x27] = '\0';
      pcVar20[0x38] = '\0';
      pcVar20[0x39] = '\0';
      pcVar20[0x3a] = '\0';
      pcVar20[0x3b] = '\0';
      pcVar20[0x3c] = '\0';
      pcVar20[0x3d] = '\0';
      pcVar20[0x3e] = '\0';
      pcVar20[0x3f] = '\0';
      pcVar20[0x30] = '\0';
      pcVar20[0x31] = '\0';
      pcVar20[0x32] = '\0';
      pcVar20[0x33] = '\0';
      pcVar20[0x34] = '\0';
      pcVar20[0x35] = '\0';
      pcVar20[0x36] = '\0';
      pcVar20[0x37] = '\0';
      pcVar20[0x48] = '\0';
      pcVar20[0x49] = '\0';
      pcVar20[0x4a] = '\0';
      pcVar20[0x4b] = '\0';
      pcVar20[0x4c] = '\0';
      pcVar20[0x4d] = '\0';
      pcVar20[0x4e] = '\0';
      pcVar20[0x4f] = '\0';
      pcVar20[0x40] = '\0';
      pcVar20[0x41] = '\0';
      pcVar20[0x42] = '\0';
      pcVar20[0x43] = '\0';
      pcVar20[0x44] = '\0';
      pcVar20[0x45] = '\0';
      pcVar20[0x46] = '\0';
      pcVar20[0x47] = '\0';
      pcVar20[0x58] = '\0';
      pcVar20[0x59] = '\0';
      pcVar20[0x5a] = '\0';
      pcVar20[0x5b] = '\0';
      pcVar20[0x5c] = '\0';
      pcVar20[0x5d] = '\0';
      pcVar20[0x5e] = '\0';
      pcVar20[0x5f] = '\0';
      pcVar20[0x50] = '\0';
      pcVar20[0x51] = '\0';
      pcVar20[0x52] = '\0';
      pcVar20[0x53] = '\0';
      pcVar20[0x54] = '\0';
      pcVar20[0x55] = '\0';
      pcVar20[0x56] = '\0';
      pcVar20[0x57] = '\0';
      pcVar20[0x68] = '\0';
      pcVar20[0x69] = '\0';
      pcVar20[0x6a] = '\0';
      pcVar20[0x6b] = '\0';
      pcVar20[0x6c] = '\0';
      pcVar20[0x6d] = '\0';
      pcVar20[0x6e] = '\0';
      pcVar20[0x6f] = '\0';
      pcVar20[0x60] = '\0';
      pcVar20[0x61] = '\0';
      pcVar20[0x62] = '\0';
      pcVar20[99] = '\0';
      pcVar20[100] = '\0';
      pcVar20[0x65] = '\0';
      pcVar20[0x66] = '\0';
      pcVar20[0x67] = '\0';
      pcVar20[0x78] = '\0';
      pcVar20[0x79] = '\0';
      pcVar20[0x7a] = '\0';
      pcVar20[0x7b] = '\0';
      pcVar20[0x7c] = '\0';
      pcVar20[0x7d] = '\0';
      pcVar20[0x7e] = '\0';
      pcVar20[0x7f] = '\0';
      pcVar20[0x70] = '\0';
      pcVar20[0x71] = '\0';
      pcVar20[0x72] = '\0';
      pcVar20[0x73] = '\0';
      pcVar20[0x74] = '\0';
      pcVar20[0x75] = '\0';
      pcVar20[0x76] = '\0';
      pcVar20[0x77] = '\0';
      pcVar20[0x80] = '\0';
      pcVar20[0x81] = '\0';
      pcVar20[0x82] = '\0';
      pcVar20[0x83] = '\0';
      pcVar20[0x84] = '\0';
      pcVar20[0x85] = '\0';
      pcVar20[0x86] = '\0';
      pcVar20[0x87] = '\0';
      pcVar20[0x88] = '\0';
      pcVar20[0x89] = '\0';
      pcVar20[0x8a] = '\0';
      pcVar20[0x8b] = '\0';
      pcVar20[0x8c] = '\0';
      local_110 = pcVar20;
      FUN_180099d78(pcVar20,0x8d,0x1800d6bb0,0x8c);
      uStack_108 = 0x100000001;
      local_348 = pcVar20;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_110);
    uVar51 = extraout_s0_28;
    uVar45 = extraout_var_28;
    uVar21 = extraout_var_79;
    if ((pcVar20 != (char *)0x0) && (local_348 != (char *)0x0)) {
      pvVar19 = GetProcessHeap();
      HeapFree(pvVar19,0,local_348);
      local_110 = (char *)0x0;
      uStack_108 = 0;
      uVar51 = extraout_s0_29;
      uVar45 = extraout_var_29;
      uVar21 = extraout_var_80;
    }
    pcVar39 = "";
    pcVar20 = *(char **)(param_3 + 0x46);
    if (*(char **)(param_3 + 0x46) == (char *)0x0) {
      pcVar20 = pcVar39;
    }
    auVar5._4_4_ = uVar45;
    auVar5._0_4_ = uVar51;
    auVar5._8_8_ = uVar21;
    FUN_180026368(auVar5,CONCAT44(uVar47,uVar15),(undefined8 *)(param_3 + 0xce),0x1800d6c60,pcVar20,
                  param_6,param_7,param_8,param_9,param_10);
    local_110 = "";
    uStack_108 = 0;
    local_100 = &DAT_1800d4ecd;
    pvVar19 = GetProcessHeap();
    pcVar20 = (char *)HeapAlloc(pvVar19,0,7);
    if (pcVar20 == (char *)0x0) {
      local_110 = "";
    }
    else {
      pcVar20[0] = '\0';
      pcVar20[1] = '\0';
      pcVar20[2] = '\0';
      pcVar20[3] = '\0';
      pcVar20[4] = '\0';
      pcVar20[5] = '\0';
      param_6 = 6;
      pcVar20[6] = '\0';
      local_110 = pcVar20;
      FUN_180099d78(pcVar20,7,0x1800d6c58,6);
      uStack_108 = 0x100000001;
      pcVar39 = pcVar20;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_110);
    if ((pcVar20 != (char *)0x0) && (pcVar39 != (char *)0x0)) {
      pvVar19 = GetProcessHeap();
      HeapFree(pvVar19,0,pcVar39);
      local_110 = (char *)0x0;
      uStack_108 = 0;
    }
    param_3[0xdf] = 0;
    param_3[4] = 0;
    FUN_1800079f8(local_300,0x1800db540,0x13);
    *(int *)((longlong)local_190 + 0xc) = 1;
    *(undefined2 *)((longlong)local_190 + 0x24) = 5;
    *(undefined2 *)(local_190 + 5) = 6;
    *(int *)(local_190 + 3) = 0x221f0;
    *(int *)(local_190 + 0x1b) = 0;
    FUN_1800079f8(local_280,0x1800db578,0x13);
    *(int *)((longlong)local_208 + 0xc) = 1;
    *(undefined2 *)((longlong)local_208 + 0x24) = 5;
    *(undefined2 *)(local_208 + 5) = 6;
    *(int *)(local_208 + 3) = 0xf0a60d;
    *(int *)(local_208 + 0x1b) = 0;
    FUN_1800079f8(local_278,0x1800db590,0xf);
    *(int *)((longlong)local_200 + 0xc) = 1;
    *(undefined2 *)((longlong)local_200 + 0x24) = 5;
    *(undefined2 *)(local_200 + 5) = 6;
    *(int *)(local_200 + 3) = 0xf0a60d;
    *(int *)(local_200 + 0x1b) = 0;
    FUN_1800079f8(local_270,0x1800db558,0xf);
    *(int *)((longlong)local_1f8 + 0xc) = 1;
    *(undefined2 *)((longlong)local_1f8 + 0x24) = 5;
    *(undefined2 *)(local_1f8 + 5) = 6;
    *(int *)(local_1f8 + 3) = 0x221f0;
    *(int *)(local_1f8 + 0x1b) = 0;
    FUN_1800079f8(local_338,0x1800d7d98,2);
    *(int *)((longlong)local_1f0 + 0xc) = 1;
    *(undefined2 *)((longlong)local_1f0 + 0x24) = 3;
    *(int *)(local_1f0 + 3) = 0xc0c0c0;
    *(undefined2 *)(local_1f0 + 5) = 1;
    *(int *)(local_1f0 + 0x1c) = 0x84121;
    FUN_1800079f8(local_330,0x1800d7d94,2);
    *(int *)((longlong)local_1e8 + 0xc) = 1;
    *(undefined2 *)((longlong)local_1e8 + 0x24) = 3;
    *(int *)(local_1e8 + 3) = 0xc0c0c0;
    *(undefined2 *)(local_1e8 + 5) = 1;
    *(int *)(local_1e8 + 0x1c) = 0x84121;
    FUN_1800079f8(local_2a0,0x1800d7da0,2);
    *(int *)((longlong)local_1e0 + 0xc) = 1;
    *(undefined2 *)((longlong)local_1e0 + 0x24) = 3;
    *(int *)(local_1e0 + 3) = 0xc0c0c0;
    *(undefined2 *)(local_1e0 + 5) = 1;
    *(int *)(local_1e0 + 0x1c) = 0x84121;
    FUN_1800079f8(local_328,0x1800d7d9c,2);
    *(int *)((longlong)local_1d8 + 0xc) = 1;
    *(undefined2 *)((longlong)local_1d8 + 0x24) = 3;
    *(int *)(local_1d8 + 3) = 0xc0c0c0;
    *(undefined2 *)(local_1d8 + 5) = 1;
    *(int *)(local_1d8 + 0x1c) = 0x84121;
    FUN_1800079f8(local_298,0x1800d7da8,2);
    *(int *)((longlong)local_1d0 + 0xc) = 1;
    *(undefined2 *)((longlong)local_1d0 + 0x24) = 3;
    *(int *)(local_1d0 + 3) = 0xc0c0c0;
    *(undefined2 *)(local_1d0 + 5) = 1;
    *(int *)(local_1d0 + 0x1c) = 0x84121;
    FUN_1800079f8(local_290,0x1800d7da4,2);
    *(int *)((longlong)local_1c8 + 0xc) = 1;
    *(undefined2 *)((longlong)local_1c8 + 0x24) = 3;
    *(int *)(local_1c8 + 3) = 0xc0c0c0;
    *(undefined2 *)(local_1c8 + 5) = 1;
    *(int *)(local_1c8 + 0x1c) = 0x84121;
    FUN_1800079f8(local_288,0x1800d7db0,2);
    *(int *)((longlong)local_1c0 + 0xc) = 1;
    *(undefined2 *)((longlong)local_1c0 + 0x24) = 3;
    *(int *)(local_1c0 + 3) = 0xc0c0c0;
    *(undefined2 *)(local_1c0 + 5) = 1;
    *(int *)(local_1c0 + 0x1c) = 0x84121;
    FUN_1800079f8(local_320,0x1800d7dac,2);
    *(int *)((longlong)local_1b8 + 0xc) = 1;
    *(undefined2 *)((longlong)local_1b8 + 0x24) = 3;
    *(int *)(local_1b8 + 3) = 0xc0c0c0;
    *(undefined2 *)(local_1b8 + 5) = 1;
    *(int *)(local_1b8 + 0x1c) = 0x84121;
    FUN_1800079f8(local_310,0x1800d7db8,2);
    *(int *)((longlong)local_1b0 + 0xc) = 1;
    *(undefined2 *)((longlong)local_1b0 + 0x24) = 3;
    *(int *)(local_1b0 + 3) = 0xc0c0c0;
    *(undefined2 *)(local_1b0 + 5) = 1;
    *(int *)(local_1b0 + 0x1c) = 0x84121;
    FUN_1800079f8(plVar32,0x1800d7db4,2);
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined2 *)((longlong)plVar32 + 0x24) = 3;
    *(undefined4 *)(plVar32 + 3) = 0xc0c0c0;
    *(undefined2 *)(plVar32 + 5) = 1;
    *(undefined4 *)(plVar32 + 0x1c) = 0x84121;
    FUN_1800079f8(local_2f8,0x1800db568,0xd);
    *(int *)((longlong)local_210 + 0xc) = 1;
    local_210[4] = 0x100000000;
    *(undefined1 *)(local_210 + 3) = 0x14;
    FUN_1800079f8(local_2f0,0x1800db5d0,0xc);
    *(int *)((longlong)local_230 + 0xc) = 1;
    local_230[4] = 0x200000000;
    *(undefined1 *)(local_230 + 3) = 0x14;
    FUN_1800079f8(local_2b0,0x1800db5e0,10);
    *(int *)((longlong)local_1a8 + 0xc) = 1;
    *(undefined1 *)(local_1a8 + 3) = 0xe;
    *(int *)((longlong)local_1a8 + 0x1c) = 0xc0c0c0;
    FUN_1800079f8(local_2b8,0x1800db5a0,0x11);
    *(int *)((longlong)local_1a0 + 0xc) = 1;
    *(undefined1 *)(local_1a0 + 3) = 0xe;
    *(int *)((longlong)local_1a0 + 0x1c) = 0x808080;
    local_100 = &DAT_1800d4ecd;
    local_110 = "";
    uStack_108 = 0;
    auVar6._4_4_ = extraout_var_30;
    auVar6._0_4_ = extraout_s0_30;
    auVar6._8_8_ = extraout_var_81;
    FUN_180006050(auVar6,CONCAT44(uVar47,uVar15),&local_110,0x1800d8410,1,param_6,param_7,param_8,
                  param_9,param_10);
    *(undefined1 *)(local_198 + 6) = 0xb;
    local_198[7] = 1;
    local_198[0xb] = 0;
    local_198[0xf] = 1;
    FUN_1800079f8(local_2c0,0x1800d83f8,0x11);
    *(int *)((longlong)local_260 + 0xc) = 1;
    *(undefined1 *)(local_260 + 3) = 0xb;
    *(int *)((longlong)local_260 + 0x1c) = 1;
    FUN_1800079f8(local_2c8,0x1800d8448,0x10);
    *(int *)((longlong)local_c8 + 0xc) = 1;
    *(undefined1 *)(local_c8 + 3) = 5;
    *(int *)((longlong)local_c8 + 0x1c) = 0;
    FUN_1800079f8(local_2d0,0x1800d8430,0x13);
    *(int *)((longlong)local_220 + 0xc) = 1;
    puVar18 = FUN_180029680((longlong *)&local_d0,&local_260);
    plVar32 = local_220;
    if (0xf < (ulonglong)puVar18[3]) {
      puVar18 = (undefined8 *)*puVar18;
    }
    if ((code *)local_220[10] != (code *)0x0) {
      (*(code *)local_220[10])(*(int *)((longlong)local_220 + 0x4c),puVar18);
      *(undefined1 *)(plVar32 + 3) = 0x16;
    }
    if (0xf < local_248) {
      FUN_1800966b8(local_260);
    }
    local_260 = (longlong *)((ulonglong)local_260 & 0xffffffffffffff00);
    local_250 = 0;
    local_248 = 0xf;
    *(undefined1 *)(local_220 + 3) = 0x16;
    *(int *)((longlong)local_220 + 0x1c) = 1;
    FUN_1800079f8((longlong *)local_268,0x1800d7210,0x12);
    *(undefined4 *)((longlong)local_218 + 0xc) = 1;
    *(undefined1 *)(local_218 + 3) = 0xb;
    *(undefined4 *)((longlong)local_218 + 0x1c) = 0x5a;
    *(undefined4 *)((longlong)local_218 + 0x3c) = 100;
    *(undefined4 *)((longlong)local_218 + 0x2c) = 0;
    FUN_1800079f8(plVar26,0x1800d8460,0x19);
    *(undefined4 *)((longlong)plVar26 + 0xc) = 1;
    *(undefined4 *)((longlong)plVar26 + 0x1c) = 1;
    *(undefined1 *)(plVar26 + 3) = 5;
    FUN_1800079f8(plVar43,0x1800d73a0,0xc);
    *(undefined4 *)((longlong)plVar43 + 0xc) = 1;
    *(undefined4 *)((longlong)plVar43 + 0x1c) = 0;
    *(undefined1 *)(plVar43 + 3) = 5;
    FUN_1800079f8(plVar41,0x1800d7388,0x11);
    *(undefined4 *)((longlong)plVar41 + 0xc) = 1;
    puVar18 = FUN_180029680((longlong *)&local_b8,&local_260);
    if (0xf < (ulonglong)puVar18[3]) {
      puVar18 = (undefined8 *)*puVar18;
    }
    if ((code *)plVar41[10] != (code *)0x0) {
      (*(code *)plVar41[10])(*(undefined4 *)((longlong)plVar41 + 0x4c),puVar18);
      *(undefined1 *)(plVar41 + 3) = 0x16;
    }
    if (0xf < local_248) {
      FUN_1800966b8(local_260);
    }
    local_250 = 0;
    local_248 = 0xf;
    local_260 = (longlong *)((ulonglong)local_260 & 0xffffffffffffff00);
    *(undefined1 *)(plVar41 + 3) = 0x16;
    *(undefined4 *)((longlong)plVar41 + 0x1c) = 0;
    FUN_1800079f8((longlong *)local_2e8,0x1800db5b8,0x16);
    puVar8 = local_e0;
    local_e0[3] = 1;
    if (*(code **)(local_e0 + 0x14) != (code *)0x0) {
      (**(code **)(local_e0 + 0x14))(local_e0[0x13],"At Current Change;At Previous Change");
      *(undefined1 *)(puVar8 + 6) = 0x16;
    }
    FUN_1800079f8(plVar33,0x1800db618,0x1a);
    pcVar20 = local_110;
    *(undefined4 *)((longlong)plVar33 + 0xc) = 1;
    *(undefined4 *)((longlong)plVar33 + 0x1c) = 0x3f800000;
    *(undefined1 *)(plVar33 + 3) = 2;
    *(undefined4 *)((longlong)plVar33 + 0x2c) = 0;
    *(undefined4 *)((longlong)plVar33 + 0x3c) = 0x41200000;
    if (((int)uStack_108 != 0) && (local_110 != (char *)0x0)) {
      pvVar19 = GetProcessHeap();
      HeapFree(pvVar19,0,pcVar20);
    }
  }
  return;
}

