// OrderflowLabs.com Complete Study Suite - Reverse Engineered
// Based on decompiled analysis of OrderflowLabs ARM64 DLL

#include "sierrachart.h"

// DLL Information
SCDLLName("OrderflowLabs Reverse Engineered Suite")

// Include all individual study files
#include "ExhaustionAbsorptionDetector.cpp"
#include "DeltaDominanceDetector.cpp" 
#include "DeltaMap.cpp"
#include "MGI_MarketGeneratedInformation.cpp"
#include "ATR_Ranges.cpp"
#include "ReconstructedTape.cpp"
#include "BuySellZones.cpp"

/*==========================================================================*/
// Additional studies based on decompiled analysis

SCSFExport scsf_LiquidityZones(SCStudyInterfaceRef sc)
{
    SCSubgraphRef Subgraph_LiquidityHigh = sc.Subgraph[0];
    SCSubgraphRef Subgraph_LiquidityLow = sc.Subgraph[1];
    SCSubgraphRef Subgraph_LiquidityStrength = sc.Subgraph[2];
    
    SCInputRef Input_LookbackPeriod = sc.Input[0];
    SCInputRef Input_VolumeThreshold = sc.Input[1];
    
    if (sc.SetDefaults)
    {
        sc.GraphName = "Liquidity Zones";
        sc.StudyDescription = "Identifies areas of high liquidity concentration";
        sc.AutoLoop = 1;
        sc.GraphRegion = 0;
        
        Subgraph_LiquidityHigh.Name = "Liquidity High";
        Subgraph_LiquidityHigh.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_LiquidityHigh.PrimaryColor = RGB(0, 255, 255);
        Subgraph_LiquidityHigh.LineWidth = 2;
        
        Subgraph_LiquidityLow.Name = "Liquidity Low";
        Subgraph_LiquidityLow.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_LiquidityLow.PrimaryColor = RGB(255, 0, 255);
        Subgraph_LiquidityLow.LineWidth = 2;
        
        Input_LookbackPeriod.Name = "Lookback Period";
        Input_LookbackPeriod.SetInt(20);
        Input_LookbackPeriod.SetIntLimits(5, 100);
        
        Input_VolumeThreshold.Name = "Volume Threshold";
        Input_VolumeThreshold.SetInt(1000);
        
        return;
    }
    
    if (sc.Index < Input_LookbackPeriod.GetInt())
        return;
        
    // Find highest and lowest volume areas in lookback period
    float MaxVolume = 0;
    float MinVolume = FLT_MAX;
    int MaxVolumeIndex = sc.Index;
    int MinVolumeIndex = sc.Index;
    
    for (int i = 0; i < Input_LookbackPeriod.GetInt(); i++)
    {
        int LookbackIndex = sc.Index - i;
        if (LookbackIndex >= 0)
        {
            float Volume = sc.Volume[LookbackIndex];
            if (Volume > MaxVolume)
            {
                MaxVolume = Volume;
                MaxVolumeIndex = LookbackIndex;
            }
            if (Volume < MinVolume && Volume > 0)
            {
                MinVolume = Volume;
                MinVolumeIndex = LookbackIndex;
            }
        }
    }
    
    if (MaxVolume > Input_VolumeThreshold.GetInt())
    {
        Subgraph_LiquidityHigh[sc.Index] = sc.High[MaxVolumeIndex];
        Subgraph_LiquidityLow[sc.Index] = sc.Low[MaxVolumeIndex];
        Subgraph_LiquidityStrength[sc.Index] = MaxVolume;
    }
}

/*==========================================================================*/
SCSFExport scsf_MarketEfficiency(SCStudyInterfaceRef sc)
{
    SCSubgraphRef Subgraph_Efficiency = sc.Subgraph[0];
    SCSubgraphRef Subgraph_EfficiencyMA = sc.Subgraph[1];
    
    SCInputRef Input_Period = sc.Input[0];
    SCInputRef Input_MALength = sc.Input[1];
    
    if (sc.SetDefaults)
    {
        sc.GraphName = "Market Efficiency";
        sc.StudyDescription = "Measures market efficiency based on price movement vs distance traveled";
        sc.AutoLoop = 1;
        sc.GraphRegion = 1;
        
        Subgraph_Efficiency.Name = "Efficiency Ratio";
        Subgraph_Efficiency.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_Efficiency.PrimaryColor = RGB(0, 255, 0);
        
        Subgraph_EfficiencyMA.Name = "Efficiency MA";
        Subgraph_EfficiencyMA.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_EfficiencyMA.PrimaryColor = RGB(255, 0, 0);
        
        Input_Period.Name = "Calculation Period";
        Input_Period.SetInt(14);
        
        Input_MALength.Name = "Moving Average Length";
        Input_MALength.SetInt(5);
        
        return;
    }
    
    if (sc.Index < Input_Period.GetInt())
        return;
        
    // Calculate efficiency ratio
    float NetChange = abs(sc.Close[sc.Index] - sc.Close[sc.Index - Input_Period.GetInt()]);
    float TotalMovement = 0;
    
    for (int i = 1; i <= Input_Period.GetInt(); i++)
    {
        int Index = sc.Index - i + 1;
        if (Index > 0)
            TotalMovement += abs(sc.Close[Index] - sc.Close[Index - 1]);
    }
    
    float Efficiency = (TotalMovement > 0) ? NetChange / TotalMovement : 0;
    Subgraph_Efficiency[sc.Index] = Efficiency;
    
    // Calculate moving average of efficiency
    sc.SimpleMovAvg(Subgraph_Efficiency, Subgraph_EfficiencyMA, Input_MALength.GetInt());
}

/*==========================================================================*/
SCSFExport scsf_RotationalCalculator(SCStudyInterfaceRef sc)
{
    SCSubgraphRef Subgraph_RotationUp = sc.Subgraph[0];
    SCSubgraphRef Subgraph_RotationDown = sc.Subgraph[1];
    SCSubgraphRef Subgraph_RotationStrength = sc.Subgraph[2];
    
    SCInputRef Input_RotationThreshold = sc.Input[0];
    SCInputRef Input_VolumeWeight = sc.Input[1];
    
    if (sc.SetDefaults)
    {
        sc.GraphName = "Rotational Calculator";
        sc.StudyDescription = "Calculates rotational movements in price action";
        sc.AutoLoop = 1;
        sc.GraphRegion = 1;
        
        Subgraph_RotationUp.Name = "Rotation Up";
        Subgraph_RotationUp.DrawStyle = DRAWSTYLE_POINT;
        Subgraph_RotationUp.PrimaryColor = RGB(0, 255, 0);
        Subgraph_RotationUp.LineWidth = 8;
        
        Subgraph_RotationDown.Name = "Rotation Down";
        Subgraph_RotationDown.DrawStyle = DRAWSTYLE_POINT;
        Subgraph_RotationDown.PrimaryColor = RGB(255, 0, 0);
        Subgraph_RotationDown.LineWidth = 8;
        
        Input_RotationThreshold.Name = "Rotation Threshold";
        Input_RotationThreshold.SetFloat(0.5f);
        
        Input_VolumeWeight.Name = "Volume Weight Factor";
        Input_VolumeWeight.SetFloat(1.0f);
        
        return;
    }
    
    if (sc.Index < 3)
        return;
        
    // Calculate rotation based on price reversal patterns
    float PriceChange1 = sc.Close[sc.Index] - sc.Close[sc.Index - 1];
    float PriceChange2 = sc.Close[sc.Index - 1] - sc.Close[sc.Index - 2];
    float PriceChange3 = sc.Close[sc.Index - 2] - sc.Close[sc.Index - 3];
    
    float VolumeWeight = sc.Volume[sc.Index] * Input_VolumeWeight.GetFloat();
    
    // Detect rotational patterns
    if (PriceChange1 > 0 && PriceChange2 < 0 && PriceChange3 < 0)
    {
        // Upward rotation
        float RotationStrength = abs(PriceChange1) * VolumeWeight;
        if (RotationStrength > Input_RotationThreshold.GetFloat())
        {
            Subgraph_RotationUp[sc.Index] = 1;
            Subgraph_RotationStrength[sc.Index] = RotationStrength;
        }
    }
    else if (PriceChange1 < 0 && PriceChange2 > 0 && PriceChange3 > 0)
    {
        // Downward rotation
        float RotationStrength = abs(PriceChange1) * VolumeWeight;
        if (RotationStrength > Input_RotationThreshold.GetFloat())
        {
            Subgraph_RotationDown[sc.Index] = -1;
            Subgraph_RotationStrength[sc.Index] = -RotationStrength;
        }
    }
}

/*==========================================================================*/
// Study registration and version information
SCDLLEXPORT int SCDLLCALL scdll_DLLVersion()
{
    return SC_DLL_VERSION;
}

SCDLLEXPORT const char* SCDLLCALL scdll_DLLName()
{
    return "OrderflowLabs Reverse Engineered Suite v1.0";
}
