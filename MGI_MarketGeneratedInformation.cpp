#include "sierrachart.h"

/*==========================================================================*/
SCSFExport scsf_MGI(SCStudyInterfaceRef sc)
{
    // Subgraph declarations for Market Generated Information
    SCSubgraphRef Subgraph_ValueAreaHigh = sc.Subgraph[0];
    SCSubgraphRef Subgraph_ValueAreaLow = sc.Subgraph[1];
    SCSubgraphRef Subgraph_PointOfControl = sc.Subgraph[2];
    SCSubgraphRef Subgraph_VolumeProfile = sc.Subgraph[3];
    SCSubgraphRef Subgraph_InitialBalance = sc.Subgraph[4];
    SCSubgraphRef Subgraph_DayHigh = sc.Subgraph[5];
    SCSubgraphRef Subgraph_DayLow = sc.Subgraph[6];
    SCSubgraphRef Subgraph_DayOpen = sc.Subgraph[7];
    SCSubgraphRef Subgraph_DayClose = sc.Subgraph[8];
    
    // Input declarations
    SCInputRef Input_TimePeriodType = sc.Input[0];
    SCInputRef Input_TimePeriodLength = sc.Input[1];
    SCInputRef Input_ValueAreaPercent = sc.Input[2];
    SCInputRef Input_InitialBalanceMinutes = sc.Input[3];
    SCInputRef Input_NumberOfDaysToCalculate = sc.Input[4];
    SCInputRef Input_VolumeType = sc.Input[5];
    SCInputRef Input_DisplayValueArea = sc.Input[6];
    SCInputRef Input_DisplayPOC = sc.Input[7];
    SCInputRef Input_DisplayInitialBalance = sc.Input[8];
    
    // Persistent storage for market profile data
    c_VAPContainer* p_VolumeAtPriceData = (c_VAPContainer*)sc.GetPersistentPointer(1);
    SCDateTime& r_LastCalculationTime = sc.GetPersistentSCDateTime(1);
    
    if (sc.SetDefaults)
    {
        // Set study configuration
        sc.GraphName = "MGI - Market Generated Information";
        sc.StudyDescription = "Comprehensive market profile analysis with Value Area, POC, and Initial Balance";
        sc.AutoLoop = 0; // Manual looping for session-based calculations
        sc.GraphRegion = 0;
        sc.ValueFormat = 2;
        sc.ScaleRangeType = SCALE_INDEPENDENT;
        sc.MaintainVolumeAtPriceData = 1; // Enable VAP data collection
        
        // Configure subgraphs
        Subgraph_ValueAreaHigh.Name = "Value Area High";
        Subgraph_ValueAreaHigh.DrawStyle = DRAWSTYLE_DASH;
        Subgraph_ValueAreaHigh.PrimaryColor = RGB(192, 128, 64); // Brown
        Subgraph_ValueAreaHigh.LineWidth = 2;
        Subgraph_ValueAreaHigh.DrawZeros = false;
        
        Subgraph_ValueAreaLow.Name = "Value Area Low";
        Subgraph_ValueAreaLow.DrawStyle = DRAWSTYLE_DASH;
        Subgraph_ValueAreaLow.PrimaryColor = RGB(192, 128, 64); // Brown
        Subgraph_ValueAreaLow.LineWidth = 2;
        Subgraph_ValueAreaLow.DrawZeros = false;
        
        Subgraph_PointOfControl.Name = "Point of Control";
        Subgraph_PointOfControl.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_PointOfControl.PrimaryColor = RGB(128, 128, 220); // Blue-gray
        Subgraph_PointOfControl.LineWidth = 3;
        Subgraph_PointOfControl.DrawZeros = false;
        
        Subgraph_VolumeProfile.Name = "Volume Profile";
        Subgraph_VolumeProfile.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_VolumeProfile.PrimaryColor = RGB(192, 128, 64); // Brown
        Subgraph_VolumeProfile.SecondaryColor = RGB(128, 128, 220); // Blue-gray
        Subgraph_VolumeProfile.LineWidth = 1;
        Subgraph_VolumeProfile.LineStyle = 20;
        
        Subgraph_InitialBalance.Name = "Initial Balance";
        Subgraph_InitialBalance.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_InitialBalance.PrimaryColor = RGB(192, 128, 64); // Brown
        Subgraph_InitialBalance.LineWidth = 3;
        
        Subgraph_DayHigh.Name = "Session High";
        Subgraph_DayHigh.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_DayHigh.PrimaryColor = RGB(128, 128, 220); // Blue-gray
        Subgraph_DayHigh.LineWidth = 3;
        
        Subgraph_DayLow.Name = "Session Low";
        Subgraph_DayLow.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_DayLow.PrimaryColor = RGB(192, 128, 64); // Brown
        
        Subgraph_DayOpen.Name = "Session Open";
        Subgraph_DayOpen.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_DayOpen.PrimaryColor = RGB(192, 128, 64); // Brown
        Subgraph_DayOpen.SecondaryColor = RGB(128, 128, 220); // Blue-gray
        Subgraph_DayOpen.LineWidth = 1;
        Subgraph_DayOpen.LineStyle = 20;
        
        Subgraph_DayClose.Name = "Session Close";
        Subgraph_DayClose.DrawStyle = DRAWSTYLE_BACKGROUND;
        Subgraph_DayClose.PrimaryColor = RGB(192, 128, 64); // Brown
        Subgraph_DayClose.LineWidth = 3;
        
        // Configure inputs
        Input_TimePeriodType.Name = "Time Period Type";
        Input_TimePeriodType.SetTimePeriodType(TIME_PERIOD_LENGTH_UNIT_DAYS);
        
        Input_TimePeriodLength.Name = "Time Period Length";
        Input_TimePeriodLength.SetInt(1);
        Input_TimePeriodLength.SetIntLimits(1, 30);
        
        Input_ValueAreaPercent.Name = "Value Area Percentage";
        Input_ValueAreaPercent.SetFloat(70.0f);
        Input_ValueAreaPercent.SetFloatLimits(50.0f, 95.0f);
        
        Input_InitialBalanceMinutes.Name = "Initial Balance Minutes";
        Input_InitialBalanceMinutes.SetInt(60);
        Input_InitialBalanceMinutes.SetIntLimits(30, 240);
        
        Input_NumberOfDaysToCalculate.Name = "Number of Days to Calculate";
        Input_NumberOfDaysToCalculate.SetInt(10);
        Input_NumberOfDaysToCalculate.SetIntLimits(1, 50);
        
        Input_VolumeType.Name = "Volume Type";
        Input_VolumeType.SetCustomInputStrings("Volume;Bid/Ask Volume;Number of Trades");
        Input_VolumeType.SetCustomInputIndex(0);
        
        Input_DisplayValueArea.Name = "Display Value Area";
        Input_DisplayValueArea.SetYesNo(true);
        
        Input_DisplayPOC.Name = "Display Point of Control";
        Input_DisplayPOC.SetYesNo(true);
        
        Input_DisplayInitialBalance.Name = "Display Initial Balance";
        Input_DisplayInitialBalance.SetYesNo(true);
        
        return;
    }
    
    // Initialize persistent data structure
    if (p_VolumeAtPriceData == NULL)
    {
        p_VolumeAtPriceData = new c_VAPContainer();
        sc.SetPersistentPointer(1, p_VolumeAtPriceData);
    }
    
    // Get current trading day date
    SCDateTime TradingDayDate = sc.GetTradingDayDate(sc.BaseDateTimeIn[sc.ArraySize - 1]);
    
    // Check if we need to recalculate (new day or full recalculation)
    if (sc.IsFullRecalculation || TradingDayDate != r_LastCalculationTime)
    {
        r_LastCalculationTime = TradingDayDate;
        
        // Clear previous calculations
        for (int SubgraphIndex = 0; SubgraphIndex <= 8; SubgraphIndex++)
            sc.Subgraph[SubgraphIndex].Clear();
            
        p_VolumeAtPriceData->Clear();
    }
    
    // Calculate market profile for the current session
    SCDateTime SessionStartDateTime, SessionEndDateTime;
    sc.GetTradingDayStartEndTimes(TradingDayDate, SessionStartDateTime, SessionEndDateTime);
    
    // Find the bar range for the current session
    int SessionStartIndex = -1, SessionEndIndex = -1;
    for (int Index = 0; Index < sc.ArraySize; Index++)
    {
        if (sc.BaseDateTimeIn[Index] >= SessionStartDateTime && SessionStartIndex == -1)
            SessionStartIndex = Index;
        if (sc.BaseDateTimeIn[Index] <= SessionEndDateTime)
            SessionEndIndex = Index;
    }
    
    if (SessionStartIndex == -1 || SessionEndIndex == -1)
        return;
    
    // Calculate session OHLC
    float SessionOpen = sc.Open[SessionStartIndex];
    float SessionHigh = sc.High[SessionStartIndex];
    float SessionLow = sc.Low[SessionStartIndex];
    float SessionClose = sc.Close[SessionEndIndex];
    
    for (int Index = SessionStartIndex; Index <= SessionEndIndex; Index++)
    {
        if (sc.High[Index] > SessionHigh) SessionHigh = sc.High[Index];
        if (sc.Low[Index] < SessionLow) SessionLow = sc.Low[Index];
    }
    
    // Calculate Initial Balance (first hour or specified minutes)
    SCDateTime IBEndTime = SessionStartDateTime + SCDateTime::MINUTES(Input_InitialBalanceMinutes.GetInt());
    float IBHigh = SessionOpen, IBLow = SessionOpen;
    
    for (int Index = SessionStartIndex; Index <= SessionEndIndex; Index++)
    {
        if (sc.BaseDateTimeIn[Index] > IBEndTime)
            break;
            
        if (sc.High[Index] > IBHigh) IBHigh = sc.High[Index];
        if (sc.Low[Index] < IBLow) IBLow = sc.Low[Index];
    }
    
    // Build Volume at Price data for the session
    for (int Index = SessionStartIndex; Index <= SessionEndIndex; Index++)
    {
        s_VolumeAtPriceV2 VAPData;
        VAPData.PriceInTicks = sc.PriceValueToTicks((sc.High[Index] + sc.Low[Index]) / 2);
        
        switch (Input_VolumeType.GetIndex())
        {
            case 0: // Volume
                VAPData.Volume = (unsigned int)sc.Volume[Index];
                break;
            case 1: // Bid/Ask Volume
                VAPData.BidVolume = (unsigned int)sc.BidVolume[Index];
                VAPData.AskVolume = (unsigned int)sc.AskVolume[Index];
                VAPData.Volume = VAPData.BidVolume + VAPData.AskVolume;
                break;
            case 2: // Number of Trades
                VAPData.Volume = (unsigned int)sc.NumberOfTrades[Index];
                break;
        }
        
        p_VolumeAtPriceData->InsertVolumeAtPrice(VAPData);
    }
    
    // Calculate Point of Control and Value Area
    const s_VolumeAtPriceV2* p_VAPArray = NULL;
    int VAPArraySize = 0;
    p_VolumeAtPriceData->GetVAPElementsForRange(sc.PriceValueToTicks(SessionLow), 
                                               sc.PriceValueToTicks(SessionHigh), 
                                               p_VAPArray, VAPArraySize);
    
    if (VAPArraySize > 0)
    {
        // Find Point of Control (highest volume price)
        unsigned int MaxVolume = 0;
        float POCPrice = 0;
        
        for (int i = 0; i < VAPArraySize; i++)
        {
            if (p_VAPArray[i].Volume > MaxVolume)
            {
                MaxVolume = p_VAPArray[i].Volume;
                POCPrice = sc.TicksToPrice(p_VAPArray[i].PriceInTicks);
            }
        }
        
        // Calculate Value Area (70% of volume around POC)
        unsigned int TotalVolume = 0;
        for (int i = 0; i < VAPArraySize; i++)
            TotalVolume += p_VAPArray[i].Volume;
            
        unsigned int ValueAreaVolume = (unsigned int)(TotalVolume * Input_ValueAreaPercent.GetFloat() / 100.0f);
        
        // Find Value Area High and Low
        float VAHigh = POCPrice, VALow = POCPrice;
        unsigned int AccumulatedVolume = MaxVolume;
        
        // Expand around POC until we reach the value area percentage
        int UpIndex = -1, DownIndex = -1;
        for (int i = 0; i < VAPArraySize; i++)
        {
            if (sc.TicksToPrice(p_VAPArray[i].PriceInTicks) == POCPrice)
            {
                UpIndex = i + 1;
                DownIndex = i - 1;
                break;
            }
        }
        
        while (AccumulatedVolume < ValueAreaVolume && (UpIndex < VAPArraySize || DownIndex >= 0))
        {
            unsigned int UpVolume = (UpIndex < VAPArraySize) ? p_VAPArray[UpIndex].Volume : 0;
            unsigned int DownVolume = (DownIndex >= 0) ? p_VAPArray[DownIndex].Volume : 0;
            
            if (UpVolume >= DownVolume && UpIndex < VAPArraySize)
            {
                VAHigh = sc.TicksToPrice(p_VAPArray[UpIndex].PriceInTicks);
                AccumulatedVolume += UpVolume;
                UpIndex++;
            }
            else if (DownIndex >= 0)
            {
                VALow = sc.TicksToPrice(p_VAPArray[DownIndex].PriceInTicks);
                AccumulatedVolume += DownVolume;
                DownIndex--;
            }
            else
                break;
        }
        
        // Set subgraph values for the entire session
        for (int Index = SessionStartIndex; Index <= SessionEndIndex; Index++)
        {
            if (Input_DisplayValueArea.GetYesNo())
            {
                Subgraph_ValueAreaHigh[Index] = VAHigh;
                Subgraph_ValueAreaLow[Index] = VALow;
            }
            
            if (Input_DisplayPOC.GetYesNo())
                Subgraph_PointOfControl[Index] = POCPrice;
                
            if (Input_DisplayInitialBalance.GetYesNo())
            {
                Subgraph_InitialBalance[Index] = (IBHigh + IBLow) / 2;
            }
            
            Subgraph_DayHigh[Index] = SessionHigh;
            Subgraph_DayLow[Index] = SessionLow;
            Subgraph_DayOpen[Index] = SessionOpen;
            Subgraph_DayClose[Index] = SessionClose;
        }
    }
}
