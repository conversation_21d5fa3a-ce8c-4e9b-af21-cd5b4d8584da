
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_BuySellZones(undefined1 param_1 [16],double param_2,undefined8 param_3,undefined8 param_4,
                      undefined8 param_5,undefined8 param_6,undefined8 param_7,undefined8 param_8,
                      undefined8 param_9,undefined8 param_10)

{
  undefined8 uVar1;
  longlong *plVar2;
  longlong lVar3;
  undefined8 uVar4;
  undefined8 uVar5;
  undefined8 uVar6;
  undefined8 uVar7;
  undefined8 uVar8;
  undefined1 uVar9;
  byte bVar10;
  undefined2 uVar11;
  char cVar12;
  short sVar13;
  int iVar14;
  longlong lVar15;
  undefined1 *puVar16;
  bool bVar17;
  bool bVar18;
  bool bVar19;
  bool bVar20;
  bool bVar21;
  int iVar22;
  int iVar23;
  uint uVar24;
  uint uVar25;
  int iVar26;
  undefined4 uVar27;
  undefined4 uVar28;
  uint *puVar29;
  undefined8 uVar30;
  longlong *plVar31;
  undefined8 *puVar32;
  HANDLE pvVar33;
  longlong *plVar34;
  undefined4 *puVar35;
  ulonglong uVar36;
  uint *puVar37;
  float *pfVar38;
  float *pfVar39;
  ulonglong uVar40;
  uint extraout_w1;
  int extraout_w1_00;
  undefined8 extraout_x1;
  longlong extraout_x1_00;
  longlong extraout_x1_01;
  undefined8 uVar41;
  undefined8 uVar42;
  int *piVar43;
  undefined8 uVar44;
  uint *puVar45;
  code *pcVar46;
  float *pfVar47;
  longlong lVar48;
  char *pcVar49;
  longlong lVar50;
  code *pcVar51;
  uint uVar52;
  uint uVar53;
  longlong lVar54;
  longlong lVar55;
  char *pcVar56;
  longlong extraout_x10;
  longlong extraout_x10_00;
  longlong extraout_x10_01;
  longlong extraout_x10_02;
  longlong extraout_x10_03;
  longlong extraout_x10_04;
  longlong extraout_x10_05;
  longlong extraout_x10_06;
  longlong extraout_x10_07;
  longlong extraout_x10_08;
  longlong extraout_x10_09;
  longlong extraout_x10_10;
  longlong extraout_x10_11;
  longlong extraout_x10_12;
  longlong extraout_x10_13;
  longlong extraout_x10_14;
  longlong extraout_x10_15;
  longlong extraout_x10_16;
  longlong extraout_x10_17;
  longlong extraout_x10_18;
  longlong extraout_x10_19;
  longlong extraout_x10_20;
  longlong extraout_x10_21;
  longlong extraout_x10_22;
  longlong extraout_x10_23;
  longlong extraout_x10_24;
  longlong extraout_x10_25;
  longlong *extraout_x10_26;
  uint extraout_w11;
  undefined4 extraout_w11_00;
  longlong extraout_x11;
  longlong extraout_x11_00;
  longlong extraout_x11_01;
  longlong extraout_x11_02;
  longlong extraout_x11_03;
  longlong extraout_x11_04;
  longlong extraout_x11_05;
  longlong extraout_x11_06;
  longlong extraout_x11_07;
  longlong extraout_x11_08;
  longlong extraout_x11_09;
  longlong extraout_x11_10;
  longlong extraout_x11_11;
  longlong extraout_x11_12;
  longlong extraout_x11_13;
  undefined1 extraout_w12;
  undefined4 extraout_w12_00;
  longlong extraout_x12;
  longlong extraout_x12_00;
  longlong extraout_x12_01;
  longlong extraout_x12_02;
  longlong extraout_x12_03;
  longlong extraout_x12_04;
  longlong extraout_x12_05;
  longlong extraout_x12_06;
  undefined1 extraout_w13;
  undefined4 extraout_w13_00;
  undefined4 extraout_w14;
  undefined8 uVar57;
  longlong extraout_x14;
  undefined4 extraout_w15;
  undefined4 extraout_w15_00;
  longlong extraout_x15;
  undefined8 uVar58;
  longlong *plVar59;
  LPVOID pvVar60;
  uint uVar61;
  uint *puVar62;
  int *piVar63;
  float *pfVar64;
  float fVar65;
  float fVar66;
  undefined1 extraout_q0 [16];
  undefined1 extraout_q0_00 [16];
  undefined1 auVar67 [16];
  undefined1 extraout_q0_01 [16];
  undefined1 extraout_q0_02 [16];
  undefined1 extraout_q0_03 [16];
  undefined1 extraout_q0_04 [16];
  undefined1 extraout_q0_05 [16];
  undefined1 auVar68 [16];
  undefined1 auVar69 [16];
  undefined1 auVar70 [16];
  undefined1 extraout_q0_06 [16];
  undefined1 extraout_q0_07 [16];
  undefined1 extraout_q0_08 [16];
  undefined1 extraout_q0_09 [16];
  undefined1 extraout_q0_10 [16];
  undefined1 extraout_q0_11 [16];
  undefined1 extraout_q0_12 [16];
  undefined1 extraout_q0_13 [16];
  undefined1 extraout_q0_14 [16];
  undefined1 extraout_q0_15 [16];
  undefined1 extraout_q0_16 [16];
  undefined1 extraout_q0_17 [16];
  undefined1 extraout_q0_18 [16];
  undefined1 extraout_q0_19 [16];
  undefined1 extraout_q0_20 [16];
  undefined1 extraout_q0_21 [16];
  undefined1 extraout_q0_22 [16];
  undefined1 extraout_q0_23 [16];
  undefined1 extraout_q0_24 [16];
  undefined1 extraout_q0_25 [16];
  undefined1 extraout_q0_26 [16];
  undefined1 extraout_q0_27 [16];
  float fVar71;
  float fVar72;
  float fVar73;
  undefined4 uVar74;
  double dVar75;
  float fVar76;
  float extraout_s18;
  float extraout_s18_00;
  undefined4 extraout_s18_01;
  float extraout_s18_02;
  float extraout_s18_03;
  undefined4 extraout_s18_04;
  float extraout_s19;
  longlong lStack_b0;
  char acStack_a8 [8];
  longlong lStack_8;
  
                    /* 0x2d070  4  scsf_BuySellZones */
  puVar29 = (uint *)FUN_1800010c0();
  lVar15 = extraout_x15 * -0x10;
  plVar59 = (longlong *)(&stack0xffffffffffffffd0 + lVar15);
  *(undefined8 *)(&stack0x00000420 + lVar15) = 0xfffffffffffffffe;
  *(undefined8 *)(&stack0x000004e8 + lVar15) = 0;
  *(undefined8 *)(&stack0x000004e0 + lVar15) = 0;
  *(undefined8 *)(&stack0x000004f0 + lVar15) = 0;
  *(undefined1 **)(&stack0x000004f0 + lVar15) = &DAT_1800d4ecd;
  *(undefined1 **)(&stack0x000004e0 + lVar15) = &DAT_1800d4ecd;
  *(undefined8 *)(&stack0x000004e8 + lVar15) = 0;
  uVar30 = (**(code **)(puVar29 + 0x62e))(0);
  lVar54 = *(longlong *)(puVar29 + 0x140);
  *(undefined8 *)(&stack0x00000238 + lVar15) = uVar30;
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x144) != (code *)0x0) {
      (**(code **)(puVar29 + 0x144))(puVar29[0x146]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x140);
    if (lVar54 != 0) goto LAB_18002d134;
    puVar37 = puVar29 + 0x14a;
    *(uint **)(&stack0xffffffffffffffe8 + lVar15) = puVar37;
    *(uint **)(&stack0x00000068 + lVar15) = puVar37;
    *(uint **)(&stack0x00000090 + lVar15) = puVar37;
    *(uint **)(&stack0x00000018 + lVar15) = puVar37;
LAB_18002d170:
    if (*(code **)(puVar29 + 0x144) != (code *)0x0) {
      (**(code **)(puVar29 + 0x144))(puVar29[0x146]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x140);
    if (lVar54 != 0) goto LAB_18002d1a0;
    *(uint **)(&stack0x00000058 + lVar15) = puVar29 + 0x14a;
    *(uint **)(&stack0x000000a8 + lVar15) = puVar29 + 0x14a;
LAB_18002d1d4:
    if (*(code **)(puVar29 + 0x144) != (code *)0x0) {
      (**(code **)(puVar29 + 0x144))(puVar29[0x146]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x140);
    if (lVar54 != 0) goto LAB_18002d208;
    puVar37 = puVar29 + 0x14a;
    *(uint **)(&stack0x00000030 + lVar15) = puVar37;
    *(uint **)(&stack0x000001c8 + lVar15) = puVar37;
    *plVar59 = (longlong)puVar37;
LAB_18002d244:
    if (*(code **)(puVar29 + 0x144) != (code *)0x0) {
      (**(code **)(puVar29 + 0x144))(puVar29[0x146]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x140);
    if (lVar54 != 0) goto LAB_18002d278;
    puVar37 = puVar29 + 0x14a;
    *(uint **)(&stack0x00000038 + lVar15) = puVar37;
    *(uint **)(&stack0x000001c0 + lVar15) = puVar37;
    *(uint **)(&stack0x00000218 + lVar15) = puVar37;
LAB_18002d2b4:
    if (*(code **)(puVar29 + 0x144) != (code *)0x0) {
      (**(code **)(puVar29 + 0x144))(puVar29[0x146]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x140);
    if (lVar54 != 0) goto LAB_18002d2e8;
    *(uint **)(&stack0x00000050 + lVar15) = puVar29 + 0x14a;
    *(uint **)(&stack0x000001b0 + lVar15) = puVar29 + 0x14a;
LAB_18002d324:
    if (*(code **)(puVar29 + 0x144) != (code *)0x0) {
      (**(code **)(puVar29 + 0x144))(puVar29[0x146]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x140);
    if (lVar54 != 0) goto LAB_18002d358;
    puVar37 = puVar29 + 0x14a;
    *(uint **)(&stack0x00000020 + lVar15) = puVar37;
    *(uint **)(&stack0x000001b8 + lVar15) = puVar37;
    *(uint **)(&stack0x00000220 + lVar15) = puVar37;
LAB_18002d394:
    if (*(code **)(puVar29 + 0x144) != (code *)0x0) {
      (**(code **)(puVar29 + 0x144))(puVar29[0x146]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x140);
    if (lVar54 != 0) goto LAB_18002d3c4;
    *(uint **)(&stack0x00000078 + lVar15) = puVar29 + 0x14a;
    *(uint **)(&stack0x00000228 + lVar15) = puVar29 + 0x14a;
LAB_18002d3fc:
    if (*(code **)(puVar29 + 0x144) != (code *)0x0) {
      (**(code **)(puVar29 + 0x144))(puVar29[0x146]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x140);
    if (lVar54 != 0) goto LAB_18002d428;
    *(uint **)(&stack0x00000080 + lVar15) = puVar29 + 0x14a;
LAB_18002d45c:
    if (*(code **)(puVar29 + 0x144) != (code *)0x0) {
      (**(code **)(puVar29 + 0x144))(puVar29[0x146]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x140);
    if (lVar54 != 0) goto LAB_18002d488;
    *(uint **)(&stack0x00000148 + lVar15) = puVar29 + 0x14a;
    *(uint **)(&stack0x00000230 + lVar15) = puVar29 + 0x14a;
LAB_18002d4bc:
    if (*(code **)(puVar29 + 0x144) != (code *)0x0) {
      (**(code **)(puVar29 + 0x144))(puVar29[0x146]);
    }
  }
  else {
LAB_18002d134:
    uVar24 = puVar29[0x148];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x14a;
    }
    else {
      iVar22 = 0;
      if ((int)uVar24 < 1) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x170);
    }
    *(uint **)(&stack0xffffffffffffffe8 + lVar15) = puVar37;
    *(uint **)(&stack0x00000068 + lVar15) = puVar37;
    *(uint **)(&stack0x00000090 + lVar15) = puVar37;
    *(uint **)(&stack0x00000018 + lVar15) = puVar37;
    if (lVar54 == 0) goto LAB_18002d170;
LAB_18002d1a0:
    uVar24 = puVar29[0x148];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x14a;
    }
    else {
      iVar22 = 1;
      if (uVar24 - 1 == 0 || (int)uVar24 < 1) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x170);
    }
    *(uint **)(&stack0x00000058 + lVar15) = puVar37;
    *(uint **)(&stack0x000000a8 + lVar15) = puVar37;
    if (lVar54 == 0) goto LAB_18002d1d4;
LAB_18002d208:
    uVar24 = puVar29[0x148];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x14a;
    }
    else {
      iVar22 = 2;
      if ((int)uVar24 < 3) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x170);
    }
    *(uint **)(&stack0x00000030 + lVar15) = puVar37;
    *(uint **)(&stack0x000001c8 + lVar15) = puVar37;
    *plVar59 = (longlong)puVar37;
    if (lVar54 == 0) goto LAB_18002d244;
LAB_18002d278:
    uVar24 = puVar29[0x148];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x14a;
    }
    else {
      iVar22 = 3;
      if ((int)uVar24 < 4) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x170);
    }
    *(uint **)(&stack0x00000038 + lVar15) = puVar37;
    *(uint **)(&stack0x000001c0 + lVar15) = puVar37;
    *(uint **)(&stack0x00000218 + lVar15) = puVar37;
    if (lVar54 == 0) goto LAB_18002d2b4;
LAB_18002d2e8:
    uVar24 = puVar29[0x148];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x14a;
    }
    else {
      iVar22 = 4;
      if ((int)uVar24 < 5) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x170);
    }
    *(uint **)(&stack0x00000050 + lVar15) = puVar37;
    *(uint **)(&stack0x000001b0 + lVar15) = puVar37;
    if (lVar54 == 0) goto LAB_18002d324;
LAB_18002d358:
    uVar24 = puVar29[0x148];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x14a;
    }
    else {
      iVar22 = 5;
      if ((int)uVar24 < 6) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x170);
    }
    *(uint **)(&stack0x00000020 + lVar15) = puVar37;
    *(uint **)(&stack0x000001b8 + lVar15) = puVar37;
    *(uint **)(&stack0x00000220 + lVar15) = puVar37;
    if (lVar54 == 0) goto LAB_18002d394;
LAB_18002d3c4:
    uVar24 = puVar29[0x148];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x14a;
    }
    else {
      iVar22 = 6;
      if ((int)uVar24 < 7) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x170);
    }
    *(uint **)(&stack0x00000078 + lVar15) = puVar37;
    *(uint **)(&stack0x00000228 + lVar15) = puVar37;
    if (lVar54 == 0) goto LAB_18002d3fc;
LAB_18002d428:
    uVar24 = puVar29[0x148];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x14a;
    }
    else {
      iVar22 = 7;
      if ((int)uVar24 < 8) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x170);
    }
    *(uint **)(&stack0x00000080 + lVar15) = puVar37;
    if (lVar54 == 0) goto LAB_18002d45c;
LAB_18002d488:
    uVar24 = puVar29[0x148];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x14a;
    }
    else {
      iVar22 = 8;
      if ((int)uVar24 < 9) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x170);
    }
    *(uint **)(&stack0x00000148 + lVar15) = puVar37;
    *(uint **)(&stack0x00000230 + lVar15) = puVar37;
    if (lVar54 == 0) goto LAB_18002d4bc;
  }
  lVar54 = *(longlong *)(puVar29 + 0x140);
  iVar22 = 10;
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x144) != (code *)0x0) {
      (**(code **)(puVar29 + 0x144))(puVar29[0x146]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x140);
    if (lVar54 != 0) goto LAB_18002d508;
    *(uint **)(&stack0x00000048 + lVar15) = puVar29 + 0x14a;
    *(uint **)(&stack0x000000a0 + lVar15) = puVar29 + 0x14a;
LAB_18002d53c:
    if (*(code **)(puVar29 + 0x144) != (code *)0x0) {
      (**(code **)(puVar29 + 0x144))(puVar29[0x146]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x140);
    if (lVar54 != 0) goto LAB_18002d56c;
    *(uint **)(&stack0x00000040 + lVar15) = puVar29 + 0x14a;
    *(uint **)(&stack0x00000098 + lVar15) = puVar29 + 0x14a;
LAB_18002d5a4:
    if (*(code **)(puVar29 + 0x144) != (code *)0x0) {
      (**(code **)(puVar29 + 0x144))(puVar29[0x146]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x140);
    if (lVar54 != 0) goto LAB_18002d5c4;
    puVar37 = puVar29 + 0x14a;
  }
  else {
LAB_18002d508:
    uVar24 = puVar29[0x148];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x14a;
    }
    else {
      if ((int)uVar24 < 0xb) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x170);
    }
    *(uint **)(&stack0x00000048 + lVar15) = puVar37;
    *(uint **)(&stack0x000000a0 + lVar15) = puVar37;
    if (lVar54 == 0) goto LAB_18002d53c;
LAB_18002d56c:
    uVar24 = puVar29[0x148];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x14a;
    }
    else {
      iVar22 = 0xb;
      if ((int)uVar24 < 0xc) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x170);
    }
    *(uint **)(&stack0x00000040 + lVar15) = puVar37;
    *(uint **)(&stack0x00000098 + lVar15) = puVar37;
    if (lVar54 == 0) goto LAB_18002d5a4;
LAB_18002d5c4:
    uVar24 = puVar29[0x148];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x14a;
    }
    else {
      iVar22 = 0xc;
      if ((int)uVar24 < 0xd) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x170);
    }
  }
  *(uint **)(&stack0x00000140 + lVar15) = puVar37;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002d618;
    puVar37 = puVar29 + 0x8e;
  }
  else {
LAB_18002d618:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x8e;
    }
    else {
      iVar22 = 0;
      if ((int)uVar24 < 1) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000008 + lVar15) = puVar37;
  *(undefined2 *)((longlong)puVar37 + 0x1a) = 0x1a;
  iVar22 = 1;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002d670;
    puVar37 = puVar29 + 0x8e;
  }
  else {
LAB_18002d670:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 2) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000130 + lVar15) = puVar37;
  *(undefined2 *)((longlong)puVar37 + 0x1a) = 0x1b;
  iVar22 = 2;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002d6c8;
    puVar37 = puVar29 + 0x8e;
  }
  else {
LAB_18002d6c8:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 3) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000160 + lVar15) = puVar37;
  *(undefined2 *)((longlong)puVar37 + 0x1a) = 0x1c;
  iVar22 = 3;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002d720;
    puVar37 = puVar29 + 0x8e;
  }
  else {
LAB_18002d720:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 4) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0xffffffffffffffe0 + lVar15) = puVar37;
  iVar22 = 0xe;
  *(undefined2 *)((longlong)puVar37 + 0x1a) = 0x1d;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  *(undefined4 *)(&stack0xffffffffffffffd8 + lVar15) = 0xe;
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002d77c;
    puVar37 = puVar29 + 0x8e;
  }
  else {
LAB_18002d77c:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar37 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0xf) {
        iVar22 = uVar24 - 1;
      }
      puVar37 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(undefined2 *)((longlong)puVar37 + 0x1a) = 0x1e;
  iVar22 = 0xf;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002d7d0;
    puVar62 = puVar29 + 0x8e;
  }
  else {
LAB_18002d7d0:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar62 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x10) {
        iVar22 = uVar24 - 1;
      }
      puVar62 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x000000b0 + lVar15) = puVar62;
  *(undefined2 *)((longlong)puVar62 + 0x1a) = 0x1f;
  iVar22 = 0x10;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002d828;
    puVar62 = puVar29 + 0x8e;
  }
  else {
LAB_18002d828:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar62 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x11) {
        iVar22 = uVar24 - 1;
      }
      puVar62 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0xfffffffffffffff0 + lVar15) = puVar62;
  *(undefined2 *)((longlong)puVar62 + 0x1a) = 0x20;
  iVar22 = 0x11;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002d880;
    puVar62 = puVar29 + 0x8e;
  }
  else {
LAB_18002d880:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar62 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x12) {
        iVar22 = uVar24 - 1;
      }
      puVar62 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000108 + lVar15) = puVar62;
  *(undefined2 *)((longlong)puVar62 + 0x1a) = 0x21;
  iVar22 = 0x12;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002d8d8;
    puVar62 = puVar29 + 0x8e;
  }
  else {
LAB_18002d8d8:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar62 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x13) {
        iVar22 = uVar24 - 1;
      }
      puVar62 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000118 + lVar15) = puVar62;
  *(undefined2 *)((longlong)puVar62 + 0x1a) = 0x22;
  iVar22 = 0x13;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002d930;
    puVar62 = puVar29 + 0x8e;
  }
  else {
LAB_18002d930:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar62 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x14) {
        iVar22 = uVar24 - 1;
      }
      puVar62 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000100 + lVar15) = puVar62;
  *(undefined2 *)((longlong)puVar62 + 0x1a) = 1;
  iVar22 = 0x14;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002d988;
    puVar62 = puVar29 + 0x8e;
  }
  else {
LAB_18002d988:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar62 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x15) {
        iVar22 = uVar24 - 1;
      }
      puVar62 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000110 + lVar15) = puVar62;
  *(undefined2 *)((longlong)puVar62 + 0x1a) = 0x24;
  iVar22 = 0x15;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002d9e0;
    puVar62 = puVar29 + 0x8e;
  }
  else {
LAB_18002d9e0:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar62 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x16) {
        iVar22 = uVar24 - 1;
      }
      puVar62 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x000001f0 + lVar15) = puVar62;
  *(undefined2 *)((longlong)puVar62 + 0x1a) = 0x25;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  iVar22 = 0x16;
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002da3c;
    puVar62 = puVar29 + 0x8e;
  }
  else {
LAB_18002da3c:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar62 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x17) {
        iVar22 = uVar24 - 1;
      }
      puVar62 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000210 + lVar15) = puVar62;
  *(undefined2 *)((longlong)puVar62 + 0x1a) = 0x26;
  iVar22 = 0x17;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002da94;
    puVar62 = puVar29 + 0x8e;
  }
  else {
LAB_18002da94:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar62 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x18) {
        iVar22 = uVar24 - 1;
      }
      puVar62 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000178 + lVar15) = puVar62;
  *(undefined2 *)((longlong)puVar62 + 0x1a) = 0x27;
  iVar22 = 0x18;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002daec;
    puVar62 = puVar29 + 0x8e;
  }
  else {
LAB_18002daec:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar62 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x19) {
        iVar22 = uVar24 - 1;
      }
      puVar62 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000088 + lVar15) = puVar62;
  *(undefined2 *)((longlong)puVar62 + 0x1a) = 0x28;
  iVar22 = 0x19;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002db44;
    puVar62 = puVar29 + 0x8e;
  }
  else {
LAB_18002db44:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar62 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x1a) {
        iVar22 = uVar24 - 1;
      }
      puVar62 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x000001e8 + lVar15) = puVar62;
  *(undefined2 *)((longlong)puVar62 + 0x1a) = 0x29;
  iVar22 = 0x1a;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002db9c;
    puVar62 = puVar29 + 0x8e;
  }
  else {
LAB_18002db9c:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar62 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x1b) {
        iVar22 = uVar24 - 1;
      }
      puVar62 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000170 + lVar15) = puVar62;
  *(undefined2 *)((longlong)puVar62 + 0x1a) = 0x2a;
  iVar22 = 0x1b;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002dbf4;
    puVar62 = puVar29 + 0x8e;
  }
  else {
LAB_18002dbf4:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar62 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x1c) {
        iVar22 = uVar24 - 1;
      }
      puVar62 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x000000e0 + lVar15) = puVar62;
  *(undefined2 *)((longlong)puVar62 + 0x1a) = 0x2b;
  iVar22 = 0x1c;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002dc4c;
    puVar62 = puVar29 + 0x8e;
  }
  else {
LAB_18002dc4c:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar62 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x1d) {
        iVar22 = uVar24 - 1;
      }
      puVar62 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(undefined2 *)((longlong)puVar62 + 0x1a) = 0x2c;
  iVar22 = 0x1d;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002dca0;
    puVar45 = puVar29 + 0x8e;
  }
  else {
LAB_18002dca0:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar45 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x1e) {
        iVar22 = uVar24 - 1;
      }
      puVar45 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000168 + lVar15) = puVar45;
  *(undefined2 *)((longlong)puVar45 + 0x1a) = 0x2d;
  iVar22 = 0x1e;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002dcf8;
    puVar45 = puVar29 + 0x8e;
  }
  else {
LAB_18002dcf8:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar45 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x1f) {
        iVar22 = uVar24 - 1;
      }
      puVar45 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000258 + lVar15) = puVar45;
  *(undefined2 *)((longlong)puVar45 + 0x1a) = 0x2e;
  iVar22 = 0x1f;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002dd50;
    puVar45 = puVar29 + 0x8e;
  }
  else {
LAB_18002dd50:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar45 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x20) {
        iVar22 = uVar24 - 1;
      }
      puVar45 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000180 + lVar15) = puVar45;
  *(undefined2 *)((longlong)puVar45 + 0x1a) = 0x2f;
  iVar22 = 0x20;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002dda8;
    puVar45 = puVar29 + 0x8e;
  }
  else {
LAB_18002dda8:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar45 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x21) {
        iVar22 = uVar24 - 1;
      }
      puVar45 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000120 + lVar15) = puVar45;
  *(undefined2 *)((longlong)puVar45 + 0x1a) = 0x30;
  iVar22 = 0x21;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002de00;
    puVar45 = puVar29 + 0x8e;
  }
  else {
LAB_18002de00:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar45 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x22) {
        iVar22 = uVar24 - 1;
      }
      puVar45 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000250 + lVar15) = puVar45;
  *(undefined2 *)((longlong)puVar45 + 0x1a) = 0x31;
  iVar22 = 0x22;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002de58;
    puVar45 = puVar29 + 0x8e;
  }
  else {
LAB_18002de58:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar45 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x23) {
        iVar22 = uVar24 - 1;
      }
      puVar45 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000060 + lVar15) = puVar45;
  *(undefined2 *)((longlong)puVar45 + 0x1a) = 0x32;
  iVar22 = 0x23;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002deb0;
    puVar45 = puVar29 + 0x8e;
  }
  else {
LAB_18002deb0:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar45 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x24) {
        iVar22 = uVar24 - 1;
      }
      puVar45 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000248 + lVar15) = puVar45;
  *(undefined2 *)((longlong)puVar45 + 0x1a) = 0x33;
  iVar22 = 0x24;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002df08;
    puVar45 = puVar29 + 0x8e;
  }
  else {
LAB_18002df08:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar45 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x25) {
        iVar22 = uVar24 - 1;
      }
      puVar45 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x000001f8 + lVar15) = puVar45;
  *(undefined2 *)((longlong)puVar45 + 0x1a) = 0x34;
  iVar22 = 0x25;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002df60;
    puVar45 = puVar29 + 0x8e;
  }
  else {
LAB_18002df60:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar45 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x26) {
        iVar22 = uVar24 - 1;
      }
      puVar45 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x000001e0 + lVar15) = puVar45;
  *(undefined2 *)((longlong)puVar45 + 0x1a) = 0x35;
  iVar22 = 0x26;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002dfb8;
    puVar45 = puVar29 + 0x8e;
  }
  else {
LAB_18002dfb8:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar45 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x27) {
        iVar22 = uVar24 - 1;
      }
      puVar45 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x00000200 + lVar15) = puVar45;
  *(undefined2 *)((longlong)puVar45 + 0x1a) = 0x36;
  iVar22 = 0x27;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002e010;
    puVar45 = puVar29 + 0x8e;
  }
  else {
LAB_18002e010:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar45 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x28) {
        iVar22 = uVar24 - 1;
      }
      puVar45 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x000000d0 + lVar15) = puVar45;
  *(undefined2 *)((longlong)puVar45 + 0x1a) = 0x37;
  iVar22 = 0x28;
  lVar54 = *(longlong *)(puVar29 + 0x84);
  if (lVar54 == 0) {
    if (*(code **)(puVar29 + 0x88) != (code *)0x0) {
      (**(code **)(puVar29 + 0x88))(puVar29[0x8a]);
    }
    lVar54 = *(longlong *)(puVar29 + 0x84);
    if (lVar54 != 0) goto LAB_18002e068;
    puVar45 = puVar29 + 0x8e;
  }
  else {
LAB_18002e068:
    uVar24 = puVar29[0x8c];
    if (uVar24 == 0) {
      puVar45 = puVar29 + 0x8e;
    }
    else {
      if ((int)uVar24 < 0x29) {
        iVar22 = uVar24 - 1;
      }
      puVar45 = (uint *)(lVar54 + (longlong)iVar22 * 0x98);
    }
  }
  *(uint **)(&stack0x000000c8 + lVar15) = puVar45;
  *(undefined2 *)((longlong)puVar45 + 0x1a) = 0x38;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x29);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 2;
  *(longlong **)(&stack0x000002c0 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x2a);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 3;
  *(longlong **)(&stack0x000002c8 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x2b);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 4;
  *(longlong **)(&stack0x00000310 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x2c);
  *(longlong **)(&stack0x00000308 + lVar15) = plVar31;
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 5;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x2d);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 6;
  *(longlong **)(&stack0x000002b0 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x2e);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 7;
  *(longlong **)(&stack0x000002b8 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x2f);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 8;
  *(longlong **)(&stack0x00000300 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x30);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 9;
  *(longlong **)(&stack0x000002f8 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x31);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 10;
  *(longlong **)(&stack0x00000330 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x32);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 0xb;
  *(longlong **)(&stack0x00000328 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x33);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 0xc;
  *(longlong **)(&stack0x00000188 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x34);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 0xd;
  *(longlong **)(&stack0x00000208 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x35);
  *(longlong **)(&stack0x000001a0 + lVar15) = plVar31;
  *(undefined2 *)((longlong)plVar31 + 0x1a) = *(undefined2 *)(&stack0xffffffffffffffd8 + lVar15);
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x36);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 0xf;
  *(longlong **)(&stack0x00000198 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x37);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 0x10;
  *(longlong **)(&stack0x000000e8 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x38);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 0x11;
  *(longlong **)(&stack0x00000270 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x39);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 0x12;
  *(longlong **)(&stack0x00000278 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x3a);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 0x13;
  *(longlong **)(&stack0x000002a8 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x3b);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 0x14;
  *(longlong **)(&stack0x000002a0 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x3c);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 0x18;
  *(longlong **)(&stack0x00000128 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x3d);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 0x19;
  *(longlong **)(&stack0x000001a8 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x3e);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 0x1a;
  *(longlong **)(&stack0x00000138 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x3f);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 0x15;
  *(longlong **)(&stack0x00000268 + lVar15) = plVar31;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x40);
  *(longlong **)(&stack0x00000190 + lVar15) = plVar31;
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 0x16;
  plVar31 = FUN_180029980((longlong *)(puVar29 + 0x84),0x41);
  *(undefined2 *)((longlong)plVar31 + 0x1a) = 0x17;
  *(undefined ***)(&stack0x00000490 + lVar15) = CustomInput<>::vftable;
  *(longlong **)(&stack0x00000320 + lVar15) = plVar31;
  *(undefined ***)(&stack0x00000490 + lVar15) = LineStyles::vftable;
  *(undefined ***)(&stack0x000004c0 + lVar15) = CustomInput<>::vftable;
  *(undefined ***)(&stack0x000004c0 + lVar15) = ErasureModes::vftable;
  *(undefined ***)(&stack0x000002d8 + lVar15) = CustomInput<>::vftable;
  *(undefined ***)(&stack0x000002d8 + lVar15) = PitSessions::vftable;
  *(undefined ***)(&stack0x00000488 + lVar15) = CustomInput<>::vftable;
  *(undefined ***)(&stack0x00000488 + lVar15) = PricePositions::vftable;
  *(undefined ***)(&stack0x00000480 + lVar15) = CustomInput<>::vftable;
  *(undefined ***)(&stack0x00000480 + lVar15) = OverlapModes::vftable;
  *(undefined ***)(&stack0x00000478 + lVar15) = CustomInput<>::vftable;
  *(undefined ***)(&stack0x00000478 + lVar15) = ShowZonePrices::vftable;
  uVar30 = (**(code **)(puVar29 + 0x62e))(1);
  pcVar46 = *(code **)(puVar29 + 0x62e);
  *(undefined8 *)(&stack0x000000f8 + lVar15) = uVar30;
  uVar30 = (*pcVar46)(2);
  pcVar46 = *(code **)(puVar29 + 0x62e);
  *(undefined8 *)(&stack0x00000158 + lVar15) = uVar30;
  uVar30 = (*pcVar46)(3);
  pcVar46 = *(code **)(puVar29 + 0x62e);
  *(undefined8 *)(&stack0x00000240 + lVar15) = uVar30;
  uVar30 = (*pcVar46)(4);
  pcVar46 = *(code **)(puVar29 + 0x62e);
  *(undefined8 *)(&stack0x00000298 + lVar15) = uVar30;
  uVar30 = (*pcVar46)(5);
  pcVar46 = *(code **)(puVar29 + 0x62e);
  *(undefined8 *)(&stack0x00000288 + lVar15) = uVar30;
  uVar30 = (*pcVar46)(6);
  pcVar46 = *(code **)(puVar29 + 0x62e);
  *(undefined8 *)(&stack0x00000150 + lVar15) = uVar30;
  uVar30 = (*pcVar46)(7);
  pcVar46 = *(code **)(puVar29 + 0x62e);
  *(undefined8 *)(&stack0x00000260 + lVar15) = uVar30;
  uVar30 = (*pcVar46)(8);
  pcVar46 = *(code **)(puVar29 + 0x62e);
  *(undefined8 *)(&stack0x00000290 + lVar15) = uVar30;
  uVar30 = (*pcVar46)(9);
  pcVar46 = *(code **)(puVar29 + 0x62e);
  *(undefined8 *)(&stack0x00000318 + lVar15) = uVar30;
  uVar30 = (*pcVar46)(10);
  lVar55 = *(longlong *)(&stack0xffffffffffffffe8 + lVar15);
  *(undefined8 *)(&stack0x00000280 + lVar15) = uVar30;
  lVar54 = *(longlong *)(lVar55 + 0x80);
  if (lVar54 == 0) {
    if (*(code **)(lVar55 + 0x90) != (code *)0x0) {
      (**(code **)(lVar55 + 0x90))(*(undefined4 *)(lVar55 + 0x98));
    }
    lVar54 = *(longlong *)(*(longlong *)(&stack0xffffffffffffffe8 + lVar15) + 0x80);
    if (lVar54 != 0) goto LAB_18002e454;
    lVar54 = *(longlong *)(&stack0x00000068 + lVar15);
    plVar31 = (longlong *)(*(longlong *)(&stack0xffffffffffffffe8 + lVar15) + 0xa8);
    (&lStack_8)[extraout_x15 * -2] = (longlong)plVar31;
    plVar34 = (longlong *)(lVar54 + 0x80);
LAB_18002e490:
    if ((code *)plVar34[2] != (code *)0x0) {
      (*(code *)plVar34[2])((int)plVar34[3]);
    }
    lVar54 = *plVar34;
    if (lVar54 != 0) goto LAB_18002e4c8;
    plVar34 = plVar34 + 5;
    *(longlong **)(&stack0x00000010 + lVar15) = plVar34;
    *(longlong **)(&stack0x000000c0 + lVar15) = plVar34;
    *(longlong **)(&stack0x00000070 + lVar15) = plVar34;
    plVar34 = (longlong *)(*(longlong *)(&stack0x00000068 + lVar15) + 0x80);
LAB_18002e508:
    if ((code *)plVar34[2] != (code *)0x0) {
      (*(code *)plVar34[2])((int)plVar34[3]);
    }
    lVar54 = *plVar34;
    if (lVar54 != 0) goto LAB_18002e53c;
    *(longlong **)(&stack0x00000000 + lVar15) = plVar34 + 5;
    *(longlong **)(&stack0x00000028 + lVar15) = plVar34 + 5;
    pfVar64 = (float *)(*(longlong *)(&stack0x00000068 + lVar15) + 0x80);
LAB_18002e57c:
    if (*(code **)(pfVar64 + 4) != (code *)0x0) {
      (**(code **)(pfVar64 + 4))(pfVar64[6]);
    }
    lVar54 = *(longlong *)pfVar64;
    if (lVar54 != 0) goto LAB_18002e59c;
    pfVar47 = pfVar64 + 10;
  }
  else {
LAB_18002e454:
    iVar22 = *(int *)(*(longlong *)(&stack0xffffffffffffffe8 + lVar15) + 0xa0);
    if (iVar22 == 0) {
      plVar31 = (longlong *)(*(longlong *)(&stack0xffffffffffffffe8 + lVar15) + 0xa8);
    }
    else {
      iVar23 = 0;
      if (iVar22 < 1) {
        iVar23 = iVar22 + -1;
      }
      plVar31 = (longlong *)(lVar54 + (longlong)iVar23 * 0x28);
    }
    lVar55 = *(longlong *)(&stack0xffffffffffffffe8 + lVar15);
    (&lStack_8)[extraout_x15 * -2] = (longlong)plVar31;
    plVar34 = (longlong *)(lVar55 + 0x80);
    if (lVar54 == 0) goto LAB_18002e490;
LAB_18002e4c8:
    iVar22 = (int)plVar34[4];
    if (iVar22 == 0) {
      plVar34 = plVar34 + 5;
    }
    else {
      iVar23 = 1;
      if (iVar22 + -1 == 0 || iVar22 < 1) {
        iVar23 = iVar22 + -1;
      }
      plVar34 = (longlong *)(lVar54 + (longlong)iVar23 * 0x28);
    }
    *(longlong **)(&stack0x00000010 + lVar15) = plVar34;
    *(longlong **)(&stack0x000000c0 + lVar15) = plVar34;
    *(longlong **)(&stack0x00000070 + lVar15) = plVar34;
    plVar34 = (longlong *)(*(longlong *)(&stack0xffffffffffffffe8 + lVar15) + 0x80);
    if (lVar54 == 0) goto LAB_18002e508;
LAB_18002e53c:
    iVar22 = (int)plVar34[4];
    if (iVar22 == 0) {
      plVar34 = plVar34 + 5;
    }
    else {
      iVar23 = 2;
      if (iVar22 < 3) {
        iVar23 = iVar22 + -1;
      }
      plVar34 = (longlong *)(lVar54 + (longlong)iVar23 * 0x28);
    }
    *(longlong **)(&stack0x00000000 + lVar15) = plVar34;
    *(longlong **)(&stack0x00000028 + lVar15) = plVar34;
    pfVar64 = (float *)(*(longlong *)(&stack0xffffffffffffffe8 + lVar15) + 0x80);
    if (lVar54 == 0) goto LAB_18002e57c;
LAB_18002e59c:
    fVar65 = pfVar64[8];
    if (fVar65 == 0.0) {
      pfVar47 = pfVar64 + 10;
    }
    else {
      iVar22 = 3;
      if ((int)fVar65 < 4) {
        iVar22 = (int)fVar65 - 1;
      }
      pfVar47 = (float *)(lVar54 + (longlong)iVar22 * 0x28);
    }
  }
  *(float **)(&stack0x00000068 + lVar15) = pfVar47;
  puVar32 = (undefined8 *)(**(code **)(puVar29 + 0x446))(0);
  *(undefined8 *)(&stack0x000000d8 + lVar15) = *puVar32;
  if (puVar29[0x2b] != 0) {
    iVar22 = FUN_1800045e8("Buy and Sell Zones");
    FUN_1800079f8((longlong *)(puVar29 + 0x46),extraout_x10,iVar22);
    puVar29[0x49] = 1;
    if ((*(char **)(puVar29 + 0xce) != (char *)0x0) && (**(char **)(puVar29 + 0xce) != '\0')) {
      iVar22 = FUN_1800045e8("");
      FUN_1800079f8((longlong *)(puVar29 + 0xce),extraout_x1_00,iVar22);
      puVar29[0xd1] = 1;
    }
    FUN_180004498((undefined8 *)(&stack0x00000450 + lVar15),"<div align=\"center\">");
    FUN_1800263b0((undefined8 *)(puVar29 + 0xce),(longlong *)(&stack0x00000450 + lVar15));
    if ((*(int *)(&stack0x00000458 + lVar15) != 0) &&
       (pvVar60 = *(LPVOID *)(&stack0x00000450 + lVar15), pvVar60 != (LPVOID)0x0)) {
      pvVar33 = GetProcessHeap();
      HeapFree(pvVar33,0,pvVar60);
      *(undefined8 *)(&stack0x00000450 + lVar15) = 0;
      *(undefined8 *)(&stack0x00000458 + lVar15) = 0;
    }
    FUN_180004498((undefined8 *)(&stack0x00000450 + lVar15),
                  "<a href=\"https://imgur.com/zL2LDnw\"><img src=\"https://i.imgur.com/zL2LDnw.png\ " title=\"OrderFlow Labs, orderflowlabs.com\" height=\"100\" /></a>"
                 );
    FUN_1800263b0((undefined8 *)(puVar29 + 0xce),(longlong *)(&stack0x00000450 + lVar15));
    if ((*(int *)(&stack0x00000458 + lVar15) != 0) &&
       (pvVar60 = *(LPVOID *)(&stack0x00000450 + lVar15), pvVar60 != (LPVOID)0x0)) {
      pvVar33 = GetProcessHeap();
      HeapFree(pvVar33,0,pvVar60);
      *(undefined8 *)(&stack0x00000450 + lVar15) = 0;
      *(undefined8 *)(&stack0x00000458 + lVar15) = 0;
    }
    FUN_180004498((undefined8 *)(&stack0x00000450 + lVar15),
                  "<p><b>Buy and Sell Zones</b> displays zones where Buy and Sell orders maybe be of  interest.</p>"
                 );
    FUN_1800263b0((undefined8 *)(puVar29 + 0xce),(longlong *)(&stack0x00000450 + lVar15));
    if ((*(int *)(&stack0x00000458 + lVar15) != 0) &&
       (pvVar60 = *(LPVOID *)(&stack0x00000450 + lVar15), pvVar60 != (LPVOID)0x0)) {
      pvVar33 = GetProcessHeap();
      HeapFree(pvVar33,0,pvVar60);
      *(undefined8 *)(&stack0x00000450 + lVar15) = 0;
      *(undefined8 *)(&stack0x00000458 + lVar15) = 0;
    }
    FUN_180004498((undefined8 *)(&stack0x00000450 + lVar15),"</div>");
    FUN_1800263b0((undefined8 *)(puVar29 + 0xce),(longlong *)(&stack0x00000450 + lVar15));
    if ((*(int *)(&stack0x00000458 + lVar15) != 0) &&
       (pvVar60 = *(LPVOID *)(&stack0x00000450 + lVar15), pvVar60 != (LPVOID)0x0)) {
      pvVar33 = GetProcessHeap();
      HeapFree(pvVar33,0,pvVar60);
      *(undefined8 *)(&stack0x00000450 + lVar15) = 0;
      *(undefined8 *)(&stack0x00000458 + lVar15) = 0;
    }
    puVar29[0xdf] = 0;
    lVar54 = *(longlong *)(&stack0x00000018 + lVar15);
    puVar29[4] = 0;
    puVar29[0xcc] = 0x28;
    puVar29[0x32e] = 1;
    *(undefined4 *)(lVar54 + 0x24) = 0x20005;
    *(undefined8 *)(lVar54 + 0x18) = 0xff00ff00ff;
    *(undefined4 *)(lVar54 + 0x20) = 1;
    *(undefined2 *)(lVar54 + 0x28) = 3;
    *(undefined4 *)(lVar54 + 0xd8) = 0;
    lVar54 = *(longlong *)(&stack0x000000a8 + lVar15);
    *(undefined4 *)(lVar54 + 0x20) = 1;
    *(undefined8 *)(lVar54 + 0x18) = 0xffffff00ffffff;
    *(undefined2 *)(lVar54 + 0x28) = 7;
    *(undefined2 *)(lVar54 + 0x24) = 0x17;
    *(undefined2 *)(*plVar59 + 0x24) = 5;
    *(undefined2 *)(*(longlong *)(&stack0x00000218 + lVar15) + 0x24) = 5;
    *(undefined2 *)(*(longlong *)(&stack0x00000220 + lVar15) + 0x24) = 5;
    lVar54 = *(longlong *)(&stack0x000001b0 + lVar15);
    *(undefined2 *)(lVar54 + 0x24) = 5;
    *(undefined4 *)(lVar54 + 0x20) = 1;
    lVar54 = *(longlong *)(&stack0x00000228 + lVar15);
    *(undefined2 *)(lVar54 + 0x24) = 5;
    *(undefined4 *)(lVar54 + 0x18) = 0xffff00;
    *(undefined2 *)(lVar54 + 0x28) = 1;
    *(undefined4 *)(lVar54 + 0xd8) = 0;
    lVar54 = *(longlong *)(&stack0x000000a0 + lVar15);
    *(undefined2 *)(lVar54 + 0x24) = 5;
    *(undefined4 *)(lVar54 + 0x20) = 1;
    *(undefined8 *)(lVar54 + 0x18) = 0x7dff7d007d7dff;
    *(undefined2 *)(lVar54 + 0x28) = 1;
    *(undefined4 *)(lVar54 + 0xd8) = 0;
    lVar54 = *(longlong *)(&stack0x00000098 + lVar15);
    *(undefined2 *)(lVar54 + 0x24) = 5;
    *(undefined4 *)(lVar54 + 0x20) = 1;
    *(undefined8 *)(lVar54 + 0x18) = 0x7dff7d007d7dff;
    lVar55 = *(longlong *)(&stack0x00000080 + lVar15);
    *(undefined2 *)(lVar54 + 0x28) = 1;
    *(undefined4 *)(lVar54 + 0xd8) = 0;
    *(undefined2 *)(lVar55 + 0x24) = 0;
    *(undefined8 *)(lVar55 + 0x18) = 0xff008000ff8080;
    lVar54 = *(longlong *)(&stack0x00000230 + lVar15);
    *(undefined4 *)(lVar55 + 0x20) = 1;
    *(undefined2 *)(lVar55 + 0x28) = 1;
    *(undefined4 *)(lVar55 + 0xd8) = 0;
    *(undefined2 *)(lVar54 + 0x24) = 5;
    *(undefined8 *)(lVar54 + 0x18) = 0xc80000c800;
    *(undefined1 *)(lVar54 + 0xd2) = 2;
    *(undefined2 *)(lVar54 + 0x28) = 3;
    *(undefined4 *)(lVar54 + 0xd8) = 0;
    iVar22 = FUN_1800045e8("Dynamic Rotation Value");
    plVar59 = *(longlong **)(&stack0x00000140 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_00 + 0x30,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined2 *)((longlong)plVar59 + 0x24) = 5;
    *(undefined4 *)(plVar59 + 3) = 0x221f0;
    *(undefined2 *)(plVar59 + 5) = 1;
    *(undefined4 *)(plVar59 + 0x1b) = 1;
    lVar54 = *(longlong *)(&stack0x000000b0 + lVar15);
    *(undefined1 *)(puVar37 + 6) = 0xb;
    puVar37[7] = 4;
    *(undefined1 *)(lVar54 + 0x18) = 1;
    *(undefined4 *)(lVar54 + 0x1c) = 1;
    *(undefined2 *)(lVar54 + 0x1a) = 1;
    lVar54 = *(longlong *)(&stack0xfffffffffffffff0 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 1;
    *(undefined4 *)(lVar54 + 0x1c) = 2;
    *(undefined2 *)(lVar54 + 0x1a) = 2;
    lVar54 = *(longlong *)(&stack0x00000108 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 0xb;
    *(undefined4 *)(lVar54 + 0x1c) = 3;
    *(undefined4 *)(lVar54 + 0x2c) = 1;
    *(undefined4 *)(lVar54 + 0x3c) = 3;
    *(undefined2 *)(lVar54 + 0x1a) = 3;
    lVar54 = *(longlong *)(&stack0x00000118 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 2;
    *(undefined4 *)(lVar54 + 0x1c) = 0x3f99999a;
    *(undefined2 *)(lVar54 + 0x1a) = 4;
    iVar22 = FUN_1800045e8("Rotation (point value)");
    plVar59 = *(longlong **)(&stack0x00000100 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_01 + 0x18,iVar22);
    lVar54 = *(longlong *)(&stack0x00000110 + lVar15);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = 2;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0x420c0000;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 5;
    *(undefined1 *)(lVar54 + 0x18) = 0xb;
    lVar55 = *(longlong *)(&stack0x00000008 + lVar15);
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 6;
    pcVar46 = *(code **)(lVar55 + 0x50);
    if (pcVar46 != (code *)0x0) {
      (*pcVar46)(*(undefined4 *)(lVar55 + 0x4c),
                 "None;Total Volume;Bid Volume;Ask Volume;Ask Bid Volume Difference");
    }
    lVar54 = *(longlong *)(&stack0x00000008 + lVar15);
    lVar55 = *(longlong *)(&stack0x00000170 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 0x16;
    *(undefined4 *)(lVar54 + 0x1c) = 4;
    *(undefined2 *)(lVar54 + 0x1a) = 7;
    lVar54 = *(longlong *)(&stack0x00000060 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 8;
    lVar54 = *(longlong *)(&stack0x000000e0 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 9;
    lVar54 = *(longlong *)(&stack0x00000088 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 10;
    lVar54 = *(longlong *)(&stack0x00000210 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 0xb;
    lVar54 = *(longlong *)(&stack0x000001f0 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 0xc;
    pcVar46 = *(code **)(lVar55 + 0x50);
    if (pcVar46 != (code *)0x0) {
      (*pcVar46)(*(undefined4 *)(lVar55 + 0x4c),"None;Points;Ticks;Percent");
    }
    lVar54 = *(longlong *)(&stack0x00000170 + lVar15);
    uVar11 = *(undefined2 *)(&stack0xffffffffffffffd8 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 0x16;
    *(undefined4 *)(lVar54 + 0x1c) = 2;
    *(undefined2 *)(lVar54 + 0x1a) = 0xd;
    lVar54 = *(longlong *)(&stack0x00000168 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = uVar11;
    lVar54 = *(longlong *)(&stack0x00000160 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 0xf;
    lVar54 = *(longlong *)(&stack0x000001e8 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 0x10;
    lVar54 = *(longlong *)(&stack0x000001e0 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 1;
    *(undefined2 *)(lVar54 + 0x1a) = 0x11;
    lVar54 = *(longlong *)(&stack0x00000258 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 0x12;
    lVar54 = *(longlong *)(&stack0x00000250 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 0x13;
    lVar54 = *(longlong *)(&stack0x00000248 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 0x14;
    lVar54 = *(longlong *)(&stack0x00000180 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 1;
    *(undefined2 *)(lVar54 + 0x1a) = 0x15;
    lVar54 = *(longlong *)(&stack0x00000120 + lVar15);
    lVar55 = *(longlong *)(&stack0x00000130 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 1;
    *(undefined2 *)(lVar54 + 0x1a) = 0x16;
    pcVar46 = *(code **)(lVar55 + 0x50);
    if (pcVar46 != (code *)0x0) {
      (*pcVar46)(*(undefined4 *)(lVar55 + 0x4c),"Percentage Of Bar;Tick Offset");
    }
    lVar54 = *(longlong *)(&stack0x00000130 + lVar15);
    lVar55 = *(longlong *)(&stack0xffffffffffffffe0 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 0x16;
    *(undefined4 *)(lVar54 + 0x1c) = 1;
    *(undefined2 *)(lVar54 + 0x1a) = 0x17;
    lVar54 = *(longlong *)(&stack0x00000178 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 2;
    *(undefined4 *)(lVar54 + 0x1c) = 0x3f800000;
    *(undefined2 *)(lVar54 + 0x1a) = 0x18;
    pcVar46 = *(code **)(lVar55 + 0x50);
    if (pcVar46 != (code *)0x0) {
      (*pcVar46)(*(undefined4 *)(lVar55 + 0x4c),"None;Slope;Trend Confirmed Volume;Confirmed Volume"
                );
    }
    lVar54 = *(longlong *)(&stack0xffffffffffffffe0 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 0x16;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 0x19;
    lVar54 = *(longlong *)(&stack0x000001f8 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 0x1a;
    lVar54 = *(longlong *)(&stack0x00000200 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 5;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 0x1b;
    pcVar46 = *(code **)(*(longlong *)(&stack0x000000d0 + lVar15) + 0x50);
    if (pcVar46 != (code *)0x0) {
      (*pcVar46)(*(undefined4 *)(*(longlong *)(&stack0x000000d0 + lVar15) + 0x4c),
                 "Left;Center;Right");
    }
    lVar54 = *(longlong *)(&stack0x000000d0 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 0x16;
    *(undefined4 *)(lVar54 + 0x1c) = 1;
    *(undefined2 *)(lVar54 + 0x1a) = 0x1c;
    pcVar46 = *(code **)(*(longlong *)(&stack0x000000c8 + lVar15) + 0x50);
    if (pcVar46 != (code *)0x0) {
      (*pcVar46)(*(undefined4 *)(*(longlong *)(&stack0x000000c8 + lVar15) + 0x4c),
                 "Left;Center;Right");
    }
    lVar54 = *(longlong *)(&stack0x000000c8 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 0x16;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 0x1d;
    iVar22 = FUN_1800045e8("Sell Zone 1 Percent");
    plVar59 = *(longlong **)(&stack0x000002c0 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_02 + 0x110,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = 2;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0x425c0000;
    *(undefined4 *)((longlong)plVar59 + 0x2c) = 0x3a03126f;
    *(undefined4 *)((longlong)plVar59 + 0x3c) = 0x42c60000;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 0x1e;
    iVar22 = FUN_1800045e8("Sell Zone 2 Percent");
    plVar59 = *(longlong **)(&stack0x000002c8 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_03 + 0x150,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = 2;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0x42820000;
    *(undefined4 *)((longlong)plVar59 + 0x2c) = 0x3a03126f;
    *(undefined4 *)((longlong)plVar59 + 0x3c) = 0x42c60000;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 0x1f;
    iVar22 = FUN_1800045e8("Sell Zone Color");
    plVar59 = *(longlong **)(&stack0x00000310 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_04 + 0x140,iVar22);
    uVar9 = (&stack0xffffffffffffffd8)[lVar15];
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = uVar9;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0x404080;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 0x20;
    iVar22 = FUN_1800045e8("Sell Zone Border Color");
    plVar59 = *(longlong **)(&stack0x00000308 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_05 + 0x180,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = uVar9;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0x404080;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 0x21;
    iVar22 = FUN_1800045e8("Buy Zone 1 Percent");
    plVar59 = *(longlong **)(&stack0x000002b0 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_06 + 0x168,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = 2;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0x425c0000;
    *(undefined4 *)((longlong)plVar59 + 0x2c) = 0x3a03126f;
    *(undefined4 *)((longlong)plVar59 + 0x3c) = 0x42c60000;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 0x22;
    iVar22 = FUN_1800045e8("Buy Zone 2 Percent");
    plVar59 = *(longlong **)(&stack0x000002b8 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_07 + 0x1a8,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = 2;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0x42820000;
    *(undefined4 *)((longlong)plVar59 + 0x2c) = 0x3a03126f;
    *(undefined4 *)((longlong)plVar59 + 0x3c) = 0x42c60000;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 0x23;
    iVar22 = FUN_1800045e8("Buy Zone Color");
    plVar59 = *(longlong **)(&stack0x00000300 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_08 + 0x198,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = uVar9;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0xab735b;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 0x24;
    iVar22 = FUN_1800045e8("Buy Zone Border Color");
    plVar59 = *(longlong **)(&stack0x000002f8 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_09 + 0x1e0,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = uVar9;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0x694536;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 0x25;
    iVar22 = FUN_1800045e8("Buy/Sell Zone Border Width");
    plVar59 = *(longlong **)(&stack0x00000330 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_10 + 0x1c0,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 1;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 0x26;
    iVar22 = FUN_1800045e8("Transparency Level");
    plVar59 = *(longlong **)(&stack0x00000328 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_11 + 0x210,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0x59;
    *(undefined4 *)((longlong)plVar59 + 0x2c) = 0;
    *(undefined4 *)((longlong)plVar59 + 0x3c) = 100;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 0x27;
    iVar22 = FUN_1800045e8("Drawing Erasure Mode");
    plVar59 = *(longlong **)(&stack0x00000188 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_12 + 0x1f8,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    puVar32 = FUN_180029680((longlong *)(&stack0x000004c0 + lVar15),
                            (undefined8 *)(&stack0x000003a0 + lVar15));
    if (0xf < (ulonglong)puVar32[3]) {
      puVar32 = (undefined8 *)*puVar32;
    }
    lVar54 = *(longlong *)(&stack0x00000188 + lVar15);
    auVar67 = extraout_q0_00;
    if (*(code **)(lVar54 + 0x50) != (code *)0x0) {
      auVar67 = (**(code **)(lVar54 + 0x50))(*(undefined4 *)(lVar54 + 0x4c),puVar32);
      *(undefined1 *)(lVar54 + 0x18) = 0x16;
    }
    if (0xf < *(ulonglong *)(&stack0x000003b8 + lVar15)) {
      auVar67 = FUN_1800966b8(*(LPVOID *)(&stack0x000003a0 + lVar15));
    }
    lVar54 = *(longlong *)(&stack0x00000188 + lVar15);
    (&stack0x000003a0)[lVar15] = 0;
    *(undefined8 *)(&stack0x000003b0 + lVar15) = 0;
    *(undefined8 *)(&stack0x000003b8 + lVar15) = 0xf;
    *(undefined1 *)(lVar54 + 0x18) = 0x16;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    *(undefined2 *)(lVar54 + 0x1a) = 0x28;
    *(undefined8 *)(&stack0x00000458 + lVar15) = 0;
    *(undefined8 *)(&stack0x00000450 + lVar15) = 0;
    *(undefined8 *)(&stack0x00000460 + lVar15) = 0;
    *(undefined1 **)(&stack0x00000460 + lVar15) = &DAT_1800d4ecd;
    *(undefined1 **)(&stack0x00000450 + lVar15) = &DAT_1800d4ecd;
    *(undefined8 *)(&stack0x00000458 + lVar15) = 0;
    FUN_180006050(auVar67,param_2,(undefined8 *)(&stack0x00000450 + lVar15),0x1800d7248,100,param_6,
                  param_7,param_8,param_9,param_10);
    plVar59 = *(longlong **)(&stack0x00000208 + lVar15);
    FUN_180007920(plVar59,(longlong *)(&stack0x00000450 + lVar15));
    *(undefined1 *)(plVar59 + 3) = 0xb;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0x14;
    *(undefined4 *)((longlong)plVar59 + 0x2c) = 0;
    *(undefined4 *)((longlong)plVar59 + 0x3c) = 100;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 0x29;
    iVar22 = FUN_1800045e8("Show Buy/Sell Zone Prices");
    plVar59 = *(longlong **)(&stack0x000001a0 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_13 + 0x228,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    puVar32 = FUN_180029680((longlong *)(&stack0x00000478 + lVar15),
                            (undefined8 *)(&stack0x000003c0 + lVar15));
    if (0xf < (ulonglong)puVar32[3]) {
      puVar32 = (undefined8 *)*puVar32;
    }
    lVar54 = *(longlong *)(&stack0x000001a0 + lVar15);
    if (*(code **)(lVar54 + 0x50) != (code *)0x0) {
      (**(code **)(lVar54 + 0x50))(*(undefined4 *)(lVar54 + 0x4c),puVar32);
      *(undefined1 *)(lVar54 + 0x18) = 0x16;
    }
    if (0xf < *(ulonglong *)(&stack0x000003d8 + lVar15)) {
      FUN_1800966b8(*(LPVOID *)(&stack0x000003c0 + lVar15));
    }
    (&stack0x000003c0)[lVar15] = 0;
    *(undefined8 *)(&stack0x000003d0 + lVar15) = 0;
    *(undefined8 *)(&stack0x000003d8 + lVar15) = 0xf;
    lVar54 = *(longlong *)(&stack0x000001a0 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 0x16;
    *(undefined4 *)(lVar54 + 0x1c) = 3;
    *(undefined2 *)(lVar54 + 0x1a) = 0x2a;
    iVar22 = FUN_1800045e8("Buy/Sell Zone Price Position");
    plVar59 = *(longlong **)(&stack0x00000198 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_14 + 0x298,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    puVar32 = FUN_180029680((longlong *)(&stack0x00000488 + lVar15),
                            (undefined8 *)(&stack0x000003e0 + lVar15));
    if (0xf < (ulonglong)puVar32[3]) {
      puVar32 = (undefined8 *)*puVar32;
    }
    lVar54 = *(longlong *)(&stack0x00000198 + lVar15);
    if (*(code **)(lVar54 + 0x50) != (code *)0x0) {
      (**(code **)(lVar54 + 0x50))(*(undefined4 *)(lVar54 + 0x4c),puVar32);
      *(undefined1 *)(lVar54 + 0x18) = 0x16;
    }
    if (0xf < *(ulonglong *)(&stack0x000003f8 + lVar15)) {
      FUN_1800966b8(*(LPVOID *)(&stack0x000003e0 + lVar15));
    }
    (&stack0x000003e0)[lVar15] = 0;
    *(undefined8 *)(&stack0x000003f0 + lVar15) = 0;
    *(undefined8 *)(&stack0x000003f8 + lVar15) = 0xf;
    lVar54 = *(longlong *)(&stack0x00000198 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 0x16;
    *(undefined4 *)(lVar54 + 0x1c) = 1;
    *(undefined2 *)(lVar54 + 0x1a) = 0x2b;
    iVar22 = FUN_1800045e8("Buy/Sell Drawing Overlap Mode");
    plVar59 = *(longlong **)(&stack0x000000e8 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_15 + 0x278,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    puVar32 = FUN_180029680((longlong *)(&stack0x00000480 + lVar15),
                            (undefined8 *)(&stack0x00000380 + lVar15));
    if (0xf < (ulonglong)puVar32[3]) {
      puVar32 = (undefined8 *)*puVar32;
    }
    lVar54 = *(longlong *)(&stack0x000000e8 + lVar15);
    if (*(code **)(lVar54 + 0x50) != (code *)0x0) {
      (**(code **)(lVar54 + 0x50))(*(undefined4 *)(lVar54 + 0x4c),puVar32);
      *(undefined1 *)(lVar54 + 0x18) = 0x16;
    }
    if (0xf < *(ulonglong *)(&stack0x00000398 + lVar15)) {
      FUN_1800966b8(*(LPVOID *)(&stack0x00000380 + lVar15));
    }
    (&stack0x00000380)[lVar15] = 0;
    *(undefined8 *)(&stack0x00000390 + lVar15) = 0;
    *(undefined8 *)(&stack0x00000398 + lVar15) = 0xf;
    lVar54 = *(longlong *)(&stack0x000000e8 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 0x16;
    *(undefined4 *)(lVar54 + 0x1c) = 2;
    *(undefined2 *)(lVar54 + 0x1a) = 0x2c;
    iVar22 = FUN_1800045e8("Sell Zone Color Touched");
    plVar59 = *(longlong **)(&stack0x00000270 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_16 + 0x2d8,iVar22);
    uVar9 = (&stack0xffffffffffffffd8)[lVar15];
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = uVar9;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0x413934;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 0x2d;
    iVar22 = FUN_1800045e8("Sell Zone Border Color Touched");
    plVar59 = *(longlong **)(&stack0x00000278 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_17 + 0x2b8,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = uVar9;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0x1a1a5e;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 0x2e;
    iVar22 = FUN_1800045e8("Buy Zone Color Touched");
    plVar59 = *(longlong **)(&stack0x000002a8 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_18 + 0x310,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = uVar9;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0x413934;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 0x2f;
    iVar22 = FUN_1800045e8("Buy Zone Border Color Touched");
    plVar59 = *(longlong **)(&stack0x000002a0 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_19 + 0x2f0,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = uVar9;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0x664435;
    *(undefined2 *)((longlong)plVar59 + 0x1a) = 0x30;
    iVar22 = FUN_1800045e8("Use Dynamic Rotation");
    plVar59 = *(longlong **)(&stack0x00000128 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_20 + 0x340,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = 5;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0;
    iVar22 = FUN_1800045e8("Dynamic Rotation Input");
    plVar59 = *(longlong **)(&stack0x000001a8 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_21 + 0x328,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    if ((code *)plVar59[10] != (code *)0x0) {
      (*(code *)plVar59[10])(*(undefined4 *)((longlong)plVar59 + 0x4c),"Average;Mode");
    }
    lVar54 = *(longlong *)(&stack0x000001a8 + lVar15);
    *(undefined1 *)(lVar54 + 0x18) = 0x16;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    iVar22 = FUN_1800045e8("Dynamic Rotation Chart Study");
    plVar59 = *(longlong **)(&stack0x00000138 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_22 + 0x358,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = 0x15;
    *(undefined8 *)((longlong)plVar59 + 0x1c) = 0;
    iVar22 = FUN_1800045e8("Draw Midline");
    plVar59 = *(longlong **)(&stack0x00000268 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_23 + 0x3a0,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = 5;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0;
    iVar22 = FUN_1800045e8("Border Line Style");
    plVar59 = *(longlong **)(&stack0x00000190 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_24 + 0x388,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    puVar32 = FUN_180029680((longlong *)(&stack0x00000490 + lVar15),
                            (undefined8 *)(&stack0x00000400 + lVar15));
    if (0xf < (ulonglong)puVar32[3]) {
      puVar32 = (undefined8 *)*puVar32;
    }
    lVar54 = *(longlong *)(&stack0x00000190 + lVar15);
    if (*(code **)(lVar54 + 0x50) != (code *)0x0) {
      (**(code **)(lVar54 + 0x50))(*(undefined4 *)(lVar54 + 0x4c),puVar32);
      *(undefined1 *)(lVar54 + 0x18) = 0x16;
    }
    if (0xf < *(ulonglong *)(&stack0x00000418 + lVar15)) {
      FUN_1800966b8(*(LPVOID *)(&stack0x00000400 + lVar15));
    }
    lVar54 = *(longlong *)(&stack0x00000190 + lVar15);
    (&stack0x00000400)[lVar15] = 0;
    *(undefined8 *)(&stack0x00000410 + lVar15) = 0;
    *(undefined8 *)(&stack0x00000418 + lVar15) = 0xf;
    *(undefined1 *)(lVar54 + 0x18) = 0x16;
    *(undefined4 *)(lVar54 + 0x1c) = 0;
    iVar22 = FUN_1800045e8("Double Extend");
    plVar59 = *(longlong **)(&stack0x00000320 + lVar15);
    FUN_1800079f8(plVar59,extraout_x10_25 + 0x3c0,iVar22);
    *(undefined4 *)((longlong)plVar59 + 0xc) = 1;
    *(undefined1 *)(plVar59 + 3) = 5;
    *(undefined4 *)((longlong)plVar59 + 0x1c) = 0;
    if ((*(int *)(&stack0x00000458 + lVar15) != 0) &&
       (pvVar60 = *(LPVOID *)(&stack0x00000450 + lVar15), pvVar60 != (LPVOID)0x0)) {
      pvVar33 = GetProcessHeap();
      HeapFree(pvVar33,0,pvVar60);
      *(undefined8 *)(&stack0x00000450 + lVar15) = 0;
      *(undefined8 *)(&stack0x00000458 + lVar15) = 0;
    }
    goto LAB_180033460;
  }
  if (puVar29[3] == 0) {
    uVar30 = FUN_1800254e8(extraout_q0,param_2,(longlong)puVar29,extraout_x1,param_5,param_6,param_7
                           ,param_8,param_9,param_10);
    **(undefined4 **)(&stack0x00000238 + lVar15) = (int)uVar30;
  }
  if (**(int **)(&stack0x00000238 + lVar15) != 0) goto LAB_180033460;
  if (puVar29[0x47e] != 0) {
    pfVar64 = *(float **)(&stack0x000000d8 + lVar15);
    if (pfVar64 != (float *)0x0) {
      lVar54 = 100;
      *plVar59 = (longlong)plVar31;
      *(uint **)(&stack0x000000f0 + lVar15) = puVar62;
      pfVar47 = pfVar64;
      do {
        pfVar64 = pfVar47 + 1;
        fVar65 = *pfVar47;
        if (fVar65 != 0.0) {
          FUN_1800bf500(&stack0x000010c0 + lVar15,'\0',0x3e0);
          FUN_180009f70((undefined8 *)(&stack0x000010c0 + lVar15));
          FUN_18000a310((longlong)(&stack0x000010c0 + lVar15));
          iVar22 = (**(code **)(puVar29 + 0x510))(puVar29[9],fVar65,&stack0x000010c0 + lVar15);
          if (iVar22 != 0) {
            iVar22 = (**(code **)(puVar29 + 0x364))(puVar29[9],fVar65);
            if (iVar22 != 0) {
              (**(code **)(puVar29 + 0x37e))(puVar29[9],fVar65);
            }
            *pfVar47 = 0.0;
          }
          FUN_18000ae00((longlong)(&stack0x000010c0 + lVar15));
        }
        lVar54 = lVar54 + -1;
        pfVar47 = pfVar64;
      } while (lVar54 != 0);
      puVar62 = *(uint **)(&stack0x000000f0 + lVar15);
      plVar31 = (longlong *)*plVar59;
      FUN_1800966b8(*(LPVOID *)(&stack0x000000d8 + lVar15));
      (**(code **)(puVar29 + 0x448))(0,0);
    }
    puVar32 = (undefined8 *)FUN_180096718(400);
    *(undefined8 **)(&stack0x000000d8 + lVar15) = puVar32;
    puVar32[1] = 0;
    *puVar32 = 0;
    puVar32[3] = 0;
    puVar32[2] = 0;
    puVar32[5] = 0;
    puVar32[4] = 0;
    puVar32[7] = 0;
    puVar32[6] = 0;
    puVar32[9] = 0;
    puVar32[8] = 0;
    puVar32[0xb] = 0;
    puVar32[10] = 0;
    puVar32[0xd] = 0;
    puVar32[0xc] = 0;
    puVar32[0xf] = 0;
    puVar32[0xe] = 0;
    puVar32[0x11] = 0;
    puVar32[0x10] = 0;
    puVar32[0x13] = 0;
    puVar32[0x12] = 0;
    puVar32[0x15] = 0;
    puVar32[0x14] = 0;
    puVar32[0x17] = 0;
    puVar32[0x16] = 0;
    puVar32[0x19] = 0;
    puVar32[0x18] = 0;
    puVar32[0x1b] = 0;
    puVar32[0x1a] = 0;
    puVar32[0x1d] = 0;
    puVar32[0x1c] = 0;
    puVar32[0x1f] = 0;
    puVar32[0x1e] = 0;
    puVar32[0x21] = 0;
    puVar32[0x20] = 0;
    puVar32[0x23] = 0;
    puVar32[0x22] = 0;
    puVar32[0x25] = 0;
    puVar32[0x24] = 0;
    puVar32[0x27] = 0;
    puVar32[0x26] = 0;
    puVar32[0x29] = 0;
    puVar32[0x28] = 0;
    puVar32[0x2b] = 0;
    puVar32[0x2a] = 0;
    puVar32[0x2d] = 0;
    puVar32[0x2c] = 0;
    puVar32[0x2f] = 0;
    puVar32[0x2e] = 0;
    puVar32[0x31] = 0;
    puVar32[0x30] = 0;
    (**(code **)(puVar29 + 0x448))(0,puVar32);
    puVar32[1] = 0;
    *puVar32 = 0;
    puVar32[3] = 0;
    puVar32[2] = 0;
    puVar32[5] = 0;
    puVar32[4] = 0;
    puVar32[7] = 0;
    puVar32[6] = 0;
    puVar32[9] = 0;
    puVar32[8] = 0;
    puVar32[0xb] = 0;
    puVar32[10] = 0;
    puVar32[0xd] = 0;
    puVar32[0xc] = 0;
    puVar32[0xf] = 0;
    puVar32[0xe] = 0;
    puVar32[0x11] = 0;
    puVar32[0x10] = 0;
    puVar32[0x13] = 0;
    puVar32[0x12] = 0;
    puVar32[0x15] = 0;
    puVar32[0x14] = 0;
    puVar32[0x17] = 0;
    puVar32[0x16] = 0;
    puVar32[0x19] = 0;
    puVar32[0x18] = 0;
    puVar32[0x1b] = 0;
    puVar32[0x1a] = 0;
    puVar32[0x1d] = 0;
    puVar32[0x1c] = 0;
    puVar32[0x1f] = 0;
    puVar32[0x1e] = 0;
    puVar32[0x21] = 0;
    puVar32[0x20] = 0;
    puVar32[0x23] = 0;
    puVar32[0x22] = 0;
    puVar32[0x25] = 0;
    puVar32[0x24] = 0;
    puVar32[0x27] = 0;
    puVar32[0x26] = 0;
    puVar32[0x29] = 0;
    puVar32[0x28] = 0;
    puVar32[0x2b] = 0;
    puVar32[0x2a] = 0;
    puVar32[0x2d] = 0;
    puVar32[0x2c] = 0;
    puVar32[0x2f] = 0;
    puVar32[0x2e] = 0;
    puVar32[0x31] = 0;
    puVar32[0x30] = 0;
  }
  if (puVar29[0x1b] != 0) {
    if (*(int **)(&stack0x000000d8 + lVar15) != (int *)0x0) {
      lVar54 = 100;
      piVar63 = *(int **)(&stack0x000000d8 + lVar15);
      do {
        iVar22 = *piVar63;
        if (iVar22 != 0) {
          FUN_1800bf500(&stack0x000014a0 + lVar15,'\0',0x3e0);
          FUN_180009f70((undefined8 *)(&stack0x000014a0 + lVar15));
          FUN_18000a310((longlong)(&stack0x000014a0 + lVar15));
          iVar23 = (**(code **)(puVar29 + 0x510))(puVar29[9],iVar22,&stack0x000014a0 + lVar15);
          if (iVar23 != 0) {
            iVar23 = (**(code **)(puVar29 + 0x364))(puVar29[9],iVar22);
            if (iVar23 != 0) {
              (**(code **)(puVar29 + 0x37e))(puVar29[9],iVar22);
            }
            *piVar63 = 0;
          }
          FUN_18000ae00((longlong)(&stack0x000014a0 + lVar15));
        }
        lVar54 = lVar54 + -1;
        piVar63 = piVar63 + 1;
      } while (lVar54 != 0);
      FUN_1800966b8(*(LPVOID *)(&stack0x000000d8 + lVar15));
      (**(code **)(puVar29 + 0x448))(0,0);
    }
    goto LAB_180033460;
  }
  piVar63 = *(int **)(&stack0x000000d8 + lVar15);
  if (piVar63 == (int *)0x0) goto LAB_180033460;
  iVar22 = (**(code **)(puVar29 + 0x3ec))(puVar29[0x270]);
  if (iVar22 == 0) {
    lVar54 = 100;
    do {
      iVar22 = *piVar63;
      if (iVar22 != 0) {
        FUN_1800bf500(&stack0x00001880 + lVar15,'\0',0x3e0);
        FUN_180009f70((undefined8 *)(&stack0x00001880 + lVar15));
        FUN_18000a310((longlong)(&stack0x00001880 + lVar15));
        iVar23 = (**(code **)(puVar29 + 0x510))(puVar29[9],iVar22,&stack0x00001880 + lVar15);
        if (iVar23 != 0) {
          iVar23 = (**(code **)(puVar29 + 0x364))(puVar29[9],iVar22);
          if (iVar23 != 0) {
            (**(code **)(puVar29 + 0x37e))(puVar29[9],iVar22);
          }
          *piVar63 = 0;
        }
        FUN_18000ae00((longlong)(&stack0x00001880 + lVar15));
      }
      lVar54 = lVar54 + -1;
      piVar63 = piVar63 + 1;
    } while (lVar54 != 0);
    goto LAB_180033460;
  }
  if (puVar29[3] == 0) {
    FUN_180026608(*(longlong *)(&stack0x000002c0 + lVar15));
    fVar65 = FUN_180026608(*(longlong *)(&stack0x000002c8 + lVar15));
    if (extraout_s18 == fVar65) {
      fVar65 = FUN_180026608(extraout_x12);
      *(undefined1 *)(extraout_x12_00 + 0x18) = 2;
      *(float *)(extraout_x12_00 + 0x1c) = fVar65 - 0.5;
      fVar65 = FUN_180026608(extraout_x11);
      *(undefined1 *)(extraout_x11_00 + 0x18) = 2;
      *(float *)(extraout_x11_00 + 0x1c) = fVar65 + 0.5;
    }
    else {
      FUN_180026608(extraout_x12);
      fVar65 = FUN_180026608(extraout_x11_01);
      if (fVar65 < extraout_s18_00) {
        FUN_180026608(extraout_x12_01);
        fVar65 = FUN_180026608(extraout_x11_02);
        *(undefined1 *)(extraout_x12_02 + 0x18) = 2;
        *(float *)(extraout_x12_02 + 0x1c) = fVar65;
        *(undefined1 *)(extraout_x11_03 + 0x18) = 2;
        *(undefined4 *)(extraout_x11_03 + 0x1c) = extraout_s18_01;
      }
    }
    FUN_180026608(*(longlong *)(&stack0x000002b0 + lVar15));
    fVar65 = FUN_180026608(*(longlong *)(&stack0x000002b8 + lVar15));
    if (extraout_s18_02 == fVar65) {
      fVar65 = FUN_180026608(extraout_x12_03);
      *(undefined1 *)(extraout_x12_04 + 0x18) = 2;
      *(float *)(extraout_x12_04 + 0x1c) = fVar65 - 0.5;
      fVar65 = FUN_180026608(extraout_x11_04);
      *(undefined1 *)(extraout_x11_05 + 0x18) = 2;
      *(float *)(extraout_x11_05 + 0x1c) = fVar65 + 0.5;
    }
    else {
      FUN_180026608(extraout_x12_03);
      fVar65 = FUN_180026608(extraout_x11_06);
      if (fVar65 < extraout_s18_03) {
        FUN_180026608(extraout_x12_05);
        fVar65 = FUN_180026608(extraout_x11_07);
        *(undefined1 *)(extraout_x12_06 + 0x18) = 2;
        *(float *)(extraout_x12_06 + 0x1c) = fVar65;
        *(undefined1 *)(extraout_x11_08 + 0x18) = 2;
        *(undefined4 *)(extraout_x11_08 + 0x1c) = extraout_s18_04;
      }
    }
    plVar34 = *(longlong **)(&stack0x00000000 + lVar15);
    **(undefined4 **)(&stack0x00000260 + lVar15) = 1;
    **(undefined4 **)(&stack0x00000150 + lVar15) = 0xffffffff;
    **(undefined4 **)(&stack0x00000290 + lVar15) = 0;
    **(undefined4 **)(&stack0x00000318 + lVar15) = 0;
    puVar32 = *(undefined8 **)(&stack0x000000d8 + lVar15);
    puVar32[1] = 0;
    *puVar32 = 0;
    puVar32[3] = 0;
    puVar32[2] = 0;
    puVar32[5] = 0;
    puVar32[4] = 0;
    puVar32[7] = 0;
    puVar32[6] = 0;
    puVar32[9] = 0;
    puVar32[8] = 0;
    puVar32[0xb] = 0;
    puVar32[10] = 0;
    puVar32[0xd] = 0;
    puVar32[0xc] = 0;
    puVar32[0xf] = 0;
    puVar32[0xe] = 0;
    puVar32[0x11] = 0;
    puVar32[0x10] = 0;
    puVar32[0x13] = 0;
    puVar32[0x12] = 0;
    puVar32[0x15] = 0;
    puVar32[0x14] = 0;
    puVar32[0x17] = 0;
    puVar32[0x16] = 0;
    puVar32[0x19] = 0;
    puVar32[0x18] = 0;
    puVar32[0x1b] = 0;
    puVar32[0x1a] = 0;
    puVar32[0x1d] = 0;
    puVar32[0x1c] = 0;
    puVar32[0x1f] = 0;
    puVar32[0x1e] = 0;
    puVar32[0x21] = 0;
    puVar32[0x20] = 0;
    puVar32[0x23] = 0;
    puVar32[0x22] = 0;
    puVar32[0x25] = 0;
    puVar32[0x24] = 0;
    puVar32[0x27] = 0;
    puVar32[0x26] = 0;
    puVar32[0x29] = 0;
    puVar32[0x28] = 0;
    puVar32[0x2b] = 0;
    puVar32[0x2a] = 0;
    puVar32[0x2d] = 0;
    puVar32[0x2c] = 0;
    puVar32[0x2f] = 0;
    puVar32[0x2e] = 0;
    puVar32[0x31] = 0;
    puVar32[0x30] = 0;
    **(undefined4 **)(&stack0x00000280 + lVar15) = 0;
    **(undefined4 **)(&stack0x000000f8 + lVar15) = 0xffffffff;
    **(undefined4 **)(&stack0x00000298 + lVar15) = 0xffffffff;
    **(undefined4 **)(&stack0x00000288 + lVar15) = 0xffffffff;
    **(undefined4 **)(&stack0x00000158 + lVar15) = 0xffffffff;
    **(undefined4 **)(&stack0x00000240 + lVar15) = 0;
    puVar35 = (undefined4 *)FUN_180005d08(plVar34,0);
    *puVar35 = 0x3f800000;
    puVar35 = (undefined4 *)FUN_18000f448(*(longlong *)(&stack0x00000058 + lVar15),0);
    *puVar35 = 0x41200000;
    uVar36 = FUN_180026708((longlong)puVar37);
    if ((int)uVar36 < 2) {
      lVar54 = *(longlong *)(&stack0x00000060 + lVar15);
      *(undefined1 *)(lVar54 + 0x18) = 5;
      *(undefined4 *)(lVar54 + 0x1c) = 0;
      *(undefined1 *)(puVar37 + 6) = 0xb;
      puVar37[7] = 2;
    }
    uVar36 = FUN_180026708((longlong)puVar37);
    if ((int)uVar36 < 3) {
      lVar54 = *(longlong *)(&stack0x00000130 + lVar15);
      lVar55 = *(longlong *)(&stack0x00000008 + lVar15);
      *(undefined1 *)(lVar54 + 0x18) = 0x16;
      *(undefined4 *)(lVar54 + 0x1c) = 0;
      lVar54 = *(longlong *)(&stack0x00000160 + lVar15);
      *(undefined1 *)(lVar54 + 0x18) = 5;
      *(undefined4 *)(lVar54 + 0x1c) = 0;
      *(undefined8 *)(*(longlong *)(&stack0x00000018 + lVar15) + 0x1c) = 0x1000000ff;
      lVar54 = *(longlong *)(&stack0xffffffffffffffe0 + lVar15);
      *(undefined1 *)(lVar54 + 0x18) = 0x16;
      *(undefined4 *)(lVar54 + 0x1c) = 0;
      uVar24 = FUN_180026550(lVar55);
      *(undefined1 *)(extraout_x11_09 + 0x18) = 0x16;
      *(uint *)(extraout_x11_09 + 0x1c) = uVar24 + 1;
      bVar21 = FUN_180026690((longlong)puVar62);
      if (!bVar21) {
        *(undefined4 *)(extraout_x11_10 + 0x1c) = 0;
      }
      *(undefined1 *)(puVar37 + 6) = 0xb;
      puVar37[7] = 3;
    }
    uVar36 = FUN_180026708((longlong)puVar37);
    if ((int)uVar36 < 4) {
      lVar54 = *(longlong *)(&stack0x000000d0 + lVar15);
      *(undefined1 *)(lVar54 + 0x18) = 0x16;
      *(undefined4 *)(lVar54 + 0x1c) = 1;
      lVar54 = *(longlong *)(&stack0x000000c8 + lVar15);
      *(undefined1 *)(lVar54 + 0x18) = 0x16;
      *(undefined4 *)(lVar54 + 0x1c) = 0;
      *(undefined1 *)(puVar37 + 6) = 0xb;
      puVar37[7] = 4;
    }
    bVar21 = FUN_180026690(*(longlong *)(&stack0x00000088 + lVar15));
    plVar34 = *(longlong **)(&stack0x000001b8 + lVar15);
    plVar2 = *(longlong **)(&stack0x000001c0 + lVar15);
    pcVar56 = "";
    pcVar49 = pcVar56;
    if (bVar21) {
      pcVar49 = "Reversal Price";
    }
    FUN_18000bbb8(*(longlong **)(&stack0x000001c8 + lVar15),pcVar49);
    pcVar49 = pcVar56;
    if (bVar21) {
      pcVar49 = "Zig Zag Line Length";
    }
    FUN_18000bbb8(plVar2,pcVar49);
    if (bVar21) {
      pcVar56 = "Zig Zag Num Bars";
    }
    FUN_18000bbb8(plVar34,pcVar56);
    bVar21 = FUN_180026690(*(longlong *)(&stack0x00000168 + lVar15));
    if ((bVar21) && (uVar24 = FUN_180026550(*(longlong *)(&stack0x00000008 + lVar15)), uVar24 == 0))
    {
      *(undefined1 *)(extraout_x11_11 + 0x18) = 0x16;
      *(undefined4 *)(extraout_x11_11 + 0x1c) = 1;
    }
    if (((char *)**(undefined8 **)(&stack0x00000050 + lVar15) != (char *)0x0) &&
       (*(char *)**(undefined8 **)(&stack0x00000050 + lVar15) != '\0')) {
      iVar22 = FUN_1800045e8("");
      FUN_1800079f8(extraout_x10_26,extraout_x1_01,iVar22);
      *(undefined4 *)(*(longlong *)(&stack0x000001b0 + lVar15) + 0xc) = 1;
    }
  }
  else {
    piVar63 = *(int **)(&stack0x00000298 + lVar15);
    if (*piVar63 != -1) {
      (**(code **)(puVar29 + 0x16))(puVar29[9],0);
      *piVar63 = -1;
    }
    piVar63 = *(int **)(&stack0x00000288 + lVar15);
    iVar22 = *piVar63;
    if (iVar22 != -1) {
      (**(code **)(puVar29 + 0x4d6))(iVar22,0);
      *piVar63 = -1;
    }
  }
  bVar21 = FUN_180026690(*(longlong *)(&stack0x00000120 + lVar15));
  lVar54 = 0;
  (&stack0x000000b8)[lVar15] = !bVar21;
  if (0 < (int)puVar29[3]) {
    pcVar46 = *(code **)(puVar29 + 0x256);
    plVar34 = FUN_180005d78((longlong *)(puVar29 + 0x58),puVar29[3] - 1);
    plVar34 = (longlong *)(*pcVar46)(&stack0x00000428 + lVar15,plVar34);
    lVar54 = *plVar34 + 86400000000;
  }
  lVar55 = *(longlong *)(&stack0x00000128 + lVar15);
  puVar29[0x433] = puVar29[3];
  bVar21 = FUN_180026690(lVar55);
  if (bVar21) {
    lVar55 = *(longlong *)(&stack0x00000138 + lVar15);
    bVar10 = *(byte *)(lVar55 + 0x18);
    uVar24 = (uint)bVar10;
    if (bVar10 == 0x12 || bVar10 == 0x15) {
      uVar74 = *(undefined4 *)(lVar55 + 0x1c);
      if (bVar10 != 0x12) goto LAB_18002fbec;
LAB_18002fc0c:
      uVar24 = *(uint *)(*(longlong *)(&stack0x00000138 + lVar15) + 0x20);
    }
    else if (bVar10 == 0x13) {
      uVar74 = *(undefined4 *)(lVar55 + 0x1c);
      uVar24 = FUN_180026550(lVar55);
    }
    else {
      uVar36 = FUN_180026708(lVar55);
      uVar74 = (undefined4)uVar36;
      uVar24 = extraout_w11;
LAB_18002fbec:
      if ((uVar24 == 0x14) || (uVar24 == 0x15)) goto LAB_18002fc0c;
      uVar24 = FUN_180026550(*(longlong *)(&stack0x00000138 + lVar15));
    }
    plVar34 = (longlong *)(**(code **)(puVar29 + 0x45c))(uVar74,uVar24,2);
    pfVar47 = (float *)*plVar34;
    if (pfVar47 == (float *)0x0) goto LAB_180033460;
    fVar71 = *pfVar47;
    fVar72 = pfVar47[1];
    fVar65 = pfVar47[2];
    (**(code **)(puVar29 + 0x44e))(uVar74,uVar24,7);
    (**(code **)(puVar29 + 0x44a))(uVar74,uVar24,8);
    (**(code **)(puVar29 + 0x44e))(uVar74,uVar24,9);
    uVar24 = FUN_180026550(*(longlong *)(&stack0x000001a8 + lVar15));
    if (uVar24 == 0) {
      if (fVar72 <= 0.1) {
        fVar65 = 1.0;
      }
      else {
        fVar65 = fVar71 / fVar72;
      }
    }
    else if (fVar65 <= 0.1) {
      fVar65 = 1.0;
    }
  }
  else {
    fVar65 = FUN_180026608(*(longlong *)(&stack0x00000100 + lVar15));
  }
  fVar71 = (float)puVar29[3];
  if ((int)fVar71 < (int)*puVar29) {
    pfVar64 = *(float **)(&stack0x00000240 + lVar15);
    fVar72 = (float)((int)fVar71 - 1);
    *(longlong *)(&stack0x00000128 + lVar15) = lVar54;
    *plVar59 = (longlong)plVar31;
    do {
      pfVar47 = (float *)FUN_180005d08((longlong *)(*(longlong *)(&stack0x00000140 + lVar15) + 0x30)
                                       ,(int)fVar71);
      *pfVar47 = fVar65;
      bVar21 = FUN_180026690(*(longlong *)(&stack0x00000060 + lVar15));
      if (bVar21) {
        plVar31 = FUN_180005d78((longlong *)(puVar29 + 0x58),(int)fVar71);
        lVar54 = *plVar31;
        *(longlong *)(&stack0x000004d0 + lVar15) = lVar54;
        if (*(longlong *)(&stack0x00000128 + lVar15) <= lVar54) {
          plVar31 = *(longlong **)(&stack0x00000000 + lVar15);
          *pfVar64 = fVar71;
          puVar35 = (undefined4 *)FUN_180005d08(plVar31,(int)fVar71);
          *puVar35 = 0x3f800000;
          puVar35 = (undefined4 *)
                    FUN_18000f448(*(longlong *)(&stack0x00000058 + lVar15),(int)fVar71);
          pcVar46 = *(code **)(puVar29 + 0x256);
          *puVar35 = 0x41200000;
          plVar31 = (longlong *)(*pcVar46)(&stack0x00000430 + lVar15,&stack0x000004d0 + lVar15);
          *(longlong *)(&stack0x00000128 + lVar15) = *plVar31 + 86400000000;
        }
      }
      bVar21 = FUN_180026690(*(longlong *)(&stack0x000000e0 + lVar15));
      fVar76 = fVar71;
      if (bVar21) {
        if ((int)**(float **)(&stack0x000000f8 + lVar15) < (int)fVar72) {
          **(float **)(&stack0x000000f8 + lVar15) = fVar72;
          fVar76 = fVar72;
          goto LAB_18002fd88;
        }
      }
      else {
LAB_18002fd88:
        uVar36 = FUN_180026708(*(longlong *)(&stack0x00000108 + lVar15));
        iVar22 = (int)uVar36;
        if (iVar22 == 1) {
          fVar73 = *pfVar64;
          fVar66 = FUN_180026608(*(longlong *)(&stack0x00000118 + lVar15));
          fVar66 = fVar66 * 0.01;
          uVar25 = FUN_180026550(*(longlong *)(&stack0xfffffffffffffff0 + lVar15));
          uVar24 = 0x3b;
          if (uVar25 < 0x3c) {
            uVar24 = uVar25;
          }
          plVar31 = FUN_1800076d0((longlong *)(puVar29 + 0x112),uVar24);
          *(longlong **)(&stack0x000001d8 + lVar15) = plVar31;
          uVar25 = FUN_180026550(*(longlong *)(&stack0x000000b0 + lVar15));
          uVar24 = 0x3b;
          if (uVar25 < 0x3c) {
            uVar24 = uVar25;
          }
          plVar31 = FUN_1800076d0((longlong *)(puVar29 + 0x112),uVar24);
          lVar54 = *(longlong *)(&stack0xffffffffffffffe8 + lVar15);
          *(longlong **)(&stack0x000001d0 + lVar15) = plVar31;
          pcVar46 = *(code **)(puVar29 + 0x4f8);
          plVar31 = FUN_1800076d0((longlong *)(lVar54 + 0x80),1);
          *(longlong **)(&stack0x000000f0 + lVar15) = plVar31;
          plVar31 = FUN_1800076d0((longlong *)(lVar54 + 0x80),0);
          uVar42 = *(undefined8 *)(&stack0x000000f0 + lVar15);
          param_2 = 0.0;
          uVar30 = *(undefined8 *)(&stack0x000001d0 + lVar15);
          uVar41 = *(undefined8 *)(&stack0x000001d8 + lVar15);
LAB_18002ff24:
          (*pcVar46)(fVar66,uVar30,uVar41,lVar54 + 0x30,plVar31,uVar42,fVar73,fVar76,puVar29);
        }
        else if (iVar22 == 2) {
          fVar73 = *pfVar64;
          uVar36 = FUN_180026708(*(longlong *)(&stack0x00000110 + lVar15));
          uVar25 = FUN_180026550(*(longlong *)(&stack0xfffffffffffffff0 + lVar15));
          uVar24 = 0x3b;
          if (uVar25 < 0x3c) {
            uVar24 = uVar25;
          }
          plVar31 = FUN_1800076d0((longlong *)(puVar29 + 0x112),uVar24);
          uVar25 = FUN_180026550(*(longlong *)(&stack0x000000b0 + lVar15));
          uVar24 = 0x3b;
          if (uVar25 < 0x3c) {
            uVar24 = uVar25;
          }
          plVar34 = FUN_1800076d0((longlong *)(puVar29 + 0x112),uVar24);
          FUN_180011708(fVar65,(longlong)puVar29,plVar34,plVar31,
                        *(longlong *)(&stack0xffffffffffffffe8 + lVar15),fVar76,(int)uVar36,fVar73);
        }
        else if (iVar22 == 3) {
          fVar73 = *pfVar64;
          uVar25 = FUN_180026550(*(longlong *)(&stack0xfffffffffffffff0 + lVar15));
          uVar24 = 0x3b;
          if (uVar25 < 0x3c) {
            uVar24 = uVar25;
          }
          plVar31 = FUN_1800076d0((longlong *)(puVar29 + 0x112),uVar24);
          *(longlong **)(&stack0x000001d8 + lVar15) = plVar31;
          uVar25 = FUN_180026550(*(longlong *)(&stack0x000000b0 + lVar15));
          uVar24 = 0x3b;
          if (uVar25 < 0x3c) {
            uVar24 = uVar25;
          }
          plVar31 = FUN_1800076d0((longlong *)(puVar29 + 0x112),uVar24);
          lVar54 = *(longlong *)(&stack0xffffffffffffffe8 + lVar15);
          *(longlong **)(&stack0x000001d0 + lVar15) = plVar31;
          pcVar46 = *(code **)(puVar29 + 0x4f8);
          plVar31 = FUN_1800076d0((longlong *)(lVar54 + 0x80),1);
          *(longlong **)(&stack0x000000f0 + lVar15) = plVar31;
          plVar31 = FUN_1800076d0((longlong *)(lVar54 + 0x80),0);
          uVar42 = *(undefined8 *)(&stack0x000000f0 + lVar15);
          param_2 = (double)(ulonglong)(uint)fVar65;
          fVar66 = 0.0;
          uVar30 = *(undefined8 *)(&stack0x000001d0 + lVar15);
          uVar41 = *(undefined8 *)(&stack0x000001d8 + lVar15);
          goto LAB_18002ff24;
        }
      }
      fVar71 = (float)((int)fVar71 + 1);
      fVar72 = (float)((int)fVar72 + 1);
    } while ((int)fVar71 < (int)*puVar29);
    plVar31 = (longlong *)*plVar59;
  }
  uVar25 = FUN_180026550(*(longlong *)(&stack0x00000008 + lVar15));
  *(uint *)(&stack0x00000088 + lVar15) = uVar25;
  uVar24 = extraout_w1;
  if (uVar25 != 0) {
    iVar22 = 0;
    uVar25 = puVar29[3] + 1;
    if (0 < (int)uVar25) {
      pfVar64 = (float *)(&lStack_8)[extraout_x15 * -2];
      do {
        if (1 < iVar22) break;
        lVar54 = *plVar31;
        uVar25 = uVar25 - 1;
        if (lVar54 == 0) {
          if (*(code **)(pfVar64 + 4) != (code *)0x0) {
            (**(code **)(pfVar64 + 4))(pfVar64[6]);
          }
          lVar54 = *plVar31;
          if (lVar54 != 0) goto LAB_18002ffac;
          pfVar47 = (float *)((longlong)plVar31 + 0x24);
        }
        else {
LAB_18002ffac:
          fVar65 = pfVar64[8];
          if (fVar65 == 0.0) {
            pfVar47 = (float *)((longlong)plVar31 + 0x24);
          }
          else {
            uVar24 = uVar25 & ((int)uVar25 >> 0x1f ^ 0xffffffffU);
            if ((int)fVar65 <= (int)uVar24) {
              uVar24 = (int)fVar65 - 1;
            }
            pfVar47 = (float *)(lVar54 + (longlong)(int)uVar24 * 4);
          }
        }
        if (*pfVar47 != 0.0) {
          iVar22 = iVar22 + 1;
        }
      } while (0 < (int)uVar25);
    }
    uVar24 = *puVar29;
    uVar52 = uVar25 + 1;
    fVar65 = 0.0;
    iVar22 = 0;
    if ((int)uVar52 < (int)uVar24) {
      lVar54 = *(longlong *)(&stack0x00000050 + lVar15);
      lVar55 = (&lStack_8)[extraout_x15 * -2];
      do {
        lVar48 = *plVar31;
        uVar24 = (int)uVar25 >> 0x1f;
        if (lVar48 == 0) {
          if (*(code **)(lVar55 + 0x10) != (code *)0x0) {
            (**(code **)(lVar55 + 0x10))(*(undefined4 *)(lVar55 + 0x18));
          }
          lVar48 = *plVar31;
          if (lVar48 != 0) goto LAB_180030030;
          pfVar47 = (float *)((longlong)plVar31 + 0x24);
        }
        else {
LAB_180030030:
          iVar23 = *(int *)(lVar55 + 0x20);
          if (iVar23 == 0) {
            pfVar47 = (float *)((longlong)plVar31 + 0x24);
          }
          else {
            uVar61 = uVar25 & (uVar24 ^ 0xffffffff);
            if (iVar23 <= (int)uVar61) {
              uVar61 = iVar23 - 1;
            }
            pfVar47 = (float *)(lVar48 + (longlong)(int)uVar61 * 4);
          }
        }
        if (*pfVar47 != 0.0) {
          fVar65 = 0.0;
          if (lVar48 == 0) {
            if (*(code **)(lVar55 + 0x10) != (code *)0x0) {
              (**(code **)(lVar55 + 0x10))(*(undefined4 *)(lVar55 + 0x18));
            }
            lVar48 = *plVar31;
            if (lVar48 != 0) goto LAB_180030088;
            pfVar47 = (float *)((longlong)plVar31 + 0x24);
          }
          else {
LAB_180030088:
            iVar22 = *(int *)(lVar55 + 0x20);
            if (iVar22 == 0) {
              pfVar47 = (float *)((longlong)plVar31 + 0x24);
            }
            else {
              uVar61 = uVar25 & (uVar24 ^ 0xffffffff);
              if (iVar22 <= (int)uVar61) {
                uVar61 = iVar22 - 1;
              }
              pfVar47 = (float *)(lVar48 + (longlong)(int)uVar61 * 4);
            }
          }
          iVar22 = (int)*pfVar47;
        }
        lVar48 = **(longlong **)(&stack0x00000000 + lVar15);
        if (lVar48 == 0) {
          pcVar46 = *(code **)(*(longlong *)(&stack0x00000028 + lVar15) + 0x10);
          if (pcVar46 != (code *)0x0) {
            (*pcVar46)(*(undefined4 *)(*(longlong *)(&stack0x00000028 + lVar15) + 0x18));
          }
          lVar48 = **(longlong **)(&stack0x00000000 + lVar15);
          if (lVar48 != 0) goto LAB_1800300e8;
          pfVar47 = (float *)((longlong)*(longlong **)(&stack0x00000000 + lVar15) + 0x24);
        }
        else {
LAB_1800300e8:
          iVar23 = *(int *)(*(longlong *)(&stack0x00000028 + lVar15) + 0x20);
          if (iVar23 == 0) {
            pfVar47 = (float *)(*(longlong *)(&stack0x00000000 + lVar15) + 0x24);
          }
          else {
            uVar61 = uVar25 & (uVar24 ^ 0xffffffff);
            if (iVar23 <= (int)uVar61) {
              uVar61 = iVar23 - 1;
            }
            pfVar47 = (float *)(lVar48 + (longlong)(int)uVar61 * 4);
          }
        }
        if (*pfVar47 != 0.0) {
          iVar22 = *(int *)(&stack0x00000088 + lVar15);
          if (iVar22 == 1) {
            pfVar47 = (float *)FUN_180005d08((longlong *)(puVar29 + 0x2b6),uVar25);
            fVar65 = *pfVar47;
          }
          else if (iVar22 == 2) {
            pfVar47 = (float *)FUN_180005d08((longlong *)(puVar29 + 0x2f2),uVar25);
            fVar65 = *pfVar47;
          }
          else if (iVar22 == 3) {
            pfVar47 = (float *)FUN_180005d08((longlong *)(puVar29 + 0x2fc),uVar25);
            fVar65 = *pfVar47;
          }
          else if (iVar22 == 4) {
            pfVar47 = (float *)FUN_180005d08((longlong *)(puVar29 + 0x2fc),uVar25);
            pfVar38 = (float *)FUN_180005d08((longlong *)(puVar29 + 0x2f2),uVar25);
            fVar65 = *pfVar47 - *pfVar38;
          }
          pfVar47 = (float *)FUN_180005d08((longlong *)(lVar54 + 0x30),uVar25);
          *pfVar47 = fVar65;
          lVar48 = *plVar31;
          if (lVar48 == 0) {
            if (*(code **)(lVar55 + 0x10) != (code *)0x0) {
              (**(code **)(lVar55 + 0x10))(*(undefined4 *)(lVar55 + 0x18));
            }
            lVar48 = *plVar31;
            if (lVar48 != 0) goto LAB_1800301e8;
            pfVar47 = (float *)((longlong)plVar31 + 0x24);
          }
          else {
LAB_1800301e8:
            iVar22 = *(int *)(lVar55 + 0x20);
            if (iVar22 == 0) {
              pfVar47 = (float *)((longlong)plVar31 + 0x24);
            }
            else {
              uVar24 = uVar25 & (uVar24 ^ 0xffffffff);
              if (iVar22 <= (int)uVar24) {
                uVar24 = iVar22 - 1;
              }
              pfVar47 = (float *)(lVar48 + (longlong)(int)uVar24 * 4);
            }
          }
          iVar22 = (int)*pfVar47;
        }
        if (iVar22 == 1) {
          uVar24 = *(uint *)(lVar54 + 0x18);
          pfVar64 = (float *)(ulonglong)uVar24;
          lVar48 = *(longlong *)(lVar54 + 0x58);
          if (lVar48 == 0) {
            if (*(code **)(lVar54 + 0x68) != (code *)0x0) {
              (**(code **)(lVar54 + 0x68))(*(undefined4 *)(lVar54 + 0x70));
            }
            lVar48 = *(longlong *)(lVar54 + 0x58);
            if (lVar48 == 0) {
              *(uint *)(lVar54 + 0x7c) = uVar24;
              goto LAB_180030298;
            }
          }
          iVar23 = *(int *)(lVar54 + 0x78);
          if (iVar23 == 0) {
            *(uint *)(lVar54 + 0x7c) = uVar24;
          }
          else {
            uVar61 = uVar52 & ((int)uVar52 >> 0x1f ^ 0xffffffffU);
            if (iVar23 <= (int)uVar61) {
              uVar61 = iVar23 - 1;
            }
            *(uint *)(lVar48 + (longlong)(int)uVar61 * 4) = uVar24;
          }
        }
        else if (iVar22 == -1) {
          uVar24 = *(uint *)(lVar54 + 0x1c);
          pfVar64 = (float *)(ulonglong)uVar24;
          puVar37 = (uint *)FUN_180005d08((longlong *)(lVar54 + 0x58),uVar52);
          *puVar37 = uVar24;
        }
LAB_180030298:
        iVar23 = *(int *)(&stack0x00000088 + lVar15);
        if (iVar23 == 1) {
          lVar48 = *(longlong *)(puVar29 + 0x2b6);
          if (lVar48 == 0) {
            if (*(code **)(puVar29 + 0x2ba) != (code *)0x0) {
              (**(code **)(puVar29 + 0x2ba))(puVar29[700]);
            }
            lVar48 = *(longlong *)(puVar29 + 0x2b6);
            if (lVar48 == 0) {
              fVar71 = (float)puVar29[0x2bf];
              goto LAB_180030368;
            }
          }
          uVar24 = puVar29[0x2be];
          if (uVar24 == 0) {
            fVar71 = (float)puVar29[0x2bf];
          }
          else {
            uVar61 = uVar52 & ((int)uVar52 >> 0x1f ^ 0xffffffffU);
            if ((int)uVar24 <= (int)uVar61) {
              uVar61 = uVar24 - 1;
            }
            fVar71 = *(float *)(lVar48 + (longlong)(int)uVar61 * 4);
          }
LAB_180030368:
          fVar65 = fVar71 + fVar65;
        }
        else {
          if (iVar23 == 2) {
            pfVar47 = (float *)FUN_180005d08((longlong *)(puVar29 + 0x2f2),uVar52);
            fVar71 = *pfVar47;
            goto LAB_180030368;
          }
          if (iVar23 == 3) {
            pfVar47 = (float *)FUN_180005d08((longlong *)(puVar29 + 0x2fc),uVar52);
            fVar71 = *pfVar47;
            goto LAB_180030368;
          }
          if (iVar23 == 4) {
            pfVar64 = (float *)FUN_180005d08((longlong *)(puVar29 + 0x2fc),uVar52);
            pfVar47 = (float *)FUN_180005d08((longlong *)(puVar29 + 0x2f2),uVar52);
            fVar71 = *pfVar64 - *pfVar47;
            goto LAB_180030368;
          }
        }
        pfVar47 = (float *)FUN_180005d08((longlong *)(lVar54 + 0x30),uVar52);
        uVar52 = uVar52 + 1;
        uVar25 = uVar25 + 1;
        *pfVar47 = fVar65;
        uVar24 = *puVar29;
      } while ((int)uVar52 < (int)uVar24);
    }
  }
  iVar22 = **(int **)(&stack0x00000158 + lVar15);
  if (iVar22 == -1) {
LAB_180030420:
    **(undefined4 **)(&stack0x00000158 + lVar15) = 0;
  }
  else {
    if (iVar22 < (int)uVar24) {
      pfVar64 = *(float **)(&stack0x00000058 + lVar15);
      lVar54 = *(longlong *)(&stack0x00000030 + lVar15);
      lVar55 = *(longlong *)(&stack0x00000038 + lVar15);
      lVar48 = *(longlong *)(&stack0x00000020 + lVar15);
      do {
        puVar35 = (undefined4 *)FUN_180005d08((longlong *)(pfVar64 + 0xc),iVar22);
        *puVar35 = 0;
        puVar35 = (undefined4 *)FUN_180005d08((longlong *)(lVar54 + 0x30),iVar22);
        *puVar35 = 0;
        puVar35 = (undefined4 *)FUN_180005d08((longlong *)(lVar55 + 0x30),iVar22);
        *puVar35 = 0;
        puVar35 = (undefined4 *)FUN_180005d08((longlong *)(lVar48 + 0x30),iVar22);
        iVar22 = iVar22 + 1;
        *puVar35 = 0;
      } while (iVar22 < (int)*puVar29);
    }
    if (**(int **)(&stack0x00000158 + lVar15) == -1) goto LAB_180030420;
  }
  bVar21 = FUN_180026690(*(longlong *)(&stack0x000000e0 + lVar15));
  if ((bVar21) && (1 < (int)*puVar29)) {
    iVar22 = *puVar29 - 2;
  }
  else {
    iVar22 = *puVar29 - 1;
  }
  FUN_180005d08(*(longlong **)(&stack0x000000c0 + lVar15),iVar22);
  uVar24 = **(uint **)(&stack0x00000158 + lVar15);
  if ((int)uVar24 < (int)*puVar29) {
    *plVar59 = (longlong)plVar31;
    *(uint **)(&stack0xfffffffffffffff0 + lVar15) = puVar29;
    *(longlong *)(&stack0x00000008 + lVar15) =
         *(longlong *)(&stack0xffffffffffffffe8 + lVar15) + 0x30;
    auVar67 = extraout_q0_01;
    do {
      fVar65 = 1.0;
      if (**(int **)(&stack0x00000260 + lVar15) != 1) goto switchD_1800304b8_caseD_0;
      lVar54 = *(longlong *)(&stack0x00000208 + lVar15);
      switch(*(undefined1 *)(lVar54 + 0x18)) {
      default:
        goto switchD_1800304b8_caseD_0;
      case 1:
      case 3:
      case 4:
      case 6:
      case 0xb:
      case 0xd:
      case 0xe:
      case 0xf:
      case 0x10:
      case 0x11:
      case 0x13:
      case 0x16:
      case 0x18:
        uVar25 = *(uint *)(lVar54 + 0x1c);
        break;
      case 2:
        fVar71 = *(float *)(lVar54 + 0x1c);
        if (0.0 <= fVar71) {
          uVar25 = (uint)(fVar71 + 0.5);
        }
        else {
          uVar25 = (uint)(fVar71 - 0.5);
        }
        break;
      case 5:
        uVar25 = (uint)(*(int *)(lVar54 + 0x1c) != 0);
        break;
      case 8:
        plVar31 = (longlong *)
                  FUN_1800043e0(*(double *)(lVar54 + 0x1c),(longlong *)(&stack0x00000438 + lVar15));
        uVar25 = (uint)(*plVar31 / 86400000000);
        auVar67 = extraout_q0_02;
        break;
      case 9:
        puVar16 = &stack0x00000440;
        goto LAB_180030544;
      case 0x17:
        dVar75 = *(double *)(lVar54 + 0x1c);
        if (0.0 <= dVar75) {
          uVar25 = (uint)(dVar75 + 0.5);
        }
        else {
          uVar25 = (uint)(dVar75 - 0.5);
        }
        break;
      case 0x19:
        puVar16 = &stack0x00000448;
LAB_180030544:
        plVar31 = (longlong *)
                  FUN_1800043e0(*(double *)(lVar54 + 0x1c),(longlong *)(puVar16 + lVar15));
        uVar25 = FUN_180006750(plVar31);
        auVar67 = extraout_q0_03;
      }
      if (uVar25 != 0) {
        lVar54 = *(longlong *)(&stack0x00000188 + lVar15);
        switch(*(undefined1 *)(lVar54 + 0x18)) {
        default:
          goto switchD_180030574_caseD_0;
        case 1:
        case 3:
        case 4:
        case 6:
        case 0xb:
        case 0xd:
        case 0xe:
        case 0xf:
        case 0x10:
        case 0x11:
        case 0x13:
        case 0x16:
        case 0x18:
          uVar25 = *(uint *)(lVar54 + 0x1c);
          break;
        case 2:
          fVar71 = *(float *)(lVar54 + 0x1c);
          if (0.0 <= fVar71) {
            uVar25 = (uint)(fVar71 + 0.5);
          }
          else {
            uVar25 = (uint)(fVar71 - 0.5);
          }
          break;
        case 5:
          uVar25 = (uint)(*(int *)(lVar54 + 0x1c) != 0);
          break;
        case 0x17:
          dVar75 = *(double *)(lVar54 + 0x1c);
          if (0.0 <= dVar75) {
            uVar25 = (uint)(dVar75 + 0.5);
          }
          else {
            uVar25 = (uint)(dVar75 - 0.5);
          }
          break;
        case 0x19:
          uVar25 = *(uint *)(lVar54 + 0x24);
        }
        if (uVar25 < 2) {
switchD_180030574_caseD_0:
          switch(*(undefined1 *)(*(longlong *)(&stack0x00000190 + lVar15) + 0x18)) {
          default:
            break;
          case 1:
          case 3:
          case 4:
          case 6:
          case 0xb:
          case 0xd:
          case 0xe:
          case 0xf:
          case 0x10:
          case 0x11:
          case 0x13:
          case 0x16:
          case 0x18:
            break;
          case 2:
            break;
          case 5:
            break;
          case 0x17:
            break;
          case 0x19:
          }
          FUN_180026690(*(longlong *)(&stack0x00000268 + lVar15));
          switch(*(undefined1 *)(*(longlong *)(&stack0x000002a8 + lVar15) + 0x18)) {
          default:
            break;
          case 1:
          case 3:
          case 4:
          case 6:
          case 0xb:
          case 0xd:
          case 0xe:
          case 0xf:
          case 0x10:
          case 0x11:
          case 0x13:
          case 0x16:
          case 0x18:
            break;
          case 5:
          }
          switch(*(undefined1 *)(*(longlong *)(&stack0x000002a0 + lVar15) + 0x18)) {
          default:
            break;
          case 1:
          case 3:
          case 4:
          case 6:
          case 0xb:
          case 0xd:
          case 0xe:
          case 0xf:
          case 0x10:
          case 0x11:
          case 0x13:
          case 0x16:
          case 0x18:
            break;
          case 5:
          }
          lVar54 = *(longlong *)(&stack0x00000270 + lVar15);
          switch(*(undefined1 *)(lVar54 + 0x18)) {
          default:
            uVar74 = 0;
            break;
          case 1:
          case 3:
          case 4:
          case 6:
          case 0xb:
          case 0xd:
          case 0xe:
          case 0xf:
          case 0x10:
          case 0x11:
          case 0x13:
          case 0x16:
          case 0x18:
            uVar74 = *(undefined4 *)(lVar54 + 0x1c);
            break;
          case 5:
            uVar74 = 0xffffff;
            if (*(int *)(lVar54 + 0x1c) == 0) {
              uVar74 = 0;
            }
          }
          switch(*(undefined1 *)(*(longlong *)(&stack0x00000278 + lVar15) + 0x18)) {
          default:
            break;
          case 1:
          case 3:
          case 4:
          case 6:
          case 0xb:
          case 0xd:
          case 0xe:
          case 0xf:
          case 0x10:
          case 0x11:
          case 0x13:
          case 0x16:
          case 0x18:
            break;
          case 5:
          }
          lVar54 = *(longlong *)(&stack0x00000198 + lVar15);
          switch(*(undefined1 *)(lVar54 + 0x18)) {
          default:
            uVar25 = 0;
            break;
          case 1:
          case 3:
          case 4:
          case 6:
          case 0xb:
          case 0xd:
          case 0xe:
          case 0xf:
          case 0x10:
          case 0x11:
          case 0x13:
          case 0x16:
          case 0x18:
            uVar25 = *(uint *)(lVar54 + 0x1c);
            break;
          case 2:
            fVar71 = *(float *)(lVar54 + 0x1c);
            if (0.0 <= fVar71) {
              uVar25 = (uint)(fVar71 + 0.5);
            }
            else {
              uVar25 = (uint)(fVar71 - 0.5);
            }
            break;
          case 5:
            uVar25 = (uint)(*(int *)(lVar54 + 0x1c) != 0);
            break;
          case 0x17:
            dVar75 = *(double *)(lVar54 + 0x1c);
            if (0.0 <= dVar75) {
              uVar25 = (uint)(dVar75 + 0.5);
            }
            else {
              uVar25 = (uint)(dVar75 - 0.5);
            }
            break;
          case 0x19:
            uVar25 = *(uint *)(lVar54 + 0x24);
          }
          lVar54 = *(longlong *)(&stack0x000001a0 + lVar15);
          switch(*(undefined1 *)(lVar54 + 0x18)) {
          default:
            uVar52 = 0;
            break;
          case 1:
          case 3:
          case 4:
          case 6:
          case 0xb:
          case 0xd:
          case 0xe:
          case 0xf:
          case 0x10:
          case 0x11:
          case 0x13:
          case 0x16:
          case 0x18:
            uVar52 = *(uint *)(lVar54 + 0x1c);
            break;
          case 2:
            fVar71 = *(float *)(lVar54 + 0x1c);
            if (0.0 <= fVar71) {
              uVar52 = (uint)(fVar71 + 0.5);
            }
            else {
              uVar52 = (uint)(fVar71 - 0.5);
            }
            break;
          case 5:
            uVar52 = (uint)(*(int *)(lVar54 + 0x1c) != 0);
            break;
          case 0x17:
            dVar75 = *(double *)(lVar54 + 0x1c);
            if (0.0 <= dVar75) {
              uVar52 = (uint)(dVar75 + 0.5);
            }
            else {
              uVar52 = (uint)(dVar75 - 0.5);
            }
            break;
          case 0x19:
            uVar52 = *(uint *)(lVar54 + 0x24);
          }
          uVar36 = FUN_180026708(*(longlong *)(&stack0x00000188 + lVar15));
          iVar22 = (int)uVar36;
          uVar36 = FUN_180026708(*(longlong *)(&stack0x00000208 + lVar15));
          *(undefined4 *)(acStack_a8 + lVar15) = uVar74;
          piVar43 = *(int **)(&stack0x000000d8 + lVar15);
          piVar63 = *(int **)(&stack0x00000280 + lVar15);
          *(undefined4 *)(&stack0xffffffffffffff78 + lVar15) = extraout_w14;
          lVar54 = *(longlong *)(&stack0xfffffffffffffff0 + lVar15);
          (&stack0xffffffffffffff70)[lVar15] = extraout_w13;
          *(undefined4 *)(&stack0xffffffffffffff68 + lVar15) = extraout_w12_00;
          *(undefined4 *)(&stack0xffffffffffffff60 + lVar15) = extraout_w11_00;
          *(undefined4 *)(&lStack_b0 + extraout_x15 * -2) = extraout_w15;
          FUN_18002a1b0(extraout_q0_04,param_2,lVar54,piVar63,uVar24,(int)uVar36,piVar43,iVar22,
                        uVar52,uVar25,(int)(&lStack_b0)[extraout_x15 * -2],
                        *(undefined4 *)(acStack_a8 + lVar15),
                        *(undefined4 *)(&stack0xffffffffffffff60 + lVar15),
                        *(undefined4 *)(&stack0xffffffffffffff68 + lVar15),
                        (&stack0xffffffffffffff70)[lVar15],
                        *(undefined4 *)(&stack0xffffffffffffff78 + lVar15));
          auVar67 = extraout_q0_05;
        }
      }
switchD_1800304b8_caseD_0:
      plVar31 = *(longlong **)(&stack0x00000008 + lVar15);
      lVar54 = *plVar31;
      uVar25 = (int)uVar24 >> 0x1f;
      if (lVar54 == 0) {
        if ((code *)plVar31[2] != (code *)0x0) {
          auVar67 = (*(code *)plVar31[2])((int)plVar31[3]);
        }
        lVar54 = **(longlong **)(&stack0x00000008 + lVar15);
        if (lVar54 != 0) goto LAB_180030928;
        pfVar47 = (float *)((longlong)*(longlong **)(&stack0x00000008 + lVar15) + 0x24);
      }
      else {
LAB_180030928:
        iVar22 = *(int *)(*(longlong *)(&stack0x00000008 + lVar15) + 0x20);
        if (iVar22 == 0) {
          pfVar47 = (float *)(*(longlong *)(&stack0x00000008 + lVar15) + 0x24);
        }
        else {
          uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
          if (iVar22 <= (int)uVar52) {
            uVar52 = iVar22 - 1;
          }
          pfVar47 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
        }
      }
      if (*pfVar47 != 0.0) {
        lVar54 = *(longlong *)(&stack0xffffffffffffffe0 + lVar15);
        switch(*(byte *)(lVar54 + 0x18)) {
        default:
          goto switchD_18003097c_caseD_0;
        case 1:
        case 3:
        case 4:
        case 6:
        case 0xb:
        case 0xd:
        case 0xe:
        case 0xf:
        case 0x10:
        case 0x11:
        case 0x13:
        case 0x16:
        case 0x18:
          uVar52 = *(uint *)(lVar54 + 0x1c);
          break;
        case 2:
          fVar71 = *(float *)(lVar54 + 0x1c);
          if (0.0 <= fVar71) {
            uVar52 = (uint)(fVar71 + 0.5);
          }
          else {
            uVar52 = (uint)(fVar71 - 0.5);
          }
          break;
        case 5:
          uVar52 = (uint)(*(int *)(lVar54 + 0x1c) != 0);
          break;
        case 0x17:
          dVar75 = *(double *)(lVar54 + 0x1c);
          if (0.0 <= dVar75) {
            uVar52 = (uint)(dVar75 + 0.5);
          }
          else {
            uVar52 = (uint)(dVar75 - 0.5);
          }
          break;
        case 0x19:
          uVar52 = *(uint *)(lVar54 + 0x24);
        }
        if (uVar52 != 0) {
                    /* WARNING: Could not recover jumptable at 0x0001800309f8. Too many branches */
                    /* WARNING: Treating indirect jump as call */
          (*(code *)(&LAB_1800309fc +
                    (longlong)*(int *)(&DAT_180033968 + (ulonglong)*(byte *)(lVar54 + 0x18) * 4) * 4
                    ))();
          return;
        }
      }
switchD_18003097c_caseD_0:
      lVar54 = **(longlong **)(&stack0x00000010 + lVar15);
      if (lVar54 == 0) {
        pcVar46 = *(code **)(*(longlong *)(&stack0x00000070 + lVar15) + 0x10);
        if (pcVar46 != (code *)0x0) {
          auVar67 = (*pcVar46)(*(undefined4 *)(*(longlong *)(&stack0x00000070 + lVar15) + 0x18));
        }
        lVar54 = **(longlong **)(&stack0x00000010 + lVar15);
        if (lVar54 != 0) goto LAB_180030d90;
        pfVar47 = (float *)((longlong)*(longlong **)(&stack0x00000010 + lVar15) + 0x24);
      }
      else {
LAB_180030d90:
        iVar22 = *(int *)(*(longlong *)(&stack0x00000070 + lVar15) + 0x20);
        if (iVar22 == 0) {
          pfVar47 = (float *)(*(longlong *)(&stack0x00000010 + lVar15) + 0x24);
        }
        else {
          uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
          if (iVar22 <= (int)uVar52) {
            uVar52 = iVar22 - 1;
          }
          pfVar47 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
        }
      }
      fVar71 = *pfVar47;
      plVar31 = *(longlong **)(&stack0x00000008 + lVar15);
      lVar54 = *plVar31;
      if (lVar54 == 0) {
        if ((code *)plVar31[2] != (code *)0x0) {
          auVar67 = (*(code *)plVar31[2])((int)plVar31[3]);
        }
        lVar54 = **(longlong **)(&stack0x00000008 + lVar15);
        if (lVar54 != 0) goto LAB_180030df4;
        pfVar47 = (float *)((longlong)*(longlong **)(&stack0x00000008 + lVar15) + 0x24);
      }
      else {
LAB_180030df4:
        iVar22 = *(int *)(*(longlong *)(&stack0x00000008 + lVar15) + 0x20);
        if (iVar22 == 0) {
          pfVar47 = (float *)(*(longlong *)(&stack0x00000008 + lVar15) + 0x24);
        }
        else {
          uVar52 = (int)fVar71 & ((int)fVar71 >> 0x1f ^ 0xffffffffU);
          if (iVar22 <= (int)uVar52) {
            uVar52 = iVar22 - 1;
          }
          pfVar47 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
        }
      }
      lVar55 = *(longlong *)(&stack0xfffffffffffffff0 + lVar15);
      lVar54 = *(longlong *)(lVar55 + 0xab0);
      if (lVar54 == 0) {
        if (*(code **)(lVar55 + 0xac0) != (code *)0x0) {
          auVar67 = (**(code **)(lVar55 + 0xac0))(*(undefined4 *)(lVar55 + 0xac8));
        }
        lVar54 = *(longlong *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0xab0);
        if (lVar54 != 0) goto LAB_180030e4c;
        pfVar38 = (float *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0xad4);
      }
      else {
LAB_180030e4c:
        iVar22 = *(int *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0xad0);
        if (iVar22 == 0) {
          pfVar38 = (float *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0xad4);
        }
        else {
          uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
          if (iVar22 <= (int)uVar52) {
            uVar52 = iVar22 - 1;
          }
          pfVar38 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
        }
      }
      fVar72 = *pfVar38;
      fVar71 = *pfVar47;
      lVar55 = *(longlong *)(&stack0x00000148 + lVar15);
      lVar54 = *(longlong *)(lVar55 + 0x30);
      if (lVar54 == 0) {
        if (*(code **)(lVar55 + 0x40) != (code *)0x0) {
          auVar67 = (**(code **)(lVar55 + 0x40))(*(undefined4 *)(lVar55 + 0x48));
        }
        lVar54 = *(longlong *)(*(longlong *)(&stack0x00000148 + lVar15) + 0x30);
        if (lVar54 != 0) goto LAB_180030eb0;
        pfVar47 = (float *)(*(longlong *)(&stack0x00000148 + lVar15) + 0x54);
      }
      else {
LAB_180030eb0:
        iVar22 = *(int *)(*(longlong *)(&stack0x00000148 + lVar15) + 0x50);
        if (iVar22 == 0) {
          pfVar47 = (float *)(*(longlong *)(&stack0x00000148 + lVar15) + 0x54);
        }
        else {
          uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
          if (iVar22 <= (int)uVar52) {
            uVar52 = iVar22 - 1;
          }
          pfVar47 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
        }
      }
      *pfVar47 = fVar72 - fVar71;
      lVar54 = *(longlong *)*plVar59;
      if (lVar54 == 0) {
        pcVar46 = *(code **)((&lStack_8)[extraout_x15 * -2] + 0x10);
        if (pcVar46 != (code *)0x0) {
          auVar67 = (*pcVar46)(*(undefined4 *)((&lStack_8)[extraout_x15 * -2] + 0x18));
        }
        lVar54 = *(longlong *)*plVar59;
        if (lVar54 != 0) goto LAB_180030f10;
        pfVar47 = (float *)(*plVar59 + 0x24);
      }
      else {
LAB_180030f10:
        iVar22 = *(int *)((&lStack_8)[extraout_x15 * -2] + 0x20);
        if (iVar22 == 0) {
          pfVar47 = (float *)(*plVar59 + 0x24);
        }
        else {
          uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
          if (iVar22 <= (int)uVar52) {
            uVar52 = iVar22 - 1;
          }
          pfVar47 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
        }
      }
      if (*pfVar47 != 0.0) {
        lVar54 = **(longlong **)(&stack0x00000000 + lVar15);
        if (lVar54 == 0) {
          pcVar46 = *(code **)(*(longlong *)(&stack0x00000028 + lVar15) + 0x10);
          if (pcVar46 != (code *)0x0) {
            auVar67 = (*pcVar46)(*(undefined4 *)(*(longlong *)(&stack0x00000028 + lVar15) + 0x18));
          }
          lVar54 = **(longlong **)(&stack0x00000000 + lVar15);
          if (lVar54 != 0) goto LAB_180030f7c;
          pfVar47 = (float *)((longlong)*(longlong **)(&stack0x00000000 + lVar15) + 0x24);
        }
        else {
LAB_180030f7c:
          iVar22 = *(int *)(*(longlong *)(&stack0x00000028 + lVar15) + 0x20);
          if (iVar22 == 0) {
            pfVar47 = (float *)(*(longlong *)(&stack0x00000000 + lVar15) + 0x24);
          }
          else {
            uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
            if (iVar22 <= (int)uVar52) {
              uVar52 = iVar22 - 1;
            }
            pfVar47 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
          }
        }
        if (*pfVar47 != 0.0) goto LAB_1800332e0;
        iVar22 = -1;
        if (0 < (int)uVar24) {
          lVar54 = **(longlong **)(&stack0x00000010 + lVar15);
          if (lVar54 == 0) {
            pcVar46 = *(code **)(*(longlong *)(&stack0x00000070 + lVar15) + 0x10);
            if (pcVar46 != (code *)0x0) {
              auVar67 = (*pcVar46)(*(undefined4 *)(*(longlong *)(&stack0x00000070 + lVar15) + 0x18))
              ;
            }
            lVar54 = **(longlong **)(&stack0x00000010 + lVar15);
            if (lVar54 != 0) goto LAB_180030ff8;
            pfVar47 = (float *)((longlong)*(longlong **)(&stack0x00000010 + lVar15) + 0x24);
          }
          else {
LAB_180030ff8:
            iVar22 = *(int *)(*(longlong *)(&stack0x00000070 + lVar15) + 0x20);
            if (iVar22 == 0) {
              pfVar47 = (float *)(*(longlong *)(&stack0x00000010 + lVar15) + 0x24);
            }
            else {
              uVar52 = uVar24 - 1 & ((int)(uVar24 - 1) >> 0x1f ^ 0xffffffffU);
              if (iVar22 <= (int)uVar52) {
                uVar52 = iVar22 - 1;
              }
              pfVar47 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
            }
          }
          iVar22 = (int)*pfVar47;
        }
        *(undefined4 *)(&stack0xffffffffffffffd8 + lVar15) =
             **(undefined4 **)(&stack0x00000290 + lVar15);
        if (-1 < iVar22) {
          plVar31 = *(longlong **)(&stack0x00000008 + lVar15);
          pfVar47 = (float *)FUN_180005d08(plVar31,iVar22);
          pfVar38 = (float *)FUN_180005d08(plVar31,uVar24);
          fVar71 = *pfVar47;
          fVar72 = *pfVar38;
          pfVar47 = (float *)FUN_180005d08(plVar31,uVar24);
          pfVar38 = (float *)FUN_180005d08(plVar31,iVar22);
          if (*pfVar47 < *pfVar38) {
            *(undefined4 *)(&stack0xffffffffffffffd8 + lVar15) = 0xffffffff;
            pfVar47 = (float *)FUN_180005d08(plVar31,iVar22);
            pfVar38 = (float *)FUN_180005d08(plVar31,uVar24);
            pfVar39 = (float *)FUN_180005d08(plVar31,iVar22);
            lVar54 = *(longlong *)(&stack0x000002c0 + lVar15);
            switch(*(undefined1 *)(lVar54 + 0x18)) {
            default:
              fVar65 = 0.0;
              break;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xd:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x16:
            case 0x18:
              fVar65 = (float)*(uint *)(lVar54 + 0x1c);
              break;
            case 2:
              fVar65 = *(float *)(lVar54 + 0x1c);
              break;
            case 5:
              if (*(int *)(lVar54 + 0x1c) == 0) {
                fVar65 = 0.0;
              }
              break;
            case 0xb:
            case 0x13:
              fVar65 = (float)*(int *)(lVar54 + 0x1c);
              break;
            case 0x17:
              fVar65 = (float)*(double *)(lVar54 + 0x1c);
            }
            fVar65 = *pfVar47 - (1.0 - fVar65 / 100.0) * ABS(*pfVar39 - *pfVar38);
            switch(*(undefined1 *)(*(longlong *)(&stack0x000002c8 + lVar15) + 0x18)) {
            default:
              break;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xd:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x16:
            case 0x18:
              break;
            case 2:
              break;
            case 5:
              break;
            case 0xb:
            case 0x13:
              break;
            case 0x17:
            }
            plVar31 = *(longlong **)(&stack0x00000008 + lVar15);
            pfVar47 = (float *)FUN_180005d08(plVar31,iVar22);
            pfVar38 = (float *)FUN_180005d08(plVar31,uVar24);
            *(float *)(&stack0x00000060 + lVar15) = *pfVar47 - *pfVar38;
            FUN_180005d08(plVar31,iVar22);
            lVar54 = *(longlong *)(&stack0x00000308 + lVar15);
            switch(*(undefined1 *)(lVar54 + 0x18)) {
            default:
              uVar74 = 0;
              break;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xb:
            case 0xd:
            case 0xe:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x13:
            case 0x16:
            case 0x18:
              uVar74 = *(undefined4 *)(lVar54 + 0x1c);
              break;
            case 5:
              uVar74 = 0xffffff;
              if (*(int *)(lVar54 + 0x1c) == 0) {
                uVar74 = 0;
              }
            }
            lVar54 = *(longlong *)(&stack0x00000310 + lVar15);
            *(undefined4 *)(&stack0x000000e0 + lVar15) = uVar74;
            switch(*(undefined1 *)(lVar54 + 0x18)) {
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xb:
            case 0xd:
            case 0xe:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x13:
            case 0x16:
            case 0x18:
              goto switchD_180031274_caseD_1;
            case 5:
              goto switchD_180031274_caseD_5;
            }
switchD_180031274_caseD_0:
            uVar74 = 0;
          }
          else {
            *(undefined4 *)(&stack0xffffffffffffffd8 + lVar15) = 1;
            pfVar47 = (float *)FUN_180005d08(plVar31,iVar22);
            pfVar38 = (float *)FUN_180005d08(plVar31,uVar24);
            pfVar39 = (float *)FUN_180005d08(plVar31,iVar22);
            lVar54 = *(longlong *)(&stack0x000002b0 + lVar15);
            switch(*(undefined1 *)(lVar54 + 0x18)) {
            default:
              fVar65 = 0.0;
              break;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xd:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x16:
            case 0x18:
              fVar65 = (float)*(uint *)(lVar54 + 0x1c);
              break;
            case 2:
              fVar65 = *(float *)(lVar54 + 0x1c);
              break;
            case 5:
              if (*(int *)(lVar54 + 0x1c) == 0) {
                fVar65 = 0.0;
              }
              break;
            case 0xb:
            case 0x13:
              fVar65 = (float)*(int *)(lVar54 + 0x1c);
              break;
            case 0x17:
              fVar65 = (float)*(double *)(lVar54 + 0x1c);
            }
            fVar65 = (1.0 - fVar65 / 100.0) * ABS(*pfVar38 - *pfVar39) + *pfVar47;
            switch(*(undefined1 *)(*(longlong *)(&stack0x000002b8 + lVar15) + 0x18)) {
            default:
              break;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xd:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x16:
            case 0x18:
              break;
            case 2:
              break;
            case 5:
              break;
            case 0xb:
            case 0x13:
              break;
            case 0x17:
            }
            plVar31 = *(longlong **)(&stack0x00000008 + lVar15);
            FUN_180005d08(plVar31,iVar22);
            FUN_180005d08(plVar31,uVar24);
            FUN_180005d08(plVar31,iVar22);
            lVar54 = *(longlong *)(&stack0x000002f8 + lVar15);
            switch(*(undefined1 *)(lVar54 + 0x18)) {
            default:
              uVar74 = 0;
              break;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xb:
            case 0xd:
            case 0xe:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x13:
            case 0x16:
            case 0x18:
              uVar74 = *(undefined4 *)(lVar54 + 0x1c);
              break;
            case 5:
              uVar74 = 0xffffff;
              if (*(int *)(lVar54 + 0x1c) == 0) {
                uVar74 = 0;
              }
            }
            lVar54 = *(longlong *)(&stack0x00000300 + lVar15);
            *(undefined4 *)(&stack0x000000e0 + lVar15) = uVar74;
            switch(*(undefined1 *)(lVar54 + 0x18)) {
            default:
              goto switchD_180031274_caseD_0;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xb:
            case 0xd:
            case 0xe:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x13:
            case 0x16:
            case 0x18:
switchD_180031274_caseD_1:
              uVar74 = *(undefined4 *)(lVar54 + 0x1c);
              break;
            case 5:
switchD_180031274_caseD_5:
              uVar74 = 0xffffff;
              if (*(int *)(lVar54 + 0x1c) == 0) {
                uVar74 = 0;
              }
            }
          }
          lVar54 = *(longlong *)(&stack0xfffffffffffffff0 + lVar15);
          auVar67._0_8_ = (double)((fVar72 + fVar71) * 0.5);
          auVar67._8_8_ = 0;
          *(undefined4 *)(&stack0x00000060 + lVar15) = uVar74;
          param_2 = (double)*(float *)(lVar54 + 0x698);
          auVar68 = FUN_180007c30(auVar67,param_2);
          auVar69._0_8_ = (double)fVar65;
          auVar69._8_8_ = 0;
          auVar69 = FUN_180007c30(auVar69,param_2);
          auVar70._0_8_ = (double)extraout_s19;
          auVar70._8_8_ = 0;
          auVar70 = FUN_180007c30(auVar70,param_2);
          pfVar47 = (float *)FUN_180005d08((longlong *)
                                           (*(longlong *)(&stack0x00000078 + lVar15) + 0x30),uVar24)
          ;
          auVar67 = extraout_q0_06;
          if ((*pfVar47 == (float)auVar68._0_8_) && (*(int *)(lVar54 + 0xc) != 0))
          goto LAB_180031e9c;
          *(undefined8 *)(&stack0x000000f8 + lVar15) = 0;
          *(undefined8 *)(&stack0x00000458 + lVar15) = 0;
          *(undefined8 *)(&stack0x00000450 + lVar15) = 0;
          *(undefined8 *)(&stack0x00000460 + lVar15) = 0;
          pcVar56 = "+";
          if (*(int *)(&stack0xffffffffffffffd8 + lVar15) != 1) {
            pcVar56 = "-";
          }
          *(undefined1 **)(&stack0x00000450 + lVar15) = &DAT_1800d4ecd;
          *(undefined8 *)(&stack0x00000458 + lVar15) = 0;
          *(undefined1 **)(&stack0x00000460 + lVar15) = &DAT_1800d4ecd;
          cVar12 = *pcVar56;
          pcVar49 = pcVar56;
          while (cVar12 != '\0') {
            pcVar49 = pcVar49 + 1;
            cVar12 = *pcVar49;
            pfVar64 = (float *)(longlong)cVar12;
          }
          if ((ulonglong)((longlong)pcVar49 - (longlong)pcVar56) < 0x7fffffff) {
            iVar23 = (int)((longlong)pcVar49 - (longlong)pcVar56);
            if (iVar23 != 0) goto LAB_180031540;
LAB_180031578:
            pcVar49 = "";
            *(undefined1 **)(&stack0x00000450 + lVar15) = &DAT_1800d4ecd;
          }
          else {
            iVar23 = 0x7ffffffe;
LAB_180031540:
            pcVar49 = FUN_180004620(iVar23 + 1);
            *(char **)(&stack0x00000450 + lVar15) = pcVar49;
            auVar67 = extraout_q0_07;
            if (pcVar49 == (char *)0x0) goto LAB_180031578;
            FUN_180099d78(pcVar49,(longlong)(iVar23 + 1),(longlong)pcVar56,(longlong)iVar23);
            *(undefined8 *)(&stack0x000000f8 + lVar15) = 0x100000001;
            *(undefined8 *)(&stack0x00000458 + lVar15) = 0x100000001;
            auVar67 = extraout_q0_08;
          }
          uVar52 = iVar22 + 1;
          if ((int)uVar52 <= (int)uVar24) {
            *(char **)(&stack0x000000c0 + lVar15) = pcVar49;
            pfVar64 = *(float **)(&stack0x00000078 + lVar15);
            *(int *)(&stack0x00000088 + lVar15) = iVar22;
            lVar54 = *(longlong *)(&stack0x00000098 + lVar15);
            lVar48 = *(longlong *)(&stack0x000000a0 + lVar15);
            lVar55 = *(longlong *)(&stack0x00000040 + lVar15);
            lVar3 = *(longlong *)(&stack0x00000048 + lVar15);
            do {
              pfVar47 = (float *)FUN_180005d08((longlong *)(pfVar64 + 0xc),uVar52);
              *pfVar47 = (float)auVar68._0_8_;
              pfVar47 = (float *)FUN_180005d08((longlong *)(lVar3 + 0x30),uVar52);
              *pfVar47 = (float)auVar69._0_8_;
              pfVar47 = (float *)FUN_180005d08((longlong *)(lVar55 + 0x30),uVar52);
              iVar22 = *(int *)(&stack0xffffffffffffffd8 + lVar15);
              *pfVar47 = (float)auVar70._0_8_;
              lVar50 = *(longlong *)(lVar3 + 0x58);
              uVar61 = (int)uVar52 >> 0x1f;
              auVar67 = extraout_q0_09;
              if (iVar22 == 1) {
                uVar74 = *(undefined4 *)(lVar48 + 0x1c);
                if (lVar50 == 0) {
                  if (*(code **)(lVar3 + 0x68) != (code *)0x0) {
                    auVar67 = (**(code **)(lVar3 + 0x68))(*(undefined4 *)(lVar3 + 0x70));
                  }
                  lVar50 = *(longlong *)(lVar3 + 0x58);
                  if (lVar50 == 0) {
                    *(undefined4 *)(lVar3 + 0x7c) = uVar74;
                    uVar74 = *(undefined4 *)(lVar54 + 0x1c);
                    goto LAB_1800316a0;
                  }
                }
                iVar22 = *(int *)(lVar3 + 0x78);
                if (iVar22 == 0) {
                  *(undefined4 *)(lVar3 + 0x7c) = uVar74;
                  uVar74 = *(undefined4 *)(lVar54 + 0x1c);
                }
                else {
                  uVar53 = uVar52 & (uVar61 ^ 0xffffffff);
                  if (iVar22 <= (int)uVar53) {
                    uVar53 = iVar22 - 1;
                  }
                  *(undefined4 *)(lVar50 + (longlong)(int)uVar53 * 4) = uVar74;
                  uVar74 = *(undefined4 *)(lVar54 + 0x1c);
                }
              }
              else {
                uVar74 = *(undefined4 *)(lVar48 + 0x18);
                if (lVar50 == 0) {
                  if (*(code **)(lVar3 + 0x68) != (code *)0x0) {
                    auVar67 = (**(code **)(lVar3 + 0x68))(*(undefined4 *)(lVar3 + 0x70));
                  }
                  lVar50 = *(longlong *)(lVar3 + 0x58);
                  if (lVar50 != 0) goto LAB_180031674;
                  puVar35 = (undefined4 *)(lVar3 + 0x7c);
                }
                else {
LAB_180031674:
                  iVar22 = *(int *)(lVar3 + 0x78);
                  if (iVar22 == 0) {
                    puVar35 = (undefined4 *)(lVar3 + 0x7c);
                  }
                  else {
                    uVar53 = uVar52 & (uVar61 ^ 0xffffffff);
                    if (iVar22 <= (int)uVar53) {
                      uVar53 = iVar22 - 1;
                    }
                    puVar35 = (undefined4 *)(lVar50 + (longlong)(int)uVar53 * 4);
                  }
                }
                *puVar35 = uVar74;
                uVar74 = *(undefined4 *)(lVar54 + 0x18);
              }
LAB_1800316a0:
              lVar50 = *(longlong *)(lVar55 + 0x58);
              if (lVar50 == 0) {
                if (*(code **)(lVar55 + 0x68) != (code *)0x0) {
                  auVar67 = (**(code **)(lVar55 + 0x68))(*(undefined4 *)(lVar55 + 0x70));
                }
                lVar50 = *(longlong *)(lVar55 + 0x58);
                if (lVar50 != 0) goto LAB_1800316c8;
                puVar35 = (undefined4 *)(lVar55 + 0x7c);
              }
              else {
LAB_1800316c8:
                iVar22 = *(int *)(lVar55 + 0x78);
                if (iVar22 == 0) {
                  puVar35 = (undefined4 *)(lVar55 + 0x7c);
                }
                else {
                  uVar61 = uVar52 & (uVar61 ^ 0xffffffff);
                  if (iVar22 <= (int)uVar61) {
                    uVar61 = iVar22 - 1;
                  }
                  puVar35 = (undefined4 *)(lVar50 + (longlong)(int)uVar61 * 4);
                }
              }
              uVar52 = uVar52 + 1;
              *puVar35 = uVar74;
            } while ((int)uVar52 <= (int)uVar24);
            iVar22 = *(int *)(&stack0x00000088 + lVar15);
            pcVar49 = *(char **)(&stack0x000000c0 + lVar15);
          }
          if ((**(int **)(&stack0x00000260 + lVar15) == 1) &&
             (uVar36 = FUN_180026708(*(longlong *)(&stack0x00000208 + lVar15)),
             auVar67 = extraout_q0_10, (int)uVar36 != 0)) {
            if (**(int **)(&stack0x00000318 + lVar15) == iVar22) {
              iVar23 = *(int *)(*(longlong *)(&stack0x000000d8 + lVar15) +
                               (longlong)**(int **)(&stack0x00000150 + lVar15) * 4);
              if (iVar23 != 0) {
                lVar54 = *(longlong *)(&stack0xfffffffffffffff0 + lVar15);
                iVar26 = (**(code **)(lVar54 + 0xd90))(*(undefined4 *)(lVar54 + 0x24),iVar23);
                auVar67 = extraout_q0_11;
                if (iVar26 != 0) {
                  auVar67 = (**(code **)(lVar54 + 0xdf8))(*(undefined4 *)(lVar54 + 0x24),iVar23);
                }
              }
            }
            else {
              **(int **)(&stack0x00000318 + lVar15) = iVar22;
              uVar36 = FUN_180026708(extraout_x11_12);
              iVar23 = **(int **)(&stack0x00000150 + lVar15) + 1;
              iVar26 = (int)uVar36;
              if (iVar26 == 0) {
                    /* WARNING: Does not return */
                pcVar46 = (code *)SoftwareBreakpoint(0xf004,0x180031798);
                (*pcVar46)();
              }
              iVar14 = 0;
              if (iVar26 != 0) {
                iVar14 = iVar23 / iVar26;
              }
              iVar23 = iVar23 - iVar14 * iVar26;
              lVar54 = *(longlong *)(&stack0x000000d8 + lVar15);
              **(int **)(&stack0x00000150 + lVar15) = iVar23;
              iVar23 = *(int *)(lVar54 + (longlong)iVar23 * 4);
              auVar67 = extraout_q0_12;
              if (iVar23 != 0) {
                lVar54 = *(longlong *)(&stack0xfffffffffffffff0 + lVar15);
                iVar26 = (**(code **)(lVar54 + 0xd90))(*(undefined4 *)(lVar54 + 0x24),iVar23);
                auVar67 = extraout_q0_13;
                if (iVar26 != 0) {
                  auVar67 = (**(code **)(lVar54 + 0xdf8))(*(undefined4 *)(lVar54 + 0x24),iVar23);
                }
                *(undefined4 *)
                 (*(longlong *)(&stack0x000000d8 + lVar15) +
                 (longlong)**(int **)(&stack0x00000150 + lVar15) * 4) = 0;
              }
            }
            bVar17 = false;
            bVar21 = false;
            lVar54 = *(longlong *)(&stack0x000000e8 + lVar15);
            iVar26 = **(int **)(&stack0x00000150 + lVar15);
            iVar23 = 0;
            if (0 < iVar26) {
              iVar23 = iVar26;
            }
            **(int **)(&stack0x00000150 + lVar15) = iVar23;
            uVar52 = (uint)*(byte *)(lVar54 + 0x18);
            switch(uVar52) {
            default:
              uVar61 = 0;
              if (uVar52 < 0x1d) break;
              goto switchD_180031a0c_caseD_80031b20;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xb:
            case 0xd:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x13:
            case 0x16:
            case 0x18:
              uVar61 = *(uint *)(lVar54 + 0x1c);
              break;
            case 2:
              fVar65 = *(float *)(lVar54 + 0x1c);
              if (0.0 <= fVar65) {
                fVar65 = fVar65 + 0.5;
              }
              else {
                fVar65 = fVar65 - 0.5;
              }
              uVar61 = (uint)fVar65;
              goto switchD_180031a0c_caseD_80031848;
            case 5:
              uVar61 = (uint)(*(int *)(lVar54 + 0x1c) != 0);
              goto switchD_180031a0c_caseD_80031870;
            case 8:
            case 9:
            case 10:
            case 0x12:
            case 0x14:
            case 0x15:
            case 0x1a:
            case 0x1b:
            case 0x1c:
              uVar61 = 0;
              break;
            case 0xe:
              uVar61 = *(uint *)(lVar54 + 0x1c);
              goto switchD_180031a0c_caseD_80031890;
            case 0x17:
              dVar75 = *(double *)(lVar54 + 0x1c);
              if (0.0 <= dVar75) {
                dVar75 = dVar75 + 0.5;
              }
              else {
                dVar75 = dVar75 - 0.5;
              }
              uVar61 = (uint)dVar75;
              goto switchD_180031a0c_caseD_800318b8;
            case 0x19:
              uVar61 = *(uint *)(lVar54 + 0x24);
              goto switchD_180031a0c_caseD_800318d8;
            }
            switch((longlong)(int)(&switchD_180031a0c::switchdataD_180033ed8)[uVar52] * 4 +
                   0x180031890) {
            case 0x180031848:
switchD_180031a0c_caseD_80031848:
              fVar65 = *(float *)(*(longlong *)(&stack0x000000e8 + lVar15) + 0x1c);
              if (0.0 <= fVar65) {
                uVar52 = (uint)(fVar65 + 0.5);
              }
              else {
                uVar52 = (uint)(fVar65 - 0.5);
              }
              break;
            case 0x180031870:
switchD_180031a0c_caseD_80031870:
              uVar52 = (uint)(*(int *)(*(longlong *)(&stack0x000000e8 + lVar15) + 0x1c) != 0);
              break;
            case 0x180031890:
switchD_180031a0c_caseD_80031890:
              uVar52 = *(uint *)(*(longlong *)(&stack0x000000e8 + lVar15) + 0x1c);
              break;
            case 0x1800318b8:
switchD_180031a0c_caseD_800318b8:
              dVar75 = *(double *)(*(longlong *)(&stack0x000000e8 + lVar15) + 0x1c);
              if (0.0 <= dVar75) {
                uVar52 = (uint)(dVar75 + 0.5);
              }
              else {
                uVar52 = (uint)(dVar75 - 0.5);
              }
              break;
            case 0x1800318d8:
switchD_180031a0c_caseD_800318d8:
              uVar52 = *(uint *)(*(longlong *)(&stack0x000000e8 + lVar15) + 0x24);
              break;
            case 0x180031b20:
              goto switchD_180031a0c_caseD_80031b20;
            }
            if (uVar52 != 0) {
              *(uint *)(&stack0x000000b0 + lVar15) = uVar61;
              *(char **)(&stack0x000000c0 + lVar15) = pcVar49;
              pfVar64 = *(float **)(&stack0xfffffffffffffff0 + lVar15);
              lVar48 = 100;
              lVar54 = *(longlong *)(&stack0x00000040 + lVar15);
              lVar55 = *(longlong *)(&stack0x00000048 + lVar15);
              *(int *)(&stack0x00000088 + lVar15) = iVar22;
              piVar63 = *(int **)(&stack0x000000d8 + lVar15);
              do {
                iVar22 = *piVar63;
                if (iVar22 != 0) {
                  FUN_1800bf500(&stack0x00000ce0 + lVar15,'\0',0x3e0);
                  FUN_180009f70((undefined8 *)(&stack0x00000ce0 + lVar15));
                  FUN_18000a310((longlong)(&stack0x00000ce0 + lVar15));
                  iVar22 = (**(code **)(pfVar64 + 0x510))
                                     (pfVar64[9],iVar22,&stack0x00000ce0 + lVar15);
                  if (iVar22 == 0) {
                    auVar67 = extraout_q0_14;
                    if ((*(int *)(&stack0x00001080 + lVar15) != 0) &&
                       (pvVar60 = *(LPVOID *)(&stack0x00001078 + lVar15), pvVar60 != (LPVOID)0x0)) {
                      pvVar33 = GetProcessHeap();
                      HeapFree(pvVar33,0,pvVar60);
                      *(undefined8 *)(&stack0x00001078 + lVar15) = 0;
                      *(undefined8 *)(&stack0x00001080 + lVar15) = 0;
                      auVar67 = extraout_q0_15;
                    }
                    if ((*(int *)(&stack0x00000de8 + lVar15) != 0) &&
                       (pvVar60 = *(LPVOID *)(&stack0x00000de0 + lVar15), pvVar60 != (LPVOID)0x0)) {
                      pvVar33 = GetProcessHeap();
                      HeapFree(pvVar33,0,pvVar60);
                      *(undefined8 *)(&stack0x00000de0 + lVar15) = 0;
                      *(undefined8 *)(&stack0x00000de8 + lVar15) = 0;
                      auVar67 = extraout_q0_16;
                    }
                    if ((*(int *)(&stack0x00000db8 + lVar15) != 0) &&
                       (pvVar60 = *(LPVOID *)(&stack0x00000db0 + lVar15), pvVar60 != (LPVOID)0x0)) {
                      pvVar33 = GetProcessHeap();
                      HeapFree(pvVar33,0,pvVar60);
                      *(undefined8 *)(&stack0x00000db0 + lVar15) = 0;
                      *(undefined8 *)(&stack0x00000db8 + lVar15) = 0;
                      auVar67 = extraout_q0_17;
                    }
                  }
                  else {
                    if ((*(int *)(&stack0x00000dc8 + lVar15) == 0x1a) &&
                       (*(int *)(&stack0x00000e24 + lVar15) == *(int *)(&stack0x00000e28 + lVar15)))
                    {
                      pfVar47 = (float *)FUN_18000f448(lVar55,uVar24);
                      pfVar38 = (float *)FUN_18000f448(lVar54,uVar24);
                      fVar65 = *pfVar47;
                      if (*pfVar47 <= *pfVar38) {
                        fVar65 = *pfVar38;
                      }
                      pfVar47 = (float *)FUN_18000f448(lVar55,uVar24);
                      pfVar38 = (float *)FUN_18000f448(lVar54,uVar24);
                      fVar71 = *pfVar47;
                      if (*pfVar38 <= *pfVar47) {
                        fVar71 = *pfVar38;
                      }
                      fVar76 = *(float *)(&stack0x00000d00 + lVar15);
                      fVar73 = *(float *)(&stack0x00000d04 + lVar15);
                      fVar72 = fVar76;
                      if (fVar76 <= fVar73) {
                        fVar72 = fVar73;
                      }
                      if (fVar73 <= fVar76) {
                        fVar76 = fVar73;
                      }
                      FUN_18000f448(lVar55,uVar24);
                      FUN_18000f448(lVar54,uVar24);
                      bVar18 = true;
                      bVar19 = false;
                      if (fVar71 < fVar72) {
                        bVar18 = false;
                        bVar19 = true;
                        if (!NAN(fVar65) && !NAN(fVar76)) {
                          bVar18 = fVar65 == fVar76;
                          bVar19 = fVar76 <= fVar65;
                        }
                      }
                      if (bVar19 && !bVar18) {
                        bVar18 = false;
                        if ((fVar71 <= fVar72) && (bVar18 = true, !NAN(fVar65) && !NAN(fVar76))) {
                          bVar18 = fVar76 <= fVar65;
                        }
                        if (bVar18) {
                          bVar18 = true;
                          bVar19 = true;
                          bVar20 = false;
                          if (fVar65 < fVar72) {
                            bVar18 = false;
                            bVar19 = false;
                            bVar20 = true;
                            if (!NAN(fVar71) && !NAN(fVar76)) {
                              bVar18 = fVar71 < fVar76;
                              bVar19 = fVar71 == fVar76;
                              bVar20 = false;
                            }
                          }
                          if (bVar19 || bVar18 != bVar20) {
                            bVar21 = true;
                          }
                          else {
                            bVar17 = true;
                          }
                        }
                      }
                    }
                    auVar67 = FUN_18000ae00((longlong)(&stack0x00000ce0 + lVar15));
                  }
                }
                lVar48 = lVar48 + -1;
                piVar63 = piVar63 + 1;
              } while (lVar48 != 0);
              iVar22 = *(int *)(&stack0x00000088 + lVar15);
              pcVar49 = *(char **)(&stack0x000000c0 + lVar15);
              uVar61 = *(uint *)(&stack0x000000b0 + lVar15);
            }
switchD_180031a0c_caseD_80031b20:
            if (uVar61 != 0) {
              if (uVar61 != 1) {
                if (uVar61 != 2) goto LAB_180031b3c;
                if (bVar21) goto LAB_180031e68;
              }
              if (bVar17) goto LAB_180031e68;
            }
LAB_180031b3c:
            bVar21 = FUN_180026690(*(longlong *)(&stack0x00000320 + lVar15));
            lVar54 = *(longlong *)(&stack0x00000190 + lVar15);
            *(uint *)(&stack0x00000088 + lVar15) = (uint)bVar21;
            switch(*(undefined1 *)(lVar54 + 0x18)) {
            default:
              pfVar64 = (float *)0x0;
              break;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xb:
            case 0xd:
            case 0xe:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x13:
            case 0x16:
            case 0x18:
              pfVar64 = (float *)(ulonglong)*(uint *)(lVar54 + 0x1c);
              break;
            case 2:
              fVar65 = *(float *)(lVar54 + 0x1c);
              if (0.0 <= fVar65) {
                pfVar64 = (float *)(ulonglong)(uint)(int)(fVar65 + 0.5);
              }
              else {
                pfVar64 = (float *)(ulonglong)(uint)(int)(fVar65 - 0.5);
              }
              break;
            case 5:
              pfVar64 = (float *)(ulonglong)(*(int *)(lVar54 + 0x1c) != 0);
              break;
            case 0x17:
              dVar75 = *(double *)(lVar54 + 0x1c);
              if (0.0 <= dVar75) {
                pfVar64 = (float *)(ulonglong)(uint)(int)(dVar75 + 0.5);
              }
              else {
                pfVar64 = (float *)(ulonglong)(uint)(int)(dVar75 - 0.5);
              }
              break;
            case 0x19:
              pfVar64 = (float *)(ulonglong)*(uint *)(lVar54 + 0x24);
            }
            bVar21 = FUN_180026690(*(longlong *)(&stack0x00000268 + lVar15));
            *(uint *)(&stack0x000000b0 + lVar15) = (uint)bVar21;
            uVar36 = FUN_180026708(*(longlong *)(&stack0x00000328 + lVar15));
            lVar54 = *(longlong *)(&stack0x00000198 + lVar15);
            switch(*(undefined1 *)(lVar54 + 0x18)) {
            default:
              uVar52 = 0;
              break;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xb:
            case 0xd:
            case 0xe:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x13:
            case 0x16:
            case 0x18:
              uVar52 = *(uint *)(lVar54 + 0x1c);
              break;
            case 2:
              fVar65 = *(float *)(lVar54 + 0x1c);
              if (0.0 <= fVar65) {
                uVar52 = (uint)(fVar65 + 0.5);
              }
              else {
                uVar52 = (uint)(fVar65 - 0.5);
              }
              break;
            case 5:
              uVar52 = (uint)(*(int *)(lVar54 + 0x1c) != 0);
              break;
            case 0x17:
              dVar75 = *(double *)(lVar54 + 0x1c);
              if (0.0 <= dVar75) {
                uVar52 = (uint)(dVar75 + 0.5);
              }
              else {
                uVar52 = (uint)(dVar75 - 0.5);
              }
              break;
            case 0x19:
              uVar52 = *(uint *)(lVar54 + 0x24);
            }
            lVar54 = *(longlong *)(&stack0x000001a0 + lVar15);
            switch(*(undefined1 *)(lVar54 + 0x18)) {
            default:
              uVar61 = 0;
              break;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xb:
            case 0xd:
            case 0xe:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x13:
            case 0x16:
            case 0x18:
              uVar61 = *(uint *)(lVar54 + 0x1c);
              break;
            case 2:
              fVar65 = *(float *)(lVar54 + 0x1c);
              if (0.0 <= fVar65) {
                uVar61 = (uint)(fVar65 + 0.5);
              }
              else {
                uVar61 = (uint)(fVar65 - 0.5);
              }
              break;
            case 5:
              uVar61 = (uint)(*(int *)(lVar54 + 0x1c) != 0);
              break;
            case 0x17:
              dVar75 = *(double *)(lVar54 + 0x1c);
              if (0.0 <= dVar75) {
                uVar61 = (uint)(dVar75 + 0.5);
              }
              else {
                uVar61 = (uint)(dVar75 - 0.5);
              }
              break;
            case 0x19:
              uVar61 = *(uint *)(lVar54 + 0x24);
            }
            uVar40 = FUN_180026708(*(longlong *)(&stack0x00000330 + lVar15));
            *(undefined8 *)(&stack0x00000508 + lVar15) = 0;
            *(undefined8 *)(&stack0x00000500 + lVar15) = 0;
            *(undefined8 *)(&stack0x00000510 + lVar15) = 0;
            *(undefined1 **)(&stack0x00000510 + lVar15) = &DAT_1800d4ecd;
            *(undefined1 **)(&stack0x00000500 + lVar15) = &DAT_1800d4ecd;
            *(undefined8 *)(&stack0x00000508 + lVar15) = 0;
            FUN_1800bf500(&stack0x00000900 + lVar15,'\0',0x3e0);
            FUN_180009f70((undefined8 *)(&stack0x00000900 + lVar15));
            FUN_18000a310((longlong)(&stack0x00000900 + lVar15));
            *(undefined4 *)(&stack0x00000904 + lVar15) =
                 *(undefined4 *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0x24);
            uVar74 = *(undefined4 *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0x10);
            *(uint *)(&stack0x00000a44 + lVar15) = uVar24;
            *(uint *)(&stack0x00000a48 + lVar15) = uVar24;
            *(undefined4 *)(&stack0x00000928 + lVar15) = uVar74;
            *(undefined4 *)(&stack0x000009e8 + lVar15) = 0x1a;
            puVar35 = (undefined4 *)FUN_18000f448(*(longlong *)(&stack0x00000048 + lVar15),uVar24);
            *(undefined4 *)(&stack0x00000920 + lVar15) = *puVar35;
            puVar35 = (undefined4 *)FUN_18000f448(*(longlong *)(&stack0x00000040 + lVar15),uVar24);
            uVar74 = *puVar35;
            *(short *)(&stack0x000009ec + lVar15) = (short)uVar40;
            *(int *)(&stack0x00000918 + lVar15) = (int)uVar36;
            *(undefined4 *)(&stack0x000009f0 + lVar15) = 1;
            *(undefined4 *)(&stack0x0000092c + lVar15) = *(undefined4 *)(&stack0x000000e0 + lVar15);
            *(undefined4 *)(&stack0x00000924 + lVar15) = uVar74;
            *(undefined4 *)(&stack0x00000c74 + lVar15) = 1;
            *(undefined4 *)(&stack0x000009f8 + lVar15) = 1;
            *(undefined4 *)(&stack0x00000a28 + lVar15) = *(undefined4 *)(&stack0x00000060 + lVar15);
            *(undefined8 *)(&stack0x00000c4c + lVar15) = 0x100000001;
            uVar74 = 0;
            if (uVar52 != 0) {
              uVar74 = 2;
            }
            *(undefined4 *)(&stack0x00000c3c + lVar15) = 1;
            *(undefined4 *)(&stack0x00000a20 + lVar15) = uVar74;
            *(undefined2 *)(&stack0x00000c96 + lVar15) = 1;
            *(uint *)(&stack0x00000c04 + lVar15) = (uint)(uVar61 < 2);
            *(undefined2 *)(&stack0x00000c88 + lVar15) = 1;
            uVar30 = FUN_180009f20((int)pfVar64);
            *(short *)(&stack0x000009ee + lVar15) = (short)uVar30;
            *(ushort *)(&stack0x00000c94 + lVar15) =
                 (ushort)(*(int *)(&stack0x000000b0 + lVar15) != 0);
            uVar74 = *(undefined4 *)(&stack0x00000910 + lVar15);
            if (*(int *)(&stack0x00000088 + lVar15) != 0) {
              uVar74 = 1;
            }
            *(undefined4 *)(&stack0x00000910 + lVar15) = uVar74;
            (**(code **)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0xf0))
                      (&stack0x00000900 + lVar15);
            uVar74 = *(undefined4 *)(&stack0x0000090c + lVar15);
            auVar67 = FUN_18000ae00((longlong)(&stack0x00000900 + lVar15));
            *(undefined4 *)
             (*(longlong *)(&stack0x000000d8 + lVar15) +
             (longlong)**(int **)(&stack0x00000150 + lVar15) * 4) = uVar74;
          }
LAB_180031e68:
          if ((*(int *)(&stack0x000000f8 + lVar15) != 0) && (pcVar49 != (char *)0x0)) {
            pvVar33 = GetProcessHeap();
            HeapFree(pvVar33,0,pcVar49);
            *(undefined8 *)(&stack0x00000450 + lVar15) = 0;
            *(undefined8 *)(&stack0x00000458 + lVar15) = 0;
            auVar67 = extraout_q0_18;
          }
        }
LAB_180031e9c:
        fVar65 = 1.0;
        lVar54 = *(longlong *)(&stack0xffffffffffffffe0 + lVar15);
        **(undefined4 **)(&stack0x00000290 + lVar15) =
             *(undefined4 *)(&stack0xffffffffffffffd8 + lVar15);
        bVar10 = *(byte *)(lVar54 + 0x18);
        if (bVar10 < 0x1d) {
          switch(bVar10) {
          case 0:
          case 7:
          case 8:
          case 9:
          case 10:
          case 0xc:
          case 0x12:
          case 0x14:
          case 0x15:
          case 0x1a:
          case 0x1b:
          case 0x1c:
            goto switchD_180031ec8_caseD_0;
          default:
            uVar52 = *(uint *)(lVar54 + 0x1c);
            break;
          case 2:
            fVar71 = *(float *)(lVar54 + 0x1c);
            if (0.0 <= fVar71) {
              uVar52 = (uint)(fVar71 + 0.5);
            }
            else {
              uVar52 = (uint)(fVar71 - 0.5);
            }
            break;
          case 5:
            uVar52 = (uint)(*(int *)(lVar54 + 0x1c) != 0);
            break;
          case 0x17:
            dVar75 = *(double *)(lVar54 + 0x1c);
            if (0.0 <= dVar75) {
              uVar52 = (uint)(dVar75 + 0.5);
            }
            else {
              uVar52 = (uint)(dVar75 - 0.5);
            }
            break;
          case 0x19:
            uVar52 = *(uint *)(lVar54 + 0x24);
          }
          if (uVar52 != 2) {
            if (bVar10 < 0x1d) {
switchD_180031ec8_caseD_0:
                    /* WARNING: Could not recover jumptable at 0x000180031f50. Too many branches */
                    /* WARNING: Treating indirect jump as call */
              (*(code *)(&LAB_180031f54 +
                        (longlong)*(int *)(&DAT_18003411c + (ulonglong)(uint)bVar10 * 4) * 4))();
              return;
            }
            goto LAB_1800322d8;
          }
          if (iVar22 < 0) goto LAB_1800322d8;
          lVar54 = *(longlong *)*plVar59;
          if (lVar54 == 0) {
            pcVar46 = *(code **)((&lStack_8)[extraout_x15 * -2] + 0x10);
            if (pcVar46 != (code *)0x0) {
              auVar67 = (*pcVar46)(*(undefined4 *)((&lStack_8)[extraout_x15 * -2] + 0x18));
            }
            lVar54 = *(longlong *)*plVar59;
            if (lVar54 != 0) goto LAB_18003200c;
            pfVar47 = (float *)(*plVar59 + 0x24);
          }
          else {
LAB_18003200c:
            iVar23 = *(int *)((&lStack_8)[extraout_x15 * -2] + 0x20);
            if (iVar23 == 0) {
              pfVar47 = (float *)(*plVar59 + 0x24);
            }
            else {
              uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
              if (iVar23 <= (int)uVar52) {
                uVar52 = iVar23 - 1;
              }
              pfVar47 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
            }
          }
          fVar71 = *pfVar47;
          if (0 < iVar22) {
            lVar54 = **(longlong **)(&stack0x00000000 + lVar15);
            if (lVar54 == 0) {
              pcVar46 = *(code **)(*(longlong *)(&stack0x00000028 + lVar15) + 0x10);
              if (pcVar46 != (code *)0x0) {
                auVar67 = (*pcVar46)(*(undefined4 *)
                                      (*(longlong *)(&stack0x00000028 + lVar15) + 0x18));
              }
              lVar54 = **(longlong **)(&stack0x00000000 + lVar15);
              if (lVar54 != 0) goto LAB_18003207c;
              pfVar47 = (float *)((longlong)*(longlong **)(&stack0x00000000 + lVar15) + 0x24);
            }
            else {
LAB_18003207c:
              iVar23 = *(int *)(*(longlong *)(&stack0x00000028 + lVar15) + 0x20);
              if (iVar23 == 0) {
                pfVar47 = (float *)(*(longlong *)(&stack0x00000000 + lVar15) + 0x24);
              }
              else {
                iVar26 = iVar22;
                if (iVar23 <= iVar22) {
                  iVar26 = iVar23 + -1;
                }
                pfVar47 = (float *)(lVar54 + (longlong)iVar26 * 4);
              }
            }
            if (*pfVar47 != 0.0) goto LAB_18003219c;
            lVar54 = *(longlong *)(&stack0xffffffffffffffe0 + lVar15);
            switch(*(undefined1 *)(lVar54 + 0x18)) {
            default:
              goto switchD_1800320d0_caseD_0;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xb:
            case 0xd:
            case 0xe:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x13:
            case 0x16:
            case 0x18:
              uVar52 = *(uint *)(lVar54 + 0x1c);
              break;
            case 2:
              fVar72 = *(float *)(lVar54 + 0x1c);
              if (0.0 <= fVar72) {
                uVar52 = (uint)(fVar72 + 0.5);
              }
              else {
                uVar52 = (uint)(fVar72 - 0.5);
              }
              break;
            case 5:
              uVar52 = (uint)(*(int *)(lVar54 + 0x1c) != 0);
              break;
            case 0x17:
              dVar75 = *(double *)(lVar54 + 0x1c);
              if (0.0 <= dVar75) {
                uVar52 = (uint)(dVar75 + 0.5);
              }
              else {
                uVar52 = (uint)(dVar75 - 0.5);
              }
              break;
            case 0x19:
              uVar52 = *(uint *)(lVar54 + 0x24);
            }
            if ((uVar52 != 2) ||
               (pfVar47 = (float *)FUN_180005d08(*(longlong **)(&stack0x00000068 + lVar15),iVar22),
               fVar71 == *pfVar47)) {
switchD_1800320d0_caseD_0:
              uVar52 = FUN_180026550(*(longlong *)(&stack0xffffffffffffffe0 + lVar15));
              auVar67 = extraout_q0_19;
              if (uVar52 != 3) goto LAB_18003219c;
            }
            lVar54 = *(longlong *)(&stack0x00000050 + lVar15);
            pfVar47 = (float *)FUN_18000f448(lVar54,uVar24);
            pfVar38 = (float *)FUN_18000f448(lVar54,iVar22);
            auVar67 = extraout_q0_20;
            if (*pfVar47 < *pfVar38) {
              fVar71 = -fVar71;
            }
          }
LAB_18003219c:
          plVar31 = *(longlong **)(&stack0x00000068 + lVar15);
          lVar54 = *plVar31;
          if (lVar54 == 0) {
            if ((code *)plVar31[2] != (code *)0x0) {
              auVar67 = (*(code *)plVar31[2])((int)plVar31[3]);
            }
            lVar54 = **(longlong **)(&stack0x00000068 + lVar15);
            if (lVar54 != 0) goto LAB_1800321cc;
            pfVar47 = (float *)((longlong)*(longlong **)(&stack0x00000068 + lVar15) + 0x24);
          }
          else {
LAB_1800321cc:
            iVar23 = *(int *)(*(longlong *)(&stack0x00000068 + lVar15) + 0x20);
            if (iVar23 == 0) {
              pfVar47 = (float *)(*(longlong *)(&stack0x00000068 + lVar15) + 0x24);
            }
            else {
              uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
              if (iVar23 <= (int)uVar52) {
                uVar52 = iVar23 - 1;
              }
              pfVar47 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
            }
          }
          if ((*pfVar47 != fVar71) && (uVar52 = iVar22 + 1, (int)uVar52 <= (int)uVar24)) {
            lVar54 = *(longlong *)(&stack0xffffffffffffffe8 + lVar15);
            pfVar64 = *(float **)(&stack0x00000018 + lVar15);
            plVar31 = *(longlong **)(&stack0x00000068 + lVar15);
            do {
              lVar55 = *plVar31;
              if (lVar55 == 0) {
                if ((code *)plVar31[2] != (code *)0x0) {
                  auVar67 = (*(code *)plVar31[2])((int)plVar31[3]);
                }
                lVar55 = *plVar31;
                if (lVar55 != 0) goto LAB_180032240;
                pfVar47 = (float *)((longlong)plVar31 + 0x24);
              }
              else {
LAB_180032240:
                iVar22 = (int)plVar31[4];
                if (iVar22 == 0) {
                  pfVar47 = (float *)((longlong)plVar31 + 0x24);
                }
                else {
                  uVar61 = uVar52 & ((int)uVar52 >> 0x1f ^ 0xffffffffU);
                  if (iVar22 <= (int)uVar61) {
                    uVar61 = iVar22 - 1;
                  }
                  pfVar47 = (float *)(lVar55 + (longlong)(int)uVar61 * 4);
                }
              }
              *pfVar47 = fVar71;
              lVar55 = *(longlong *)(lVar54 + 0x58);
              if (fVar71 <= 0.0) {
                fVar72 = pfVar64[7];
              }
              else {
                fVar72 = pfVar64[6];
              }
              if (lVar55 == 0) {
                if (*(code **)(lVar54 + 0x68) != (code *)0x0) {
                  auVar67 = (**(code **)(lVar54 + 0x68))(*(undefined4 *)(lVar54 + 0x70));
                }
                lVar55 = *(longlong *)(lVar54 + 0x58);
                if (lVar55 != 0) goto LAB_1800322a4;
                pfVar47 = (float *)(lVar54 + 0x7c);
              }
              else {
LAB_1800322a4:
                iVar22 = *(int *)(lVar54 + 0x78);
                if (iVar22 == 0) {
                  pfVar47 = (float *)(lVar54 + 0x7c);
                }
                else {
                  uVar61 = uVar52 & ((int)uVar52 >> 0x1f ^ 0xffffffffU);
                  if (iVar22 <= (int)uVar61) {
                    uVar61 = iVar22 - 1;
                  }
                  pfVar47 = (float *)(lVar55 + (longlong)(int)uVar61 * 4);
                }
              }
              uVar52 = uVar52 + 1;
              *pfVar47 = fVar72;
            } while ((int)uVar52 <= (int)uVar24);
          }
        }
LAB_1800322d8:
        uVar4 = *(undefined8 *)(&stack0x00000030 + lVar15);
        uVar30 = *(undefined8 *)(&stack0x00000070 + lVar15);
        uVar5 = *(undefined8 *)(&stack0x00000078 + lVar15);
        uVar41 = *(undefined8 *)(&stack0x00000090 + lVar15);
        uVar6 = *(undefined8 *)(&stack0x00000098 + lVar15);
        uVar42 = *(undefined8 *)(&stack0x00000018 + lVar15);
        uVar7 = *(undefined8 *)(&stack0x00000020 + lVar15);
        *(undefined8 *)(&stack0x000000c0 + lVar15) = *(undefined8 *)(&stack0x00000028 + lVar15);
        uVar1 = *(undefined8 *)(&stack0x00000038 + lVar15);
        uVar8 = *(undefined8 *)(&stack0x00000040 + lVar15);
        *(undefined8 *)(&stack0x00000238 + lVar15) = uVar30;
        *(undefined8 *)(&stack0x00000240 + lVar15) = *(undefined8 *)(&stack0x00000028 + lVar15);
        uVar44 = *(undefined8 *)(&stack0x00000080 + lVar15);
        *(undefined8 *)(&stack0x00000100 + lVar15) = uVar41;
        uVar58 = *(undefined8 *)(&stack0x000000a0 + lVar15);
        *(undefined8 *)(&stack0x00000118 + lVar15) = uVar4;
        *(undefined8 *)(&stack0x00000120 + lVar15) = uVar42;
        uVar57 = *(undefined8 *)(&stack0x00000048 + lVar15);
        *(undefined8 *)(&stack0x00000138 + lVar15) = uVar1;
        **(uint **)(&stack0x00000158 + lVar15) = uVar24;
        lVar55 = (&lStack_8)[extraout_x15 * -2];
        plVar31 = (longlong *)*plVar59;
        *(undefined8 *)(&stack0x000004a8 + lVar15) = 0;
        *(undefined8 *)(&stack0x000004a0 + lVar15) = 0;
        *(undefined8 *)(&stack0x000004b0 + lVar15) = 0;
        *(undefined1 **)(&stack0x000004b0 + lVar15) = &DAT_1800d4ecd;
        *(undefined1 **)(&stack0x000004a0 + lVar15) = &DAT_1800d4ecd;
        *(undefined8 *)(&stack0x000004a8 + lVar15) = 0;
        *(undefined8 *)(&stack0x000001b8 + lVar15) = uVar7;
        *(undefined8 *)(&stack0x000001c0 + lVar15) = uVar5;
        *(undefined8 *)(&stack0x000001c8 + lVar15) = uVar44;
        *(undefined8 *)(&stack0x000001a8 + lVar15) = uVar57;
        *(undefined8 *)(&stack0x000001b0 + lVar15) = uVar58;
        *(undefined8 *)(&stack0x00000218 + lVar15) = uVar6;
        *(undefined8 *)(&stack0x00000220 + lVar15) = uVar8;
        *(longlong *)(&stack0x00000228 + lVar15) = lVar55;
        *(longlong **)(&stack0x00000230 + lVar15) = plVar31;
        *(undefined8 *)(&stack0x000000f0 + lVar15) = uVar42;
        *(undefined8 *)(&stack0x000001d8 + lVar15) = uVar41;
        *(undefined8 *)(&stack0x000002d0 + lVar15) = uVar4;
        *(undefined8 *)(&stack0x00000378 + lVar15) = uVar1;
        *(undefined8 *)(&stack0x00000368 + lVar15) = uVar5;
        *(undefined8 *)(&stack0x00000370 + lVar15) = uVar7;
        *(undefined8 *)(&stack0x00000358 + lVar15) = uVar58;
        *(undefined8 *)(&stack0x00000360 + lVar15) = uVar44;
        *(undefined8 *)(&stack0x00000348 + lVar15) = uVar6;
        *(undefined8 *)(&stack0x00000350 + lVar15) = uVar57;
        *(longlong *)(&stack0x00000338 + lVar15) = lVar55;
        *(undefined8 *)(&stack0x00000340 + lVar15) = uVar8;
        *(longlong **)(&stack0x00000088 + lVar15) = plVar31;
        *(undefined8 *)(&stack0x000000e0 + lVar15) = uVar30;
        lVar54 = *plVar31;
        if (lVar54 == 0) {
          if (*(code **)(lVar55 + 0x10) != (code *)0x0) {
            auVar67 = (**(code **)(lVar55 + 0x10))(*(undefined4 *)(lVar55 + 0x18));
          }
          lVar54 = *(longlong *)*plVar59;
          if (lVar54 != 0) goto LAB_1800323c0;
          pfVar47 = (float *)(*plVar59 + 0x24);
        }
        else {
LAB_1800323c0:
          iVar22 = *(int *)((&lStack_8)[extraout_x15 * -2] + 0x20);
          if (iVar22 == 0) {
            pfVar47 = (float *)(*plVar59 + 0x24);
          }
          else {
            uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
            if (iVar22 <= (int)uVar52) {
              uVar52 = iVar22 - 1;
            }
            pfVar47 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
          }
        }
        fVar71 = *pfVar47;
        *(undefined8 *)(&stack0x000004c8 + lVar15) = 0;
        uVar30 = *(undefined8 *)(&stack0x00000050 + lVar15);
        uVar41 = *(undefined8 *)(&stack0x00000058 + lVar15);
        *(undefined4 *)(&stack0x00000470 + lVar15) = 0;
        lVar54 = *(longlong *)(&stack0xffffffffffffffe8 + lVar15);
        piVar63 = *(int **)(&stack0xfffffffffffffff0 + lVar15);
        uVar42 = *(undefined8 *)(&stack0x00000000 + lVar15);
        plVar34 = *(longlong **)(&stack0x00000010 + lVar15);
        *(undefined8 *)(&stack0x000001d0 + lVar15) = uVar41;
        lVar55 = *(longlong *)(&stack0x00000180 + lVar15);
        *(longlong *)(&stack0x00000108 + lVar15) = lVar54;
        *(undefined8 *)(&stack0x00000110 + lVar15) = uVar41;
        plVar31 = (longlong *)*plVar59;
        uVar74 = 8;
        if (fVar71 != 1.0) {
          uVar74 = 0;
        }
        *(undefined8 *)(&stack0x00000140 + lVar15) = uVar30;
        *(undefined4 *)(&stack0xffffffffffffffd8 + lVar15) = uVar74;
        *(undefined1 **)(&stack0xffffffffffffffc0 + lVar15) = &stack0x00000470 + lVar15;
        *(undefined8 *)(&stack0xffffffffffffffb0 + lVar15) = uVar41;
        *(undefined1 **)(&stack0xffffffffffffffb8 + lVar15) = &stack0x000004c8 + lVar15;
        *(longlong **)(&stack0x00000128 + lVar15) = plVar34;
        *(undefined8 *)(&stack0x000000f8 + lVar15) = uVar42;
        *(longlong *)(&stack0x00000060 + lVar15) = lVar54;
        *(undefined8 *)(&stack0xffffffffffffffa8 + lVar15) =
             *(undefined8 *)(&stack0x00000210 + lVar15);
        *(undefined8 *)(&stack0xffffffffffffff98 + lVar15) =
             *(undefined8 *)(&stack0x00000248 + lVar15);
        *(undefined8 *)(&stack0xffffffffffffffa0 + lVar15) = uVar42;
        *(undefined8 *)(&stack0xffffffffffffff90 + lVar15) =
             *(undefined8 *)(&stack0x00000250 + lVar15);
        *(undefined8 *)(&stack0xffffffffffffff88 + lVar15) =
             *(undefined8 *)(&stack0x00000258 + lVar15);
        *(undefined8 *)(&stack0xffffffffffffff80 + lVar15) =
             *(undefined8 *)(&stack0x00000160 + lVar15);
        *(undefined8 *)(&stack0xffffffffffffff70 + lVar15) =
             *(undefined8 *)(&stack0x00000168 + lVar15);
        *(undefined8 *)(&stack0xffffffffffffff78 + lVar15) = uVar30;
        *(undefined8 *)(&stack0xffffffffffffff68 + lVar15) =
             *(undefined8 *)(&stack0x000001e0 + lVar15);
        uVar36 = *(ulonglong *)(&stack0x000001f0 + lVar15);
        *(undefined8 *)(&stack0xffffffffffffff60 + lVar15) =
             *(undefined8 *)(&stack0x000001e8 + lVar15);
        acStack_a8[lVar15] = (&stack0x000000b8)[lVar15];
        (&lStack_b0)[extraout_x15 * -2] = *(undefined8 *)(&stack0x00000170 + lVar15);
        auVar67 = FUN_18002ab40(auVar67,param_2,(undefined8 *)(&stack0x000004a0 + lVar15),plVar31,
                                uVar24,plVar34,uVar36,lVar55,piVar63,lVar54,
                                (&lStack_b0)[extraout_x15 * -2],acStack_a8[lVar15],
                                *(longlong *)(&stack0xffffffffffffff60 + lVar15),
                                *(longlong *)(&stack0xffffffffffffff68 + lVar15),
                                *(longlong *)(&stack0xffffffffffffff70 + lVar15),
                                *(longlong *)(&stack0xffffffffffffff78 + lVar15),
                                *(longlong *)(&stack0xffffffffffffff80 + lVar15),
                                *(longlong *)(&stack0xffffffffffffff88 + lVar15),
                                *(longlong *)(&stack0xffffffffffffff90 + lVar15),
                                *(longlong *)(&stack0xffffffffffffff98 + lVar15),
                                *(longlong **)(&stack0xffffffffffffffa0 + lVar15),
                                *(longlong *)(&stack0xffffffffffffffa8 + lVar15),
                                *(longlong *)(&stack0xffffffffffffffb0 + lVar15),
                                *(double **)(&stack0xffffffffffffffb8 + lVar15),
                                *(uint **)(&stack0xffffffffffffffc0 + lVar15));
        pcVar56 = *(char **)(&stack0x000004a0 + lVar15);
        if (pcVar56 != (char *)0x0) {
          cVar12 = *pcVar56;
          pcVar49 = pcVar56;
          while (cVar12 != '\0') {
            pcVar49 = pcVar49 + 1;
            cVar12 = *pcVar49;
            pfVar64 = (float *)(longlong)cVar12;
          }
          if ((((ulonglong)((longlong)pcVar49 - (longlong)pcVar56) < 0x7fffffff) &&
              ((int)((longlong)pcVar49 - (longlong)pcVar56) < 1)) ||
             (lVar54 = *(longlong *)(&stack0xfffffffffffffff0 + lVar15),
             *(int *)(lVar54 + 0x968) != 0)) goto LAB_180032f18;
          FUN_1800bf500(&stack0x00000520 + lVar15,'\0',0x3e0);
          FUN_180009f70((undefined8 *)(&stack0x00000520 + lVar15));
          FUN_18000a310((longlong)(&stack0x00000520 + lVar15));
          lVar55 = *(longlong *)(&stack0x00000130 + lVar15);
          *(undefined4 *)(&stack0x00000524 + lVar15) = *(undefined4 *)(lVar54 + 0x24);
          uVar74 = *(undefined4 *)(lVar54 + 0x10);
          *(undefined4 *)(&stack0x00000618 + lVar15) = 0;
          *(uint *)(&stack0x00000664 + lVar15) = uVar24;
          *(undefined4 *)(&stack0x00000548 + lVar15) = uVar74;
          *(undefined4 *)(&stack0x00000608 + lVar15) = 6;
          switch(*(undefined1 *)(lVar55 + 0x18)) {
          default:
            goto switchD_180032550_caseD_0;
          case 1:
          case 3:
          case 4:
          case 6:
          case 0xb:
          case 0xd:
          case 0xe:
          case 0xf:
          case 0x10:
          case 0x11:
          case 0x13:
          case 0x16:
          case 0x18:
            uVar52 = *(uint *)(lVar55 + 0x1c);
            break;
          case 2:
            fVar71 = *(float *)(lVar55 + 0x1c);
            if (0.0 <= fVar71) {
              uVar52 = (uint)(fVar71 + 0.5);
            }
            else {
              uVar52 = (uint)(fVar71 - 0.5);
            }
            break;
          case 5:
            uVar52 = (uint)(*(int *)(lVar55 + 0x1c) != 0);
            break;
          case 0x17:
            dVar75 = *(double *)(lVar55 + 0x1c);
            if (0.0 <= dVar75) {
              uVar52 = (uint)(dVar75 + 0.5);
            }
            else {
              uVar52 = (uint)(dVar75 - 0.5);
            }
            break;
          case 0x19:
            uVar52 = *(uint *)(lVar55 + 0x24);
          }
          if (uVar52 == 0) {
switchD_180032550_caseD_0:
            lVar55 = *(longlong *)(&stack0xfffffffffffffff0 + lVar15);
            lVar54 = *(longlong *)(lVar55 + 0xa60);
            if (lVar54 == 0) {
              if (*(code **)(lVar55 + 0xa70) != (code *)0x0) {
                (**(code **)(lVar55 + 0xa70))(*(undefined4 *)(lVar55 + 0xa78));
              }
              lVar54 = *(longlong *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0xa60);
              if (lVar54 != 0) goto LAB_18003269c;
              pfVar47 = (float *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0xa84);
            }
            else {
LAB_18003269c:
              iVar22 = *(int *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0xa80);
              if (iVar22 == 0) {
                pfVar47 = (float *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0xa84);
              }
              else {
                uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
                if (iVar22 <= (int)uVar52) {
                  uVar52 = iVar22 - 1;
                }
                pfVar47 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
              }
            }
            lVar55 = *(longlong *)(&stack0xfffffffffffffff0 + lVar15);
            lVar54 = *(longlong *)(lVar55 + 0xa88);
            if (lVar54 == 0) {
              if (*(code **)(lVar55 + 0xa98) != (code *)0x0) {
                (**(code **)(lVar55 + 0xa98))(*(undefined4 *)(lVar55 + 0xaa0));
              }
              lVar54 = *(longlong *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0xa88);
              if (lVar54 != 0) goto LAB_1800326f4;
              pfVar38 = (float *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0xaac);
            }
            else {
LAB_1800326f4:
              iVar22 = *(int *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0xaa8);
              if (iVar22 == 0) {
                pfVar38 = (float *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0xaac);
              }
              else {
                uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
                if (iVar22 <= (int)uVar52) {
                  uVar52 = iVar22 - 1;
                }
                pfVar38 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
              }
            }
            lVar54 = *(longlong *)(&stack0x00000178 + lVar15);
            switch(*(undefined1 *)(lVar54 + 0x18)) {
            default:
              fVar65 = 0.0;
              break;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xd:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x16:
            case 0x18:
              fVar65 = (float)*(uint *)(lVar54 + 0x1c);
              break;
            case 2:
              fVar65 = *(float *)(lVar54 + 0x1c);
              break;
            case 5:
              if (*(int *)(lVar54 + 0x1c) == 0) {
                fVar65 = 0.0;
              }
              break;
            case 0xb:
            case 0x13:
              fVar65 = (float)*(int *)(lVar54 + 0x1c);
              break;
            case 0x17:
              fVar65 = (float)*(double *)(lVar54 + 0x1c);
            }
            fVar65 = (*pfVar47 - *pfVar38) * fVar65 * 0.01;
          }
          else {
            lVar54 = *(longlong *)(&stack0x00000178 + lVar15);
            switch(*(undefined1 *)(lVar54 + 0x18)) {
            default:
              fVar65 = *(float *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0x698) * 0.0;
              break;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xd:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x16:
            case 0x18:
              fVar65 = *(float *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0x698) *
                       (float)*(uint *)(lVar54 + 0x1c);
              break;
            case 2:
              fVar65 = *(float *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0x698) *
                       *(float *)(lVar54 + 0x1c);
              break;
            case 5:
              if (*(int *)(lVar54 + 0x1c) == 0) {
                fVar65 = 0.0;
              }
              fVar65 = *(float *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0x698) * fVar65
              ;
              break;
            case 0xb:
            case 0x13:
              fVar65 = *(float *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0x698) *
                       (float)*(int *)(lVar54 + 0x1c);
              break;
            case 0x17:
              fVar65 = *(float *)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0x698) *
                       (float)*(double *)(lVar54 + 0x1c);
            }
          }
          lVar54 = *(longlong *)*plVar59;
          if (lVar54 == 0) {
            pcVar46 = *(code **)((&lStack_8)[extraout_x15 * -2] + 0x10);
            if (pcVar46 != (code *)0x0) {
              (*pcVar46)(*(undefined4 *)((&lStack_8)[extraout_x15 * -2] + 0x18));
            }
            lVar54 = *(longlong *)*plVar59;
            if (lVar54 != 0) goto LAB_1800327cc;
            pfVar47 = (float *)(*plVar59 + 0x24);
          }
          else {
LAB_1800327cc:
            iVar22 = *(int *)((&lStack_8)[extraout_x15 * -2] + 0x20);
            if (iVar22 == 0) {
              pfVar47 = (float *)(*plVar59 + 0x24);
            }
            else {
              uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
              if (iVar22 <= (int)uVar52) {
                uVar52 = iVar22 - 1;
              }
              pfVar47 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
            }
          }
          if (*pfVar47 == 1.0) {
            pfVar47 = (float *)FUN_180005d08(*(longlong **)(&stack0x00000008 + lVar15),uVar24);
            fVar65 = fVar65 + *pfVar47;
          }
          else {
            pfVar47 = (float *)FUN_180005d08(*(longlong **)(&stack0x00000008 + lVar15),uVar24);
            fVar65 = *pfVar47 - fVar65;
          }
          *(float *)(&stack0x00000540 + lVar15) = fVar65;
          lVar54 = *(longlong *)*plVar59;
          if (lVar54 == 0) {
            pcVar46 = *(code **)((&lStack_8)[extraout_x15 * -2] + 0x10);
            if (pcVar46 != (code *)0x0) {
              (*pcVar46)(*(undefined4 *)((&lStack_8)[extraout_x15 * -2] + 0x18));
            }
            lVar54 = *(longlong *)*plVar59;
            if (lVar54 != 0) goto LAB_180032860;
            pfVar47 = (float *)(*plVar59 + 0x24);
          }
          else {
LAB_180032860:
            iVar22 = *(int *)((&lStack_8)[extraout_x15 * -2] + 0x20);
            if (iVar22 == 0) {
              pfVar47 = (float *)(*plVar59 + 0x24);
            }
            else {
              uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
              if (iVar22 <= (int)uVar52) {
                uVar52 = iVar22 - 1;
              }
              pfVar47 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
            }
          }
          lVar54 = *(longlong *)(&stack0x000000a8 + lVar15);
          if (*pfVar47 == 1.0) {
            uVar74 = *(undefined4 *)(lVar54 + 0x18);
          }
          else {
            uVar74 = *(undefined4 *)(lVar54 + 0x1c);
          }
          *(undefined4 *)(&stack0x0000054c + lVar15) = uVar74;
          pcVar56 = *(char **)(&stack0x000004a0 + lVar15);
          *(uint *)(&stack0x00000558 + lVar15) =
               (uint)*(ushort *)(*(longlong *)(&stack0x000000a8 + lVar15) + 0x28);
          *(undefined4 *)(&stack0x00000638 + lVar15) = 1;
          if (((pcVar56 != (char *)0x0) && (*pcVar56 != '\0')) ||
             ((*(char **)(&stack0x000005f0 + lVar15) != (char *)0x0 &&
              (**(char **)(&stack0x000005f0 + lVar15) != '\0')))) {
            if (pcVar56 == (char *)0x0) {
              FUN_180005ff0((undefined8 *)(&stack0x000005f0 + lVar15));
            }
            else {
              cVar12 = *pcVar56;
              pcVar49 = pcVar56;
              while (cVar12 != '\0') {
                pcVar49 = pcVar49 + 1;
                cVar12 = *pcVar49;
                pfVar64 = (float *)(longlong)cVar12;
              }
              iVar22 = 0x7ffffffe;
              if ((ulonglong)((longlong)pcVar49 - (longlong)pcVar56) < 0x7fffffff) {
                iVar22 = (int)((longlong)pcVar49 - (longlong)pcVar56);
              }
              FUN_1800079f8((longlong *)(&stack0x000005f0 + lVar15),(longlong)pcVar56,iVar22);
            }
            *(undefined4 *)(&stack0x000005fc + lVar15) = 1;
          }
          *(undefined4 *)(&stack0x00000610 + lVar15) = 1;
          *(undefined4 *)(&stack0x0000052c + lVar15) = 0xffffffff;
          bVar21 = FUN_180026690(*(longlong *)(&stack0x00000180 + lVar15));
          if (bVar21) {
            lVar55 = *(longlong *)(&stack0x000000c8 + lVar15);
            bVar10 = *(byte *)(lVar55 + 0x18);
            *(uint *)(&stack0x000000b0 + lVar15) = (uint)bVar10;
            switch(bVar10) {
            default:
              goto switchD_18003296c_caseD_0;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xb:
            case 0xd:
            case 0xe:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x13:
            case 0x16:
            case 0x18:
              uVar52 = *(uint *)(lVar55 + 0x1c);
              break;
            case 2:
              fVar65 = *(float *)(lVar55 + 0x1c);
              if (0.0 <= fVar65) {
                uVar52 = (uint)(fVar65 + 0.5);
              }
              else {
                uVar52 = (uint)(fVar65 - 0.5);
              }
              break;
            case 5:
              uVar52 = (uint)(*(int *)(lVar55 + 0x1c) != 0);
              break;
            case 0x17:
              dVar75 = *(double *)(lVar55 + 0x1c);
              if (0.0 <= dVar75) {
                uVar52 = (uint)(dVar75 + 0.5);
              }
              else {
                uVar52 = (uint)(dVar75 - 0.5);
              }
              break;
            case 0x19:
              uVar52 = *(uint *)(lVar55 + 0x24);
            }
            if (uVar52 != 0) {
              uVar30 = *(undefined8 *)(&stack0x000000f8 + lVar15);
              uVar5 = *(undefined8 *)(&stack0x00000100 + lVar15);
              *(float **)(&stack0x000000f0 + lVar15) = pfVar64;
              lVar55 = *(longlong *)(&stack0x00000230 + lVar15);
              uVar41 = *(undefined8 *)(&stack0x00000238 + lVar15);
              uVar6 = *(undefined8 *)(&stack0x00000240 + lVar15);
              uVar42 = *(undefined8 *)(&stack0x00000218 + lVar15);
              uVar7 = *(undefined8 *)(&stack0x00000220 + lVar15);
              uVar1 = *(undefined8 *)(&stack0x000001a8 + lVar15);
              uVar8 = *(undefined8 *)(&stack0x000001b0 + lVar15);
              *(undefined8 *)(&stack0x000000c0 + lVar15) = uVar30;
              uVar4 = *(undefined8 *)(&stack0x000001b8 + lVar15);
              uVar44 = *(undefined8 *)(&stack0x000001c0 + lVar15);
              (&lStack_8)[extraout_x15 * -2] = *(longlong *)(&stack0x00000228 + lVar15);
              *(undefined8 *)(&stack0x00000000 + lVar15) = uVar30;
              *plVar59 = lVar55;
              *(undefined8 *)(&stack0x00000090 + lVar15) = uVar5;
              *(undefined8 *)(&stack0x00000098 + lVar15) = uVar42;
              *(undefined8 *)(&stack0x00000040 + lVar15) = uVar7;
              *(undefined8 *)(&stack0x00000048 + lVar15) = uVar1;
              *(undefined8 *)(&stack0x00000070 + lVar15) = uVar41;
              *(undefined8 *)(&stack0x00000078 + lVar15) = uVar44;
              *(undefined8 *)(&stack0x00000020 + lVar15) = uVar4;
              *(undefined8 *)(&stack0x00000028 + lVar15) = uVar6;
              *(undefined8 *)(&stack0x00000030 + lVar15) =
                   *(undefined8 *)(&stack0x00000118 + lVar15);
              *(undefined8 *)(&stack0x00000038 + lVar15) =
                   *(undefined8 *)(&stack0x00000138 + lVar15);
              *(undefined8 *)(&stack0x00000050 + lVar15) =
                   *(undefined8 *)(&stack0x00000140 + lVar15);
              *(undefined8 *)(&stack0x00000058 + lVar15) =
                   *(undefined8 *)(&stack0x00000110 + lVar15);
              *(undefined8 *)(&stack0x000000a0 + lVar15) = uVar8;
              *(longlong *)(&stack0x000000a8 + lVar15) = lVar54;
              *(undefined8 *)(&stack0x00000080 + lVar15) =
                   *(undefined8 *)(&stack0x000001c8 + lVar15);
              *(undefined8 *)(&stack0xffffffffffffffe8 + lVar15) =
                   *(undefined8 *)(&stack0x00000108 + lVar15);
              *(undefined8 *)(&stack0x00000010 + lVar15) =
                   *(undefined8 *)(&stack0x00000128 + lVar15);
              *(undefined8 *)(&stack0x00000018 + lVar15) =
                   *(undefined8 *)(&stack0x00000120 + lVar15);
                    /* WARNING: Could not recover jumptable at 0x000180032a5c. Too many branches */
                    /* WARNING: Treating indirect jump as call */
              (*(code *)(&LAB_180032a60 +
                        (longlong)
                        *(int *)(&DAT_1800343d4 +
                                (ulonglong)*(uint *)(&stack0x000000b0 + lVar15) * 4) * 4))();
              return;
            }
          }
          else {
            lVar55 = *(longlong *)(&stack0x000000d0 + lVar15);
            bVar10 = *(byte *)(lVar55 + 0x18);
            *(uint *)(&stack0x000000b0 + lVar15) = (uint)bVar10;
            switch(bVar10) {
            default:
              goto switchD_18003296c_caseD_0;
            case 1:
            case 3:
            case 4:
            case 6:
            case 0xb:
            case 0xd:
            case 0xe:
            case 0xf:
            case 0x10:
            case 0x11:
            case 0x13:
            case 0x16:
            case 0x18:
              uVar52 = *(uint *)(lVar55 + 0x1c);
              break;
            case 2:
              fVar65 = *(float *)(lVar55 + 0x1c);
              if (0.0 <= fVar65) {
                uVar52 = (uint)(fVar65 + 0.5);
              }
              else {
                uVar52 = (uint)(fVar65 - 0.5);
              }
              break;
            case 5:
              uVar52 = (uint)(*(int *)(lVar55 + 0x1c) != 0);
              break;
            case 0x17:
              dVar75 = *(double *)(lVar55 + 0x1c);
              if (0.0 <= dVar75) {
                uVar52 = (uint)(dVar75 + 0.5);
              }
              else {
                uVar52 = (uint)(dVar75 - 0.5);
              }
              break;
            case 0x19:
              uVar52 = *(uint *)(lVar55 + 0x24);
            }
            if (uVar52 != 0) {
              *(float **)(&stack0x000000f8 + lVar15) = pfVar64;
              uVar6 = *(undefined8 *)(&stack0x00000340 + lVar15);
              uVar41 = *(undefined8 *)(&stack0x00000348 + lVar15);
              uVar7 = *(undefined8 *)(&stack0x00000350 + lVar15);
              uVar1 = *(undefined8 *)(&stack0x00000358 + lVar15);
              uVar8 = *(undefined8 *)(&stack0x00000360 + lVar15);
              uVar4 = *(undefined8 *)(&stack0x00000368 + lVar15);
              uVar44 = *(undefined8 *)(&stack0x00000370 + lVar15);
              *(undefined8 *)(&stack0xffffffffffffffe8 + lVar15) =
                   *(undefined8 *)(&stack0x00000060 + lVar15);
              uVar5 = *(undefined8 *)(&stack0x000001d0 + lVar15);
              uVar57 = *(undefined8 *)(&stack0x000001d8 + lVar15);
              (&lStack_8)[extraout_x15 * -2] = *(longlong *)(&stack0x00000338 + lVar15);
              *(undefined8 *)(&stack0x00000000 + lVar15) = uVar42;
              *(undefined8 *)(&stack0x00000040 + lVar15) = uVar6;
              *(undefined8 *)(&stack0x00000048 + lVar15) = uVar7;
              *(undefined8 *)(&stack0x00000080 + lVar15) = uVar8;
              *(undefined8 *)(&stack0x000000a0 + lVar15) = uVar1;
              *(longlong *)(&stack0x000000a8 + lVar15) = lVar54;
              *(undefined8 *)(&stack0x00000050 + lVar15) = uVar30;
              *(undefined8 *)(&stack0x00000058 + lVar15) = uVar5;
              *(undefined8 *)(&stack0x00000020 + lVar15) = uVar44;
              *(undefined8 *)(&stack0x00000028 + lVar15) =
                   *(undefined8 *)(&stack0x000000c0 + lVar15);
              *(undefined8 *)(&stack0x00000070 + lVar15) =
                   *(undefined8 *)(&stack0x000000e0 + lVar15);
              *(undefined8 *)(&stack0x00000078 + lVar15) = uVar4;
              *plVar59 = *(longlong *)(&stack0x00000088 + lVar15);
              *(undefined8 *)(&stack0x00000090 + lVar15) = uVar57;
              *(undefined8 *)(&stack0x00000098 + lVar15) = uVar41;
              *(undefined8 *)(&stack0x00000030 + lVar15) =
                   *(undefined8 *)(&stack0x000002d0 + lVar15);
              *(undefined8 *)(&stack0x00000038 + lVar15) =
                   *(undefined8 *)(&stack0x00000378 + lVar15);
              *(longlong **)(&stack0x00000010 + lVar15) = plVar34;
              *(undefined8 *)(&stack0x00000018 + lVar15) =
                   *(undefined8 *)(&stack0x000000f0 + lVar15);
                    /* WARNING: Could not recover jumptable at 0x000180032ce4. Too many branches */
                    /* WARNING: Treating indirect jump as call */
              (*(code *)(&LAB_180032ce8 +
                        (longlong)
                        *(int *)(&DAT_1800344bc +
                                (ulonglong)*(uint *)(&stack0x000000b0 + lVar15) * 4) * 4))();
              return;
            }
          }
switchD_18003296c_caseD_0:
          *(undefined4 *)(&stack0x00000640 + lVar15) =
               *(undefined4 *)(&stack0xffffffffffffffd8 + lVar15);
          **(undefined4 **)(&stack0x00000298 + lVar15) = 0xffffffff;
          auVar67 = extraout_q0_21;
          if ((*(int *)(&stack0x000008c0 + lVar15) != 0) &&
             (pvVar60 = *(LPVOID *)(&stack0x000008b8 + lVar15), pvVar60 != (LPVOID)0x0)) {
            pvVar33 = GetProcessHeap();
            HeapFree(pvVar33,0,pvVar60);
            *(undefined8 *)(&stack0x000008b8 + lVar15) = 0;
            *(undefined8 *)(&stack0x000008c0 + lVar15) = 0;
            auVar67 = extraout_q0_22;
          }
          if ((*(int *)(&stack0x00000628 + lVar15) != 0) &&
             (pvVar60 = *(LPVOID *)(&stack0x00000620 + lVar15), pvVar60 != (LPVOID)0x0)) {
            pvVar33 = GetProcessHeap();
            HeapFree(pvVar33,0,pvVar60);
            *(undefined8 *)(&stack0x00000620 + lVar15) = 0;
            *(undefined8 *)(&stack0x00000628 + lVar15) = 0;
            auVar67 = extraout_q0_23;
          }
          if ((*(int *)(&stack0x000005f8 + lVar15) != 0) &&
             (pvVar60 = *(LPVOID *)(&stack0x000005f0 + lVar15), pvVar60 != (LPVOID)0x0)) {
            pvVar33 = GetProcessHeap();
            HeapFree(pvVar33,0,pvVar60);
            *(undefined8 *)(&stack0x000005f0 + lVar15) = 0;
            *(undefined8 *)(&stack0x000005f8 + lVar15) = 0;
            auVar67 = extraout_q0_24;
          }
        }
LAB_180032f18:
        lVar54 = *(longlong *)(&stack0x000001f8 + lVar15);
        switch(*(undefined1 *)(lVar54 + 0x18)) {
        default:
          goto switchD_180032f38_caseD_0;
        case 1:
        case 3:
        case 4:
        case 5:
        case 6:
        case 0xb:
        case 0xd:
        case 0xe:
        case 0xf:
        case 0x10:
        case 0x11:
        case 0x13:
        case 0x16:
        case 0x18:
          bVar21 = *(int *)(lVar54 + 0x1c) == 0;
          break;
        case 2:
          bVar21 = *(float *)(lVar54 + 0x1c) == 0.0;
          break;
        case 8:
        case 9:
        case 10:
        case 0x17:
        case 0x19:
          bVar21 = false;
          if (!NAN(*(double *)(lVar54 + 0x1c))) {
            bVar21 = *(double *)(lVar54 + 0x1c) == 0.0;
          }
        }
        if (!bVar21) {
          pcVar46 = *(code **)(*(longlong *)(&stack0xfffffffffffffff0 + lVar15) + 0x1330);
          *(undefined1 **)(&stack0x000002e0 + lVar15) = &DAT_1800d4ecd;
          *(undefined8 *)(&stack0x000002e8 + lVar15) = 0;
          *(undefined1 **)(&stack0x000002e0 + lVar15) = &DAT_1800d4ecd;
          *(undefined1 **)(&stack0x000002f0 + lVar15) = &DAT_1800d4ecd;
          lVar54 = *(longlong *)(&stack0x00000200 + lVar15);
          switch(*(undefined1 *)(lVar54 + 0x18)) {
          default:
            bVar21 = false;
            break;
          case 1:
          case 3:
          case 4:
          case 5:
          case 6:
          case 0xb:
          case 0xd:
          case 0xe:
          case 0xf:
          case 0x10:
          case 0x11:
          case 0x13:
          case 0x16:
          case 0x18:
            bVar21 = *(int *)(lVar54 + 0x1c) != 0;
            break;
          case 2:
            bVar21 = *(float *)(lVar54 + 0x1c) != 0.0;
            break;
          case 8:
          case 9:
          case 10:
          case 0x17:
          case 0x19:
            bVar21 = *(double *)(lVar54 + 0x1c) != 0.0;
          }
          sVar13 = *(short *)(*(longlong *)(&stack0x00000080 + lVar15) + 0x26);
          uVar11 = *(undefined2 *)(*(longlong *)(&stack0x00000080 + lVar15) + 0x28);
          lVar54 = *(longlong *)*plVar59;
          if (lVar54 == 0) {
            pcVar51 = *(code **)((&lStack_8)[extraout_x15 * -2] + 0x10);
            if (pcVar51 != (code *)0x0) {
              (*pcVar51)(*(undefined4 *)((&lStack_8)[extraout_x15 * -2] + 0x18));
            }
            lVar54 = *(longlong *)*plVar59;
            if (lVar54 != 0) goto LAB_18003301c;
            pfVar47 = (float *)(*plVar59 + 0x24);
          }
          else {
LAB_18003301c:
            iVar22 = *(int *)((&lStack_8)[extraout_x15 * -2] + 0x20);
            if (iVar22 == 0) {
              pfVar47 = (float *)(*plVar59 + 0x24);
            }
            else {
              uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
              if (iVar22 <= (int)uVar52) {
                uVar52 = iVar22 - 1;
              }
              pfVar47 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
            }
          }
          if (*pfVar47 == 1.0) {
            uVar74 = *(undefined4 *)(*(longlong *)(&stack0x00000080 + lVar15) + 0x18);
          }
          else {
            uVar74 = *(undefined4 *)(*(longlong *)(&stack0x00000080 + lVar15) + 0x1c);
          }
          plVar31 = *(longlong **)(&stack0x00000008 + lVar15);
          if ((*plVar31 == 0) && ((code *)plVar31[2] != (code *)0x0)) {
            (*(code *)plVar31[2])((int)plVar31[3]);
          }
          auVar67 = (*pcVar46)(uVar24,0,uVar74,uVar11,(int)sVar13,bVar21,0,&stack0x000002e0 + lVar15
                              );
          if ((*(int *)(&stack0x000002e8 + lVar15) != 0) &&
             (pvVar60 = *(LPVOID *)(&stack0x000002e0 + lVar15), pvVar60 != (LPVOID)0x0)) {
            pvVar33 = GetProcessHeap();
            HeapFree(pvVar33,0,pvVar60);
            *(undefined8 *)(&stack0x000002e0 + lVar15) = 0;
            *(undefined8 *)(&stack0x000002e8 + lVar15) = 0;
            auVar67 = extraout_q0_25;
          }
          **(uint **)(&stack0x00000288 + lVar15) = uVar24;
        }
switchD_180032f38_caseD_0:
        plVar31 = *(longlong **)(&stack0x00000008 + lVar15);
        lVar54 = *plVar31;
        if (lVar54 == 0) {
          if ((code *)plVar31[2] != (code *)0x0) {
            auVar67 = (*(code *)plVar31[2])((int)plVar31[3]);
          }
          lVar54 = **(longlong **)(&stack0x00000008 + lVar15);
          if (lVar54 != 0) goto LAB_180033158;
          puVar35 = (undefined4 *)((longlong)*(longlong **)(&stack0x00000008 + lVar15) + 0x24);
        }
        else {
LAB_180033158:
          iVar22 = *(int *)(*(longlong *)(&stack0x00000008 + lVar15) + 0x20);
          if (iVar22 == 0) {
            puVar35 = (undefined4 *)(*(longlong *)(&stack0x00000008 + lVar15) + 0x24);
          }
          else {
            uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
            if (iVar22 <= (int)uVar52) {
              uVar52 = iVar22 - 1;
            }
            puVar35 = (undefined4 *)(lVar54 + (longlong)(int)uVar52 * 4);
          }
        }
        lVar55 = *(longlong *)(&stack0x00000030 + lVar15);
        uVar74 = *puVar35;
        lVar54 = *(longlong *)(lVar55 + 0x30);
        if (lVar54 == 0) {
          if (*(code **)(lVar55 + 0x40) != (code *)0x0) {
            auVar67 = (**(code **)(lVar55 + 0x40))(*(undefined4 *)(lVar55 + 0x48));
          }
          lVar54 = *(longlong *)(*(longlong *)(&stack0x00000030 + lVar15) + 0x30);
          if (lVar54 != 0) goto LAB_1800331b4;
          puVar35 = (undefined4 *)(*(longlong *)(&stack0x00000030 + lVar15) + 0x54);
        }
        else {
LAB_1800331b4:
          iVar22 = *(int *)(*(longlong *)(&stack0x00000030 + lVar15) + 0x50);
          if (iVar22 == 0) {
            puVar35 = (undefined4 *)(*(longlong *)(&stack0x00000030 + lVar15) + 0x54);
          }
          else {
            uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
            if (iVar22 <= (int)uVar52) {
              uVar52 = iVar22 - 1;
            }
            puVar35 = (undefined4 *)(lVar54 + (longlong)(int)uVar52 * 4);
          }
        }
        *puVar35 = uVar74;
        lVar55 = *(longlong *)(&stack0x00000038 + lVar15);
        dVar75 = *(double *)(&stack0x000004c8 + lVar15);
        lVar54 = *(longlong *)(lVar55 + 0x30);
        if (lVar54 == 0) {
          if (*(code **)(lVar55 + 0x40) != (code *)0x0) {
            auVar67 = (**(code **)(lVar55 + 0x40))(*(undefined4 *)(lVar55 + 0x48));
          }
          lVar54 = *(longlong *)(*(longlong *)(&stack0x00000038 + lVar15) + 0x30);
          if (lVar54 != 0) goto LAB_180033218;
          pfVar47 = (float *)(*(longlong *)(&stack0x00000038 + lVar15) + 0x54);
        }
        else {
LAB_180033218:
          iVar22 = *(int *)(*(longlong *)(&stack0x00000038 + lVar15) + 0x50);
          if (iVar22 == 0) {
            pfVar47 = (float *)(*(longlong *)(&stack0x00000038 + lVar15) + 0x54);
          }
          else {
            uVar52 = uVar24 & (uVar25 ^ 0xffffffff);
            if (iVar22 <= (int)uVar52) {
              uVar52 = iVar22 - 1;
            }
            pfVar47 = (float *)(lVar54 + (longlong)(int)uVar52 * 4);
          }
        }
        *pfVar47 = (float)dVar75;
        iVar22 = *(int *)(&stack0x00000470 + lVar15);
        lVar55 = *(longlong *)(&stack0x00000020 + lVar15);
        lVar54 = *(longlong *)(lVar55 + 0x30);
        if (lVar54 == 0) {
          if (*(code **)(lVar55 + 0x40) != (code *)0x0) {
            auVar67 = (**(code **)(lVar55 + 0x40))(*(undefined4 *)(lVar55 + 0x48));
          }
          lVar54 = *(longlong *)(*(longlong *)(&stack0x00000020 + lVar15) + 0x30);
          if (lVar54 != 0) goto LAB_18003327c;
          pfVar47 = (float *)(*(longlong *)(&stack0x00000020 + lVar15) + 0x54);
        }
        else {
LAB_18003327c:
          iVar23 = *(int *)(*(longlong *)(&stack0x00000020 + lVar15) + 0x50);
          if (iVar23 == 0) {
            pfVar47 = (float *)(*(longlong *)(&stack0x00000020 + lVar15) + 0x54);
          }
          else {
            uVar25 = uVar24 & (uVar25 ^ 0xffffffff);
            if (iVar23 <= (int)uVar25) {
              uVar25 = iVar23 - 1;
            }
            pfVar47 = (float *)(lVar54 + (longlong)(int)uVar25 * 4);
          }
        }
        *pfVar47 = (float)iVar22;
        if ((*(int *)(&stack0x000004a8 + lVar15) != 0) &&
           (pvVar60 = *(LPVOID *)(&stack0x000004a0 + lVar15), pvVar60 != (LPVOID)0x0)) {
          pvVar33 = GetProcessHeap();
          HeapFree(pvVar33,0,pvVar60);
          *(undefined8 *)(&stack0x000004a0 + lVar15) = 0;
          *(undefined8 *)(&stack0x000004a8 + lVar15) = 0;
          auVar67 = extraout_q0_26;
        }
      }
LAB_1800332e0:
      puVar29 = *(uint **)(&stack0xfffffffffffffff0 + lVar15);
      uVar24 = uVar24 + 1;
    } while ((int)uVar24 < (int)*puVar29);
    plVar31 = (longlong *)*plVar59;
  }
  if (((**(int **)(&stack0x00000260 + lVar15) == 1) &&
      (uVar36 = FUN_180026708(*(longlong *)(&stack0x00000208 + lVar15)), (int)uVar36 != 0)) &&
     (uVar24 = FUN_180026550(*(longlong *)(&stack0x00000188 + lVar15)), uVar24 < 2)) {
    FUN_180026550(*(longlong *)(&stack0x00000190 + lVar15));
    FUN_180026690(*(longlong *)(&stack0x00000268 + lVar15));
    uVar74 = FUN_180026888(*(longlong *)(&stack0x000002a8 + lVar15));
    uVar27 = FUN_180026888(*(longlong *)(&stack0x000002a0 + lVar15));
    uVar28 = FUN_180026888(*(longlong *)(&stack0x00000270 + lVar15));
    FUN_180026888(*(longlong *)(&stack0x00000278 + lVar15));
    uVar24 = FUN_180026550(*(longlong *)(&stack0x00000198 + lVar15));
    uVar25 = FUN_180026550(*(longlong *)(&stack0x000001a0 + lVar15));
    uVar36 = FUN_180026708(extraout_x14);
    iVar22 = (int)uVar36;
    uVar36 = FUN_180026708(extraout_x11_13);
    *(undefined4 *)(&stack0xffffffffffffff68 + lVar15) = uVar74;
    *(undefined4 *)(acStack_a8 + lVar15) = uVar28;
    piVar63 = *(int **)(&stack0x00000280 + lVar15);
    piVar43 = *(int **)(&stack0x000000d8 + lVar15);
    *(undefined4 *)(&stack0xffffffffffffff60 + lVar15) = uVar27;
    *(undefined4 *)(&stack0xffffffffffffff78 + lVar15) = extraout_w13_00;
    (&stack0xffffffffffffff70)[lVar15] = extraout_w12;
    *(undefined4 *)(&lStack_b0 + extraout_x15 * -2) = extraout_w15_00;
    FUN_18002a1b0(extraout_q0_27,param_2,(longlong)puVar29,piVar63,extraout_w1_00 + -1,(int)uVar36,
                  piVar43,iVar22,uVar25,uVar24,(int)(&lStack_b0)[extraout_x15 * -2],
                  *(undefined4 *)(acStack_a8 + lVar15),
                  *(undefined4 *)(&stack0xffffffffffffff60 + lVar15),
                  *(undefined4 *)(&stack0xffffffffffffff68 + lVar15),
                  (&stack0xffffffffffffff70)[lVar15],
                  *(undefined4 *)(&stack0xffffffffffffff78 + lVar15));
  }
  uVar24 = *puVar29;
  iVar22 = 0;
  if (0 < (int)uVar24) {
    lVar54 = (&lStack_8)[extraout_x15 * -2];
    do {
      if (1 < iVar22) break;
      lVar55 = *plVar31;
      uVar24 = uVar24 - 1;
      if (lVar55 == 0) {
        if (*(code **)(lVar54 + 0x10) != (code *)0x0) {
          (**(code **)(lVar54 + 0x10))(*(undefined4 *)(lVar54 + 0x18));
        }
        lVar55 = *plVar31;
        if (lVar55 != 0) goto LAB_180033420;
        pfVar64 = (float *)((longlong)plVar31 + 0x24);
      }
      else {
LAB_180033420:
        iVar23 = *(int *)(lVar54 + 0x20);
        if (iVar23 == 0) {
          pfVar64 = (float *)((longlong)plVar31 + 0x24);
        }
        else {
          uVar25 = uVar24 & ((int)uVar24 >> 0x1f ^ 0xffffffffU);
          if (iVar23 <= (int)uVar25) {
            uVar25 = iVar23 - 1;
          }
          pfVar64 = (float *)(lVar55 + (longlong)(int)uVar25 * 4);
        }
      }
      if (*pfVar64 != 0.0) {
        iVar22 = iVar22 + 1;
      }
    } while (0 < (int)uVar24);
  }
  puVar29[0x433] = uVar24;
LAB_180033460:
  *(undefined ***)(&stack0x00000478 + lVar15) = CustomInput<>::vftable;
  *(undefined ***)(&stack0x00000480 + lVar15) = CustomInput<>::vftable;
  *(undefined ***)(&stack0x00000488 + lVar15) = CustomInput<>::vftable;
  *(undefined ***)(&stack0x000002d8 + lVar15) = CustomInput<>::vftable;
  *(undefined ***)(&stack0x000004c0 + lVar15) = CustomInput<>::vftable;
  *(undefined ***)(&stack0x00000490 + lVar15) = CustomInput<>::vftable;
  return;
}

