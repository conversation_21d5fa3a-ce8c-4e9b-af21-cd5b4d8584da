
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */
/* WARNING: Globals starting with '_' overlap smaller symbols at the same address */

void scsf_DeltaMap(undefined1 param_1 [16],undefined8 param_2,int *param_3,undefined8 param_4,
                  undefined8 param_5,undefined8 param_6,undefined8 param_7,undefined8 param_8,
                  undefined8 param_9,undefined8 param_10)

{
  uint uVar1;
  char *pcVar2;
  undefined1 *puVar3;
  longlong lVar4;
  longlong lVar5;
  int iVar6;
  byte bVar7;
  ushort uVar8;
  undefined1 auVar9 [16];
  undefined1 auVar10 [16];
  bool bVar11;
  bool bVar12;
  bool bVar13;
  uint uVar14;
  uint uVar15;
  int iVar16;
  int iVar17;
  float *pfVar18;
  int *piVar19;
  uint *puVar20;
  int *piVar21;
  undefined8 *puVar22;
  float *pfVar23;
  ulonglong uVar24;
  ulonglong uVar25;
  undefined4 *puVar26;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  uint uVar27;
  int iVar28;
  longlong lVar29;
  longlong lVar30;
  longlong lVar31;
  float *pfVar32;
  undefined8 uVar33;
  longlong *plVar34;
  float extraout_w11;
  float extraout_w11_00;
  float extraout_w11_01;
  float extraout_w11_02;
  longlong extraout_x11;
  longlong extraout_x11_00;
  longlong extraout_x11_01;
  float *extraout_x11_02;
  longlong extraout_x11_03;
  uint extraout_w12;
  uint extraout_w12_00;
  uint uVar35;
  longlong *plVar36;
  longlong *plVar37;
  float *pfVar38;
  int iVar39;
  longlong lVar40;
  longlong *plVar41;
  float *pfVar42;
  float *pfVar43;
  longlong *plVar44;
  longlong lVar45;
  longlong lVar46;
  longlong *plVar47;
  float *pfVar48;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  undefined4 extraout_s0_02;
  float fVar49;
  float fVar50;
  undefined4 extraout_var;
  undefined4 extraout_var_00;
  undefined4 extraout_var_01;
  undefined4 uVar51;
  undefined4 extraout_var_02;
  undefined8 extraout_var_03;
  undefined8 extraout_var_04;
  undefined8 extraout_var_05;
  undefined8 uVar52;
  undefined8 extraout_var_06;
  undefined4 uVar53;
  float fVar54;
  float fVar55;
  float fVar56;
  double dVar57;
  float fVar58;
  float extraout_s18;
  uint local_640;
  uint local_63c;
  longlong local_638;
  float *local_630;
  float local_628;
  ulonglong local_624;
  longlong local_618;
  float *local_610;
  longlong local_608;
  int *local_600;
  longlong local_5f8;
  longlong local_5f0;
  longlong local_5e8;
  longlong local_5e0;
  float local_5d8;
  float local_5d4;
  longlong local_5d0;
  float *local_5c8;
  longlong local_5c0;
  longlong local_5b8;
  float *local_5b0;
  uint *local_5a8;
  int *local_5a0;
  longlong local_598;
  float *local_590;
  float *local_588;
  float *local_580;
  float *local_578;
  longlong local_570;
  longlong local_568;
  longlong local_560;
  float *local_558;
  longlong local_550;
  undefined4 local_548;
  undefined4 *local_540;
  longlong local_538;
  longlong local_530;
  longlong local_528;
  longlong local_520;
  undefined8 local_518;
  longlong lStack_510;
  undefined8 auStack_508 [3];
  undefined4 auStack_4f0 [16];
  float local_4b0;
  float local_4ac;
  float local_4a8;
  float local_4a4;
  float local_4a0;
  float local_49c;
  undefined4 local_498;
  undefined4 local_494;
  char acStack_490 [4];
  int local_48c;
  undefined4 local_484;
  undefined8 local_470;
  int local_468;
  undefined4 local_464;
  longlong local_3d0;
  undefined4 local_3a8;
  undefined2 local_3a4;
  undefined2 local_3a2;
  undefined4 local_3a0;
  
                    /* 0x38cd0  5  scsf_DeltaMap */
  local_518 = 0xfffffffffffffffe;
  pfVar18 = (float *)(**(code **)(param_3 + 0x62e))(param_1._0_4_,0);
  local_5b0 = (float *)(**(code **)(param_3 + 0x62e))(1);
  local_578 = (float *)(**(code **)(param_3 + 0x62e))(2);
  local_588 = (float *)(**(code **)(param_3 + 0x62e))(3);
  local_580 = (float *)(**(code **)(param_3 + 0x62e))(4);
  piVar19 = (int *)(**(code **)(param_3 + 0x62e))(5);
  puVar20 = (uint *)(**(code **)(param_3 + 0x62e))(6);
  piVar21 = (int *)(**(code **)(param_3 + 0x62e))(7);
  local_590 = (float *)(**(code **)(param_3 + 0x630))(0);
  local_558 = (float *)(**(code **)(param_3 + 0x630))(1);
  local_540 = (undefined4 *)(**(code **)(param_3 + 0x630))(2);
  puVar22 = (undefined8 *)(**(code **)(param_3 + 0x446))(0);
  pfVar43 = (float *)*puVar22;
  puVar22 = (undefined8 *)(**(code **)(param_3 + 0x446))(1);
  pfVar38 = (float *)*puVar22;
  if (param_3[0x1b] != 0) {
    if (pfVar43 != (float *)0x0) {
      local_5c8 = pfVar43;
      FUN_180037238((longlong)pfVar43);
      FUN_1800966b8(pfVar43);
      (**(code **)(param_3 + 0x448))(0,0);
    }
    if (pfVar38 == (float *)0x0) {
      return;
    }
    FUN_1800966b8(pfVar38);
    (**(code **)(param_3 + 0x448))(1,0);
    return;
  }
  uVar33 = extraout_x1;
  uVar53 = extraout_s0;
  uVar51 = extraout_var;
  uVar52 = extraout_var_03;
  if (pfVar43 == (float *)0x0) {
    pfVar43 = (float *)FUN_180096150(0x44);
    local_5c8 = pfVar43;
    FUN_18003d0a0(pfVar43);
    pfVar23 = (float *)FUN_18003d0a0(auStack_4f0);
    if (pfVar43 != pfVar23) {
      FUN_18003d110((longlong)pfVar43);
      *pfVar43 = *pfVar23;
      uVar33 = *(undefined8 *)(pfVar43 + 2);
      *(undefined8 *)(pfVar43 + 2) = *(undefined8 *)(pfVar23 + 2);
      *(undefined8 *)(pfVar23 + 2) = uVar33;
      uVar33 = *(undefined8 *)(pfVar43 + 4);
      *(undefined8 *)(pfVar43 + 4) = *(undefined8 *)(pfVar23 + 4);
      *(undefined8 *)(pfVar23 + 4) = uVar33;
      uVar33 = *(undefined8 *)(pfVar43 + 6);
      *(undefined8 *)(pfVar43 + 6) = *(undefined8 *)(pfVar23 + 6);
      *(undefined8 *)(pfVar23 + 6) = uVar33;
      uVar33 = *(undefined8 *)(pfVar43 + 8);
      *(undefined8 *)(pfVar43 + 8) = *(undefined8 *)(pfVar23 + 8);
      *(undefined8 *)(pfVar23 + 8) = uVar33;
      uVar33 = *(undefined8 *)(pfVar43 + 10);
      *(undefined8 *)(pfVar43 + 10) = *(undefined8 *)(pfVar23 + 10);
      *(undefined8 *)(pfVar23 + 10) = uVar33;
      uVar33 = *(undefined8 *)(pfVar43 + 0xc);
      *(undefined8 *)(pfVar43 + 0xc) = *(undefined8 *)(pfVar23 + 0xc);
      *(undefined8 *)(pfVar23 + 0xc) = uVar33;
      uVar33 = *(undefined8 *)(pfVar43 + 0xe);
      *(undefined8 *)(pfVar43 + 0xe) = *(undefined8 *)(pfVar23 + 0xe);
      *(undefined8 *)(pfVar23 + 0xe) = uVar33;
    }
    FUN_180037238((longlong)auStack_4f0);
    pfVar43[0x10] = 0.0;
    (**(code **)(param_3 + 0x448))(0,pfVar43);
    uVar33 = extraout_x1_00;
    uVar53 = extraout_s0_00;
    uVar51 = extraout_var_00;
    uVar52 = extraout_var_04;
  }
  if (pfVar38 == (float *)0x0) {
    pfVar38 = (float *)FUN_180096150(0x248);
    local_5c8 = pfVar38;
    FUN_180035b28((longlong *)pfVar38,(longlong)param_3);
    FUN_1800364b8((longlong)pfVar38,(longlong)param_3);
    (**(code **)(param_3 + 0x448))(1,pfVar38);
    uVar33 = extraout_x1_01;
    uVar53 = extraout_s0_01;
    uVar51 = extraout_var_01;
    uVar52 = extraout_var_05;
  }
  if (param_3[0x2b] != 0) {
    pcVar2 = DAT_1800fd230;
    if (DAT_1800fd248 < 0x10) {
      pcVar2 = (char *)&DAT_1800fd230;
    }
    FUN_18000bbb8((longlong *)(param_3 + 0x46),pcVar2);
    param_3[0xdf] = 0;
    param_3[0x32e] = 1;
    param_3[4] = 0;
    param_3[5] = 1;
    param_3[0x27a] = 1;
    param_3[0x34a] = 1;
    param_3[0x276] = 1;
    plVar36 = *(longlong **)(pfVar38 + 0x44);
    FUN_1800079f8(plVar36,0x1800d7838,0x12);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x44) + 0x24) = 0x12;
    plVar36 = *(longlong **)(pfVar38 + 0x46);
    FUN_1800079f8(plVar36,0x1800d7828,0xf);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x46) + 0x24) = 2;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x46) + 0x18) = 0xc08040;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x46) + 0x1c) = 0x8080dc;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x46) + 0x20) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x46) + 0x28) = 2;
    plVar36 = *(longlong **)(pfVar38 + 0x48);
    FUN_1800079f8(plVar36,0x1800d7868,0x15);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x48) + 0x24) = 0x45;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x48) + 0x18) = 0xc08040;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x48) + 0x1c) = 0x8080dc;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x48) + 0x20) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x48) + 0x28) = 0x14;
    plVar36 = *(longlong **)(pfVar38 + 0x4a);
    FUN_1800079f8(plVar36,0x1800d7850,0x17);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x4a) + 0x24) = 7;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x4a) + 0x18) = 0xc08040;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x4a) + 0x1c) = 0x8080dc;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x4a) + 0x20) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x4a) + 0x28) = 3;
    plVar36 = *(longlong **)(pfVar38 + 0x4c);
    FUN_1800079f8(plVar36,0x1800d78a0,0x1b);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x4c) + 0x24) = 5;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x4c) + 0x18) = 0xc08040;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x4c) + 0x28) = 3;
    plVar36 = *(longlong **)(pfVar38 + 0x4e);
    FUN_1800079f8(plVar36,0x1800d7880,0x1c);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x4e) + 0x24) = 5;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x4e) + 0x18) = 0x8080dc;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x4e) + 0x28) = 3;
    plVar36 = *(longlong **)(pfVar38 + 0x7c);
    FUN_1800079f8(plVar36,0x1800d78d8,0x16);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x7c) + 0x24) = 5;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x7c) + 0x18) = 0xc08040;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x7c) + 0x28) = 4;
    plVar36 = *(longlong **)(pfVar38 + 0x7e);
    FUN_1800079f8(plVar36,0x1800d78c0,0x17);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x7e) + 0x24) = 5;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x7e) + 0x18) = 0x8080dc;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x7e) + 0x28) = 4;
    plVar36 = *(longlong **)(pfVar38 + 0x80);
    FUN_1800079f8(plVar36,0x1800d7908,0x10);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x80) + 0x24) = 5;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x80) + 0x18) = 0xc08040;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x80) + 0x28) = 4;
    plVar36 = *(longlong **)(pfVar38 + 0x82);
    FUN_1800079f8(plVar36,0x1800d78f0,0x11);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x82) + 0x24) = 5;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x82) + 0x18) = 0x8080dc;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x82) + 0x28) = 4;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x84) + 0x24) = 5;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x84) + 0x18) = 0xc08040;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x84) + 0x28) = 4;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x86) + 0x24) = 5;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x86) + 0x18) = 0x8080dc;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x86) + 0x28) = 4;
    plVar36 = *(longlong **)(pfVar38 + 0x88);
    FUN_1800079f8(plVar36,0x1800d7030,0x16);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x88) + 0x24) = 5;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x88) + 0x18) = 0x8080dc;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x88) + 0x28) = 1;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x88) + 0xd8) = 1;
    plVar36 = *(longlong **)(pfVar38 + 0x8a);
    FUN_1800079f8(plVar36,0x1800d7930,0xf);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x8a) + 0x24) = 8;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x8a) + 0x18) = 0x7d7dff;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x8a) + 0x28) = 7;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x8a) + 0xd8) = 0;
    plVar36 = *(longlong **)(pfVar38 + 0x8c);
    FUN_1800079f8(plVar36,0x1800d7920,0xd);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x8c) + 0x24) = 8;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x8c) + 0x18) = 0xe9c49e;
    *(undefined2 *)(*(longlong *)(pfVar38 + 0x8c) + 0x28) = 7;
    *(undefined4 *)(*(longlong *)(pfVar38 + 0x8c) + 0xd8) = 0;
    plVar36 = *(longlong **)pfVar38;
    FUN_1800079f8(plVar36,0x1800d7500,0x10);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)pfVar38;
    puVar3 = _DAT_1800ffc00;
    if (DAT_1800ffc18 < 0x10) {
      puVar3 = &DAT_1800ffc00;
    }
    if (*(code **)(lVar45 + 0x50) != (code *)0x0) {
      (**(code **)(lVar45 + 0x50))(*(undefined4 *)(lVar45 + 0x4c),puVar3);
      *(undefined1 *)(lVar45 + 0x18) = 0x16;
    }
    lVar45 = *(longlong *)pfVar38;
    *(undefined1 *)(lVar45 + 0x18) = 0x16;
    *(undefined4 *)(lVar45 + 0x1c) = 0;
    plVar36 = *(longlong **)(pfVar38 + 4);
    FUN_1800079f8(plVar36,0x1800d7540,0x14);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 4);
    *(undefined1 *)(lVar45 + 0x18) = 0xb;
    *(undefined4 *)(lVar45 + 0x1c) = 7;
    lVar45 = *(longlong *)(pfVar38 + 4);
    *(undefined4 *)(lVar45 + 0x3c) = 0x19;
    *(undefined4 *)(lVar45 + 0x2c) = 4;
    plVar36 = *(longlong **)(pfVar38 + 2);
    FUN_1800079f8(plVar36,0x1800d7520,0x1a);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 2);
    if (*(code **)(lVar45 + 0x50) != (code *)0x0) {
      (**(code **)(lVar45 + 0x50))(*(undefined4 *)(lVar45 + 0x4c),"Regular;High;");
      *(undefined1 *)(lVar45 + 0x18) = 0x16;
    }
    lVar45 = *(longlong *)(pfVar38 + 2);
    *(undefined1 *)(lVar45 + 0x18) = 0x16;
    *(undefined4 *)(lVar45 + 0x1c) = 0;
    plVar36 = *(longlong **)(pfVar38 + 0xc);
    FUN_1800079f8(plVar36,0x1800d7558,0x20);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0xc);
    *(undefined4 *)(lVar45 + 0x1c) = 0x966738;
    *(undefined1 *)(lVar45 + 0x18) = 0xe;
    plVar36 = *(longlong **)(pfVar38 + 0xe);
    FUN_1800079f8(plVar36,0x1800d75b0,0x20);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0xe);
    *(undefined4 *)(lVar45 + 0x1c) = 0x25256b;
    *(undefined1 *)(lVar45 + 0x18) = 0xe;
    plVar36 = *(longlong **)(pfVar38 + 0x10);
    FUN_1800079f8(plVar36,0x1800d7590,0x19);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x10);
    *(undefined4 *)(lVar45 + 0x1c) = 0xc6792d;
    *(undefined1 *)(lVar45 + 0x18) = 0xe;
    plVar36 = *(longlong **)(pfVar38 + 0x12);
    FUN_1800079f8(plVar36,0x1800d75f8,0x19);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x12);
    *(undefined4 *)(lVar45 + 0x1c) = 0xefa254;
    *(undefined1 *)(lVar45 + 0x18) = 0xe;
    plVar36 = *(longlong **)(pfVar38 + 0x14);
    FUN_1800079f8(plVar36,0x1800d75d8,0x19);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x14);
    *(undefined4 *)(lVar45 + 0x1c) = 0xd6b18d;
    *(undefined1 *)(lVar45 + 0x18) = 0xe;
    plVar36 = *(longlong **)(pfVar38 + 0x16);
    FUN_1800079f8(plVar36,0x1800d7638,0x19);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x16);
    *(undefined4 *)(lVar45 + 0x1c) = 0x24249f;
    *(undefined1 *)(lVar45 + 0x18) = 0xe;
    plVar36 = *(longlong **)(pfVar38 + 0x18);
    FUN_1800079f8(plVar36,0x1800d7618,0x19);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x18);
    *(undefined1 *)(lVar45 + 0x18) = 0xe;
    *(undefined4 *)(lVar45 + 0x1c) = 0x2e2ed6;
    plVar36 = *(longlong **)(pfVar38 + 0x1a);
    FUN_1800079f8(plVar36,0x1800d7680,0x19);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x1a);
    *(undefined4 *)(lVar45 + 0x1c) = 0x7474e0;
    *(undefined1 *)(lVar45 + 0x18) = 0xe;
    plVar36 = *(longlong **)(pfVar38 + 0x1c);
    FUN_1800079f8(plVar36,0x1800d7658,0x22);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x1c);
    *(undefined4 *)(lVar45 + 0x1c) = 0x40800000;
    *(undefined1 *)(lVar45 + 0x18) = 2;
    lVar45 = *(longlong *)(pfVar38 + 0x1c);
    lVar30 = *(longlong *)(pfVar38 + 0x1e);
    *(undefined4 *)(lVar45 + 0x2c) = 0x40000000;
    *(undefined4 *)(lVar45 + 0x3c) = 0x41c80000;
    *(undefined1 *)(lVar30 + 0x18) = 2;
    *(undefined4 *)(lVar30 + 0x1c) = 0x3f400000;
    lVar45 = *(longlong *)(pfVar38 + 0x1e);
    plVar36 = *(longlong **)(pfVar38 + 0x20);
    *(undefined4 *)(lVar45 + 0x2c) = 0x3dcccccd;
    *(undefined4 *)(lVar45 + 0x3c) = 0x40200000;
    FUN_1800079f8(plVar36,0x1800d76c0,0x1d);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x20);
    *(undefined1 *)(lVar45 + 0x18) = 5;
    *(undefined4 *)(lVar45 + 0x1c) = 1;
    plVar36 = *(longlong **)(pfVar38 + 0x22);
    FUN_1800079f8(plVar36,0x1800d76a0,0x1d);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x22);
    *(undefined4 *)(lVar45 + 0x1c) = 0x10;
    *(undefined1 *)(lVar45 + 0x18) = 0xb;
    lVar45 = *(longlong *)(pfVar38 + 0x22);
    *(undefined4 *)(lVar45 + 0x3c) = 100000;
    *(undefined4 *)(lVar45 + 0x2c) = 2;
    plVar36 = *(longlong **)(pfVar38 + 0x24);
    FUN_1800079f8(plVar36,0x1800d7710,0x1f);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x24);
    *(undefined4 *)(lVar45 + 0x1c) = 2000;
    *(undefined1 *)(lVar45 + 0x18) = 0xb;
    lVar45 = *(longlong *)(pfVar38 + 0x24);
    *(undefined4 *)(lVar45 + 0x2c) = 0x96;
    *(undefined4 *)(lVar45 + 0x3c) = 100000000;
    plVar36 = *(longlong **)(pfVar38 + 0x26);
    FUN_1800079f8(plVar36,0x1800d76e0,0x2a);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x26);
    *(undefined4 *)(lVar45 + 0x1c) = 0x3f000000;
    *(undefined1 *)(lVar45 + 0x18) = 2;
    lVar45 = *(longlong *)(pfVar38 + 0x26);
    lVar30 = *(longlong *)(pfVar38 + 0x28);
    *(undefined4 *)(lVar45 + 0x2c) = 0x3c23d70a;
    *(undefined4 *)(lVar45 + 0x3c) = 0x41c80000;
    if (*(code **)(lVar30 + 0x50) != (code *)0x0) {
      (**(code **)(lVar30 + 0x50))(*(undefined4 *)(lVar30 + 0x4c),"One Leg;Two Legs;");
      *(undefined1 *)(lVar30 + 0x18) = 0x16;
    }
    lVar45 = *(longlong *)(pfVar38 + 0x28);
    *(undefined1 *)(lVar45 + 0x18) = 0x16;
    *(undefined4 *)(lVar45 + 0x1c) = 0;
    lVar45 = *(longlong *)(pfVar38 + 0x2a);
    *(undefined1 *)(lVar45 + 0x18) = 0xb;
    *(undefined4 *)(lVar45 + 0x1c) = 0;
    lVar45 = *(longlong *)(pfVar38 + 0x2a);
    *(undefined4 *)(lVar45 + 0x3c) = 0xfa;
    *(undefined4 *)(lVar45 + 0x2c) = 0;
    plVar36 = *(longlong **)(pfVar38 + 0x2c);
    FUN_1800079f8(plVar36,0x1800d7730,0x18);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x2c);
    *(undefined4 *)(lVar45 + 0x1c) = 0x41200000;
    *(undefined1 *)(lVar45 + 0x18) = 2;
    lVar45 = *(longlong *)(pfVar38 + 0x2c);
    lVar30 = *(longlong *)(pfVar38 + 0x2e);
    *(undefined4 *)(lVar45 + 0x2c) = 0x3a03126f;
    *(undefined4 *)(lVar45 + 0x3c) = 0x437a0000;
    *(undefined1 *)(lVar30 + 0x18) = 0xb;
    *(undefined4 *)(lVar30 + 0x1c) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x2e);
    *(undefined4 *)(lVar45 + 0x3c) = 100;
    *(undefined4 *)(lVar45 + 0x2c) = 1;
    plVar36 = *(longlong **)(pfVar38 + 0x30);
    FUN_1800079f8(plVar36,0x1800d7340,0x14);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x30);
    *(undefined1 *)(lVar45 + 0x18) = 5;
    *(undefined4 *)(lVar45 + 0x1c) = 0;
    plVar36 = *(longlong **)(pfVar38 + 0x32);
    FUN_1800079f8(plVar36,0x1800d7328,0x16);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x32);
    if (*(code **)(lVar45 + 0x50) != (code *)0x0) {
      (**(code **)(lVar45 + 0x50))(*(undefined4 *)(lVar45 + 0x4c),"Average;Mode");
      *(undefined1 *)(lVar45 + 0x18) = 0x16;
    }
    lVar45 = *(longlong *)(pfVar38 + 0x32);
    *(undefined1 *)(lVar45 + 0x18) = 0x16;
    *(undefined4 *)(lVar45 + 0x1c) = 0;
    plVar36 = *(longlong **)(pfVar38 + 0x34);
    FUN_1800079f8(plVar36,0x1800d7358,0x1c);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x34);
    *(undefined1 *)(lVar45 + 0x18) = 0x15;
    *(undefined8 *)(lVar45 + 0x1c) = 0;
    plVar36 = *(longlong **)(pfVar38 + 0x36);
    FUN_1800079f8(plVar36,0x1800d7788,0x1a);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x36);
    *(undefined1 *)(lVar45 + 0x18) = 0xb;
    *(undefined4 *)(lVar45 + 0x1c) = 0xe;
    lVar45 = *(longlong *)(pfVar38 + 0x36);
    *(undefined4 *)(lVar45 + 0x2c) = 4;
    *(undefined4 *)(lVar45 + 0x3c) = 0x7fff;
    lVar45 = *(longlong *)(pfVar38 + 0x3c);
    *(undefined4 *)(lVar45 + 0x1c) = 10;
    *(undefined1 *)(lVar45 + 0x18) = 0xb;
    lVar45 = *(longlong *)(pfVar38 + 0x3c);
    *(undefined4 *)(lVar45 + 0x2c) = 0;
    *(undefined4 *)(lVar45 + 0x3c) = 0x7fff;
    lVar45 = *(longlong *)(pfVar38 + 0x42);
    *(undefined4 *)(lVar45 + 0x1c) = 0x3f800000;
    *(undefined1 *)(lVar45 + 0x18) = 2;
    plVar36 = *(longlong **)(pfVar38 + 0x38);
    FUN_1800079f8(plVar36,0x1800d7768,0x1b);
    *(undefined4 *)((longlong)plVar36 + 0xc) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x38);
    if (*(code **)(lVar45 + 0x50) != (code *)0x0) {
      (**(code **)(lVar45 + 0x50))(*(undefined4 *)(lVar45 + 0x4c),"Low;Medium;High");
      *(undefined1 *)(lVar45 + 0x18) = 0x16;
    }
    lVar45 = *(longlong *)(pfVar38 + 0x38);
    *(undefined1 *)(lVar45 + 0x18) = 0x16;
    *(undefined4 *)(lVar45 + 0x1c) = 1;
    lVar45 = *(longlong *)(pfVar38 + 0x40);
    *(undefined4 *)(lVar45 + 0x1c) = 0x3cf5c28f;
    *(undefined1 *)(lVar45 + 0x18) = 2;
    lVar45 = *(longlong *)(pfVar38 + 0x3a);
    if (*(code **)(lVar45 + 0x50) != (code *)0x0) {
      (**(code **)(lVar45 + 0x50))
                (*(undefined4 *)(lVar45 + 0x4c),
                 "EXPONENTIAL;LINEARREGRESSION;SIMPLE;WEIGHTED;WILDERS;SIMPLE_SKIP_ZEROS;SMOOTHED;NU MBER_OF_AVERAGE;HULL"
                );
      *(undefined1 *)(lVar45 + 0x18) = 0x16;
    }
    lVar45 = *(longlong *)(pfVar38 + 0x3a);
    *(undefined1 *)(lVar45 + 0x18) = 0x16;
    *(undefined4 *)(lVar45 + 0x1c) = 3;
    return;
  }
  if (param_3[3] == 0) {
    auVar9._4_4_ = uVar51;
    auVar9._0_4_ = uVar53;
    auVar9._8_8_ = uVar52;
    uVar33 = FUN_1800254e8(auVar9,param_2,(longlong)param_3,uVar33,param_5,param_6,param_7,param_8,
                           param_9,param_10);
    *piVar19 = (int)uVar33;
  }
  if (*piVar19 != 0) {
    return;
  }
  FUN_180035b28((longlong *)pfVar38,(longlong)param_3);
  FUN_1800364b8((longlong)pfVar38,(longlong)param_3);
  local_5b8 = *(longlong *)(pfVar38 + 0x44);
  local_638 = *(longlong *)(pfVar38 + 0x46);
  local_5f8 = *(longlong *)(pfVar38 + 0x48);
  local_550 = *(longlong *)(pfVar38 + 0x7c);
  local_598 = *(longlong *)(pfVar38 + 0x7e);
  local_5c0 = *(longlong *)(pfVar38 + 0x80);
  local_5d0 = *(longlong *)(pfVar38 + 0x82);
  local_528 = *(longlong *)(pfVar38 + 0x84);
  local_520 = *(longlong *)(pfVar38 + 0x86);
  if ((param_3[0x47e] != 0) && (param_3[3] == 0)) {
    iVar39 = 0;
    local_630 = pfVar43;
    local_600 = piVar21;
    local_5a8 = puVar20;
    local_5a0 = piVar19;
    do {
      uVar14 = 0;
      if (0 < *param_3) {
        do {
          plVar36 = FUN_18000f880((longlong *)(param_3 + 0x140),iVar39);
          lVar45 = plVar36[6];
          if (lVar45 == 0) {
            if ((code *)plVar36[8] != (code *)0x0) {
              (*(code *)plVar36[8])((int)plVar36[9]);
            }
            lVar45 = plVar36[6];
            if (lVar45 != 0) goto LAB_1800399c8;
            puVar26 = (undefined4 *)((longlong)plVar36 + 0x54);
          }
          else {
LAB_1800399c8:
            iVar16 = (int)plVar36[10];
            if (iVar16 == 0) {
              puVar26 = (undefined4 *)((longlong)plVar36 + 0x54);
            }
            else {
              uVar15 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
              if (iVar16 <= (int)uVar15) {
                uVar15 = iVar16 - 1;
              }
              puVar26 = (undefined4 *)(lVar45 + (longlong)(int)uVar15 * 4);
            }
          }
          *puVar26 = 0;
          iVar16 = 0;
          do {
            lVar45 = *(longlong *)(param_3 + 0x140);
            if (lVar45 == 0) {
              if (*(code **)(param_3 + 0x144) != (code *)0x0) {
                (**(code **)(param_3 + 0x144))(param_3[0x146]);
              }
              lVar45 = *(longlong *)(param_3 + 0x140);
              if (lVar45 != 0) goto LAB_180039a1c;
              piVar21 = param_3 + 0x14a;
            }
            else {
LAB_180039a1c:
              iVar28 = param_3[0x148];
              if (iVar28 == 0) {
                piVar21 = param_3 + 0x14a;
              }
              else {
                iVar17 = iVar39;
                if (iVar28 <= iVar39) {
                  iVar17 = iVar28 + -1;
                }
                piVar21 = (int *)(lVar45 + (longlong)iVar17 * 0x170);
              }
            }
            lVar45 = *(longlong *)(piVar21 + 0x20);
            if (lVar45 == 0) {
              if (*(code **)(piVar21 + 0x24) != (code *)0x0) {
                (**(code **)(piVar21 + 0x24))(piVar21[0x26]);
              }
              lVar45 = *(longlong *)(piVar21 + 0x20);
              if (lVar45 != 0) goto LAB_180039a6c;
              plVar36 = (longlong *)(piVar21 + 0x2a);
            }
            else {
LAB_180039a6c:
              iVar28 = piVar21[0x28];
              if (iVar28 == 0) {
                plVar36 = (longlong *)(piVar21 + 0x2a);
              }
              else {
                iVar17 = iVar16;
                if (iVar28 <= iVar16) {
                  iVar17 = iVar28 + -1;
                }
                plVar36 = (longlong *)(lVar45 + (longlong)iVar17 * 0x28);
              }
            }
            lVar45 = *plVar36;
            if (lVar45 == 0) {
              if ((code *)plVar36[2] != (code *)0x0) {
                (*(code *)plVar36[2])((int)plVar36[3]);
              }
              lVar45 = *plVar36;
              if (lVar45 != 0) goto LAB_180039ab8;
              piVar21 = (int *)((longlong)plVar36 + 0x24);
            }
            else {
LAB_180039ab8:
              iVar28 = (int)plVar36[4];
              if (iVar28 == 0) {
                piVar21 = (int *)((longlong)plVar36 + 0x24);
              }
              else {
                uVar15 = uVar14 & ((int)uVar14 >> 0x1f ^ 0xffffffffU);
                if (iVar28 <= (int)uVar15) {
                  uVar15 = iVar28 - 1;
                }
                piVar21 = (int *)(lVar45 + (longlong)(int)uVar15 * 4);
              }
            }
            iVar16 = iVar16 + 1;
            *piVar21 = 0;
          } while (iVar16 < 0xc);
          uVar14 = uVar14 + 1;
        } while ((int)uVar14 < *param_3);
      }
      piVar19 = local_5a0;
      puVar20 = local_5a8;
      piVar21 = local_600;
      pfVar43 = local_630;
      iVar39 = iVar39 + 1;
    } while (iVar39 < 0x3c);
    if (0 < param_3[4]) {
      FUN_1800bf500(acStack_490,'\0',0x3e0);
      FUN_180009f70((undefined8 *)acStack_490);
      FUN_18000a310((longlong)acStack_490);
      local_48c = param_3[9];
      local_484 = 0;
      local_3a2 = 0;
      local_3a8 = 3;
      local_468 = param_3[4];
      plVar36 = FUN_180005d78((longlong *)(param_3 + 0x58),0);
      local_3d0 = *plVar36;
      local_470 = 0;
      local_3a0 = 1;
      local_3a4 = 1;
      local_464 = 0xc0c0c0;
      (**(code **)(param_3 + 0x3c))(acStack_490);
      FUN_18000ae00((longlong)acStack_490);
    }
    *pfVar18 = 0.0;
    *local_5b0 = 0.0;
    *local_588 = -NAN;
    *local_578 = -NAN;
    *local_580 = -NAN;
    *piVar19 = 0;
    *puVar20 = 0;
    *piVar21 = 0;
    *local_590 = 0.0;
    *local_558 = 0.0;
    *local_540 = 0;
    FUN_18003d110((longlong)pfVar43);
    uVar24 = FUN_180026708(*(longlong *)(pfVar38 + 0x22));
    pfVar43[0x10] = (float)uVar24;
    bVar13 = FUN_180026690(*(longlong *)(pfVar38 + 0x30));
    if (bVar13) {
      lVar45 = *(longlong *)(pfVar38 + 0x34);
      bVar7 = *(byte *)(lVar45 + 0x18);
      uVar14 = (uint)bVar7;
      if (bVar7 == 0x12 || bVar7 == 0x15) {
        uVar15 = *(uint *)(lVar45 + 0x1c);
        if (bVar7 != 0x12) goto LAB_180039c6c;
LAB_180039c88:
        uVar24 = (ulonglong)*(uint *)(lVar45 + 0x20);
      }
      else if (bVar7 == 0x13) {
        uVar15 = *(uint *)(lVar45 + 0x1c);
        uVar14 = FUN_180026550(lVar45);
        uVar24 = (ulonglong)uVar14;
      }
      else {
        uVar24 = FUN_180026708(lVar45);
        uVar15 = (uint)uVar24;
        lVar45 = extraout_x11;
        uVar14 = extraout_w12;
LAB_180039c6c:
        if ((uVar14 == 0x14) || (uVar14 == 0x15)) goto LAB_180039c88;
        uVar14 = FUN_180026550(lVar45);
        uVar24 = (ulonglong)uVar14;
      }
      FUN_180004498(auStack_508,"Rotation Calculator");
      auVar10._4_4_ = extraout_var_02;
      auVar10._0_4_ = extraout_s0_02;
      auVar10._8_8_ = extraout_var_06;
      uVar33 = FUN_1800939c8(auVar10,param_2,(longlong)param_3,(ulonglong)uVar15,uVar24,auStack_508,
                             param_7,param_8,param_9,param_10);
      *puVar20 = (uint)uVar33 & 0xff;
      FUN_180004590(auStack_508);
      if (*puVar20 == 0) {
        return;
      }
    }
    *piVar21 = 0;
  }
  bVar13 = FUN_180026690(*(longlong *)(pfVar38 + 0x30));
  if ((bVar13) && (*puVar20 == 0)) {
    return;
  }
  uVar24 = FUN_180026708(*(longlong *)(pfVar38 + 0x2a));
  fVar49 = FUN_180026608(*(longlong *)(pfVar38 + 0x1e));
  fVar50 = FUN_180026608(*(longlong *)(pfVar38 + 0x2c));
  bVar13 = FUN_180026690(extraout_x11_00);
  if (!bVar13) goto LAB_180039e1c;
  lVar45 = *(longlong *)(pfVar38 + 0x34);
  bVar7 = *(byte *)(lVar45 + 0x18);
  uVar14 = (uint)bVar7;
  if (bVar7 == 0x12 || bVar7 == 0x15) {
    uVar53 = *(undefined4 *)(lVar45 + 0x1c);
    if (bVar7 != 0x12) goto LAB_180039d6c;
LAB_180039d88:
    uVar14 = *(uint *)(lVar45 + 0x20);
  }
  else if (bVar7 == 0x13) {
    uVar53 = *(undefined4 *)(lVar45 + 0x1c);
    uVar14 = FUN_180026550(lVar45);
  }
  else {
    uVar25 = FUN_180026708(lVar45);
    uVar53 = (undefined4)uVar25;
    lVar45 = extraout_x11_01;
    uVar14 = extraout_w12_00;
LAB_180039d6c:
    if ((uVar14 == 0x14) || (uVar14 == 0x15)) goto LAB_180039d88;
    uVar14 = FUN_180026550(lVar45);
  }
  plVar36 = (longlong *)(**(code **)(param_3 + 0x45c))(uVar53,uVar14,2);
  pfVar23 = (float *)*plVar36;
  if (pfVar23 == (float *)0x0) {
    return;
  }
  fVar55 = *pfVar23;
  fVar54 = pfVar23[1];
  fVar50 = pfVar23[2];
  (**(code **)(param_3 + 0x44e))(uVar53,uVar14,7);
  (**(code **)(param_3 + 0x44a))(uVar53,uVar14,8);
  (**(code **)(param_3 + 0x44e))(uVar53,uVar14,9);
  uVar14 = FUN_180026550(*(longlong *)(pfVar38 + 0x32));
  if (uVar14 == 0) {
    if (fVar54 <= 0.1) {
      fVar50 = 1.0;
    }
    else {
      fVar50 = fVar55 / fVar54;
    }
  }
  else if (fVar50 <= 0.1) {
    fVar50 = 1.0;
  }
LAB_180039e1c:
  fVar54 = (float)param_3[3];
  local_630 = pfVar43;
  local_610 = pfVar38;
  local_600 = piVar21;
  local_5d8 = fVar49;
  local_5d4 = fVar50;
  local_548 = (int)uVar24;
  if ((int)fVar54 < *param_3) {
    do {
      local_618 = *(longlong *)(local_610 + 0x52);
      lVar30 = *(longlong *)(local_610 + 0x54);
      lVar40 = *(longlong *)(local_610 + 0x88);
      lVar46 = *(longlong *)(local_610 + 0x58);
      lVar45 = *(longlong *)(local_610 + 100);
      lVar31 = *(longlong *)(local_610 + 0x66);
      local_5a0 = *(int **)(local_610 + 0x8e);
      local_5a8 = *(uint **)(local_610 + 0x90);
      local_5e0 = *(longlong *)(local_610 + 0x4a);
      local_538 = *(longlong *)(local_610 + 0x4c);
      local_530 = *(longlong *)(local_610 + 0x4e);
      lVar4 = *(longlong *)(local_610 + 0x50);
      local_5e8 = *(longlong *)(local_610 + 0x78);
      lVar5 = *(longlong *)(local_610 + 0x7a);
      local_5f0 = *(longlong *)(local_610 + 0x76);
      local_568 = *(longlong *)(local_610 + 0x74);
      local_570 = *(longlong *)(local_610 + 0x6c);
      local_560 = *(longlong *)(local_610 + 0x6e);
      local_608 = *(longlong *)(local_610 + 0x72);
      local_5c8 = *(float **)(local_610 + 0x70);
      lVar29 = *(longlong *)(lVar40 + 0x30);
      local_4b0 = fVar54;
      if (lVar29 == 0) {
        if (*(code **)(lVar40 + 0x40) != (code *)0x0) {
          (**(code **)(lVar40 + 0x40))(*(undefined4 *)(lVar40 + 0x48));
        }
        lVar29 = *(longlong *)(lVar40 + 0x30);
        if (lVar29 != 0) goto LAB_180039ef0;
        pfVar43 = (float *)(lVar40 + 0x54);
      }
      else {
LAB_180039ef0:
        iVar39 = *(int *)(lVar40 + 0x50);
        if (iVar39 == 0) {
          pfVar43 = (float *)(lVar40 + 0x54);
        }
        else {
          uVar14 = (uint)fVar54 & ((int)fVar54 >> 0x1f ^ 0xffffffffU);
          if (iVar39 <= (int)uVar14) {
            uVar14 = iVar39 - 1;
          }
          pfVar43 = (float *)(lVar29 + (longlong)(int)uVar14 * 4);
        }
      }
      pfVar38 = local_610;
      *pfVar43 = local_5d4;
      if ((int)*pfVar18 < (int)local_4b0) {
        FUN_1800377c8((longlong)param_3,(longlong *)local_610,(uint *)pfVar18);
        pfVar43 = (float *)FUN_18000f448(lVar30,(int)*pfVar18);
        fVar54 = *pfVar43;
        fVar49 = FUN_180026608(*(longlong *)(pfVar38 + 0x1c));
        uVar24 = FUN_180026708(*(longlong *)(pfVar38 + 0x2e));
        fVar50 = (float)(int)uVar24 * (float)param_3[0x1a6];
        pfVar43 = (float *)FUN_18000f448(lVar45,(int)*pfVar18);
        fVar55 = *pfVar43;
        pfVar43 = (float *)FUN_18000f448(lVar31,(int)*pfVar18);
        local_628 = *pfVar43;
        pfVar43 = (float *)FUN_18000f448(lVar46,(int)*pfVar18);
        pfVar38 = (float *)FUN_18000f448(lVar46,(int)*pfVar18 - 1);
        fVar58 = *pfVar43;
        fVar56 = *pfVar38;
        lVar45 = *(longlong *)(lVar46 + 0x80);
        if (lVar45 == 0) {
          if (*(code **)(lVar46 + 0x90) != (code *)0x0) {
            (**(code **)(lVar46 + 0x90))(*(undefined4 *)(lVar46 + 0x98));
          }
          lVar45 = *(longlong *)(lVar46 + 0x80);
          if (lVar45 != 0) goto LAB_180039ff0;
          plVar36 = (longlong *)(lVar46 + 0xa8);
        }
        else {
LAB_180039ff0:
          iVar39 = *(int *)(lVar46 + 0xa0);
          if (iVar39 == 0) {
            plVar36 = (longlong *)(lVar46 + 0xa8);
          }
          else {
            iVar16 = 0;
            if (iVar39 < 1) {
              iVar16 = iVar39 + -1;
            }
            plVar36 = (longlong *)(lVar45 + (longlong)iVar16 * 0x28);
          }
        }
        pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18);
        *pfVar43 = fVar58 - fVar56;
        pfVar43 = (float *)FUN_18000f448(lVar30,(int)*pfVar18);
        pfVar38 = (float *)FUN_18000f448(lVar30,(int)*pfVar18 - 1);
        fVar58 = *pfVar43;
        iVar39 = 4;
        fVar56 = *pfVar38;
        lVar45 = *(longlong *)(local_618 + 0x80);
        if (lVar45 == 0) {
          if (*(code **)(local_618 + 0x90) != (code *)0x0) {
            (**(code **)(local_618 + 0x90))(*(undefined4 *)(local_618 + 0x98));
          }
          lVar45 = *(longlong *)(local_618 + 0x80);
          if (lVar45 != 0) goto LAB_18003a084;
          plVar36 = (longlong *)(local_618 + 0xa8);
        }
        else {
LAB_18003a084:
          iVar16 = *(int *)(local_618 + 0xa0);
          if (iVar16 == 0) {
            plVar36 = (longlong *)(local_618 + 0xa8);
          }
          else {
            if (iVar16 < 5) {
              iVar39 = iVar16 + -1;
            }
            plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
          }
        }
        pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18);
        iVar39 = 4;
        *pfVar43 = fVar58 - fVar56;
        lVar45 = *(longlong *)(local_618 + 0x80);
        if (lVar45 == 0) {
          if (*(code **)(local_618 + 0x90) != (code *)0x0) {
            (**(code **)(local_618 + 0x90))(*(undefined4 *)(local_618 + 0x98));
          }
          lVar45 = *(longlong *)(local_618 + 0x80);
          if (lVar45 != 0) goto LAB_18003a0f0;
          plVar36 = (longlong *)(local_618 + 0xa8);
        }
        else {
LAB_18003a0f0:
          iVar16 = *(int *)(local_618 + 0xa0);
          if (iVar16 == 0) {
            plVar36 = (longlong *)(local_618 + 0xa8);
          }
          else {
            if (iVar16 < 5) {
              iVar39 = iVar16 + -1;
            }
            plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
          }
        }
        pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18);
        fVar56 = *pfVar43;
        if (fVar54 <= 0.0 || (int)local_4b0 < 0x47) {
LAB_18003a164:
          iVar39 = 1;
          lVar45 = *(longlong *)(local_638 + 0x80);
          if (lVar45 == 0) {
            if (*(code **)(local_638 + 0x90) != (code *)0x0) {
              (**(code **)(local_638 + 0x90))(*(undefined4 *)(local_638 + 0x98));
            }
            lVar45 = *(longlong *)(local_638 + 0x80);
            if (lVar45 != 0) goto LAB_18003a198;
            plVar36 = (longlong *)(local_638 + 0xa8);
          }
          else {
LAB_18003a198:
            iVar16 = *(int *)(local_638 + 0xa0);
            if (iVar16 == 0) {
              plVar36 = (longlong *)(local_638 + 0xa8);
            }
            else {
              if (iVar16 < 2) {
                iVar39 = iVar16 + -1;
              }
              plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
            }
          }
          pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18 - 1);
          if (*pfVar43 == 1.0) goto LAB_18003a1dc;
          if (fVar54 < 0.0 && 0x46 < (int)local_4b0) {
            plVar36 = FUN_1800076d0((longlong *)(local_638 + 0x80),1);
            pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18 - 1);
            if (*pfVar43 != 0.0) goto LAB_18003a6dc;
LAB_18003a70c:
            lVar45 = local_638;
            plVar36 = FUN_1800076d0((longlong *)(local_638 + 0x80),1);
            pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18 - 1);
            fVar55 = local_628;
            if (*pfVar43 == -1.0) {
              iVar16 = 1;
              plVar36 = FUN_1800076d0((longlong *)(param_3 + 0x112),2);
              pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18);
              iVar39 = 1;
              fVar49 = *pfVar43;
              do {
                plVar36 = FUN_1800076d0((longlong *)(param_3 + 0x112),2);
                pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18 - iVar39);
                fVar54 = *pfVar43;
                plVar36 = FUN_1800076d0((longlong *)(lVar45 + 0x80),1);
                pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18 - iVar39);
                if (*pfVar43 == 0.0) {
                  iVar16 = iVar39 + -1;
                  break;
                }
                iVar39 = iVar39 + 1;
                if (fVar49 <= fVar54) {
                  fVar54 = fVar49;
                }
                fVar49 = fVar54;
              } while (iVar39 < 0x14);
              iVar39 = 4;
              lVar45 = *(longlong *)(local_618 + 0x80);
              if (lVar45 == 0) {
                if (*(code **)(local_618 + 0x90) != (code *)0x0) {
                  (**(code **)(local_618 + 0x90))(*(undefined4 *)(local_618 + 0x98));
                }
                lVar45 = *(longlong *)(local_618 + 0x80);
                if (lVar45 != 0) goto LAB_18003a7fc;
                plVar36 = (longlong *)(local_618 + 0xa8);
              }
              else {
LAB_18003a7fc:
                iVar28 = *(int *)(local_618 + 0xa0);
                if (iVar28 == 0) {
                  plVar36 = (longlong *)(local_618 + 0xa8);
                }
                else {
                  if (iVar28 < 5) {
                    iVar39 = iVar28 + -1;
                  }
                  plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
                }
              }
              pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18 - iVar16);
              lVar45 = local_638;
              if (*pfVar43 * -0.1 <= fVar56) {
                uVar8 = *(ushort *)(param_3 + 0x238);
                plVar36 = FUN_1800076d0((longlong *)(local_638 + 0x80),1);
                puVar26 = (undefined4 *)FUN_180005d08(plVar36,(int)*pfVar18);
                lVar45 = local_5f8;
                *puVar26 = 0;
                plVar36 = FUN_1800076d0((longlong *)(local_5f8 + 0x80),0);
                pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18);
                *pfVar43 = (float)(uVar8 + 2);
                pfVar43 = (float *)FUN_18000f448(lVar45,(int)*pfVar18);
                *pfVar43 = fVar49 - fVar50;
                uVar53 = *(undefined4 *)(lVar45 + 0x1c);
                puVar26 = (undefined4 *)FUN_180005d08((longlong *)(lVar45 + 0x58),(int)*pfVar18);
                *puVar26 = uVar53;
                puVar26 = (undefined4 *)FUN_18000f448(lVar45,(int)*pfVar18);
                uVar53 = *puVar26;
                puVar26 = (undefined4 *)FUN_18000f448(local_598,(int)*pfVar18);
                *puVar26 = uVar53;
                puVar26 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x2ac),(int)local_4b0);
                uVar53 = *puVar26;
                puVar26 = (undefined4 *)FUN_18000f448(local_520,(int)local_4b0);
                *puVar26 = uVar53;
              }
              else {
                plVar36 = FUN_1800076d0((longlong *)(local_638 + 0x80),1);
                puVar26 = (undefined4 *)FUN_180005d08(plVar36,(int)*pfVar18);
                *puVar26 = 0xbf800000;
                pfVar43 = (float *)FUN_18000f448(lVar45,(int)*pfVar18);
                *pfVar43 = fVar49 - fVar50;
                uVar53 = *(undefined4 *)(lVar45 + 0x1c);
                puVar26 = (undefined4 *)FUN_180005d08((longlong *)(lVar45 + 0x58),(int)*pfVar18);
                *puVar26 = uVar53;
                puVar26 = (undefined4 *)FUN_18000f448(lVar45,(int)*pfVar18);
                uVar53 = *puVar26;
                puVar26 = (undefined4 *)FUN_18000f448(local_5d0,(int)*pfVar18);
                *puVar26 = uVar53;
              }
              goto LAB_18003aaac;
            }
            if ((fVar54 < local_628 * local_5d8) &&
               (pfVar43 = (float *)FUN_18000f448(lVar4,(int)*pfVar18), *pfVar43 < fVar55 * fVar49))
            {
              plVar36 = FUN_1800076d0((longlong *)(lVar45 + 0x80),1);
              puVar26 = (undefined4 *)FUN_180005d08(plVar36,(int)*pfVar18);
              *puVar26 = 0xbf800000;
              plVar36 = FUN_1800076d0((longlong *)(param_3 + 0x112),2);
              pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18);
              fVar49 = *pfVar43;
              pfVar43 = (float *)FUN_18000f448(lVar45,(int)*pfVar18);
              *pfVar43 = fVar49 - fVar50;
              uVar53 = *(undefined4 *)(lVar45 + 0x1c);
              puVar26 = (undefined4 *)FUN_180005d08((longlong *)(lVar45 + 0x58),(int)*pfVar18);
              *puVar26 = uVar53;
              puVar26 = (undefined4 *)FUN_18000f448(lVar45,(int)*pfVar18);
              uVar53 = *puVar26;
              puVar26 = (undefined4 *)FUN_18000f448(local_5d0,(int)*pfVar18);
              *puVar26 = uVar53;
              goto LAB_18003aaac;
            }
            goto LAB_18003aa10;
          }
LAB_18003a6dc:
          plVar36 = FUN_1800076d0((longlong *)(local_638 + 0x80),1);
          pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18 - 1);
          if (*pfVar43 == -1.0) goto LAB_18003a70c;
        }
        else {
          plVar36 = FUN_1800076d0((longlong *)(local_638 + 0x80),1);
          pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18 - 1);
          if (*pfVar43 != 0.0) goto LAB_18003a164;
LAB_18003a1dc:
          lVar45 = local_638;
          plVar36 = FUN_1800076d0((longlong *)(local_638 + 0x80),1);
          pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18 - 1);
          if (*pfVar43 == 1.0) {
            plVar36 = FUN_1800076d0((longlong *)(param_3 + 0x112),1);
            pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18);
            iVar39 = 1;
            local_628 = 1.4013e-45;
            fVar49 = *pfVar43;
            do {
              lVar30 = *(longlong *)(param_3 + 0x112);
              iVar16 = 1;
              if (lVar30 == 0) {
                if (*(code **)(param_3 + 0x116) != (code *)0x0) {
                  (**(code **)(param_3 + 0x116))(param_3[0x118]);
                }
                lVar30 = *(longlong *)(param_3 + 0x112);
                if (lVar30 != 0) goto LAB_18003a260;
                plVar36 = (longlong *)(param_3 + 0x11c);
              }
              else {
LAB_18003a260:
                iVar28 = param_3[0x11a];
                if (iVar28 == 0) {
                  plVar36 = (longlong *)(param_3 + 0x11c);
                }
                else {
                  if (iVar28 < 2) {
                    iVar16 = iVar28 + -1;
                  }
                  plVar36 = (longlong *)(lVar30 + (longlong)iVar16 * 0x28);
                }
              }
              pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18 - iVar39);
              lVar30 = *(longlong *)(lVar45 + 0x80);
              iVar16 = 1;
              fVar54 = *pfVar43;
              if (lVar30 == 0) {
                if (*(code **)(lVar45 + 0x90) != (code *)0x0) {
                  (**(code **)(lVar45 + 0x90))(*(undefined4 *)(lVar45 + 0x98));
                }
                lVar30 = *(longlong *)(lVar45 + 0x80);
                if (lVar30 != 0) goto LAB_18003a2bc;
                plVar36 = (longlong *)(lVar45 + 0xa8);
              }
              else {
LAB_18003a2bc:
                iVar28 = *(int *)(lVar45 + 0xa0);
                if (iVar28 == 0) {
                  plVar36 = (longlong *)(lVar45 + 0xa8);
                }
                else {
                  if (iVar28 < 2) {
                    iVar16 = iVar28 + -1;
                  }
                  plVar36 = (longlong *)(lVar30 + (longlong)iVar16 * 0x28);
                }
              }
              pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18 - iVar39);
              if (*pfVar43 == 0.0) {
                fVar54 = (float)(iVar39 - 1);
                break;
              }
              iVar39 = iVar39 + 1;
              if (fVar54 <= fVar49) {
                fVar54 = fVar49;
              }
              fVar49 = fVar54;
              fVar54 = local_628;
            } while (iVar39 < 0x14);
            iVar39 = 4;
            lVar45 = *(longlong *)(local_618 + 0x80);
            if (lVar45 == 0) {
              if (*(code **)(local_618 + 0x90) != (code *)0x0) {
                (**(code **)(local_618 + 0x90))(*(undefined4 *)(local_618 + 0x98));
              }
              lVar45 = *(longlong *)(local_618 + 0x80);
              if (lVar45 != 0) goto LAB_18003a354;
              plVar36 = (longlong *)(local_618 + 0xa8);
            }
            else {
LAB_18003a354:
              iVar16 = *(int *)(local_618 + 0xa0);
              if (iVar16 == 0) {
                plVar36 = (longlong *)(local_618 + 0xa8);
              }
              else {
                if (iVar16 < 5) {
                  iVar39 = iVar16 + -1;
                }
                plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
              }
            }
            fVar55 = *pfVar18;
            lVar45 = *plVar36;
            if (lVar45 == 0) {
              if ((code *)plVar36[2] != (code *)0x0) {
                (*(code *)plVar36[2])((int)plVar36[3]);
              }
              lVar45 = *plVar36;
              if (lVar45 != 0) goto LAB_18003a3b0;
              pfVar43 = (float *)((longlong)plVar36 + 0x24);
            }
            else {
LAB_18003a3b0:
              iVar39 = (int)plVar36[4];
              if (iVar39 == 0) {
                pfVar43 = (float *)((longlong)plVar36 + 0x24);
              }
              else {
                uVar14 = (int)fVar55 - (int)fVar54 &
                         ((int)fVar55 - (int)fVar54 >> 0x1f ^ 0xffffffffU);
                if (iVar39 <= (int)uVar14) {
                  uVar14 = iVar39 - 1;
                }
                pfVar43 = (float *)(lVar45 + (longlong)(int)uVar14 * 4);
              }
            }
            iVar39 = 1;
            if (fVar56 <= *pfVar43 * -0.1) {
              uVar8 = *(ushort *)(param_3 + 0x238);
              lVar45 = *(longlong *)(local_638 + 0x80);
              if (lVar45 == 0) {
                if (*(code **)(local_638 + 0x90) != (code *)0x0) {
                  (**(code **)(local_638 + 0x90))(*(undefined4 *)(local_638 + 0x98));
                }
                lVar45 = *(longlong *)(local_638 + 0x80);
                if (lVar45 != 0) goto LAB_18003a4dc;
                plVar36 = (longlong *)(local_638 + 0xa8);
              }
              else {
LAB_18003a4dc:
                iVar16 = *(int *)(local_638 + 0xa0);
                if (iVar16 == 0) {
                  plVar36 = (longlong *)(local_638 + 0xa8);
                }
                else {
                  if (iVar16 < 2) {
                    iVar39 = iVar16 + -1;
                  }
                  plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
                }
              }
              puVar26 = (undefined4 *)FUN_180005d08(plVar36,(int)*pfVar18);
              *puVar26 = 0;
              lVar45 = *(longlong *)(local_5f8 + 0x80);
              if (lVar45 == 0) {
                if (*(code **)(local_5f8 + 0x90) != (code *)0x0) {
                  (**(code **)(local_5f8 + 0x90))(*(undefined4 *)(local_5f8 + 0x98));
                }
                lVar45 = *(longlong *)(local_5f8 + 0x80);
                if (lVar45 != 0) goto LAB_18003a544;
                plVar36 = (longlong *)(local_5f8 + 0xa8);
              }
              else {
LAB_18003a544:
                iVar39 = *(int *)(local_5f8 + 0xa0);
                if (iVar39 == 0) {
                  plVar36 = (longlong *)(local_5f8 + 0xa8);
                }
                else {
                  iVar16 = 0;
                  if (iVar39 < 1) {
                    iVar16 = iVar39 + -1;
                  }
                  plVar36 = (longlong *)(lVar45 + (longlong)iVar16 * 0x28);
                }
              }
              pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18);
              lVar45 = local_5f8;
              *pfVar43 = (float)(uVar8 + 2);
              pfVar43 = (float *)FUN_18000f448(local_5f8,(int)*pfVar18);
              *pfVar43 = fVar49 + fVar50;
              uVar53 = *(undefined4 *)(lVar45 + 0x18);
              puVar26 = (undefined4 *)FUN_180005d08((longlong *)(lVar45 + 0x58),(int)*pfVar18);
              *puVar26 = uVar53;
              puVar26 = (undefined4 *)FUN_18000f448(lVar45,(int)*pfVar18);
              uVar53 = *puVar26;
              puVar26 = (undefined4 *)FUN_18000f448(local_550,(int)*pfVar18);
              *puVar26 = uVar53;
              puVar26 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x2ac),(int)local_4b0);
              uVar53 = *puVar26;
              puVar26 = (undefined4 *)FUN_18000f448(local_528,(int)local_4b0);
              *puVar26 = uVar53;
            }
            else {
              lVar45 = *(longlong *)(local_638 + 0x80);
              if (lVar45 == 0) {
                if (*(code **)(local_638 + 0x90) != (code *)0x0) {
                  (**(code **)(local_638 + 0x90))(*(undefined4 *)(local_638 + 0x98));
                }
                lVar45 = *(longlong *)(local_638 + 0x80);
                if (lVar45 != 0) goto LAB_18003a41c;
                plVar36 = (longlong *)(local_638 + 0xa8);
              }
              else {
LAB_18003a41c:
                iVar16 = *(int *)(local_638 + 0xa0);
                if (iVar16 == 0) {
                  plVar36 = (longlong *)(local_638 + 0xa8);
                }
                else {
                  if (iVar16 < 2) {
                    iVar39 = iVar16 + -1;
                  }
                  plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
                }
              }
              puVar26 = (undefined4 *)FUN_180005d08(plVar36,(int)*pfVar18);
              lVar45 = local_638;
              *puVar26 = 0x3f800000;
              pfVar43 = (float *)FUN_18000f448(local_638,(int)*pfVar18);
              *pfVar43 = fVar49 + fVar50;
              uVar53 = *(undefined4 *)(lVar45 + 0x18);
              puVar26 = (undefined4 *)FUN_180005d08((longlong *)(lVar45 + 0x58),(int)*pfVar18);
              *puVar26 = uVar53;
              puVar26 = (undefined4 *)FUN_18000f448(lVar45,(int)*pfVar18);
              uVar53 = *puVar26;
              puVar26 = (undefined4 *)FUN_18000f448(local_5c0,(int)*pfVar18);
              *puVar26 = uVar53;
            }
          }
          else if ((fVar54 <= fVar55 * local_5d8) ||
                  (pfVar43 = (float *)FUN_18000f448(lVar4,(int)*pfVar18),
                  *pfVar43 <= fVar55 * fVar49)) {
LAB_18003aa10:
            lVar45 = local_638;
            plVar36 = FUN_1800076d0((longlong *)(local_638 + 0x80),1);
            puVar26 = (undefined4 *)FUN_180005d08(plVar36,(int)*pfVar18);
            lVar30 = local_5f8;
            *puVar26 = 0;
            plVar36 = FUN_1800076d0((longlong *)(local_5f8 + 0x80),0);
            puVar26 = (undefined4 *)FUN_180005d08(plVar36,(int)*pfVar18);
            *puVar26 = 0;
            puVar26 = (undefined4 *)FUN_18000f448(lVar45,(int)*pfVar18);
            *puVar26 = 0;
            puVar26 = (undefined4 *)FUN_18000f448(lVar30,(int)*pfVar18);
            *puVar26 = 0;
            puVar26 = (undefined4 *)FUN_18000f448(local_550,(int)*pfVar18);
            *puVar26 = 0;
            puVar26 = (undefined4 *)FUN_18000f448(local_598,(int)*pfVar18);
            *puVar26 = 0;
            puVar26 = (undefined4 *)FUN_18000f448(local_5c0,(int)*pfVar18);
            *puVar26 = 0;
            puVar26 = (undefined4 *)FUN_18000f448(local_5d0,(int)*pfVar18);
            *puVar26 = 0;
          }
          else {
            plVar36 = FUN_1800076d0((longlong *)(lVar45 + 0x80),1);
            puVar26 = (undefined4 *)FUN_180005d08(plVar36,(int)*pfVar18);
            *puVar26 = 0x3f800000;
            plVar36 = FUN_1800076d0((longlong *)(param_3 + 0x112),1);
            pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18);
            fVar49 = *pfVar43;
            pfVar43 = (float *)FUN_18000f448(lVar45,(int)*pfVar18);
            *pfVar43 = fVar50 + fVar49;
            uVar53 = *(undefined4 *)(lVar45 + 0x18);
            puVar26 = (undefined4 *)FUN_180005d08((longlong *)(lVar45 + 0x58),(int)*pfVar18);
            *puVar26 = uVar53;
            puVar26 = (undefined4 *)FUN_18000f448(lVar45,(int)*pfVar18);
            uVar53 = *puVar26;
            puVar26 = (undefined4 *)FUN_18000f448(local_5c0,(int)*pfVar18);
            *puVar26 = uVar53;
          }
        }
LAB_18003aaac:
        bVar13 = FUN_180026690(*(longlong *)(local_610 + 0x20));
        if (bVar13) {
          plVar36 = FUN_180005d78((longlong *)(param_3 + 0x502),(int)*pfVar18);
          fVar49 = *pfVar18;
          lVar45 = (longlong)param_3[0xd5] * 1000000 + (*plVar36 / 86400000000) * 86400000000;
          plVar36 = FUN_180005d78((longlong *)(param_3 + 0x58),(int)fVar49);
          lVar30 = *plVar36;
          if ((int)fVar49 < *param_3 + -1) {
            plVar36 = FUN_180005d78((longlong *)(param_3 + 0x58),(int)fVar49 + 1);
            lVar31 = *plVar36;
          }
          else {
            lVar31 = *(longlong *)(param_3 + 0xb4);
          }
          if (lVar30 <= lVar45 && lVar45 < lVar31) {
            FUN_18003d110((longlong)local_630);
            fVar49 = *local_580;
            pfVar43 = local_580;
            if (fVar49 == -NAN) {
              fVar49 = *pfVar18;
              pfVar43 = pfVar18;
            }
            *local_5b0 = fVar49;
            plVar36 = FUN_1800076d0((longlong *)(lVar5 + 0x80),2);
            puVar26 = (undefined4 *)FUN_180005d08(plVar36,(int)*pfVar43);
            *puVar26 = 0x3f800000;
          }
          iVar39 = 2;
          fVar49 = *local_5b0;
          fVar50 = *pfVar18;
          lVar45 = *(longlong *)(param_3 + 0x112);
          if (lVar45 == 0) {
            if (*(code **)(param_3 + 0x116) != (code *)0x0) {
              (**(code **)(param_3 + 0x116))(param_3[0x118]);
            }
            lVar45 = *(longlong *)(param_3 + 0x112);
            if (lVar45 != 0) goto LAB_18003abc8;
            piVar21 = param_3 + 0x11c;
LAB_18003ac00:
            if (*(code **)(param_3 + 0x116) != (code *)0x0) {
              (**(code **)(param_3 + 0x116))(param_3[0x118]);
            }
            lVar45 = *(longlong *)(param_3 + 0x112);
            if (lVar45 != 0) goto LAB_18003ac20;
            piVar19 = param_3 + 0x11c;
          }
          else {
LAB_18003abc8:
            iVar16 = param_3[0x11a];
            if (iVar16 == 0) {
              piVar21 = param_3 + 0x11c;
            }
            else {
              if (iVar16 < 3) {
                iVar39 = iVar16 + -1;
              }
              piVar21 = (int *)(lVar45 + (longlong)iVar39 * 0x28);
            }
            if (lVar45 == 0) goto LAB_18003ac00;
LAB_18003ac20:
            iVar39 = param_3[0x11a];
            if (iVar39 == 0) {
              piVar19 = param_3 + 0x11c;
            }
            else {
              iVar16 = 1;
              if (iVar39 + -1 == 0 || iVar39 < 1) {
                iVar16 = iVar39 + -1;
              }
              piVar19 = (int *)(lVar45 + (longlong)iVar16 * 0x28);
            }
          }
          FUN_180011708(local_5d4,(longlong)param_3,piVar19,piVar21,lVar5,fVar50,local_548,fVar49);
          lVar45 = *(longlong *)(lVar5 + 0x80);
          iVar39 = 1;
          if (lVar45 == 0) {
            if (*(code **)(lVar5 + 0x90) != (code *)0x0) {
              (**(code **)(lVar5 + 0x90))(*(undefined4 *)(lVar5 + 0x98));
            }
            lVar45 = *(longlong *)(lVar5 + 0x80);
            if (lVar45 != 0) goto LAB_18003ac90;
            plVar36 = (longlong *)(lVar5 + 0xa8);
          }
          else {
LAB_18003ac90:
            iVar16 = *(int *)(lVar5 + 0xa0);
            if (iVar16 == 0) {
              plVar36 = (longlong *)(lVar5 + 0xa8);
            }
            else {
              if (iVar16 < 2) {
                iVar39 = iVar16 + -1;
              }
              plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
            }
          }
          pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18);
          lVar45 = *(longlong *)(lVar5 + 0x80);
          iVar39 = 1;
          fVar49 = *pfVar43;
          if (lVar45 == 0) {
            if (*(code **)(lVar5 + 0x90) != (code *)0x0) {
              (**(code **)(lVar5 + 0x90))(*(undefined4 *)(lVar5 + 0x98));
            }
            lVar45 = *(longlong *)(lVar5 + 0x80);
            if (lVar45 != 0) goto LAB_18003acf4;
            plVar36 = (longlong *)(lVar5 + 0xa8);
          }
          else {
LAB_18003acf4:
            iVar16 = *(int *)(lVar5 + 0xa0);
            if (iVar16 == 0) {
              plVar36 = (longlong *)(lVar5 + 0xa8);
            }
            else {
              if (iVar16 < 2) {
                iVar39 = iVar16 + -1;
              }
              plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
            }
          }
          pfVar43 = (float *)FUN_180005d08(plVar36,(int)fVar49 + -1);
          lVar45 = *(longlong *)(lVar5 + 0x80);
          iVar39 = 1;
          fVar49 = (float)(int)*pfVar43;
          if (lVar45 == 0) {
            if (*(code **)(lVar5 + 0x90) != (code *)0x0) {
              (**(code **)(lVar5 + 0x90))(*(undefined4 *)(lVar5 + 0x98));
            }
            lVar45 = *(longlong *)(lVar5 + 0x80);
            if (lVar45 != 0) goto LAB_18003ad54;
            plVar36 = (longlong *)(lVar5 + 0xa8);
          }
          else {
LAB_18003ad54:
            iVar16 = *(int *)(lVar5 + 0xa0);
            if (iVar16 == 0) {
              plVar36 = (longlong *)(lVar5 + 0xa8);
            }
            else {
              if (iVar16 < 2) {
                iVar39 = iVar16 + -1;
              }
              plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
            }
          }
          pfVar43 = (float *)FUN_180005d08(plVar36,(int)fVar49 - 1);
          fVar54 = 0.0;
          fVar55 = 0.0;
          local_4a4 = (float)(int)*pfVar43;
          uVar14 = FUN_180026550(*(longlong *)(local_610 + 0x28));
          lVar45 = *(longlong *)(lVar5 + 0x80);
          fVar50 = fVar49;
          if (uVar14 != 0) {
            fVar50 = extraout_w11;
          }
          if (lVar45 == 0) {
            if (*(code **)(lVar5 + 0x90) != (code *)0x0) {
              (**(code **)(lVar5 + 0x90))(*(undefined4 *)(lVar5 + 0x98));
            }
            lVar45 = *(longlong *)(lVar5 + 0x80);
            if (lVar45 != 0) goto LAB_18003add0;
            plVar36 = (longlong *)(lVar5 + 0xa8);
          }
          else {
LAB_18003add0:
            iVar39 = *(int *)(lVar5 + 0xa0);
            if (iVar39 == 0) {
              plVar36 = (longlong *)(lVar5 + 0xa8);
            }
            else {
              iVar16 = 0;
              if (iVar39 < 1) {
                iVar16 = iVar39 + -1;
              }
              plVar36 = (longlong *)(lVar45 + (longlong)iVar16 * 0x28);
            }
          }
          pfVar43 = (float *)FUN_180005d08(plVar36,(int)fVar50);
          if (*pfVar43 == 1.0) {
            iVar39 = 1;
            lVar45 = *(longlong *)(param_3 + 0x112);
            if (lVar45 == 0) {
              if (*(code **)(param_3 + 0x116) != (code *)0x0) {
                (**(code **)(param_3 + 0x116))(param_3[0x118]);
              }
              lVar45 = *(longlong *)(param_3 + 0x112);
              if (lVar45 != 0) goto LAB_18003ae3c;
              plVar36 = (longlong *)(param_3 + 0x11c);
            }
            else {
LAB_18003ae3c:
              iVar16 = param_3[0x11a];
              if (iVar16 == 0) {
                plVar36 = (longlong *)(param_3 + 0x11c);
              }
              else {
                if (iVar16 < 2) {
                  iVar39 = iVar16 + -1;
                }
                plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
              }
            }
            pfVar43 = (float *)FUN_180005d08(plVar36,(int)fVar50);
            fVar55 = *pfVar43;
          }
          lVar45 = *(longlong *)(lVar5 + 0x80);
          if (lVar45 == 0) {
            if (*(code **)(lVar5 + 0x90) != (code *)0x0) {
              (**(code **)(lVar5 + 0x90))(*(undefined4 *)(lVar5 + 0x98));
            }
            lVar45 = *(longlong *)(lVar5 + 0x80);
            if (lVar45 != 0) goto LAB_18003ae98;
            plVar36 = (longlong *)(lVar5 + 0xa8);
          }
          else {
LAB_18003ae98:
            iVar39 = *(int *)(lVar5 + 0xa0);
            if (iVar39 == 0) {
              plVar36 = (longlong *)(lVar5 + 0xa8);
            }
            else {
              iVar16 = 0;
              if (iVar39 < 1) {
                iVar16 = iVar39 + -1;
              }
              plVar36 = (longlong *)(lVar45 + (longlong)iVar16 * 0x28);
            }
          }
          pfVar43 = (float *)FUN_180005d08(plVar36,(int)fVar50);
          if (*pfVar43 == -1.0) {
            iVar39 = 2;
            lVar45 = *(longlong *)(param_3 + 0x112);
            if (lVar45 == 0) {
              if (*(code **)(param_3 + 0x116) != (code *)0x0) {
                (**(code **)(param_3 + 0x116))(param_3[0x118]);
              }
              lVar45 = *(longlong *)(param_3 + 0x112);
              if (lVar45 != 0) goto LAB_18003af08;
              plVar36 = (longlong *)(param_3 + 0x11c);
            }
            else {
LAB_18003af08:
              iVar16 = param_3[0x11a];
              if (iVar16 == 0) {
                plVar36 = (longlong *)(param_3 + 0x11c);
              }
              else {
                if (iVar16 < 3) {
                  iVar39 = iVar16 + -1;
                }
                plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
              }
            }
            pfVar43 = (float *)FUN_180005d08(plVar36,(int)fVar50);
            fVar54 = *pfVar43;
          }
          if (fVar55 == 0.0) {
            fVar55 = *local_590;
          }
          if (fVar54 == 0.0) {
            fVar54 = *local_558;
          }
          if ((fVar49 != *local_578) || (local_4a4 != *local_588)) {
            iVar39 = 3;
            lVar45 = *(longlong *)(param_3 + 0x112);
            if (lVar45 == 0) {
              if (*(code **)(param_3 + 0x116) != (code *)0x0) {
                (**(code **)(param_3 + 0x116))(param_3[0x118]);
              }
              lVar45 = *(longlong *)(param_3 + 0x112);
              if (lVar45 != 0) goto LAB_18003afb4;
              plVar36 = (longlong *)(param_3 + 0x11c);
            }
            else {
LAB_18003afb4:
              iVar16 = param_3[0x11a];
              if (iVar16 == 0) {
                plVar36 = (longlong *)(param_3 + 0x11c);
              }
              else {
                if (iVar16 < 4) {
                  iVar39 = iVar16 + -1;
                }
                plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
              }
            }
            puVar26 = (undefined4 *)FUN_180005d08(plVar36,(int)*pfVar18);
            pfVar43 = local_630;
            *local_540 = *puVar26;
            plVar34 = *(longlong **)(local_630 + 2);
            for (plVar36 = (longlong *)*plVar34; plVar36 != plVar34; plVar36 = (longlong *)*plVar36)
            {
              *(undefined4 *)((longlong)plVar36 + 0x14) = 0;
            }
            if ((int)fVar50 < (int)*pfVar18) {
              do {
                local_4ac = fVar55;
                local_628 = fVar49;
                iVar16 = FUN_1800064f0(*(longlong **)(param_3 + 0x3a6),(uint)fVar50);
                iVar39 = 0;
                if (0 < iVar16) {
                  do {
                    plVar36 = *(longlong **)(param_3 + 0x3a6);
                    if ((uint)fVar50 < (uint)*(float *)(plVar36 + 1)) {
                      if (*plVar36 == 0) {
                        uVar24 = (ulonglong)*(uint *)(plVar36 + 3);
                      }
                      else {
                        uVar24 = (ulonglong)*(uint *)(*plVar36 + (ulonglong)(uint)fVar50 * 4);
                      }
                    }
                    else {
                      uVar24 = (ulonglong)*(uint *)(plVar36 + 3);
                    }
                    if ((int)fVar50 + 1U < (uint)*(float *)(plVar36 + 1)) {
                      if (*plVar36 == 0) {
                        uVar14 = *(uint *)(plVar36 + 3);
                      }
                      else {
                        uVar14 = *(uint *)(*plVar36 + (ulonglong)((int)fVar50 + 1U) * 4);
                      }
                    }
                    else {
                      uVar14 = *(uint *)(plVar36 + 3);
                    }
                    if ((uint)uVar24 < uVar14) {
                      iVar17 = (uVar14 - (uint)uVar24) + -1;
                      iVar28 = iVar39;
                      if (iVar17 < iVar39) {
                        iVar28 = iVar17;
                      }
                      puVar26 = (undefined4 *)(plVar36[2] + (uVar24 + (longlong)iVar28) * 0x14);
                      iVar28 = puVar26[2];
                      iVar17 = puVar26[3];
                      local_494 = *puVar26;
                      piVar21 = (int *)FUN_18003cc68(pfVar43,(byte *)&local_494);
                      iVar6 = *piVar21;
                      piVar21 = (int *)FUN_18003cc68(pfVar43,(byte *)&local_494);
                      *piVar21 = iVar6 + (iVar17 - iVar28);
                    }
                    iVar39 = iVar39 + 1;
                  } while (iVar39 < iVar16);
                }
                fVar50 = (float)((int)fVar50 + 1);
                fVar49 = local_628;
                fVar55 = local_4ac;
              } while ((int)fVar50 < (int)*pfVar18);
            }
            *local_590 = fVar55;
            *local_558 = fVar54;
          }
          pfVar43 = local_630;
          *local_580 = *local_588;
          *local_578 = fVar49;
          *local_588 = local_4a4;
          plVar36 = *(longlong **)(param_3 + 0x3a6);
          fVar49 = *pfVar18;
          if ((uint)fVar49 < (uint)*(float *)(plVar36 + 1)) {
            if (*plVar36 == 0) {
              uVar14 = *(uint *)(plVar36 + 3);
            }
            else {
              uVar14 = *(uint *)(*plVar36 + (ulonglong)(uint)fVar49 * 4);
            }
          }
          else {
            uVar14 = *(uint *)(plVar36 + 3);
          }
          if ((int)fVar49 + 1U < (uint)*(float *)(plVar36 + 1)) {
            if (*plVar36 == 0) {
              uVar15 = *(uint *)(plVar36 + 3);
            }
            else {
              uVar15 = *(uint *)(*plVar36 + (ulonglong)((int)fVar49 + 1U) * 4);
            }
          }
          else {
            uVar15 = *(uint *)(plVar36 + 3);
          }
          if (uVar14 < uVar15) {
            uVar35 = 0;
            if (0 < (int)(uVar15 - uVar14)) {
              do {
                plVar36 = *(longlong **)(param_3 + 0x3a6);
                fVar49 = *pfVar18;
                if ((uint)fVar49 < (uint)*(float *)(plVar36 + 1)) {
                  if (*plVar36 == 0) {
                    uVar24 = (ulonglong)*(uint *)(plVar36 + 3);
                  }
                  else {
                    uVar24 = (ulonglong)*(uint *)(*plVar36 + (ulonglong)(uint)fVar49 * 4);
                  }
                }
                else {
                  uVar24 = (ulonglong)*(uint *)(plVar36 + 3);
                }
                if ((int)fVar49 + 1U < (uint)*(float *)(plVar36 + 1)) {
                  if (*plVar36 == 0) {
                    uVar27 = *(uint *)(plVar36 + 3);
                  }
                  else {
                    uVar27 = *(uint *)(*plVar36 + (ulonglong)((int)fVar49 + 1U) * 4);
                  }
                }
                else {
                  uVar27 = *(uint *)(plVar36 + 3);
                }
                if ((uint)uVar24 < uVar27) {
                  uVar1 = uVar35 & ((int)uVar35 >> 0x1f ^ 0xffffffffU);
                  uVar27 = (uVar27 - (uint)uVar24) - 1;
                  if ((int)uVar27 < (int)uVar1) {
                    uVar1 = uVar27;
                  }
                  puVar26 = (undefined4 *)(plVar36[2] + (uVar24 + (longlong)(int)uVar1) * 0x14);
                  iVar39 = puVar26[2];
                  iVar16 = puVar26[3];
                  local_498 = *puVar26;
                  piVar21 = (int *)FUN_18003cc68(pfVar43,(byte *)&local_498);
                  iVar28 = *piVar21;
                  piVar21 = (int *)FUN_18003cc68(pfVar43,(byte *)&local_498);
                  *piVar21 = (iVar16 - iVar39) + iVar28;
                }
                uVar35 = uVar35 + 1;
              } while ((int)uVar35 < (int)(uVar15 - uVar14));
            }
          }
          lVar45 = *(longlong *)(local_610 + 0x24);
          switch(*(undefined1 *)(lVar45 + 0x18)) {
          default:
            local_624 = 0;
            break;
          case 1:
          case 3:
          case 4:
          case 6:
          case 0xb:
          case 0xd:
          case 0xe:
          case 0xf:
          case 0x10:
          case 0x11:
          case 0x13:
          case 0x16:
          case 0x18:
            local_624 = (ulonglong)*(uint *)(lVar45 + 0x1c);
            break;
          case 2:
            fVar49 = *(float *)(lVar45 + 0x1c);
            if (0.0 <= fVar49) {
              local_624 = (ulonglong)(uint)(int)(fVar49 + 0.5);
            }
            else {
              local_624 = (ulonglong)(uint)(int)(fVar49 - 0.5);
            }
            break;
          case 5:
            local_624 = (ulonglong)(*(int *)(lVar45 + 0x1c) != 0);
            break;
          case 8:
            if (*(double *)(lVar45 + 0x1c) == 0.0) {
              local_624 = 0;
            }
            else {
              dVar57 = *(double *)(lVar45 + 0x1c) * 86400000000.0;
              if (0.0 <= dVar57) {
                local_624 = (longlong)(dVar57 + 0.5) / 86400000000;
              }
              else {
                local_624 = (longlong)(dVar57 - 0.5) / 86400000000;
              }
            }
            break;
          case 9:
          case 0x19:
            if (*(double *)(lVar45 + 0x1c) == 0.0) {
              local_624 = 0;
            }
            else {
              dVar57 = *(double *)(lVar45 + 0x1c) * 86400000000.0;
              if (0.0 <= dVar57) {
                dVar57 = dVar57 + 0.5;
              }
              else {
                dVar57 = dVar57 - 0.5;
              }
              uVar24 = ((longlong)dVar57 % 86400000000) / 1000000;
              local_624 = 0;
              if ((longlong)uVar24 < 0x15180) {
                local_624 = uVar24;
              }
            }
            break;
          case 0x17:
            dVar57 = *(double *)(lVar45 + 0x1c);
            if (0.0 <= dVar57) {
              local_624 = (ulonglong)(uint)(int)(dVar57 + 0.5);
            }
            else {
              local_624 = (ulonglong)(uint)(int)(dVar57 - 0.5);
            }
          }
          iVar39 = 3;
          lVar45 = *(longlong *)(param_3 + 0x112);
          if (lVar45 == 0) {
            if (*(code **)(param_3 + 0x116) != (code *)0x0) {
              (**(code **)(param_3 + 0x116))(param_3[0x118]);
            }
            lVar45 = *(longlong *)(param_3 + 0x112);
            if (lVar45 != 0) goto LAB_18003b460;
            plVar36 = (longlong *)(param_3 + 0x11c);
            local_4a4 = *pfVar18;
LAB_18003b4a4:
            if (*(code **)(param_3 + 0x116) != (code *)0x0) {
              (**(code **)(param_3 + 0x116))(param_3[0x118]);
            }
            lVar45 = *(longlong *)(param_3 + 0x112);
            if (lVar45 != 0) goto LAB_18003b4e0;
            plVar34 = (longlong *)(param_3 + 0x11c);
            local_628 = (float)((int)*pfVar18 - 3);
LAB_18003b528:
            if (*(code **)(param_3 + 0x116) != (code *)0x0) {
              (**(code **)(param_3 + 0x116))(param_3[0x118]);
            }
            lVar45 = *(longlong *)(param_3 + 0x112);
            if (lVar45 != 0) goto LAB_18003b560;
            plVar41 = (longlong *)(param_3 + 0x11c);
            local_4ac = *pfVar18;
LAB_18003b5a4:
            if (*(code **)(param_3 + 0x116) != (code *)0x0) {
              (**(code **)(param_3 + 0x116))(param_3[0x118]);
            }
            lVar45 = *(longlong *)(param_3 + 0x112);
            if (lVar45 != 0) goto LAB_18003b5e0;
            plVar47 = (longlong *)(param_3 + 0x11c);
            local_63c = (int)*pfVar18 - 5;
LAB_18003b628:
            if (*(code **)(param_3 + 0x116) != (code *)0x0) {
              (**(code **)(param_3 + 0x116))(param_3[0x118]);
            }
            lVar45 = *(longlong *)(param_3 + 0x112);
            if (lVar45 != 0) goto LAB_18003b65c;
            plVar44 = (longlong *)(param_3 + 0x11c);
            fVar49 = *pfVar18;
LAB_18003b69c:
            if (*(code **)(param_3 + 0x116) != (code *)0x0) {
              (**(code **)(param_3 + 0x116))(param_3[0x118]);
            }
            lVar45 = *(longlong *)(param_3 + 0x112);
            if (lVar45 != 0) goto LAB_18003b6bc;
            plVar37 = (longlong *)(param_3 + 0x11c);
          }
          else {
LAB_18003b460:
            iVar16 = param_3[0x11a];
            if (iVar16 == 0) {
              plVar36 = (longlong *)(param_3 + 0x11c);
            }
            else {
              if (iVar16 < 4) {
                iVar39 = iVar16 + -1;
              }
              plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
            }
            local_4a4 = *pfVar18;
            if (lVar45 == 0) goto LAB_18003b4a4;
LAB_18003b4e0:
            iVar39 = param_3[0x11a];
            if (iVar39 == 0) {
              plVar34 = (longlong *)(param_3 + 0x11c);
            }
            else {
              iVar16 = 3;
              if (iVar39 < 4) {
                iVar16 = iVar39 + -1;
              }
              plVar34 = (longlong *)(lVar45 + (longlong)iVar16 * 0x28);
            }
            local_628 = (float)((int)*pfVar18 - 3);
            if (lVar45 == 0) goto LAB_18003b528;
LAB_18003b560:
            iVar39 = param_3[0x11a];
            if (iVar39 == 0) {
              plVar41 = (longlong *)(param_3 + 0x11c);
            }
            else {
              iVar16 = 3;
              if (iVar39 < 4) {
                iVar16 = iVar39 + -1;
              }
              plVar41 = (longlong *)(lVar45 + (longlong)iVar16 * 0x28);
            }
            local_4ac = *pfVar18;
            if (lVar45 == 0) goto LAB_18003b5a4;
LAB_18003b5e0:
            iVar39 = param_3[0x11a];
            if (iVar39 == 0) {
              plVar47 = (longlong *)(param_3 + 0x11c);
            }
            else {
              iVar16 = 3;
              if (iVar39 < 4) {
                iVar16 = iVar39 + -1;
              }
              plVar47 = (longlong *)(lVar45 + (longlong)iVar16 * 0x28);
            }
            local_63c = (int)*pfVar18 - 5;
            if (lVar45 == 0) goto LAB_18003b628;
LAB_18003b65c:
            iVar39 = param_3[0x11a];
            if (iVar39 == 0) {
              plVar44 = (longlong *)(param_3 + 0x11c);
            }
            else {
              iVar16 = 3;
              if (iVar39 < 4) {
                iVar16 = iVar39 + -1;
              }
              plVar44 = (longlong *)(lVar45 + (longlong)iVar16 * 0x28);
            }
            fVar49 = *pfVar18;
            if (lVar45 == 0) goto LAB_18003b69c;
LAB_18003b6bc:
            iVar39 = param_3[0x11a];
            if (iVar39 == 0) {
              plVar37 = (longlong *)(param_3 + 0x11c);
            }
            else {
              iVar16 = 3;
              if (iVar39 < 4) {
                iVar16 = iVar39 + -1;
              }
              plVar37 = (longlong *)(lVar45 + (longlong)iVar16 * 0x28);
            }
          }
          local_640 = (int)*pfVar18 - 8;
          lVar45 = *plVar36;
          if (lVar45 == 0) {
            if ((code *)plVar36[2] != (code *)0x0) {
              (*(code *)plVar36[2])((int)plVar36[3]);
            }
            lVar45 = *plVar36;
            if (lVar45 != 0) goto LAB_18003b71c;
            pfVar43 = (float *)((longlong)plVar36 + 0x24);
          }
          else {
LAB_18003b71c:
            iVar39 = (int)plVar36[4];
            if (iVar39 == 0) {
              pfVar43 = (float *)((longlong)plVar36 + 0x24);
            }
            else {
              uVar14 = (uint)local_4a4 & ((int)local_4a4 >> 0x1f ^ 0xffffffffU);
              if (iVar39 <= (int)uVar14) {
                uVar14 = iVar39 - 1;
              }
              pfVar43 = (float *)(lVar45 + (longlong)(int)uVar14 * 4);
            }
          }
          lVar45 = *plVar41;
          if (lVar45 == 0) {
            if ((code *)plVar41[2] != (code *)0x0) {
              (*(code *)plVar41[2])((int)plVar41[3]);
            }
            lVar45 = *plVar41;
            if (lVar45 != 0) goto LAB_18003b76c;
            pfVar38 = (float *)((longlong)plVar41 + 0x24);
          }
          else {
LAB_18003b76c:
            iVar39 = (int)plVar41[4];
            if (iVar39 == 0) {
              pfVar38 = (float *)((longlong)plVar41 + 0x24);
            }
            else {
              uVar14 = (uint)local_4ac & ((int)local_4ac >> 0x1f ^ 0xffffffffU);
              if (iVar39 <= (int)uVar14) {
                uVar14 = iVar39 - 1;
              }
              pfVar38 = (float *)(lVar45 + (longlong)(int)uVar14 * 4);
            }
          }
          lVar45 = *plVar37;
          if (lVar45 == 0) {
            if ((code *)plVar37[2] != (code *)0x0) {
              (*(code *)plVar37[2])((int)plVar37[3]);
            }
            lVar45 = *plVar37;
            if (lVar45 != 0) goto LAB_18003b7bc;
            pfVar23 = (float *)((longlong)plVar37 + 0x24);
          }
          else {
LAB_18003b7bc:
            iVar39 = (int)plVar37[4];
            if (iVar39 == 0) {
              pfVar23 = (float *)((longlong)plVar37 + 0x24);
            }
            else {
              uVar14 = local_640 & ((int)local_640 >> 0x1f ^ 0xffffffffU);
              if (iVar39 <= (int)uVar14) {
                uVar14 = iVar39 - 1;
              }
              pfVar23 = (float *)(lVar45 + (longlong)(int)uVar14 * 4);
            }
          }
          lVar45 = *plVar44;
          if (lVar45 == 0) {
            if ((code *)plVar44[2] != (code *)0x0) {
              (*(code *)plVar44[2])((int)plVar44[3]);
            }
            lVar45 = *plVar44;
            if (lVar45 != 0) goto LAB_18003b80c;
            pfVar42 = (float *)((longlong)plVar44 + 0x24);
          }
          else {
LAB_18003b80c:
            iVar39 = (int)plVar44[4];
            if (iVar39 == 0) {
              pfVar42 = (float *)((longlong)plVar44 + 0x24);
            }
            else {
              uVar14 = (uint)fVar49 & ((int)fVar49 >> 0x1f ^ 0xffffffffU);
              if (iVar39 <= (int)uVar14) {
                uVar14 = iVar39 - 1;
              }
              pfVar42 = (float *)(lVar45 + (longlong)(int)uVar14 * 4);
            }
          }
          lVar45 = *plVar47;
          if (lVar45 == 0) {
            if ((code *)plVar47[2] != (code *)0x0) {
              (*(code *)plVar47[2])((int)plVar47[3]);
            }
            lVar45 = *plVar47;
            if (lVar45 != 0) goto LAB_18003b858;
            pfVar48 = (float *)((longlong)plVar47 + 0x24);
          }
          else {
LAB_18003b858:
            iVar39 = (int)plVar47[4];
            if (iVar39 == 0) {
              pfVar48 = (float *)((longlong)plVar47 + 0x24);
            }
            else {
              uVar14 = local_63c & ((int)local_63c >> 0x1f ^ 0xffffffffU);
              if (iVar39 <= (int)uVar14) {
                uVar14 = iVar39 - 1;
              }
              pfVar48 = (float *)(lVar45 + (longlong)(int)uVar14 * 4);
            }
          }
          lVar45 = *plVar34;
          if (lVar45 == 0) {
            if ((code *)plVar34[2] != (code *)0x0) {
              (*(code *)plVar34[2])((int)plVar34[3]);
            }
            lVar45 = *plVar34;
            if (lVar45 != 0) goto LAB_18003b8a8;
            pfVar32 = (float *)((longlong)plVar34 + 0x24);
          }
          else {
LAB_18003b8a8:
            iVar39 = (int)plVar34[4];
            if (iVar39 == 0) {
              pfVar32 = (float *)((longlong)plVar34 + 0x24);
            }
            else {
              uVar14 = (uint)local_628 & ((int)local_628 >> 0x1f ^ 0xffffffffU);
              if (iVar39 <= (int)uVar14) {
                uVar14 = iVar39 - 1;
              }
              pfVar32 = (float *)(lVar45 + (longlong)(int)uVar14 * 4);
            }
          }
          lVar45 = *(longlong *)(param_3 + 0x112);
          iVar39 = 3;
          fVar50 = ((*pfVar38 - *pfVar48) + (*pfVar43 - *pfVar32) + (*pfVar42 - *pfVar23)) / 3.0;
          fVar49 = 1.0 / (float)param_3[0x1a6];
          if (lVar45 == 0) {
            if (*(code **)(param_3 + 0x116) != (code *)0x0) {
              (**(code **)(param_3 + 0x116))(param_3[0x118]);
            }
            lVar45 = *(longlong *)(param_3 + 0x112);
            if (lVar45 != 0) goto LAB_18003b950;
            plVar36 = (longlong *)(param_3 + 0x11c);
          }
          else {
LAB_18003b950:
            iVar16 = param_3[0x11a];
            if (iVar16 == 0) {
              plVar36 = (longlong *)(param_3 + 0x11c);
            }
            else {
              if (iVar16 < 4) {
                iVar39 = iVar16 + -1;
              }
              plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
            }
          }
          fVar54 = *pfVar18;
          lVar45 = *plVar36;
          if (lVar45 == 0) {
            if ((code *)plVar36[2] != (code *)0x0) {
              (*(code *)plVar36[2])((int)plVar36[3]);
            }
            lVar45 = *plVar36;
            if (lVar45 != 0) goto LAB_18003b9a8;
            pfVar43 = (float *)((longlong)plVar36 + 0x24);
          }
          else {
LAB_18003b9a8:
            iVar39 = (int)plVar36[4];
            if (iVar39 == 0) {
              pfVar43 = (float *)((longlong)plVar36 + 0x24);
            }
            else {
              uVar14 = (uint)fVar54 & ((int)fVar54 >> 0x1f ^ 0xffffffffU);
              if (iVar39 <= (int)uVar14) {
                uVar14 = iVar39 - 1;
              }
              pfVar43 = (float *)(lVar45 + (longlong)(int)uVar14 * 4);
            }
          }
          fVar54 = *pfVar43;
          lVar45 = *(longlong *)(param_3 + 0x112);
          if (0.0 <= fVar50) {
            if (fVar50 <= 0.0) {
              iVar39 = 3;
              if (lVar45 == 0) {
                if (*(code **)(param_3 + 0x116) != (code *)0x0) {
                  (**(code **)(param_3 + 0x116))(param_3[0x118]);
                }
                lVar45 = *(longlong *)(param_3 + 0x112);
                if (lVar45 == 0) {
                  plVar36 = (longlong *)(param_3 + 0x11c);
                  goto LAB_18003bb44;
                }
              }
              iVar16 = param_3[0x11a];
              if (iVar16 != 0) {
                bVar12 = SBORROW4(iVar16,3);
                iVar28 = iVar16 + -3;
                bVar13 = iVar16 == 3;
                goto LAB_18003bb34;
              }
              plVar36 = (longlong *)(param_3 + 0x11c);
            }
            else {
              iVar39 = 1;
              if (lVar45 == 0) {
                if (*(code **)(param_3 + 0x116) != (code *)0x0) {
                  (**(code **)(param_3 + 0x116))(param_3[0x118]);
                }
                lVar45 = *(longlong *)(param_3 + 0x112);
                if (lVar45 == 0) {
                  plVar36 = (longlong *)(param_3 + 0x11c);
                  goto LAB_18003bb44;
                }
              }
              iVar16 = param_3[0x11a];
              if (iVar16 == 0) {
                plVar36 = (longlong *)(param_3 + 0x11c);
              }
              else {
                bVar12 = SBORROW4(iVar16,1);
                iVar28 = iVar16 + -1;
                bVar13 = iVar16 == 1;
LAB_18003bb34:
                if (bVar13 || iVar28 < 0 != bVar12) {
                  iVar39 = iVar16 + -1;
                }
                plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
              }
            }
LAB_18003bb44:
            pfVar43 = (float *)FUN_180005d08(plVar36,(int)*pfVar18);
            fVar55 = *pfVar43;
          }
          else {
            iVar39 = 2;
            if (lVar45 == 0) {
              if (*(code **)(param_3 + 0x116) != (code *)0x0) {
                (**(code **)(param_3 + 0x116))(param_3[0x118]);
              }
              lVar45 = *(longlong *)(param_3 + 0x112);
              if (lVar45 != 0) goto LAB_18003ba14;
              plVar36 = (longlong *)(param_3 + 0x11c);
            }
            else {
LAB_18003ba14:
              iVar16 = param_3[0x11a];
              if (iVar16 == 0) {
                plVar36 = (longlong *)(param_3 + 0x11c);
              }
              else {
                if (iVar16 < 3) {
                  iVar39 = iVar16 + -1;
                }
                plVar36 = (longlong *)(lVar45 + (longlong)iVar39 * 0x28);
              }
            }
            fVar55 = *pfVar18;
            lVar45 = *plVar36;
            if (lVar45 == 0) {
              if ((code *)plVar36[2] != (code *)0x0) {
                (*(code *)plVar36[2])((int)plVar36[3]);
              }
              lVar45 = *plVar36;
              if (lVar45 == 0) {
                fVar55 = *(float *)((longlong)plVar36 + 0x24);
                goto LAB_18003bb54;
              }
            }
            iVar39 = (int)plVar36[4];
            if (iVar39 == 0) {
              fVar55 = *(float *)((longlong)plVar36 + 0x24);
            }
            else {
              uVar14 = (uint)fVar55 & ((int)fVar55 >> 0x1f ^ 0xffffffffU);
              if (iVar39 <= (int)uVar14) {
                uVar14 = iVar39 - 1;
              }
              fVar55 = *(float *)(lVar45 + (longlong)(int)uVar14 * 4);
            }
          }
LAB_18003bb54:
          lVar45 = *(longlong *)(local_610 + 0x22);
          fVar55 = (float)(int)(fVar49 * fVar55);
          switch(*(undefined1 *)(lVar45 + 0x18)) {
          default:
            uVar14 = 0;
            break;
          case 1:
          case 3:
          case 4:
          case 6:
          case 0xb:
          case 0xd:
          case 0xe:
          case 0xf:
          case 0x10:
          case 0x11:
          case 0x13:
          case 0x16:
          case 0x18:
            uVar14 = *(uint *)(lVar45 + 0x1c);
            break;
          case 2:
            fVar56 = *(float *)(lVar45 + 0x1c);
            if (0.0 <= fVar56) {
              uVar14 = (uint)(fVar56 + 0.5);
            }
            else {
              uVar14 = (uint)(fVar56 - 0.5);
            }
            break;
          case 5:
            uVar14 = (uint)(*(int *)(lVar45 + 0x1c) != 0);
            break;
          case 8:
            if (*(double *)(lVar45 + 0x1c) == 0.0) {
              uVar14 = 0;
            }
            else {
              dVar57 = *(double *)(lVar45 + 0x1c) * 86400000000.0;
              if (0.0 <= dVar57) {
                uVar14 = (uint)((longlong)(dVar57 + 0.5) / 86400000000);
              }
              else {
                uVar14 = (uint)((longlong)(dVar57 - 0.5) / 86400000000);
              }
            }
            break;
          case 9:
            if (*(double *)(lVar45 + 0x1c) == 0.0) {
              uVar14 = 0;
            }
            else {
              dVar57 = *(double *)(lVar45 + 0x1c) * 86400000000.0;
              if (0.0 <= dVar57) {
                dVar57 = dVar57 + 0.5;
              }
              else {
                dVar57 = dVar57 - 0.5;
              }
              lVar30 = ((longlong)dVar57 % 86400000000) / 1000000;
              lVar45 = 0;
              if (lVar30 < 0x15180) {
                lVar45 = lVar30;
              }
              uVar14 = (uint)lVar45;
            }
            break;
          case 0x17:
            dVar57 = *(double *)(lVar45 + 0x1c);
            if (0.0 <= dVar57) {
              uVar14 = (uint)(dVar57 + 0.5);
            }
            else {
              uVar14 = (uint)(dVar57 - 0.5);
            }
            break;
          case 0x19:
            plVar36 = (longlong *)FUN_1800043e0(*(double *)(lVar45 + 0x1c),&lStack_510);
            lVar45 = *plVar36 % 86400000000;
            uVar14 = ((int)(lVar45 / 1000000) + (int)(lVar45 >> 0x3f)) -
                     (SUB164(SEXT816(lVar45) * SEXT816(0x431bde82d7b634db),0xc) >> 0x1f);
            fVar55 = extraout_w11_00;
            if (86399999999 < lVar45) {
              uVar14 = 0;
            }
          }
          pfVar43 = local_630;
          fVar56 = (float)((int)(fVar49 * fVar54) + (int)uVar14 / 2);
          local_4a8 = (float)((int)(fVar49 * fVar54) - (int)uVar14 / 2);
          if (0.0 > fVar50) {
            fVar56 = (float)((int)fVar55 + uVar14);
            local_4a8 = fVar55;
          }
          if (fVar50 > 0.0) {
            local_4a8 = (float)((int)fVar55 - uVar14);
            fVar56 = fVar55;
          }
          local_63c = 0;
          uVar15 = local_63c;
          local_4ac = local_4a8;
          if ((int)local_4a8 <= (int)fVar56) {
            uVar15 = 0;
            do {
              piVar21 = (int *)FUN_18003cc68(pfVar43,(byte *)&local_4ac);
              uVar15 = uVar15 + *piVar21;
              local_4ac = (float)((int)local_4ac + 1);
            } while ((int)local_4ac <= (int)fVar56);
          }
          local_63c = uVar15;
          fVar54 = (float)(int)local_63c;
          fVar50 = *pfVar18;
          lVar45 = *(longlong *)(local_570 + 0x30);
          if (lVar45 == 0) {
            if (*(code **)(local_570 + 0x40) != (code *)0x0) {
              (**(code **)(local_570 + 0x40))(*(undefined4 *)(local_570 + 0x48));
            }
            lVar45 = *(longlong *)(local_570 + 0x30);
            if (lVar45 != 0) goto LAB_18003bdb8;
            pfVar43 = (float *)(local_570 + 0x54);
          }
          else {
LAB_18003bdb8:
            iVar39 = *(int *)(local_570 + 0x50);
            if (iVar39 == 0) {
              pfVar43 = (float *)(local_570 + 0x54);
            }
            else {
              uVar15 = (uint)fVar50 & ((int)fVar50 >> 0x1f ^ 0xffffffffU);
              if (iVar39 <= (int)uVar15) {
                uVar15 = iVar39 - 1;
              }
              pfVar43 = (float *)(lVar45 + (longlong)(int)uVar15 * 4);
            }
          }
          pfVar38 = local_630;
          *pfVar43 = fVar54;
          iVar39 = 0;
          pfVar43 = local_630;
          for (local_4ac = local_4a8; local_630 = pfVar43, (int)local_4ac <= (int)fVar56;
              local_4ac = (float)((int)local_4ac + 1)) {
            lVar45 = FUN_18003cc68(pfVar38,(byte *)&local_4ac);
            iVar39 = iVar39 + *(int *)(lVar45 + 8);
            pfVar43 = local_630;
          }
          iVar16 = 0;
          for (local_4ac = local_4a8; (int)local_4ac <= (int)fVar56;
              local_4ac = (float)((int)local_4ac + 1)) {
            lVar45 = FUN_18003cc68(pfVar43,(byte *)&local_4ac);
            iVar16 = iVar16 + *(int *)(lVar45 + 4);
          }
          iVar28 = (int)fVar56 + (int)local_4a8;
          fVar50 = *pfVar18;
          lVar45 = *(longlong *)(local_568 + 0x30);
          if (lVar45 == 0) {
            if (*(code **)(local_568 + 0x40) != (code *)0x0) {
              (**(code **)(local_568 + 0x40))(*(undefined4 *)(local_568 + 0x48));
            }
            lVar45 = *(longlong *)(local_568 + 0x30);
            if (lVar45 != 0) goto LAB_18003beb4;
            pfVar43 = (float *)(local_568 + 0x54);
          }
          else {
LAB_18003beb4:
            iVar17 = *(int *)(local_568 + 0x50);
            if (iVar17 == 0) {
              pfVar43 = (float *)(local_568 + 0x54);
            }
            else {
              uVar15 = (uint)fVar50 & ((int)fVar50 >> 0x1f ^ 0xffffffffU);
              if (iVar17 <= (int)uVar15) {
                uVar15 = iVar17 - 1;
              }
              pfVar43 = (float *)(lVar45 + (longlong)(int)uVar15 * 4);
            }
          }
          *pfVar43 = ((float)iVar28 * 0.5) / fVar49;
          fVar50 = *pfVar18;
          lVar45 = *(longlong *)(local_5f0 + 0x30);
          if (lVar45 == 0) {
            if (*(code **)(local_5f0 + 0x40) != (code *)0x0) {
              (**(code **)(local_5f0 + 0x40))(*(undefined4 *)(local_5f0 + 0x48));
            }
            lVar45 = *(longlong *)(local_5f0 + 0x30);
            if (lVar45 != 0) goto LAB_18003bf20;
            pfVar43 = (float *)(local_5f0 + 0x54);
          }
          else {
LAB_18003bf20:
            iVar28 = *(int *)(local_5f0 + 0x50);
            if (iVar28 == 0) {
              pfVar43 = (float *)(local_5f0 + 0x54);
            }
            else {
              uVar15 = (uint)fVar50 & ((int)fVar50 >> 0x1f ^ 0xffffffffU);
              if (iVar28 <= (int)uVar15) {
                uVar15 = iVar28 - 1;
              }
              pfVar43 = (float *)(lVar45 + (longlong)(int)uVar15 * 4);
            }
          }
          *pfVar43 = (float)(int)fVar56 / fVar49;
          fVar54 = (float)(int)local_4a8;
          fVar50 = *pfVar18;
          lVar45 = *(longlong *)(local_5e8 + 0x30);
          if (lVar45 == 0) {
            if (*(code **)(local_5e8 + 0x40) != (code *)0x0) {
              (**(code **)(local_5e8 + 0x40))(*(undefined4 *)(local_5e8 + 0x48));
            }
            lVar45 = *(longlong *)(local_5e8 + 0x30);
            if (lVar45 != 0) goto LAB_18003bf90;
            pfVar43 = (float *)(local_5e8 + 0x54);
          }
          else {
LAB_18003bf90:
            iVar28 = *(int *)(local_5e8 + 0x50);
            if (iVar28 == 0) {
              pfVar43 = (float *)(local_5e8 + 0x54);
            }
            else {
              uVar15 = (uint)fVar50 & ((int)fVar50 >> 0x1f ^ 0xffffffffU);
              if (iVar28 <= (int)uVar15) {
                uVar15 = iVar28 - 1;
              }
              pfVar43 = (float *)(lVar45 + (longlong)(int)uVar15 * 4);
            }
          }
          *pfVar43 = fVar54 / fVar49;
          lVar45 = *(longlong *)(local_610 + 0x26);
          switch(*(undefined1 *)(lVar45 + 0x18)) {
          default:
            fVar50 = 0.0;
            break;
          case 1:
          case 3:
          case 4:
          case 6:
          case 0xd:
          case 0xf:
          case 0x10:
          case 0x11:
          case 0x16:
          case 0x18:
            fVar50 = (float)*(uint *)(lVar45 + 0x1c);
            break;
          case 2:
            fVar50 = *(float *)(lVar45 + 0x1c);
            break;
          case 5:
            fVar50 = 1.0;
            if (*(int *)(lVar45 + 0x1c) == 0) {
              fVar50 = 0.0;
            }
            break;
          case 0xb:
          case 0x13:
            fVar50 = (float)*(int *)(lVar45 + 0x1c);
            break;
          case 0x17:
            fVar50 = (float)*(double *)(lVar45 + 0x1c);
          }
          fVar55 = (float)(int)local_624;
          iVar28 = (int)((float)iVar16 * fVar50 * fVar55 + fVar55);
          iVar16 = -iVar28;
          fVar54 = *pfVar18;
          lVar45 = *(longlong *)(local_608 + 0x30);
          iVar39 = (int)((float)iVar39 * fVar50 * fVar55 + fVar55);
          if ((int)local_63c < 1) {
            if (lVar45 == 0) {
              if (*(code **)(local_608 + 0x40) != (code *)0x0) {
                (**(code **)(local_608 + 0x40))(*(undefined4 *)(local_608 + 0x48));
              }
              lVar45 = *(longlong *)(local_608 + 0x30);
              if (lVar45 != 0) goto LAB_18003c0fc;
              pfVar43 = (float *)(local_608 + 0x54);
            }
            else {
LAB_18003c0fc:
              iVar17 = *(int *)(local_608 + 0x50);
              if (iVar17 == 0) {
                pfVar43 = (float *)(local_608 + 0x54);
              }
              else {
                uVar15 = (uint)fVar54 & ((int)fVar54 >> 0x1f ^ 0xffffffffU);
                if (iVar17 <= (int)uVar15) {
                  uVar15 = iVar17 - 1;
                }
                pfVar43 = (float *)(lVar45 + (longlong)(int)uVar15 * 4);
              }
            }
            fVar54 = (float)iVar16;
          }
          else {
            if (lVar45 == 0) {
              if (*(code **)(local_608 + 0x40) != (code *)0x0) {
                (**(code **)(local_608 + 0x40))(*(undefined4 *)(local_608 + 0x48));
              }
              lVar45 = *(longlong *)(local_608 + 0x30);
              if (lVar45 == 0) {
                pfVar43 = (float *)(local_608 + 0x54);
                fVar54 = (float)iVar39;
                goto LAB_18003c128;
              }
            }
            iVar17 = *(int *)(local_608 + 0x50);
            if (iVar17 == 0) {
              pfVar43 = (float *)(local_608 + 0x54);
              fVar54 = (float)iVar39;
            }
            else {
              uVar15 = (uint)fVar54 & ((int)fVar54 >> 0x1f ^ 0xffffffffU);
              if (iVar17 <= (int)uVar15) {
                uVar15 = iVar17 - 1;
              }
              pfVar43 = (float *)(lVar45 + (longlong)(int)uVar15 * 4);
              fVar54 = (float)iVar39;
            }
          }
LAB_18003c128:
          fVar55 = local_4a8;
          lVar45 = local_5e0;
          *pfVar43 = fVar54;
          if (iVar39 < (int)local_63c) {
            piVar21 = (int *)FUN_18003cc68(local_630,(byte *)&local_4a8);
            pfVar38 = local_630;
            iVar39 = *piVar21;
            pfVar43 = local_630;
            local_4ac = local_4a8;
            while (local_630 = pfVar43, (int)local_4ac <= (int)fVar56) {
              piVar21 = (int *)FUN_18003cc68(pfVar38,(byte *)&local_4ac);
              iVar16 = *piVar21;
              fVar54 = local_4ac;
              if (iVar16 <= iVar39) {
                fVar54 = fVar55;
              }
              local_4ac = (float)((int)local_4ac + 1);
              pfVar43 = local_630;
              fVar55 = fVar54;
              if (iVar39 < iVar16) {
                iVar39 = iVar16;
              }
            }
            fVar54 = (float)(uVar14 + (int)fVar55);
            iVar39 = 0;
            local_4a4 = (float)((int)fVar55 - uVar14);
            local_4a8 = local_4a4;
            local_4ac = fVar54;
            local_49c = fVar55;
            for (; (int)local_4a4 <= (int)fVar54; local_4a4 = (float)((int)local_4a4 + 1)) {
              lVar45 = FUN_18003cc68(pfVar43,(byte *)&local_4a4);
              iVar39 = *(int *)(lVar45 + 8) + iVar39;
            }
            fVar55 = *pfVar18;
            lVar45 = *(longlong *)(local_560 + 0x30);
            if (lVar45 == 0) {
              if (*(code **)(local_560 + 0x40) != (code *)0x0) {
                (**(code **)(local_560 + 0x40))(*(undefined4 *)(local_560 + 0x48));
              }
              lVar45 = *(longlong *)(local_560 + 0x30);
              if (lVar45 != 0) goto LAB_18003c220;
              pfVar43 = (float *)(local_560 + 0x54);
            }
            else {
LAB_18003c220:
              iVar16 = *(int *)(local_560 + 0x50);
              if (iVar16 == 0) {
                pfVar43 = (float *)(local_560 + 0x54);
              }
              else {
                uVar14 = (uint)fVar55 & ((int)fVar55 >> 0x1f ^ 0xffffffffU);
                if (iVar16 <= (int)uVar14) {
                  uVar14 = iVar16 - 1;
                }
                pfVar43 = (float *)(lVar45 + (longlong)(int)uVar14 * 4);
              }
            }
            *pfVar43 = (float)iVar39;
            if (0 < iVar39) {
              fVar55 = *pfVar18;
              lVar45 = *(longlong *)(local_5f0 + 0x30);
              if (lVar45 == 0) {
                if (*(code **)(local_5f0 + 0x40) != (code *)0x0) {
                  (**(code **)(local_5f0 + 0x40))(*(undefined4 *)(local_5f0 + 0x48));
                }
                lVar45 = *(longlong *)(local_5f0 + 0x30);
                if (lVar45 != 0) goto LAB_18003c298;
                pfVar43 = (float *)(local_5f0 + 0x54);
              }
              else {
LAB_18003c298:
                iVar16 = *(int *)(local_5f0 + 0x50);
                if (iVar16 == 0) {
                  pfVar43 = (float *)(local_5f0 + 0x54);
                }
                else {
                  uVar14 = (uint)fVar55 & ((int)fVar55 >> 0x1f ^ 0xffffffffU);
                  if (iVar16 <= (int)uVar14) {
                    uVar14 = iVar16 - 1;
                  }
                  pfVar43 = (float *)(lVar45 + (longlong)(int)uVar14 * 4);
                }
              }
              *pfVar43 = (float)(int)fVar54 / fVar49;
              fVar55 = (float)(int)local_4a8;
              fVar54 = *pfVar18;
              lVar45 = *(longlong *)(local_5e8 + 0x30);
              if (lVar45 == 0) {
                if (*(code **)(local_5e8 + 0x40) != (code *)0x0) {
                  (**(code **)(local_5e8 + 0x40))(*(undefined4 *)(local_5e8 + 0x48));
                }
                lVar45 = *(longlong *)(local_5e8 + 0x30);
                if (lVar45 != 0) goto LAB_18003c308;
                pfVar43 = (float *)(local_5e8 + 0x54);
              }
              else {
LAB_18003c308:
                iVar16 = *(int *)(local_5e8 + 0x50);
                if (iVar16 == 0) {
                  pfVar43 = (float *)(local_5e8 + 0x54);
                }
                else {
                  uVar14 = (uint)fVar54 & ((int)fVar54 >> 0x1f ^ 0xffffffffU);
                  if (iVar16 <= (int)uVar14) {
                    uVar14 = iVar16 - 1;
                  }
                  pfVar43 = (float *)(lVar45 + (longlong)(int)uVar14 * 4);
                }
              }
              pfVar38 = local_630;
              *pfVar43 = fVar55 / fVar49;
              local_640 = 0;
              iVar28 = FUN_180037118(local_630,(byte *)&local_4a8,(int *)&local_4ac,
                                     (int *)&local_640);
              iVar16 = (int)local_624;
              if ((int)local_624 < iVar28) {
                local_640 = 0;
                iVar16 = FUN_180037118(pfVar38,(byte *)&local_4a8,(int *)&local_4ac,
                                       (int *)&local_640);
              }
              fVar54 = *pfVar18;
              lVar45 = *(longlong *)(local_608 + 0x30);
              iVar39 = (int)(((float)iVar39 * fVar50 + 1.0) * (float)iVar16);
              if (lVar45 == 0) {
                if (*(code **)(local_608 + 0x40) != (code *)0x0) {
                  (**(code **)(local_608 + 0x40))(*(undefined4 *)(local_608 + 0x48));
                }
                lVar45 = *(longlong *)(local_608 + 0x30);
                if (lVar45 != 0) goto LAB_18003c3cc;
                pfVar43 = (float *)(local_608 + 0x54);
              }
              else {
LAB_18003c3cc:
                iVar16 = *(int *)(local_608 + 0x50);
                if (iVar16 == 0) {
                  pfVar43 = (float *)(local_608 + 0x54);
                }
                else {
                  uVar14 = (uint)fVar54 & ((int)fVar54 >> 0x1f ^ 0xffffffffU);
                  if (iVar16 <= (int)uVar14) {
                    uVar14 = iVar16 - 1;
                  }
                  pfVar43 = (float *)(lVar45 + (longlong)(int)uVar14 * 4);
                }
              }
              *pfVar43 = (float)iVar39;
              if ((int)local_63c <= iVar39) goto LAB_18003c7a8;
            }
            fVar54 = (float)(int)local_49c;
            fVar50 = *pfVar18;
            lVar45 = *(longlong *)(local_5e0 + 0x30);
            if (lVar45 == 0) {
              if (*(code **)(local_5e0 + 0x40) != (code *)0x0) {
                (**(code **)(local_5e0 + 0x40))(*(undefined4 *)(local_5e0 + 0x48));
              }
              lVar45 = *(longlong *)(local_5e0 + 0x30);
              if (lVar45 != 0) goto LAB_18003c44c;
              pfVar43 = (float *)(local_5e0 + 0x54);
            }
            else {
LAB_18003c44c:
              iVar39 = *(int *)(local_5e0 + 0x50);
              if (iVar39 == 0) {
                pfVar43 = (float *)(local_5e0 + 0x54);
              }
              else {
                uVar14 = (uint)fVar50 & ((int)fVar50 >> 0x1f ^ 0xffffffffU);
                if (iVar39 <= (int)uVar14) {
                  uVar14 = iVar39 - 1;
                }
                pfVar43 = (float *)(lVar45 + (longlong)(int)uVar14 * 4);
              }
            }
            lVar45 = local_5e0;
            *pfVar43 = fVar54 / fVar49;
            uVar53 = *(undefined4 *)(local_5e0 + 0x18);
            puVar26 = (undefined4 *)FUN_180005d08((longlong *)(local_5e0 + 0x58),(int)*pfVar18);
            *puVar26 = uVar53;
            puVar26 = (undefined4 *)FUN_18000f448(lVar45,(int)*pfVar18);
            uVar53 = *puVar26;
            puVar26 = (undefined4 *)FUN_18000f448(local_538,(int)*pfVar18);
            pfVar43 = local_630;
            *puVar26 = uVar53;
            lVar45 = FUN_18003cc68(local_630,(byte *)&local_49c);
            iVar39 = *(int *)(lVar45 + 8);
            lVar45 = FUN_18003cc68(pfVar43,(byte *)&local_49c);
            *(int *)(lVar45 + 8) = iVar39 + 1;
            lVar45 = FUN_18003cc68(pfVar43,(byte *)&local_49c);
            uVar14 = local_63c;
            if (*(int *)(lVar45 + 0xc) < (int)local_63c) {
              lVar45 = FUN_18003cc68(pfVar43,(byte *)&local_49c);
              *(uint *)(lVar45 + 0xc) = uVar14;
            }
          }
          else if ((int)(local_63c + iVar28) < 0 == SBORROW4(local_63c,iVar16)) {
            puVar26 = (undefined4 *)FUN_18000f448(local_5e0,(int)*pfVar18);
            *puVar26 = 0;
            puVar26 = (undefined4 *)FUN_18000f448(lVar45,(int)*pfVar18);
            uVar53 = *puVar26;
            puVar26 = (undefined4 *)FUN_18000f448(local_538,(int)*pfVar18);
            *puVar26 = uVar53;
            puVar26 = (undefined4 *)FUN_18000f448(lVar45,(int)*pfVar18);
            uVar53 = *puVar26;
            puVar26 = (undefined4 *)FUN_18000f448(local_530,(int)*pfVar18);
            *puVar26 = uVar53;
          }
          else {
            piVar21 = (int *)FUN_18003cc68(local_630,(byte *)&local_4a8);
            pfVar38 = local_630;
            iVar39 = *piVar21;
            pfVar43 = local_630;
            local_4a4 = local_4a8;
            while (local_630 = pfVar43, (int)local_4a4 <= (int)fVar56) {
              piVar21 = (int *)FUN_18003cc68(pfVar38,(byte *)&local_4a4);
              iVar16 = *piVar21;
              fVar54 = local_4a4;
              if (iVar39 <= iVar16) {
                fVar54 = fVar55;
              }
              local_4a4 = (float)((int)local_4a4 + 1);
              pfVar43 = local_630;
              fVar55 = fVar54;
              if (iVar16 < iVar39) {
                iVar39 = iVar16;
              }
            }
            fVar54 = (float)((int)fVar55 + uVar14);
            iVar39 = 0;
            local_4a4 = (float)((int)fVar55 - uVar14);
            local_4a8 = local_4a4;
            local_4ac = fVar54;
            local_4a0 = fVar55;
            for (; (int)local_4a4 <= (int)fVar54; local_4a4 = (float)((int)local_4a4 + 1)) {
              lVar45 = FUN_18003cc68(pfVar43,(byte *)&local_4a4);
              iVar39 = *(int *)(lVar45 + 4) + iVar39;
            }
            pfVar43 = (float *)FUN_18000f448((longlong)local_5c8,(int)*pfVar18);
            *pfVar43 = (float)iVar39;
            if (0 < iVar39) {
              pfVar43 = (float *)FUN_18000f448(local_5f0,(int)*pfVar18);
              *pfVar43 = (float)(int)fVar54 / fVar49;
              fVar54 = (float)(int)local_4a8;
              pfVar38 = (float *)FUN_18000f448(local_5e8,(int)*pfVar18);
              pfVar43 = local_630;
              iVar28 = (int)local_624;
              *pfVar38 = fVar54 / fVar49;
              local_640 = 1;
              iVar16 = -(int)local_624;
              iVar17 = FUN_180037118(local_630,(byte *)&local_4a8,(int *)&local_4ac,
                                     (int *)&local_640);
              if (iVar17 + iVar28 < 0 != SBORROW4(iVar17,iVar16)) {
                local_640 = 1;
                iVar16 = FUN_180037118(pfVar43,(byte *)&local_4a8,(int *)&local_4ac,
                                       (int *)&local_640);
              }
              iVar39 = (int)(((float)iVar39 * fVar50 + 1.0) * (float)iVar16);
              pfVar43 = (float *)FUN_18000f448(local_608,(int)*pfVar18);
              *pfVar43 = (float)iVar39;
              if (iVar39 <= (int)local_63c) goto LAB_18003c7a8;
            }
            lVar45 = local_5e0;
            fVar50 = (float)(int)local_4a0;
            pfVar43 = (float *)FUN_18000f448(local_5e0,(int)*pfVar18);
            *pfVar43 = fVar50 / fVar49;
            uVar53 = *(undefined4 *)(lVar45 + 0x1c);
            puVar26 = (undefined4 *)FUN_180005d08((longlong *)(lVar45 + 0x58),(int)*pfVar18);
            *puVar26 = uVar53;
            puVar26 = (undefined4 *)FUN_18000f448(lVar45,(int)*pfVar18);
            uVar53 = *puVar26;
            puVar26 = (undefined4 *)FUN_18000f448(local_530,(int)*pfVar18);
            pfVar43 = local_630;
            *puVar26 = uVar53;
            lVar45 = FUN_18003cc68(local_630,(byte *)&local_4a0);
            iVar39 = *(int *)(lVar45 + 4);
            lVar45 = FUN_18003cc68(pfVar43,(byte *)&local_4a0);
            *(int *)(lVar45 + 4) = iVar39 + 1;
            lVar45 = FUN_18003cc68(pfVar43,(byte *)&local_4a0);
            uVar14 = local_63c;
            if ((int)local_63c < *(int *)(lVar45 + 0x10)) {
              lVar45 = FUN_18003cc68(pfVar43,(byte *)&local_4a0);
              *(uint *)(lVar45 + 0x10) = uVar14;
            }
          }
        }
      }
LAB_18003c7a8:
      FUN_1800377c8((longlong)param_3,(longlong *)local_610,(uint *)&local_4b0);
      fVar50 = local_4b0;
      fVar49 = (float)param_3[3];
      if ((((fVar49 == 0.0) && (param_3[0x2b] == 0)) && (local_4b0 != (float)(*param_3 + -1))) ||
         (((local_4b0 == fVar49 && (param_3[0x2b] == 0)) &&
          ((local_4b0 != (float)(*param_3 - 1U) &&
           (*pfVar18 == fVar49 && *pfVar18 != (float)(*param_3 - 1U))))))) {
        lVar45 = *(longlong *)(local_610 + 0x8a);
        lVar30 = *(longlong *)(local_610 + 0x8c);
        lVar31 = *(longlong *)(local_5b8 + 0x30);
        if (lVar31 == 0) {
          if (*(code **)(local_5b8 + 0x40) != (code *)0x0) {
            (**(code **)(local_5b8 + 0x40))(*(undefined4 *)(local_5b8 + 0x48));
          }
          lVar31 = *(longlong *)(local_5b8 + 0x30);
          if (lVar31 != 0) goto LAB_18003c868;
          fVar49 = *(float *)(local_5b8 + 0x54);
          plVar36 = (longlong *)(local_5b8 + 0x30);
          iVar39 = (int)local_4b0 - 1;
LAB_18003c8a8:
          if ((code *)plVar36[2] != (code *)0x0) {
            (*(code *)plVar36[2])((int)plVar36[3]);
          }
          lVar31 = *plVar36;
          if (lVar31 != 0) goto LAB_18003c8cc;
          pfVar43 = (float *)((longlong)plVar36 + 0x24);
        }
        else {
LAB_18003c868:
          iVar39 = *(int *)(local_5b8 + 0x50);
          if (iVar39 == 0) {
            pfVar43 = (float *)(local_5b8 + 0x54);
          }
          else {
            uVar14 = (uint)fVar50 & ((int)fVar50 >> 0x1f ^ 0xffffffffU);
            if (iVar39 <= (int)uVar14) {
              uVar14 = iVar39 - 1;
            }
            pfVar43 = (float *)(lVar31 + (longlong)(int)uVar14 * 4);
          }
          fVar49 = *pfVar43;
          iVar39 = (int)local_4b0 - 1;
          plVar36 = (longlong *)(local_5b8 + 0x30);
          if (lVar31 == 0) goto LAB_18003c8a8;
LAB_18003c8cc:
          iVar16 = (int)plVar36[4];
          if (iVar16 == 0) {
            pfVar43 = (float *)((longlong)plVar36 + 0x24);
          }
          else {
            if (iVar39 < 0) {
              iVar39 = 0;
            }
            if (iVar16 <= iVar39) {
              iVar39 = iVar16 + -1;
            }
            pfVar43 = (float *)(lVar31 + (longlong)iVar39 * 4);
          }
        }
        pfVar38 = local_610;
        fVar50 = *pfVar43;
        bVar12 = 0.0 <= fVar50;
        bVar13 = fVar50 == 0.0;
        if (0.0 <= fVar50) {
          if (0.0 <= fVar49) {
            *local_600 = *local_600 + 1;
            goto LAB_18003ca84;
          }
          bVar13 = false;
          bVar12 = true;
          if (!NAN(fVar50)) {
            bVar13 = fVar50 == 0.0;
            bVar12 = 0.0 <= fVar50;
          }
        }
        if ((bVar12 && !bVar13) || (0.0 < fVar49)) {
          bVar13 = NAN(fVar50);
          bVar11 = fVar50 == 0.0;
          bVar12 = fVar50 < 0.0;
          if (0.0 <= fVar50) {
LAB_18003c9e8:
            if ((bVar11 || bVar12 != bVar13) || (0.0 < fVar49)) goto LAB_18003ca84;
            uVar24 = FUN_180026708(*(longlong *)(local_610 + 0x36));
            fVar49 = extraout_w11_02;
            if ((int)uVar24 <= *local_600) {
              uVar24 = FUN_180026708(*(longlong *)(pfVar38 + 0x3c));
              local_640 = (uint)uVar24;
              uVar24 = FUN_180037328(uVar24,local_618,(int *)&local_4b0,(int *)&local_640,
                                     (longlong)local_5a0,(longlong)local_5a8,1);
              fVar49 = local_4b0;
              if ((uVar24 & 1) != 0) {
                uVar24 = FUN_180026708(*(longlong *)(pfVar38 + 0x2e));
                pfVar43 = (float *)FUN_180005d08((longlong *)(extraout_x11_03 + 0xa60),
                                                 (int)local_4b0);
                fVar49 = *pfVar43;
                pfVar43 = (float *)FUN_18000f448(lVar45,(int)local_4b0);
                *pfVar43 = fVar49 + (float)(int)uVar24 * extraout_s18;
                fVar49 = local_4b0;
              }
            }
            local_4b0 = fVar49;
            iVar39 = -1;
          }
          else {
            if (fVar49 < 0.0) {
              bVar12 = false;
              bVar11 = false;
              bVar13 = true;
              if (!NAN(fVar50)) {
                bVar12 = fVar50 < 0.0;
                bVar11 = fVar50 == 0.0;
                bVar13 = false;
              }
              goto LAB_18003c9e8;
            }
            uVar24 = FUN_180026708(*(longlong *)(local_610 + 0x36));
            iVar16 = -(int)uVar24;
            iVar39 = *local_600;
            fVar49 = extraout_w11_01;
            if (iVar39 == iVar16 || iVar39 + (int)uVar24 < 0 != SBORROW4(iVar39,iVar16)) {
              uVar24 = FUN_180026708(*(longlong *)(pfVar38 + 0x3c));
              local_640 = (uint)uVar24;
              uVar24 = FUN_180037328(uVar24,local_618,(int *)&local_4b0,(int *)&local_640,
                                     (longlong)local_5a0,(longlong)local_5a8,-1);
              fVar49 = local_4b0;
              if ((uVar24 & 1) != 0) {
                FUN_180005d08((longlong *)(param_3 + 0x2a2),(int)local_4b0);
                uVar24 = FUN_180026708(*(longlong *)(pfVar38 + 0x2e));
                fVar49 = (float)param_3[0x1a6];
                fVar50 = *extraout_x11_02;
                pfVar43 = (float *)FUN_18000f448(lVar30,(int)local_4b0);
                *pfVar43 = fVar50 - (float)(int)uVar24 * fVar49;
                fVar49 = local_4b0;
              }
            }
            local_4b0 = fVar49;
            iVar39 = 1;
          }
          *local_600 = iVar39;
        }
        else {
          *local_600 = *local_600 + -1;
        }
      }
LAB_18003ca84:
      fVar54 = (float)((int)local_4b0 + 1);
      *pfVar18 = local_4b0;
    } while ((int)fVar54 < *param_3);
  }
  return;
}

