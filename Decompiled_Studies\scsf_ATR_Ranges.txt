
/* WARNING: Function: __security_push_cookie replaced with injection: security_push_cookie */
/* WARNING: Function: __security_pop_cookie replaced with injection: security_pop_cookie */

void scsf_ATR_Ranges(undefined1 param_1 [16],undefined8 param_2,int *param_3,undefined8 param_4,
                    undefined8 param_5,undefined8 param_6,undefined8 param_7,undefined8 param_8,
                    undefined8 param_9,undefined8 param_10)

{
  int *piVar1;
  undefined1 auVar2 [16];
  undefined1 auVar3 [16];
  uint *puVar4;
  bool bVar5;
  uint uVar6;
  int iVar7;
  uint uVar8;
  HANDLE pvVar9;
  char *pcVar10;
  undefined8 *puVar11;
  undefined8 uVar12;
  ulonglong uVar13;
  float *pfVar14;
  float *pfVar15;
  float *pfVar16;
  undefined4 *puVar17;
  int extraout_w1;
  int extraout_w1_00;
  uint extraout_w1_01;
  int extraout_w1_02;
  undefined8 extraout_x1;
  undefined8 extraout_x1_00;
  undefined8 extraout_x1_01;
  undefined8 extraout_x1_02;
  undefined8 extraout_x1_03;
  undefined8 extraout_x1_04;
  undefined8 extraout_x1_05;
  undefined8 extraout_x1_06;
  undefined8 extraout_x1_07;
  undefined8 extraout_x1_08;
  undefined8 extraout_x1_09;
  undefined8 extraout_x1_10;
  undefined8 extraout_x1_11;
  undefined8 extraout_x1_12;
  undefined8 extraout_x1_13;
  undefined8 extraout_x1_14;
  undefined8 extraout_x1_15;
  undefined8 extraout_x1_16;
  undefined8 extraout_x1_17;
  undefined8 extraout_x1_18;
  undefined8 extraout_x1_19;
  undefined8 extraout_x1_20;
  undefined8 extraout_x1_21;
  undefined8 extraout_x1_22;
  undefined8 extraout_x1_23;
  undefined8 extraout_x1_24;
  undefined8 extraout_x1_25;
  undefined8 extraout_x1_26;
  undefined8 extraout_x1_27;
  undefined8 extraout_x1_28;
  undefined8 extraout_x1_29;
  longlong lVar18;
  int *piVar19;
  longlong lVar20;
  int *piVar21;
  int iVar22;
  int *piVar23;
  longlong lVar24;
  longlong *plVar25;
  longlong *plVar27;
  int *piVar28;
  longlong *plVar29;
  int *piVar30;
  longlong *plVar31;
  longlong *plVar32;
  longlong *plVar33;
  longlong *plVar34;
  longlong *plVar35;
  int *piVar36;
  longlong *plVar37;
  int *piVar38;
  longlong *plVar39;
  int extraout_w11;
  int extraout_w11_00;
  uint extraout_w11_01;
  int extraout_w11_02;
  float *extraout_x11;
  float *extraout_x11_00;
  int *extraout_x12;
  longlong extraout_x12_00;
  int *extraout_x12_01;
  int *extraout_x12_02;
  int extraout_w13;
  longlong *plVar40;
  longlong *plVar41;
  char *lpMem;
  char *pcVar42;
  longlong lVar43;
  longlong *plVar44;
  longlong *plVar45;
  undefined4 extraout_s0;
  undefined4 extraout_s0_00;
  undefined4 extraout_s0_01;
  undefined4 extraout_s0_02;
  undefined4 extraout_s0_03;
  undefined4 extraout_s0_04;
  undefined4 extraout_s0_05;
  undefined4 extraout_s0_06;
  undefined4 extraout_s0_07;
  undefined4 extraout_s0_08;
  undefined4 extraout_s0_09;
  undefined4 extraout_s0_10;
  undefined4 extraout_s0_11;
  undefined4 extraout_s0_12;
  undefined4 extraout_s0_13;
  undefined4 extraout_s0_14;
  undefined4 extraout_s0_15;
  undefined4 extraout_s0_16;
  undefined4 extraout_s0_17;
  undefined4 extraout_s0_18;
  undefined4 extraout_s0_19;
  undefined4 extraout_s0_20;
  undefined4 extraout_s0_21;
  undefined4 extraout_s0_22;
  undefined4 extraout_s0_23;
  undefined4 extraout_s0_24;
  undefined4 extraout_s0_25;
  undefined4 extraout_s0_26;
  undefined4 extraout_s0_27;
  undefined4 extraout_s0_28;
  undefined4 extraout_s0_29;
  undefined4 extraout_s0_30;
  undefined4 extraout_s0_31;
  float fVar46;
  undefined4 extraout_var;
  undefined4 extraout_var_00;
  undefined4 extraout_var_01;
  undefined4 extraout_var_02;
  undefined4 extraout_var_03;
  undefined4 extraout_var_04;
  undefined4 extraout_var_05;
  undefined4 extraout_var_06;
  undefined4 extraout_var_07;
  undefined4 extraout_var_08;
  undefined4 extraout_var_09;
  undefined4 extraout_var_10;
  undefined4 extraout_var_11;
  undefined4 extraout_var_12;
  undefined4 extraout_var_13;
  undefined4 extraout_var_14;
  undefined4 extraout_var_15;
  undefined4 extraout_var_16;
  undefined4 extraout_var_17;
  undefined4 extraout_var_18;
  undefined4 extraout_var_19;
  undefined4 extraout_var_20;
  undefined4 extraout_var_21;
  undefined4 extraout_var_22;
  undefined4 extraout_var_23;
  undefined4 extraout_var_24;
  undefined4 extraout_var_25;
  undefined4 extraout_var_26;
  undefined4 extraout_var_27;
  undefined4 extraout_var_28;
  undefined4 extraout_var_29;
  undefined4 extraout_var_30;
  undefined4 extraout_var_31;
  undefined4 uVar47;
  undefined8 extraout_var_32;
  undefined8 extraout_var_33;
  undefined8 extraout_var_34;
  undefined8 extraout_var_35;
  undefined8 extraout_var_36;
  undefined8 extraout_var_37;
  undefined8 extraout_var_38;
  undefined8 extraout_var_39;
  undefined8 extraout_var_40;
  undefined8 extraout_var_41;
  undefined8 extraout_var_42;
  undefined8 extraout_var_43;
  undefined8 extraout_var_44;
  undefined8 extraout_var_45;
  undefined8 extraout_var_46;
  undefined8 extraout_var_47;
  undefined8 extraout_var_48;
  undefined8 extraout_var_49;
  undefined8 extraout_var_50;
  undefined8 extraout_var_51;
  undefined8 extraout_var_52;
  undefined8 extraout_var_53;
  undefined8 extraout_var_54;
  undefined8 extraout_var_55;
  undefined8 extraout_var_56;
  undefined8 extraout_var_57;
  undefined8 extraout_var_58;
  undefined8 extraout_var_59;
  undefined8 extraout_var_60;
  undefined8 extraout_var_61;
  undefined8 extraout_var_62;
  undefined8 uVar48;
  undefined8 extraout_var_63;
  undefined8 extraout_var_64;
  undefined4 uVar49;
  float fVar50;
  float fVar51;
  undefined1 uVar52;
  undefined1 uVar53;
  undefined1 uVar54;
  undefined1 uVar55;
  float fVar56;
  float extraout_s18;
  float extraout_s18_00;
  float extraout_s18_01;
  int *local_248;
  int *local_240;
  longlong *local_200;
  char *local_1f8;
  undefined8 local_1f0;
  undefined1 *local_1e8;
  ulonglong local_1e0;
  int *local_1d8;
  longlong *local_1d0;
  int *local_1c8;
  uint *local_1c0;
  float *local_1b8;
  float *local_1b0;
  int *local_1a8;
  float *local_1a0;
  int *local_198;
  longlong *local_190;
  longlong *local_188;
  longlong *local_180;
  longlong *local_178;
  int *local_170;
  longlong *local_168;
  int *local_160;
  int *local_158;
  longlong *local_150;
  int *local_148;
  int *local_140;
  int *local_138;
  int *local_130;
  undefined8 local_128;
  undefined **local_120 [2];
  longlong local_110 [20];
  int *piVar26;
  
                    /* 0x26b80  3  scsf_ATR_Ranges */
  local_128 = 0xfffffffffffffffe;
  local_198 = param_3;
  local_138 = (int *)(**(code **)(param_3 + 0x62e))(param_1._0_4_,0);
  local_1c0 = (uint *)(**(code **)(param_3 + 0x62e))(1);
  local_130 = (int *)(**(code **)(param_3 + 0x62e))(2);
  local_1a8 = (int *)(**(code **)(param_3 + 0x62e))(3);
  local_1b8 = (float *)(**(code **)(param_3 + 0x630))(0);
  local_1b0 = (float *)(**(code **)(param_3 + 0x630))(1);
  local_1a0 = (float *)(**(code **)(param_3 + 0x630))(2);
  lVar18 = *(longlong *)(param_3 + 0x140);
  uVar12 = extraout_x1;
  uVar49 = extraout_s0;
  uVar47 = extraout_var;
  uVar48 = extraout_var_32;
  if (lVar18 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar12 = extraout_x1_00;
      uVar49 = extraout_s0_00;
      uVar47 = extraout_var_00;
      uVar48 = extraout_var_33;
    }
    lVar18 = *(longlong *)(param_3 + 0x140);
    if (lVar18 != 0) goto LAB_180026c60;
    piVar23 = param_3 + 0x14a;
  }
  else {
LAB_180026c60:
    iVar7 = param_3[0x148];
    if (iVar7 == 0) {
      piVar23 = param_3 + 0x14a;
    }
    else {
      iVar22 = 0;
      if (iVar7 < 1) {
        iVar22 = iVar7 + -1;
      }
      piVar23 = (int *)(lVar18 + (longlong)iVar22 * 0x170);
    }
  }
  lVar24 = *(longlong *)(piVar23 + 0x20);
  if (lVar24 == 0) {
    if (*(code **)(piVar23 + 0x24) != (code *)0x0) {
      (**(code **)(piVar23 + 0x24))(piVar23[0x26]);
      lVar18 = *(longlong *)(param_3 + 0x140);
      uVar12 = extraout_x1_01;
      uVar49 = extraout_s0_01;
      uVar47 = extraout_var_01;
      uVar48 = extraout_var_34;
    }
    lVar24 = *(longlong *)(piVar23 + 0x20);
    if (lVar24 != 0) goto LAB_180026cbc;
    plVar25 = (longlong *)(piVar23 + 0x2a);
  }
  else {
LAB_180026cbc:
    iVar7 = piVar23[0x28];
    if (iVar7 == 0) {
      plVar25 = (longlong *)(piVar23 + 0x2a);
    }
    else {
      iVar22 = 0;
      if (iVar7 < 1) {
        iVar22 = iVar7 + -1;
      }
      plVar25 = (longlong *)(lVar24 + (longlong)iVar22 * 0x28);
    }
  }
  if (lVar18 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar12 = extraout_x1_02;
      uVar49 = extraout_s0_02;
      uVar47 = extraout_var_02;
      uVar48 = extraout_var_35;
    }
    lVar18 = *(longlong *)(param_3 + 0x140);
    if (lVar18 != 0) goto LAB_180026d08;
    piVar26 = param_3 + 0x14a;
  }
  else {
LAB_180026d08:
    iVar7 = param_3[0x148];
    if (iVar7 == 0) {
      piVar26 = param_3 + 0x14a;
    }
    else {
      iVar22 = 1;
      if (iVar7 < 2) {
        iVar22 = iVar7 + -1;
      }
      piVar26 = (int *)(lVar18 + (longlong)iVar22 * 0x170);
    }
  }
  lVar24 = *(longlong *)(piVar26 + 0x20);
  if (lVar24 == 0) {
    if (*(code **)(piVar26 + 0x24) != (code *)0x0) {
      (**(code **)(piVar26 + 0x24))(piVar26[0x26]);
      lVar18 = *(longlong *)(param_3 + 0x140);
      uVar12 = extraout_x1_03;
      uVar49 = extraout_s0_03;
      uVar47 = extraout_var_03;
      uVar48 = extraout_var_36;
    }
    lVar24 = *(longlong *)(piVar26 + 0x20);
    if (lVar24 != 0) goto LAB_180026d64;
    plVar27 = (longlong *)(piVar26 + 0x2a);
  }
  else {
LAB_180026d64:
    iVar7 = piVar26[0x28];
    if (iVar7 == 0) {
      plVar27 = (longlong *)(piVar26 + 0x2a);
    }
    else {
      iVar22 = 0;
      if (iVar7 < 1) {
        iVar22 = iVar7 + -1;
      }
      plVar27 = (longlong *)(lVar24 + (longlong)iVar22 * 0x28);
    }
  }
  if (lVar18 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar12 = extraout_x1_04;
      uVar49 = extraout_s0_04;
      uVar47 = extraout_var_04;
      uVar48 = extraout_var_37;
    }
    lVar18 = *(longlong *)(param_3 + 0x140);
    if (lVar18 != 0) goto LAB_180026db0;
    piVar28 = param_3 + 0x14a;
  }
  else {
LAB_180026db0:
    iVar7 = param_3[0x148];
    if (iVar7 == 0) {
      piVar28 = param_3 + 0x14a;
    }
    else {
      iVar22 = 2;
      if (iVar7 < 3) {
        iVar22 = iVar7 + -1;
      }
      piVar28 = (int *)(lVar18 + (longlong)iVar22 * 0x170);
    }
  }
  lVar24 = *(longlong *)(piVar28 + 0x20);
  if (lVar24 == 0) {
    if (*(code **)(piVar28 + 0x24) != (code *)0x0) {
      (**(code **)(piVar28 + 0x24))(piVar28[0x26]);
      lVar18 = *(longlong *)(param_3 + 0x140);
      uVar12 = extraout_x1_05;
      uVar49 = extraout_s0_05;
      uVar47 = extraout_var_05;
      uVar48 = extraout_var_38;
    }
    lVar24 = *(longlong *)(piVar28 + 0x20);
    if (lVar24 != 0) goto LAB_180026e0c;
    plVar29 = (longlong *)(piVar28 + 0x2a);
  }
  else {
LAB_180026e0c:
    iVar7 = piVar28[0x28];
    if (iVar7 == 0) {
      plVar29 = (longlong *)(piVar28 + 0x2a);
    }
    else {
      iVar22 = 0;
      if (iVar7 < 1) {
        iVar22 = iVar7 + -1;
      }
      plVar29 = (longlong *)(lVar24 + (longlong)iVar22 * 0x28);
    }
  }
  if (lVar18 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar12 = extraout_x1_06;
      uVar49 = extraout_s0_06;
      uVar47 = extraout_var_06;
      uVar48 = extraout_var_39;
    }
    lVar18 = *(longlong *)(param_3 + 0x140);
    if (lVar18 != 0) goto LAB_180026e58;
    piVar30 = param_3 + 0x14a;
  }
  else {
LAB_180026e58:
    iVar7 = param_3[0x148];
    if (iVar7 == 0) {
      piVar30 = param_3 + 0x14a;
    }
    else {
      iVar22 = 3;
      if (iVar7 < 4) {
        iVar22 = iVar7 + -1;
      }
      piVar30 = (int *)(lVar18 + (longlong)iVar22 * 0x170);
    }
  }
  lVar24 = *(longlong *)(piVar30 + 0x20);
  if (lVar24 == 0) {
    if (*(code **)(piVar30 + 0x24) != (code *)0x0) {
      (**(code **)(piVar30 + 0x24))(piVar30[0x26]);
      lVar18 = *(longlong *)(param_3 + 0x140);
      uVar12 = extraout_x1_07;
      uVar49 = extraout_s0_07;
      uVar47 = extraout_var_07;
      uVar48 = extraout_var_40;
    }
    lVar24 = *(longlong *)(piVar30 + 0x20);
    if (lVar24 != 0) goto LAB_180026eb4;
    plVar31 = (longlong *)(piVar30 + 0x2a);
  }
  else {
LAB_180026eb4:
    iVar7 = piVar30[0x28];
    if (iVar7 == 0) {
      plVar31 = (longlong *)(piVar30 + 0x2a);
    }
    else {
      iVar22 = 0;
      if (iVar7 < 1) {
        iVar22 = iVar7 + -1;
      }
      plVar31 = (longlong *)(lVar24 + (longlong)iVar22 * 0x28);
    }
  }
  if (lVar18 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar12 = extraout_x1_08;
      uVar49 = extraout_s0_08;
      uVar47 = extraout_var_08;
      uVar48 = extraout_var_41;
    }
    lVar18 = *(longlong *)(param_3 + 0x140);
    if (lVar18 != 0) goto LAB_180026f00;
    plVar32 = (longlong *)(param_3 + 0x14a);
  }
  else {
LAB_180026f00:
    iVar7 = param_3[0x148];
    if (iVar7 == 0) {
      plVar32 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar22 = 4;
      if (iVar7 < 5) {
        iVar22 = iVar7 + -1;
      }
      plVar32 = (longlong *)(lVar18 + (longlong)iVar22 * 0x170);
    }
  }
  lVar24 = plVar32[0x10];
  if (lVar24 == 0) {
    if ((code *)plVar32[0x12] != (code *)0x0) {
      (*(code *)plVar32[0x12])((int)plVar32[0x13]);
      lVar18 = *(longlong *)(param_3 + 0x140);
      uVar12 = extraout_x1_09;
      uVar49 = extraout_s0_09;
      uVar47 = extraout_var_09;
      uVar48 = extraout_var_42;
    }
    lVar24 = plVar32[0x10];
    if (lVar24 != 0) goto LAB_180026f5c;
    plVar33 = plVar32 + 0x15;
  }
  else {
LAB_180026f5c:
    iVar7 = (int)plVar32[0x14];
    if (iVar7 == 0) {
      plVar33 = plVar32 + 0x15;
    }
    else {
      iVar22 = 0;
      if (iVar7 < 1) {
        iVar22 = iVar7 + -1;
      }
      plVar33 = (longlong *)(lVar24 + (longlong)iVar22 * 0x28);
    }
  }
  if (lVar18 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar12 = extraout_x1_10;
      uVar49 = extraout_s0_10;
      uVar47 = extraout_var_10;
      uVar48 = extraout_var_43;
    }
    lVar18 = *(longlong *)(param_3 + 0x140);
    if (lVar18 != 0) goto LAB_180026fa8;
    plVar34 = (longlong *)(param_3 + 0x14a);
  }
  else {
LAB_180026fa8:
    iVar7 = param_3[0x148];
    if (iVar7 == 0) {
      plVar34 = (longlong *)(param_3 + 0x14a);
    }
    else {
      iVar22 = 5;
      if (iVar7 < 6) {
        iVar22 = iVar7 + -1;
      }
      plVar34 = (longlong *)(lVar18 + (longlong)iVar22 * 0x170);
    }
  }
  lVar24 = plVar34[0x10];
  if (lVar24 == 0) {
    if ((code *)plVar34[0x12] != (code *)0x0) {
      (*(code *)plVar34[0x12])((int)plVar34[0x13]);
      lVar18 = *(longlong *)(param_3 + 0x140);
      uVar12 = extraout_x1_11;
      uVar49 = extraout_s0_11;
      uVar47 = extraout_var_11;
      uVar48 = extraout_var_44;
    }
    lVar24 = plVar34[0x10];
    if (lVar24 != 0) goto LAB_180027004;
    plVar35 = plVar34 + 0x15;
  }
  else {
LAB_180027004:
    iVar7 = (int)plVar34[0x14];
    if (iVar7 == 0) {
      plVar35 = plVar34 + 0x15;
    }
    else {
      iVar22 = 0;
      if (iVar7 < 1) {
        iVar22 = iVar7 + -1;
      }
      plVar35 = (longlong *)(lVar24 + (longlong)iVar22 * 0x28);
    }
  }
  if (lVar18 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar12 = extraout_x1_12;
      uVar49 = extraout_s0_12;
      uVar47 = extraout_var_12;
      uVar48 = extraout_var_45;
    }
    lVar18 = *(longlong *)(param_3 + 0x140);
    if (lVar18 != 0) goto LAB_180027050;
    piVar36 = param_3 + 0x14a;
  }
  else {
LAB_180027050:
    iVar7 = param_3[0x148];
    if (iVar7 == 0) {
      piVar36 = param_3 + 0x14a;
    }
    else {
      iVar22 = 6;
      if (iVar7 < 7) {
        iVar22 = iVar7 + -1;
      }
      piVar36 = (int *)(lVar18 + (longlong)iVar22 * 0x170);
    }
  }
  lVar24 = *(longlong *)(piVar36 + 0x20);
  if (lVar24 == 0) {
    if (*(code **)(piVar36 + 0x24) != (code *)0x0) {
      (**(code **)(piVar36 + 0x24))(piVar36[0x26]);
      lVar18 = *(longlong *)(param_3 + 0x140);
      uVar12 = extraout_x1_13;
      uVar49 = extraout_s0_13;
      uVar47 = extraout_var_13;
      uVar48 = extraout_var_46;
    }
    lVar24 = *(longlong *)(piVar36 + 0x20);
    if (lVar24 != 0) goto LAB_1800270ac;
    plVar37 = (longlong *)(piVar36 + 0x2a);
  }
  else {
LAB_1800270ac:
    iVar7 = piVar36[0x28];
    if (iVar7 == 0) {
      plVar37 = (longlong *)(piVar36 + 0x2a);
    }
    else {
      iVar22 = 0;
      if (iVar7 < 1) {
        iVar22 = iVar7 + -1;
      }
      plVar37 = (longlong *)(lVar24 + (longlong)iVar22 * 0x28);
    }
  }
  if (lVar18 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar12 = extraout_x1_14;
      uVar49 = extraout_s0_14;
      uVar47 = extraout_var_14;
      uVar48 = extraout_var_47;
    }
    lVar18 = *(longlong *)(param_3 + 0x140);
    if (lVar18 != 0) goto LAB_1800270f8;
    piVar38 = param_3 + 0x14a;
  }
  else {
LAB_1800270f8:
    iVar7 = param_3[0x148];
    if (iVar7 == 0) {
      piVar38 = param_3 + 0x14a;
    }
    else {
      iVar22 = 7;
      if (iVar7 < 8) {
        iVar22 = iVar7 + -1;
      }
      piVar38 = (int *)(lVar18 + (longlong)iVar22 * 0x170);
    }
  }
  lVar24 = *(longlong *)(piVar38 + 0x20);
  if (lVar24 == 0) {
    if (*(code **)(piVar38 + 0x24) != (code *)0x0) {
      (**(code **)(piVar38 + 0x24))(piVar38[0x26]);
      lVar18 = *(longlong *)(param_3 + 0x140);
      uVar12 = extraout_x1_15;
      uVar49 = extraout_s0_15;
      uVar47 = extraout_var_15;
      uVar48 = extraout_var_48;
    }
    lVar24 = *(longlong *)(piVar38 + 0x20);
    if (lVar24 != 0) goto LAB_180027154;
    plVar39 = (longlong *)(piVar38 + 0x2a);
  }
  else {
LAB_180027154:
    iVar7 = piVar38[0x28];
    if (iVar7 == 0) {
      plVar39 = (longlong *)(piVar38 + 0x2a);
    }
    else {
      iVar22 = 0;
      if (iVar7 < 1) {
        iVar22 = iVar7 + -1;
      }
      plVar39 = (longlong *)(lVar24 + (longlong)iVar22 * 0x28);
    }
  }
  if (lVar18 == 0) {
    if (*(code **)(param_3 + 0x144) != (code *)0x0) {
      (**(code **)(param_3 + 0x144))(param_3[0x146]);
      uVar12 = extraout_x1_16;
      uVar49 = extraout_s0_16;
      uVar47 = extraout_var_16;
      uVar48 = extraout_var_49;
    }
    lVar18 = *(longlong *)(param_3 + 0x140);
    if (lVar18 != 0) goto LAB_1800271a0;
    piVar19 = param_3 + 0x14a;
  }
  else {
LAB_1800271a0:
    iVar7 = param_3[0x148];
    if (iVar7 == 0) {
      piVar19 = param_3 + 0x14a;
    }
    else {
      iVar22 = 8;
      if (iVar7 < 9) {
        iVar22 = iVar7 + -1;
      }
      piVar19 = (int *)(lVar18 + (longlong)iVar22 * 0x170);
    }
  }
  lVar18 = *(longlong *)(piVar19 + 0x20);
  if (lVar18 == 0) {
    if (*(code **)(piVar19 + 0x24) != (code *)0x0) {
      (**(code **)(piVar19 + 0x24))(piVar19[0x26]);
      uVar12 = extraout_x1_17;
      uVar49 = extraout_s0_17;
      uVar47 = extraout_var_17;
      uVar48 = extraout_var_50;
    }
    lVar18 = *(longlong *)(piVar19 + 0x20);
    if (lVar18 != 0) goto LAB_1800271f8;
    local_1d0 = (longlong *)(piVar19 + 0x2a);
  }
  else {
LAB_1800271f8:
    iVar7 = piVar19[0x28];
    if (iVar7 == 0) {
      local_1d0 = (longlong *)(piVar19 + 0x2a);
    }
    else {
      iVar22 = 0;
      if (iVar7 < 1) {
        iVar22 = iVar7 + -1;
      }
      local_1d0 = (longlong *)(lVar18 + (longlong)iVar22 * 0x28);
    }
  }
  lVar18 = *(longlong *)(piVar36 + 0x20);
  if (lVar18 == 0) {
    if (*(code **)(piVar36 + 0x24) != (code *)0x0) {
      (**(code **)(piVar36 + 0x24))(piVar36[0x26]);
      uVar12 = extraout_x1_18;
      uVar49 = extraout_s0_18;
      uVar47 = extraout_var_18;
      uVar48 = extraout_var_51;
    }
    lVar18 = *(longlong *)(piVar36 + 0x20);
    if (lVar18 != 0) goto LAB_180027250;
    local_188 = (longlong *)(piVar36 + 0x2a);
  }
  else {
LAB_180027250:
    iVar7 = piVar36[0x28];
    if (iVar7 == 0) {
      local_188 = (longlong *)(piVar36 + 0x2a);
    }
    else {
      iVar22 = 1;
      if (iVar7 < 2) {
        iVar22 = iVar7 + -1;
      }
      local_188 = (longlong *)(lVar18 + (longlong)iVar22 * 0x28);
    }
  }
  lVar18 = *(longlong *)(param_3 + 0x84);
  if (lVar18 == 0) {
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_19;
      uVar49 = extraout_s0_19;
      uVar47 = extraout_var_19;
      uVar48 = extraout_var_52;
    }
    lVar18 = *(longlong *)(param_3 + 0x84);
    if (lVar18 != 0) goto LAB_1800272b8;
    local_160 = param_3 + 0x8e;
LAB_1800272ec:
    local_1d8 = local_160;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_20;
      uVar49 = extraout_s0_20;
      uVar47 = extraout_var_20;
      uVar48 = extraout_var_53;
    }
    lVar18 = *(longlong *)(param_3 + 0x84);
    if (lVar18 != 0) goto LAB_18002731c;
    local_158 = param_3 + 0x8e;
LAB_180027354:
    local_1c8 = local_158;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_21;
      uVar49 = extraout_s0_21;
      uVar47 = extraout_var_21;
      uVar48 = extraout_var_54;
    }
    lVar18 = *(longlong *)(param_3 + 0x84);
    if (lVar18 != 0) goto LAB_180027384;
    local_178 = (longlong *)(param_3 + 0x8e);
LAB_1800273bc:
    local_190 = local_178;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_22;
      uVar49 = extraout_s0_22;
      uVar47 = extraout_var_22;
      uVar48 = extraout_var_55;
    }
    lVar18 = *(longlong *)(param_3 + 0x84);
    if (lVar18 != 0) goto LAB_1800273ec;
    local_150 = (longlong *)(param_3 + 0x8e);
LAB_180027424:
    local_200 = local_150;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_23;
      uVar49 = extraout_s0_23;
      uVar47 = extraout_var_23;
      uVar48 = extraout_var_56;
    }
    lVar18 = *(longlong *)(param_3 + 0x84);
    if (lVar18 != 0) goto LAB_18002744c;
    plVar44 = (longlong *)(param_3 + 0x8e);
LAB_18002747c:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_24;
      uVar49 = extraout_s0_24;
      uVar47 = extraout_var_24;
      uVar48 = extraout_var_57;
    }
    lVar18 = *(longlong *)(param_3 + 0x84);
    if (lVar18 != 0) goto LAB_1800274a4;
    plVar40 = (longlong *)(param_3 + 0x8e);
LAB_1800274d4:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_25;
      uVar49 = extraout_s0_25;
      uVar47 = extraout_var_25;
      uVar48 = extraout_var_58;
    }
    lVar18 = *(longlong *)(param_3 + 0x84);
    if (lVar18 != 0) goto LAB_180027504;
    local_148 = param_3 + 0x8e;
LAB_18002753c:
    local_240 = local_148;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_26;
      uVar49 = extraout_s0_26;
      uVar47 = extraout_var_26;
      uVar48 = extraout_var_59;
    }
    lVar18 = *(longlong *)(param_3 + 0x84);
    if (lVar18 != 0) goto LAB_180027564;
    plVar45 = (longlong *)(param_3 + 0x8e);
LAB_180027594:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_27;
      uVar49 = extraout_s0_27;
      uVar47 = extraout_var_27;
      uVar48 = extraout_var_60;
    }
    lVar18 = *(longlong *)(param_3 + 0x84);
    if (lVar18 != 0) goto LAB_1800275c4;
    local_140 = param_3 + 0x8e;
LAB_1800275fc:
    local_248 = local_140;
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_28;
      uVar49 = extraout_s0_28;
      uVar47 = extraout_var_28;
      uVar48 = extraout_var_61;
    }
    lVar18 = *(longlong *)(param_3 + 0x84);
    if (lVar18 != 0) goto LAB_180027624;
    plVar41 = (longlong *)(param_3 + 0x8e);
LAB_180027654:
    if (*(code **)(param_3 + 0x88) != (code *)0x0) {
      (**(code **)(param_3 + 0x88))(param_3[0x8a]);
      uVar12 = extraout_x1_29;
      uVar49 = extraout_s0_29;
      uVar47 = extraout_var_29;
      uVar48 = extraout_var_62;
    }
    lVar18 = *(longlong *)(param_3 + 0x84);
    if (lVar18 != 0) goto LAB_180027674;
    local_180 = (longlong *)(param_3 + 0x8e);
  }
  else {
LAB_1800272b8:
    iVar7 = param_3[0x8c];
    if (iVar7 == 0) {
      local_1d8 = param_3 + 0x8e;
    }
    else {
      iVar22 = 0;
      if (iVar7 < 1) {
        iVar22 = iVar7 + -1;
      }
      local_1d8 = (int *)(lVar18 + (longlong)iVar22 * 0x98);
    }
    local_160 = local_1d8;
    if (lVar18 == 0) goto LAB_1800272ec;
LAB_18002731c:
    iVar7 = param_3[0x8c];
    if (iVar7 == 0) {
      local_1c8 = param_3 + 0x8e;
    }
    else {
      iVar22 = 1;
      if (iVar7 < 2) {
        iVar22 = iVar7 + -1;
      }
      local_1c8 = (int *)(lVar18 + (longlong)iVar22 * 0x98);
    }
    local_158 = local_1c8;
    if (lVar18 == 0) goto LAB_180027354;
LAB_180027384:
    iVar7 = param_3[0x8c];
    if (iVar7 == 0) {
      local_190 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar22 = 2;
      if (iVar7 < 3) {
        iVar22 = iVar7 + -1;
      }
      local_190 = (longlong *)(lVar18 + (longlong)iVar22 * 0x98);
    }
    local_178 = local_190;
    if (lVar18 == 0) goto LAB_1800273bc;
LAB_1800273ec:
    iVar7 = param_3[0x8c];
    if (iVar7 == 0) {
      local_200 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar22 = 3;
      if (iVar7 < 4) {
        iVar22 = iVar7 + -1;
      }
      local_200 = (longlong *)(lVar18 + (longlong)iVar22 * 0x98);
    }
    local_150 = local_200;
    if (lVar18 == 0) goto LAB_180027424;
LAB_18002744c:
    iVar7 = param_3[0x8c];
    if (iVar7 == 0) {
      plVar44 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar22 = 4;
      if (iVar7 < 5) {
        iVar22 = iVar7 + -1;
      }
      plVar44 = (longlong *)(lVar18 + (longlong)iVar22 * 0x98);
    }
    if (lVar18 == 0) goto LAB_18002747c;
LAB_1800274a4:
    iVar7 = param_3[0x8c];
    if (iVar7 == 0) {
      plVar40 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar22 = 5;
      if (iVar7 < 6) {
        iVar22 = iVar7 + -1;
      }
      plVar40 = (longlong *)(lVar18 + (longlong)iVar22 * 0x98);
    }
    if (lVar18 == 0) goto LAB_1800274d4;
LAB_180027504:
    iVar7 = param_3[0x8c];
    if (iVar7 == 0) {
      local_240 = param_3 + 0x8e;
    }
    else {
      iVar22 = 6;
      if (iVar7 < 7) {
        iVar22 = iVar7 + -1;
      }
      local_240 = (int *)(lVar18 + (longlong)iVar22 * 0x98);
    }
    local_148 = local_240;
    if (lVar18 == 0) goto LAB_18002753c;
LAB_180027564:
    iVar7 = param_3[0x8c];
    if (iVar7 == 0) {
      plVar45 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar22 = 7;
      if (iVar7 < 8) {
        iVar22 = iVar7 + -1;
      }
      plVar45 = (longlong *)(lVar18 + (longlong)iVar22 * 0x98);
    }
    if (lVar18 == 0) goto LAB_180027594;
LAB_1800275c4:
    iVar7 = param_3[0x8c];
    if (iVar7 == 0) {
      local_248 = param_3 + 0x8e;
    }
    else {
      iVar22 = 8;
      if (iVar7 < 9) {
        iVar22 = iVar7 + -1;
      }
      local_248 = (int *)(lVar18 + (longlong)iVar22 * 0x98);
    }
    local_140 = local_248;
    if (lVar18 == 0) goto LAB_1800275fc;
LAB_180027624:
    iVar7 = param_3[0x8c];
    if (iVar7 == 0) {
      plVar41 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar22 = 9;
      if (iVar7 < 10) {
        iVar22 = iVar7 + -1;
      }
      plVar41 = (longlong *)(lVar18 + (longlong)iVar22 * 0x98);
    }
    if (lVar18 == 0) goto LAB_180027654;
LAB_180027674:
    iVar7 = param_3[0x8c];
    if (iVar7 == 0) {
      local_180 = (longlong *)(param_3 + 0x8e);
    }
    else {
      iVar22 = 10;
      if (iVar7 < 0xb) {
        iVar22 = iVar7 + -1;
      }
      local_180 = (longlong *)(lVar18 + (longlong)iVar22 * 0x98);
    }
  }
  local_168 = local_200;
  local_170 = local_240;
  local_120[0] = PitSessions::vftable;
  if (param_3[0x2b] != 0) {
    FUN_1800079f8((longlong *)(param_3 + 0x46),0x1800d6ba0,10);
    lpMem = "";
    param_3[0x49] = 1;
    if ((*(char **)(param_3 + 0xce) != (char *)0x0) && (**(char **)(param_3 + 0xce) != '\0')) {
      FUN_1800079f8((longlong *)(param_3 + 0xce),0x1800d4ecd,0);
      param_3[0xd1] = 1;
    }
    local_1f8 = "";
    local_1f0 = 0;
    local_1e8 = &DAT_1800d4ecd;
    pvVar9 = GetProcessHeap();
    pcVar10 = (char *)HeapAlloc(pvVar9,0,0x15);
    if (pcVar10 == (char *)0x0) {
      local_1f8 = "";
      pcVar42 = lpMem;
    }
    else {
      param_6 = 0x14;
      pcVar10[8] = '\0';
      pcVar10[9] = '\0';
      pcVar10[10] = '\0';
      pcVar10[0xb] = '\0';
      pcVar10[0xc] = '\0';
      pcVar10[0xd] = '\0';
      pcVar10[0xe] = '\0';
      pcVar10[0xf] = '\0';
      pcVar10[0] = '\0';
      pcVar10[1] = '\0';
      pcVar10[2] = '\0';
      pcVar10[3] = '\0';
      pcVar10[4] = '\0';
      pcVar10[5] = '\0';
      pcVar10[6] = '\0';
      pcVar10[7] = '\0';
      pcVar10[0x10] = '\0';
      pcVar10[0x11] = '\0';
      pcVar10[0x12] = '\0';
      pcVar10[0x13] = '\0';
      pcVar10[0x14] = '\0';
      local_1f8 = pcVar10;
      FUN_180099d78(pcVar10,0x15,0x1800d6c40,0x14);
      local_1f0 = 0x100000001;
      pcVar42 = pcVar10;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_1f8);
    if ((pcVar10 != (char *)0x0) && (pcVar42 != (char *)0x0)) {
      pvVar9 = GetProcessHeap();
      HeapFree(pvVar9,0,pcVar42);
    }
    local_1f8 = "";
    local_1f0 = 0;
    local_1e8 = &DAT_1800d4ecd;
    pvVar9 = GetProcessHeap();
    pcVar10 = (char *)HeapAlloc(pvVar9,0,0x8d);
    if (pcVar10 == (char *)0x0) {
      local_1f8 = "";
      pcVar42 = lpMem;
    }
    else {
      param_6 = 0x8c;
      pcVar10[8] = '\0';
      pcVar10[9] = '\0';
      pcVar10[10] = '\0';
      pcVar10[0xb] = '\0';
      pcVar10[0xc] = '\0';
      pcVar10[0xd] = '\0';
      pcVar10[0xe] = '\0';
      pcVar10[0xf] = '\0';
      pcVar10[0] = '\0';
      pcVar10[1] = '\0';
      pcVar10[2] = '\0';
      pcVar10[3] = '\0';
      pcVar10[4] = '\0';
      pcVar10[5] = '\0';
      pcVar10[6] = '\0';
      pcVar10[7] = '\0';
      pcVar10[0x18] = '\0';
      pcVar10[0x19] = '\0';
      pcVar10[0x1a] = '\0';
      pcVar10[0x1b] = '\0';
      pcVar10[0x1c] = '\0';
      pcVar10[0x1d] = '\0';
      pcVar10[0x1e] = '\0';
      pcVar10[0x1f] = '\0';
      pcVar10[0x10] = '\0';
      pcVar10[0x11] = '\0';
      pcVar10[0x12] = '\0';
      pcVar10[0x13] = '\0';
      pcVar10[0x14] = '\0';
      pcVar10[0x15] = '\0';
      pcVar10[0x16] = '\0';
      pcVar10[0x17] = '\0';
      pcVar10[0x28] = '\0';
      pcVar10[0x29] = '\0';
      pcVar10[0x2a] = '\0';
      pcVar10[0x2b] = '\0';
      pcVar10[0x2c] = '\0';
      pcVar10[0x2d] = '\0';
      pcVar10[0x2e] = '\0';
      pcVar10[0x2f] = '\0';
      pcVar10[0x20] = '\0';
      pcVar10[0x21] = '\0';
      pcVar10[0x22] = '\0';
      pcVar10[0x23] = '\0';
      pcVar10[0x24] = '\0';
      pcVar10[0x25] = '\0';
      pcVar10[0x26] = '\0';
      pcVar10[0x27] = '\0';
      pcVar10[0x38] = '\0';
      pcVar10[0x39] = '\0';
      pcVar10[0x3a] = '\0';
      pcVar10[0x3b] = '\0';
      pcVar10[0x3c] = '\0';
      pcVar10[0x3d] = '\0';
      pcVar10[0x3e] = '\0';
      pcVar10[0x3f] = '\0';
      pcVar10[0x30] = '\0';
      pcVar10[0x31] = '\0';
      pcVar10[0x32] = '\0';
      pcVar10[0x33] = '\0';
      pcVar10[0x34] = '\0';
      pcVar10[0x35] = '\0';
      pcVar10[0x36] = '\0';
      pcVar10[0x37] = '\0';
      pcVar10[0x48] = '\0';
      pcVar10[0x49] = '\0';
      pcVar10[0x4a] = '\0';
      pcVar10[0x4b] = '\0';
      pcVar10[0x4c] = '\0';
      pcVar10[0x4d] = '\0';
      pcVar10[0x4e] = '\0';
      pcVar10[0x4f] = '\0';
      pcVar10[0x40] = '\0';
      pcVar10[0x41] = '\0';
      pcVar10[0x42] = '\0';
      pcVar10[0x43] = '\0';
      pcVar10[0x44] = '\0';
      pcVar10[0x45] = '\0';
      pcVar10[0x46] = '\0';
      pcVar10[0x47] = '\0';
      pcVar10[0x58] = '\0';
      pcVar10[0x59] = '\0';
      pcVar10[0x5a] = '\0';
      pcVar10[0x5b] = '\0';
      pcVar10[0x5c] = '\0';
      pcVar10[0x5d] = '\0';
      pcVar10[0x5e] = '\0';
      pcVar10[0x5f] = '\0';
      pcVar10[0x50] = '\0';
      pcVar10[0x51] = '\0';
      pcVar10[0x52] = '\0';
      pcVar10[0x53] = '\0';
      pcVar10[0x54] = '\0';
      pcVar10[0x55] = '\0';
      pcVar10[0x56] = '\0';
      pcVar10[0x57] = '\0';
      pcVar10[0x68] = '\0';
      pcVar10[0x69] = '\0';
      pcVar10[0x6a] = '\0';
      pcVar10[0x6b] = '\0';
      pcVar10[0x6c] = '\0';
      pcVar10[0x6d] = '\0';
      pcVar10[0x6e] = '\0';
      pcVar10[0x6f] = '\0';
      pcVar10[0x60] = '\0';
      pcVar10[0x61] = '\0';
      pcVar10[0x62] = '\0';
      pcVar10[99] = '\0';
      pcVar10[100] = '\0';
      pcVar10[0x65] = '\0';
      pcVar10[0x66] = '\0';
      pcVar10[0x67] = '\0';
      pcVar10[0x78] = '\0';
      pcVar10[0x79] = '\0';
      pcVar10[0x7a] = '\0';
      pcVar10[0x7b] = '\0';
      pcVar10[0x7c] = '\0';
      pcVar10[0x7d] = '\0';
      pcVar10[0x7e] = '\0';
      pcVar10[0x7f] = '\0';
      pcVar10[0x70] = '\0';
      pcVar10[0x71] = '\0';
      pcVar10[0x72] = '\0';
      pcVar10[0x73] = '\0';
      pcVar10[0x74] = '\0';
      pcVar10[0x75] = '\0';
      pcVar10[0x76] = '\0';
      pcVar10[0x77] = '\0';
      pcVar10[0x80] = '\0';
      pcVar10[0x81] = '\0';
      pcVar10[0x82] = '\0';
      pcVar10[0x83] = '\0';
      pcVar10[0x84] = '\0';
      pcVar10[0x85] = '\0';
      pcVar10[0x86] = '\0';
      pcVar10[0x87] = '\0';
      pcVar10[0x88] = '\0';
      pcVar10[0x89] = '\0';
      pcVar10[0x8a] = '\0';
      pcVar10[0x8b] = '\0';
      pcVar10[0x8c] = '\0';
      local_1f8 = pcVar10;
      FUN_180099d78(pcVar10,0x8d,0x1800d6bb0,0x8c);
      local_1f0 = 0x100000001;
      pcVar42 = pcVar10;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_1f8);
    uVar49 = extraout_s0_30;
    uVar47 = extraout_var_30;
    uVar12 = extraout_var_63;
    if ((pcVar10 != (char *)0x0) && (pcVar42 != (char *)0x0)) {
      pvVar9 = GetProcessHeap();
      HeapFree(pvVar9,0,pcVar42);
      local_1f8 = (char *)0x0;
      local_1f0 = 0;
      uVar49 = extraout_s0_31;
      uVar47 = extraout_var_31;
      uVar12 = extraout_var_64;
    }
    pcVar10 = *(char **)(param_3 + 0x46);
    if (*(char **)(param_3 + 0x46) == (char *)0x0) {
      pcVar10 = lpMem;
    }
    auVar3._4_4_ = uVar47;
    auVar3._0_4_ = uVar49;
    auVar3._8_8_ = uVar12;
    FUN_180026368(auVar3,param_2,(undefined8 *)(param_3 + 0xce),0x1800d6c60,pcVar10,param_6,param_7,
                  param_8,param_9,param_10);
    local_1f8 = "";
    local_1f0 = 0;
    local_1e8 = &DAT_1800d4ecd;
    pvVar9 = GetProcessHeap();
    pcVar10 = (char *)HeapAlloc(pvVar9,0,7);
    if (pcVar10 == (char *)0x0) {
      local_1f8 = "";
    }
    else {
      pcVar10[0] = '\0';
      pcVar10[1] = '\0';
      pcVar10[2] = '\0';
      pcVar10[3] = '\0';
      pcVar10[4] = '\0';
      pcVar10[5] = '\0';
      pcVar10[6] = '\0';
      local_1f8 = pcVar10;
      FUN_180099d78(pcVar10,7,0x1800d6c58,6);
      local_1f0 = 0x100000001;
      lpMem = pcVar10;
    }
    FUN_1800263b0((undefined8 *)(param_3 + 0xce),(longlong *)&local_1f8);
    if ((pcVar10 != (char *)0x0) && (lpMem != (char *)0x0)) {
      pvVar9 = GetProcessHeap();
      HeapFree(pvVar9,0,lpMem);
      local_1f8 = (char *)0x0;
      local_1f0 = 0;
    }
    param_3[0xe3] = 0;
    param_3[0xdf] = 1;
    param_3[0xcc] = 1;
    param_3[0x32e] = 1;
    param_3[4] = 0;
    param_3[1] = 0;
    *(undefined2 *)(piVar23 + 9) = 0x1c;
    piVar23[6] = 0xd2d200;
    *(undefined2 *)(piVar23 + 10) = 1;
    piVar23[0x36] = 0;
    *(undefined2 *)(piVar26 + 9) = 0x1c;
    piVar26[6] = 0xd2d200;
    *(undefined2 *)(piVar26 + 10) = 1;
    piVar26[0x36] = 0;
    *(undefined2 *)(piVar28 + 9) = 0x1b;
    piVar28[6] = 0xd2;
    *(undefined2 *)(piVar28 + 10) = 1;
    piVar28[0x36] = 0;
    *(undefined2 *)(piVar30 + 9) = 0x1b;
    piVar30[6] = 0xd2;
    *(undefined2 *)(piVar30 + 10) = 1;
    piVar30[0x36] = 0;
    FUN_1800079f8(plVar32,0x1800d6c88,0xb);
    *(undefined4 *)((longlong)plVar32 + 0xc) = 1;
    *(undefined4 *)(plVar32 + 4) = 1;
    *(undefined2 *)((longlong)plVar32 + 0x24) = 6;
    plVar32[3] = 0x221f000f0a60d;
    *(undefined2 *)(plVar32 + 5) = 1;
    *(undefined4 *)(plVar32 + 0x1b) = 0;
    FUN_1800079f8(plVar34,0x1800d6c78,0xb);
    *(undefined4 *)((longlong)plVar34 + 0xc) = 1;
    *(undefined2 *)((longlong)plVar34 + 0x24) = 6;
    plVar34[3] = 0x221f000f0a60d;
    *(undefined4 *)(plVar34 + 4) = 1;
    *(undefined2 *)(plVar34 + 5) = 1;
    *(undefined4 *)(plVar34 + 0x1b) = 0;
    *(undefined2 *)(piVar36 + 9) = 5;
    piVar36[6] = 0xffff;
    *(undefined2 *)(piVar36 + 10) = 1;
    piVar36[0x36] = 0;
    piVar38[6] = 0xffff00;
    *(undefined2 *)(piVar38 + 9) = 5;
    *(undefined2 *)(piVar38 + 10) = 1;
    piVar38[0x36] = 1;
    *(undefined2 *)(piVar19 + 9) = 5;
    piVar19[6] = 0xffff;
    *(undefined2 *)(piVar19 + 10) = 1;
    piVar19[0x36] = 0;
    *(undefined1 *)(local_160 + 6) = 0xb;
    local_160[7] = 0xe;
    local_160[0xb] = 2;
    local_160[0xf] = 999;
    local_158[7] = 7;
    local_158[0xf] = 0x3e6;
    *(undefined1 *)(local_158 + 6) = 0xb;
    local_158[0xb] = 1;
    FUN_1800079f8(local_190,0x1800d6ca0,0xc);
    *(int *)((longlong)local_178 + 0xc) = 1;
    if ((code *)local_178[10] != (code *)0x0) {
      (*(code *)local_178[10])(*(int *)((longlong)local_178 + 0x4c),&DAT_1800d6c94);
    }
    *(undefined1 *)(local_178 + 3) = 0x16;
    *(int *)((longlong)local_178 + 0x1c) = 0;
    FUN_1800079f8(local_200,0x1800d6cc8,0x14);
    *(int *)((longlong)local_150 + 0xc) = 1;
    *(int *)((longlong)local_150 + 0x1c) = 0x808080;
    *(undefined1 *)(local_150 + 3) = 0xe;
    FUN_1800079f8(plVar44,0x1800d6cb0,0x11);
    *(undefined4 *)((longlong)plVar44 + 0xc) = 1;
    puVar11 = FUN_180029680((longlong *)local_120,&local_1f8);
    if (0xf < (ulonglong)puVar11[3]) {
      puVar11 = (undefined8 *)*puVar11;
    }
    if ((code *)plVar44[10] != (code *)0x0) {
      (*(code *)plVar44[10])(*(undefined4 *)((longlong)plVar44 + 0x4c),puVar11);
      *(undefined1 *)(plVar44 + 3) = 0x16;
    }
    if (0xf < local_1e0) {
      FUN_1800966b8(local_1f8);
    }
    local_1f8 = (char *)((ulonglong)local_1f8 & 0xffffffffffffff00);
    local_1e8 = (undefined1 *)0x0;
    local_1e0 = 0xf;
    *(undefined1 *)(plVar44 + 3) = 0x16;
    *(undefined4 *)((longlong)plVar44 + 0x1c) = 0;
    FUN_1800079f8(plVar40,0x1800d6cf0,0xd);
    *(undefined4 *)((longlong)plVar40 + 0xc) = 1;
    if ((code *)plVar40[10] != (code *)0x0) {
      (*(code *)plVar40[10])(*(undefined4 *)((longlong)plVar40 + 0x4c),"Globex;PitOpen");
    }
    *(undefined1 *)(plVar40 + 3) = 0x16;
    *(undefined4 *)((longlong)plVar40 + 0x1c) = 0;
    local_148[0xf] = 1000000;
    *(undefined1 *)(local_148 + 6) = 0xb;
    local_148[7] = 0;
    local_148[0xb] = 0;
    FUN_1800079f8(plVar45,0x1800d6d18,0x10);
    *(undefined4 *)((longlong)plVar45 + 0xc) = 1;
    *(undefined4 *)((longlong)plVar45 + 0x1c) = 0x3f800000;
    *(undefined1 *)(plVar45 + 3) = 2;
    *(undefined4 *)((longlong)plVar45 + 0x2c) = 0x3c23d70a;
    *(undefined4 *)((longlong)plVar45 + 0x3c) = 0x40000000;
    *(undefined1 *)(local_140 + 6) = 5;
    local_140[7] = 0;
    FUN_1800079f8(plVar41,0x1800d6d00,0x10);
    *(undefined4 *)((longlong)plVar41 + 0xc) = 1;
    if ((code *)plVar41[10] != (code *)0x0) {
      (*(code *)plVar41[10])(*(undefined4 *)((longlong)plVar41 + 0x4c),"RTH;RTH + Globex");
    }
    plVar25 = local_180;
    *(undefined1 *)(plVar41 + 3) = 0x16;
    *(undefined4 *)((longlong)plVar41 + 0x1c) = 0;
    FUN_1800079f8(local_180,0x1800d6d30,0x14);
    *(undefined4 *)((longlong)plVar25 + 0xc) = 1;
    *(undefined1 *)(plVar25 + 3) = 5;
    *(undefined4 *)((longlong)plVar25 + 0x1c) = 0;
    return;
  }
  if (param_3[0xe1] == 0) {
    auVar2._4_4_ = uVar47;
    auVar2._0_4_ = uVar49;
    auVar2._8_8_ = uVar48;
    uVar12 = FUN_1800254e8(auVar2,param_2,(longlong)param_3,uVar12,param_5,param_6,param_7,param_8,
                           param_9,param_10);
    *local_138 = (int)uVar12;
  }
  if (*local_138 != 0) {
    return;
  }
  if ((param_3[0x47e] != 0) && (param_3[0xe1] == 0)) {
    *local_1c0 = 0;
    *local_130 = -1;
    *local_1a8 = 0;
  }
  local_110[1] = 0;
  local_110[0] = 0;
  local_110[3] = 0;
  local_110[2] = 0;
  local_110[5] = 0;
  local_110[4] = 0;
  local_110[7] = 0;
  local_110[6] = 0;
  local_110[9] = 0;
  local_110[8] = 0;
  local_110[0xb] = 0;
  local_110[10] = 0;
  local_110[0xd] = 0;
  local_110[0xc] = 0;
  local_110[0xf] = 0;
  local_110[0xe] = 0;
  local_110[0x10] = 0;
  uVar6 = FUN_180026550((longlong)plVar44);
  FUN_180011fd0(local_110,(longlong)param_3,param_3[0xe1],uVar6);
  uVar6 = FUN_180026550((longlong)plVar40);
  fVar46 = 0.0;
  if ((((uVar6 == 0) && (uVar13 = FUN_180012730((longlong)local_110), (uVar13 & 1) != 0)) ||
      ((uVar6 = FUN_180026550((longlong)plVar40), uVar6 == 1 &&
       (uVar13 = FUN_1800125b0((longlong)local_110), (uVar13 & 1) != 0)))) &&
     (piVar21 = local_130, *local_130 != param_3[0xe1])) {
    iVar7 = FUN_180006790(param_3,param_3[0xe1]);
    if (iVar7 == 2) {
      pfVar14 = (float *)FUN_180005d08(plVar39,extraout_w1 + -1);
      fVar50 = *pfVar14;
      pfVar14 = (float *)FUN_180005d08(plVar33,*piVar21);
      pfVar15 = (float *)FUN_180005d08(plVar35,*piVar21);
      fVar56 = *pfVar14;
      fVar51 = *pfVar15;
      pfVar14 = (float *)FUN_180005d08(local_1d0,param_3[0xe1]);
      puVar4 = local_1c0;
      *pfVar14 = ((fVar56 - fVar51) - fVar50) / (fVar56 - fVar51);
      fVar50 = *local_1b8;
      fVar51 = *local_1b0;
      pfVar14 = (float *)FUN_180005d08(local_188,*local_1c0);
      *pfVar14 = fVar50 - fVar51;
      *puVar4 = *puVar4 + 1;
      pfVar14 = (float *)FUN_180005d08(plVar37,param_3[0xe1]);
      *pfVar14 = fVar50 - fVar51;
      *piVar21 = param_3[0xe1];
      *local_1a8 = 0;
    }
    piVar1 = local_1c8;
    FUN_180026708((longlong)local_1c8);
    uVar13 = FUN_180026708((longlong)local_1d8);
    puVar4 = local_1c0;
    piVar21 = extraout_x12;
    if ((int)uVar13 <= extraout_w11) {
      piVar21 = piVar1;
    }
    uVar13 = FUN_180026708((longlong)piVar21);
    if ((int)uVar13 <= extraout_w1_00) {
      uVar13 = FUN_180026708(extraout_x12_00);
      plVar44 = local_188;
      FUN_180026aa8(local_188,extraout_w1_01,(uint)uVar13);
      fVar46 = FUN_180026608((longlong)plVar45);
      pfVar14 = (float *)FUN_180005d08((longlong *)(param_3 + 0x28e),param_3[0xe1]);
      fVar51 = fVar46 * extraout_s18 * 0.5;
      fVar46 = FUN_180011838(*pfVar14 + fVar51,(longlong)param_3);
      pfVar14 = (float *)FUN_180005d08(plVar25,param_3[0xe1]);
      *pfVar14 = fVar46;
      pfVar14 = (float *)FUN_180005d08((longlong *)(param_3 + 0x28e),param_3[0xe1]);
      fVar46 = FUN_180011838(*pfVar14 - fVar51,(longlong)param_3);
      pfVar14 = (float *)FUN_180005d08(plVar27,param_3[0xe1]);
      *pfVar14 = fVar46;
      uVar13 = FUN_180026708((longlong)piVar1);
      FUN_180026aa8(plVar44,*puVar4,(uint)uVar13);
      fVar46 = FUN_180026608((longlong)plVar45);
      pfVar14 = (float *)FUN_180005d08((longlong *)(param_3 + 0x28e),param_3[0xe1]);
      fVar51 = fVar46 * extraout_s18_00 * 0.5;
      fVar46 = FUN_180011838(fVar51 + *pfVar14,(longlong)param_3);
      pfVar14 = (float *)FUN_180005d08(plVar29,param_3[0xe1]);
      *pfVar14 = fVar46;
      pfVar14 = (float *)FUN_180005d08((longlong *)(param_3 + 0x28e),param_3[0xe1]);
      fVar46 = FUN_180011838(*pfVar14 - fVar51,(longlong)param_3);
      pfVar14 = (float *)FUN_180005d08(plVar31,param_3[0xe1]);
      *pfVar14 = fVar46;
      uVar8 = FUN_180026550((longlong)local_190);
      uVar6 = 5;
      if (uVar8 != 0) {
        uVar6 = 10;
      }
      fVar46 = FUN_180026aa8(plVar44,*puVar4,uVar6);
      FUN_180011838(fVar46,(longlong)param_3);
      fVar46 = FUN_180026608((longlong)plVar45);
      fVar46 = fVar46 * extraout_s18_01;
      pfVar14 = (float *)FUN_180005d08((longlong *)(param_3 + 0x28e),param_3[0xe1]);
      fVar51 = *pfVar14;
      pfVar14 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),param_3[0xe1]);
      pfVar15 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2ac),param_3[0xe1]);
      fVar51 = FUN_180011838((fVar51 + fVar46) - (*pfVar14 - *pfVar15),(longlong)param_3);
      pfVar14 = (float *)FUN_180005d08(plVar33,param_3[0xe1]);
      *pfVar14 = fVar51;
      pfVar14 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2ac),param_3[0xe1]);
      pfVar15 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),param_3[0xe1]);
      fVar51 = *pfVar15;
      fVar50 = *pfVar14;
      pfVar14 = (float *)FUN_180005d08((longlong *)(param_3 + 0x28e),param_3[0xe1]);
      fVar51 = FUN_180011838((fVar50 - fVar51) + (*pfVar14 - fVar46),(longlong)param_3);
      pfVar14 = (float *)FUN_180005d08(plVar35,param_3[0xe1]);
      *pfVar14 = fVar51;
    }
    iVar7 = FUN_180006790(param_3,param_3[0xe1]);
    if (iVar7 == 2) {
      *local_1a0 = fVar46;
      pfVar14 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),param_3[0xe1]);
      *local_1b8 = *pfVar14;
      pfVar14 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),param_3[0xe1]);
      *local_1b0 = *pfVar14;
    }
    bVar5 = FUN_180026690((longlong)local_180);
    lVar43 = local_110[0xb];
    lVar18 = local_110[2];
    lVar24 = local_110[7];
    if ((bVar5) && (uVar6 = 0, 0 < extraout_w11_00)) {
      do {
        lVar18 = plVar32[6];
        if (lVar18 == 0) {
          if ((code *)plVar32[8] != (code *)0x0) {
            (*(code *)plVar32[8])((int)plVar32[9]);
          }
          lVar18 = plVar32[6];
          if (lVar18 != 0) goto LAB_180028114;
          puVar17 = (undefined4 *)((longlong)plVar32 + 0x54);
        }
        else {
LAB_180028114:
          iVar7 = (int)plVar32[10];
          if (iVar7 == 0) {
            puVar17 = (undefined4 *)((longlong)plVar32 + 0x54);
          }
          else {
            uVar8 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
            if (iVar7 <= (int)uVar8) {
              uVar8 = iVar7 - 1;
            }
            puVar17 = (undefined4 *)(lVar18 + (longlong)(int)uVar8 * 4);
          }
        }
        *puVar17 = 0;
        lVar18 = plVar34[6];
        if (lVar18 == 0) {
          if ((code *)plVar34[8] != (code *)0x0) {
            (*(code *)plVar34[8])((int)plVar34[9]);
          }
          lVar18 = plVar34[6];
          if (lVar18 != 0) goto LAB_180028164;
          puVar17 = (undefined4 *)((longlong)plVar34 + 0x54);
        }
        else {
LAB_180028164:
          iVar7 = (int)plVar34[10];
          if (iVar7 == 0) {
            puVar17 = (undefined4 *)((longlong)plVar34 + 0x54);
          }
          else {
            uVar8 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
            if (iVar7 <= (int)uVar8) {
              uVar8 = iVar7 - 1;
            }
            puVar17 = (undefined4 *)(lVar18 + (longlong)(int)uVar8 * 4);
          }
        }
        *puVar17 = 0;
        uVar6 = uVar6 + 1;
        lVar43 = local_110[0xb];
        lVar18 = local_110[2];
        lVar24 = local_110[7];
      } while ((int)uVar6 < param_3[0xe1]);
    }
  }
  else {
    iVar7 = param_3[0xe1];
    uVar6 = iVar7 - 1;
    lVar18 = *plVar25;
    if (lVar18 == 0) {
      if ((code *)plVar25[2] != (code *)0x0) {
        (*(code *)plVar25[2])((int)plVar25[3]);
        iVar7 = param_3[0xe1];
      }
      lVar18 = *plVar25;
      if (lVar18 != 0) goto LAB_18002822c;
      uVar49 = *(undefined4 *)((longlong)plVar25 + 0x24);
LAB_180028260:
      if ((code *)plVar25[2] != (code *)0x0) {
        (*(code *)plVar25[2])((int)plVar25[3]);
      }
      lVar18 = *plVar25;
      if (lVar18 != 0) goto LAB_180028288;
      puVar17 = (undefined4 *)((longlong)plVar25 + 0x24);
    }
    else {
LAB_18002822c:
      iVar22 = (int)plVar25[4];
      if (iVar22 == 0) {
        puVar17 = (undefined4 *)((longlong)plVar25 + 0x24);
      }
      else {
        uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
        if (iVar22 <= (int)uVar6) {
          uVar6 = iVar22 - 1;
        }
        puVar17 = (undefined4 *)(lVar18 + (longlong)(int)uVar6 * 4);
      }
      uVar49 = *puVar17;
      if (lVar18 == 0) goto LAB_180028260;
LAB_180028288:
      iVar22 = (int)plVar25[4];
      if (iVar22 == 0) {
        puVar17 = (undefined4 *)((longlong)plVar25 + 0x24);
      }
      else {
        if (iVar7 < 0) {
          iVar7 = 0;
        }
        if (iVar22 <= iVar7) {
          iVar7 = iVar22 + -1;
        }
        puVar17 = (undefined4 *)(lVar18 + (longlong)iVar7 * 4);
      }
    }
    *puVar17 = uVar49;
    iVar7 = param_3[0xe1];
    lVar18 = *plVar27;
    uVar6 = iVar7 - 1;
    if (lVar18 == 0) {
      if ((code *)plVar27[2] != (code *)0x0) {
        (*(code *)plVar27[2])((int)plVar27[3]);
        iVar7 = param_3[0xe1];
      }
      lVar18 = *plVar27;
      if (lVar18 != 0) goto LAB_1800282fc;
      uVar49 = *(undefined4 *)((longlong)plVar27 + 0x24);
LAB_180028330:
      if ((code *)plVar27[2] != (code *)0x0) {
        (*(code *)plVar27[2])((int)plVar27[3]);
      }
      lVar18 = *plVar27;
      if (lVar18 != 0) goto LAB_180028358;
      puVar17 = (undefined4 *)((longlong)plVar27 + 0x24);
    }
    else {
LAB_1800282fc:
      iVar22 = (int)plVar27[4];
      if (iVar22 == 0) {
        puVar17 = (undefined4 *)((longlong)plVar27 + 0x24);
      }
      else {
        uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
        if (iVar22 <= (int)uVar6) {
          uVar6 = iVar22 - 1;
        }
        puVar17 = (undefined4 *)(lVar18 + (longlong)(int)uVar6 * 4);
      }
      uVar49 = *puVar17;
      if (lVar18 == 0) goto LAB_180028330;
LAB_180028358:
      iVar22 = (int)plVar27[4];
      if (iVar22 == 0) {
        puVar17 = (undefined4 *)((longlong)plVar27 + 0x24);
      }
      else {
        if (iVar7 < 0) {
          iVar7 = 0;
        }
        if (iVar22 <= iVar7) {
          iVar7 = iVar22 + -1;
        }
        puVar17 = (undefined4 *)(lVar18 + (longlong)iVar7 * 4);
      }
    }
    *puVar17 = uVar49;
    iVar7 = param_3[0xe1];
    lVar18 = *plVar29;
    uVar6 = iVar7 - 1;
    if (lVar18 == 0) {
      if ((code *)plVar29[2] != (code *)0x0) {
        (*(code *)plVar29[2])((int)plVar29[3]);
        iVar7 = param_3[0xe1];
      }
      lVar18 = *plVar29;
      if (lVar18 != 0) goto LAB_1800283cc;
      uVar49 = *(undefined4 *)((longlong)plVar29 + 0x24);
LAB_180028400:
      if ((code *)plVar29[2] != (code *)0x0) {
        (*(code *)plVar29[2])((int)plVar29[3]);
      }
      lVar18 = *plVar29;
      if (lVar18 != 0) goto LAB_180028428;
      puVar17 = (undefined4 *)((longlong)plVar29 + 0x24);
    }
    else {
LAB_1800283cc:
      iVar22 = (int)plVar29[4];
      if (iVar22 == 0) {
        puVar17 = (undefined4 *)((longlong)plVar29 + 0x24);
      }
      else {
        uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
        if (iVar22 <= (int)uVar6) {
          uVar6 = iVar22 - 1;
        }
        puVar17 = (undefined4 *)(lVar18 + (longlong)(int)uVar6 * 4);
      }
      uVar49 = *puVar17;
      if (lVar18 == 0) goto LAB_180028400;
LAB_180028428:
      iVar22 = (int)plVar29[4];
      if (iVar22 == 0) {
        puVar17 = (undefined4 *)((longlong)plVar29 + 0x24);
      }
      else {
        if (iVar7 < 0) {
          iVar7 = 0;
        }
        if (iVar22 <= iVar7) {
          iVar7 = iVar22 + -1;
        }
        puVar17 = (undefined4 *)(lVar18 + (longlong)iVar7 * 4);
      }
    }
    *puVar17 = uVar49;
    iVar7 = param_3[0xe1];
    lVar18 = *plVar31;
    uVar6 = iVar7 - 1;
    if (lVar18 == 0) {
      if ((code *)plVar31[2] != (code *)0x0) {
        (*(code *)plVar31[2])((int)plVar31[3]);
        iVar7 = param_3[0xe1];
      }
      lVar18 = *plVar31;
      if (lVar18 != 0) goto LAB_18002849c;
      uVar49 = *(undefined4 *)((longlong)plVar31 + 0x24);
LAB_1800284d0:
      if ((code *)plVar31[2] != (code *)0x0) {
        (*(code *)plVar31[2])((int)plVar31[3]);
      }
      lVar18 = *plVar31;
      if (lVar18 != 0) goto LAB_1800284f8;
      puVar17 = (undefined4 *)((longlong)plVar31 + 0x24);
    }
    else {
LAB_18002849c:
      iVar22 = (int)plVar31[4];
      if (iVar22 == 0) {
        puVar17 = (undefined4 *)((longlong)plVar31 + 0x24);
      }
      else {
        uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
        if (iVar22 <= (int)uVar6) {
          uVar6 = iVar22 - 1;
        }
        puVar17 = (undefined4 *)(lVar18 + (longlong)(int)uVar6 * 4);
      }
      uVar49 = *puVar17;
      if (lVar18 == 0) goto LAB_1800284d0;
LAB_1800284f8:
      iVar22 = (int)plVar31[4];
      if (iVar22 == 0) {
        puVar17 = (undefined4 *)((longlong)plVar31 + 0x24);
      }
      else {
        if (iVar7 < 0) {
          iVar7 = 0;
        }
        if (iVar22 <= iVar7) {
          iVar7 = iVar22 + -1;
        }
        puVar17 = (undefined4 *)(lVar18 + (longlong)iVar7 * 4);
      }
    }
    *puVar17 = uVar49;
    uVar6 = param_3[0xe1];
    lVar18 = *(longlong *)(param_3 + 0x298);
    uVar8 = uVar6;
    if (lVar18 == 0) {
      if (*(code **)(param_3 + 0x29c) != (code *)0x0) {
        (**(code **)(param_3 + 0x29c))(param_3[0x29e],uVar6);
        uVar8 = param_3[0xe1];
      }
      lVar18 = *(longlong *)(param_3 + 0x298);
      if (lVar18 != 0) goto LAB_180028568;
      pfVar14 = (float *)(param_3 + 0x2a1);
    }
    else {
LAB_180028568:
      iVar7 = param_3[0x2a0];
      if (iVar7 == 0) {
        pfVar14 = (float *)(param_3 + 0x2a1);
      }
      else {
        uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
        if (iVar7 <= (int)uVar6) {
          uVar6 = iVar7 - 1;
        }
        pfVar14 = (float *)(lVar18 + (longlong)(int)uVar6 * 4);
      }
    }
    pfVar15 = local_1b8;
    piVar21 = local_1c8;
    if (*pfVar14 <= *local_1b8) {
LAB_1800286ac:
      pfVar15 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),uVar8);
      pfVar14 = local_1b0;
      piVar21 = local_1c8;
      if (*local_1b0 <= *pfVar15) {
LAB_1800287c4:
        pfVar14 = (float *)FUN_180005d08(plVar33,param_3[0xe1] + -1);
        fVar46 = *pfVar14;
      }
      else {
        FUN_180026708((longlong)local_1c8);
        uVar13 = FUN_180026708((longlong)local_1d8);
        piVar1 = extraout_x12_02;
        if ((int)uVar13 <= extraout_w11_02) {
          piVar1 = piVar21;
        }
        uVar13 = FUN_180026708((longlong)piVar1);
        if ((int)*local_1c0 < (int)uVar13) goto LAB_1800287c4;
        pfVar15 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),param_3[0xe1]);
        fVar50 = *pfVar14;
        fVar46 = *pfVar15;
        pfVar14 = (float *)FUN_180005d08(plVar33,param_3[0xe1] + -1);
        fVar51 = *pfVar14;
        pfVar14 = (float *)FUN_180005d08(plVar33,param_3[0xe1]);
        *pfVar14 = fVar51 - ABS(fVar50 - fVar46);
        pfVar15 = (float *)FUN_180005d08(plVar33,param_3[0xe1]);
        pfVar16 = (float *)FUN_180005d08(plVar35,param_3[0xe1] + -1);
        pfVar14 = local_1a0;
        if (*pfVar15 <= *pfVar16 + *local_1a0) {
          pfVar15 = (float *)FUN_180005d08(plVar35,local_198[0xe1] + -1);
          fVar46 = *pfVar15 + *pfVar14;
        }
        else {
          pfVar14 = (float *)FUN_180005d08(plVar33,local_198[0xe1]);
          fVar46 = *pfVar14;
        }
      }
      pfVar14 = (float *)FUN_180005d08(plVar33,param_3[0xe1]);
      plVar44 = plVar35;
    }
    else {
      FUN_180026708((longlong)local_1c8);
      uVar13 = FUN_180026708((longlong)local_1d8);
      piVar1 = extraout_x12_01;
      if ((int)uVar13 <= extraout_w13) {
        piVar1 = piVar21;
      }
      uVar13 = FUN_180026708((longlong)piVar1);
      uVar8 = extraout_w11_01;
      if ((int)*local_1c0 < (int)uVar13) goto LAB_1800286ac;
      pfVar14 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),extraout_w1_02);
      fVar46 = *pfVar15;
      fVar50 = *pfVar14;
      pfVar14 = (float *)FUN_180005d08(plVar35,param_3[0xe1] + -1);
      fVar51 = *pfVar14;
      pfVar14 = (float *)FUN_180005d08(plVar35,param_3[0xe1]);
      *pfVar14 = fVar51 + ABS(fVar50 - fVar46);
      pfVar15 = (float *)FUN_180005d08(plVar35,param_3[0xe1]);
      pfVar16 = (float *)FUN_180005d08(plVar33,param_3[0xe1] + -1);
      pfVar14 = local_1a0;
      plVar44 = plVar33;
      if (*pfVar16 - *local_1a0 <= *pfVar15) {
        pfVar15 = (float *)FUN_180005d08(plVar33,local_198[0xe1] + -1);
        fVar46 = *pfVar15 - *pfVar14;
        pfVar14 = (float *)FUN_180005d08(plVar35,param_3[0xe1]);
      }
      else {
        pfVar14 = (float *)FUN_180005d08(plVar35,local_198[0xe1]);
        fVar46 = *pfVar14;
        pfVar14 = (float *)FUN_180005d08(plVar35,param_3[0xe1]);
      }
    }
    *pfVar14 = fVar46;
    puVar17 = (undefined4 *)FUN_180005d08(plVar44,param_3[0xe1] + -1);
    uVar49 = *puVar17;
    puVar17 = (undefined4 *)FUN_180005d08(plVar44,param_3[0xe1]);
    *puVar17 = uVar49;
    iVar7 = param_3[0xe1];
    lVar18 = *plVar37;
    uVar6 = iVar7 - 1;
    if (lVar18 == 0) {
      if ((code *)plVar37[2] != (code *)0x0) {
        (*(code *)plVar37[2])((int)plVar37[3]);
        iVar7 = param_3[0xe1];
      }
      lVar18 = *plVar37;
      if (lVar18 != 0) goto LAB_180028858;
      uVar49 = *(undefined4 *)((longlong)plVar37 + 0x24);
LAB_18002888c:
      if ((code *)plVar37[2] != (code *)0x0) {
        (*(code *)plVar37[2])((int)plVar37[3]);
      }
      lVar18 = *plVar37;
      if (lVar18 != 0) goto LAB_1800288b4;
      puVar17 = (undefined4 *)((longlong)plVar37 + 0x24);
    }
    else {
LAB_180028858:
      iVar22 = (int)plVar37[4];
      if (iVar22 == 0) {
        puVar17 = (undefined4 *)((longlong)plVar37 + 0x24);
      }
      else {
        uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
        if (iVar22 <= (int)uVar6) {
          uVar6 = iVar22 - 1;
        }
        puVar17 = (undefined4 *)(lVar18 + (longlong)(int)uVar6 * 4);
      }
      uVar49 = *puVar17;
      if (lVar18 == 0) goto LAB_18002888c;
LAB_1800288b4:
      iVar22 = (int)plVar37[4];
      if (iVar22 == 0) {
        puVar17 = (undefined4 *)((longlong)plVar37 + 0x24);
      }
      else {
        if (iVar7 < 0) {
          iVar7 = 0;
        }
        if (iVar22 <= iVar7) {
          iVar7 = iVar22 + -1;
        }
        puVar17 = (undefined4 *)(lVar18 + (longlong)iVar7 * 4);
      }
    }
    *puVar17 = uVar49;
    lVar43 = local_110[0xb];
    lVar18 = local_110[2];
    lVar24 = local_110[7];
    if ((param_3[0x2b] == 0) && (param_3[0xe1] != *param_3 + -1)) {
      uVar6 = FUN_180026550((longlong)plVar41);
      lVar43 = local_110[0xb];
      lVar24 = local_110[7];
      lVar18 = local_110[2];
      if (uVar6 == 1) {
LAB_180028984:
        pfVar14 = (float *)FUN_180005d08((longlong *)(param_3 + 0x298),param_3[0xe1]);
        fVar46 = *local_1b8;
        uVar52 = SUB41(fVar46,0);
        uVar53 = (undefined1)((uint)fVar46 >> 8);
        uVar54 = (undefined1)((uint)fVar46 >> 0x10);
        uVar55 = (undefined1)((uint)fVar46 >> 0x18);
        if (fVar46 <= *pfVar14) {
          puVar17 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x298),param_3[0xe1]);
          uVar49 = *puVar17;
          uVar52 = (undefined1)uVar49;
          uVar53 = (undefined1)((uint)uVar49 >> 8);
          uVar54 = (undefined1)((uint)uVar49 >> 0x10);
          uVar55 = (undefined1)((uint)uVar49 >> 0x18);
        }
        *local_1b8 = (float)CONCAT13(uVar55,CONCAT12(uVar54,CONCAT11(uVar53,uVar52)));
        pfVar14 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2a2),param_3[0xe1]);
        fVar46 = *local_1b0;
        uVar52 = SUB41(fVar46,0);
        uVar53 = (undefined1)((uint)fVar46 >> 8);
        uVar54 = (undefined1)((uint)fVar46 >> 0x10);
        uVar55 = (undefined1)((uint)fVar46 >> 0x18);
        if (*pfVar14 <= fVar46) {
          puVar17 = (undefined4 *)FUN_180005d08((longlong *)(param_3 + 0x2a2),param_3[0xe1]);
          uVar49 = *puVar17;
          uVar52 = (undefined1)uVar49;
          uVar53 = (undefined1)((uint)uVar49 >> 8);
          uVar54 = (undefined1)((uint)uVar49 >> 0x10);
          uVar55 = (undefined1)((uint)uVar49 >> 0x18);
        }
        local_240 = local_170;
        *local_1b0 = (float)CONCAT13(uVar55,CONCAT12(uVar54,CONCAT11(uVar53,uVar52)));
        local_200 = local_168;
      }
      else {
        local_240 = local_170;
        local_200 = local_168;
        uVar6 = FUN_180026550((longlong)plVar41);
        if (uVar6 == 0) {
          if (lVar43 < lVar24) {
            if ((lVar24 <= lVar18) || (bVar5 = false, lVar18 < lVar43)) goto LAB_18002896c;
          }
          else if ((lVar18 < lVar24) || (lVar43 <= lVar18)) {
            bVar5 = false;
          }
          else {
LAB_18002896c:
            bVar5 = true;
          }
          local_240 = local_170;
          local_200 = local_168;
          if (bVar5) goto LAB_180028984;
        }
      }
    }
  }
  uVar6 = param_3[0xe1];
  lVar20 = *plVar33;
  uVar8 = uVar6;
  if (lVar20 == 0) {
    if ((code *)plVar33[2] != (code *)0x0) {
      (*(code *)plVar33[2])((int)plVar33[3]);
      uVar8 = param_3[0xe1];
    }
    lVar20 = *plVar33;
    if (lVar20 != 0) goto LAB_180028a08;
    pfVar14 = (float *)((longlong)plVar33 + 0x24);
  }
  else {
LAB_180028a08:
    iVar7 = (int)plVar33[4];
    if (iVar7 == 0) {
      pfVar14 = (float *)((longlong)plVar33 + 0x24);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar6) {
        uVar6 = iVar7 - 1;
      }
      pfVar14 = (float *)(lVar20 + (longlong)(int)uVar6 * 4);
    }
  }
  lVar20 = *plVar35;
  uVar6 = uVar8;
  if (lVar20 == 0) {
    if ((code *)plVar35[2] != (code *)0x0) {
      (*(code *)plVar35[2])((int)plVar35[3]);
      uVar6 = param_3[0xe1];
    }
    lVar20 = *plVar35;
    if (lVar20 != 0) goto LAB_180028a6c;
    pfVar15 = (float *)((longlong)plVar35 + 0x24);
  }
  else {
LAB_180028a6c:
    iVar7 = (int)plVar35[4];
    if (iVar7 == 0) {
      pfVar15 = (float *)((longlong)plVar35 + 0x24);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar8) {
        uVar8 = iVar7 - 1;
      }
      pfVar15 = (float *)(lVar20 + (longlong)(int)uVar8 * 4);
    }
  }
  fVar46 = *pfVar15;
  fVar50 = *pfVar14;
  fVar51 = *local_1a0;
  lVar20 = *plVar39;
  if (lVar20 == 0) {
    if ((code *)plVar39[2] != (code *)0x0) {
      (*(code *)plVar39[2])((int)plVar39[3]);
    }
    lVar20 = *plVar39;
    if (lVar20 != 0) goto LAB_180028adc;
    pfVar14 = (float *)((longlong)plVar39 + 0x24);
  }
  else {
LAB_180028adc:
    iVar7 = (int)plVar39[4];
    if (iVar7 == 0) {
      pfVar14 = (float *)((longlong)plVar39 + 0x24);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar6) {
        uVar6 = iVar7 - 1;
      }
      pfVar14 = (float *)(lVar20 + (longlong)(int)uVar6 * 4);
    }
  }
  *pfVar14 = (fVar50 - fVar46) - fVar51;
  if (*local_1a8 == 0) {
    uVar6 = param_3[0xe1];
    lVar20 = *plVar33;
    if (lVar20 == 0) {
      if ((code *)plVar33[2] != (code *)0x0) {
        (*(code *)plVar33[2])((int)plVar33[3]);
      }
      lVar20 = *plVar33;
      if (lVar20 != 0) goto LAB_180028b54;
      pfVar14 = (float *)((longlong)plVar33 + 0x24);
    }
    else {
LAB_180028b54:
      iVar7 = (int)plVar33[4];
      if (iVar7 == 0) {
        pfVar14 = (float *)((longlong)plVar33 + 0x24);
      }
      else {
        uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
        if (iVar7 <= (int)uVar6) {
          uVar6 = iVar7 - 1;
        }
        pfVar14 = (float *)(lVar20 + (longlong)(int)uVar6 * 4);
      }
    }
    if ((*(longlong *)(param_3 + 0x2ac) == 0) && (*(code **)(param_3 + 0x2b0) != (code *)0x0)) {
      (**(code **)(param_3 + 0x2b0))(param_3[0x2b2]);
    }
    uVar13 = FUN_180026708((longlong)local_240);
    if (*extraout_x11 < *pfVar14 - (float)(int)uVar13 * (float)param_3[0x1a6]) {
      pfVar14 = (float *)FUN_180005d08((longlong *)(param_3 + 0x2ac),param_3[0xe1]);
      FUN_180005d08(plVar35,param_3[0xe1]);
      uVar13 = FUN_180026708((longlong)local_240);
      if ((float)(int)uVar13 * (float)param_3[0x1a6] + *extraout_x11_00 < *pfVar14)
      goto LAB_180028c48;
    }
    *local_1a8 = 1;
  }
LAB_180028c48:
  uVar13 = FUN_180026708((longlong)local_240);
  if (((int)uVar13 != 0) && (*local_1a8 == 0)) {
    return;
  }
  uVar6 = param_3[0xe1];
  lVar20 = *plVar25;
  uVar8 = uVar6;
  if (lVar20 == 0) {
    if ((code *)plVar25[2] != (code *)0x0) {
      (*(code *)plVar25[2])((int)plVar25[3]);
      uVar8 = param_3[0xe1];
    }
    lVar20 = *plVar25;
    if (lVar20 != 0) goto LAB_180028ca0;
    piVar21 = (int *)((longlong)plVar25 + 0x24);
  }
  else {
LAB_180028ca0:
    iVar7 = (int)plVar25[4];
    if (iVar7 == 0) {
      piVar21 = (int *)((longlong)plVar25 + 0x24);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar6) {
        uVar6 = iVar7 - 1;
      }
      piVar21 = (int *)(lVar20 + (longlong)(int)uVar6 * 4);
    }
  }
  iVar7 = *piVar21;
  lVar20 = *(longlong *)(piVar23 + 0xc);
  if (lVar20 == 0) {
    if (*(code **)(piVar23 + 0x10) != (code *)0x0) {
      (**(code **)(piVar23 + 0x10))(piVar23[0x12]);
    }
    lVar20 = *(longlong *)(piVar23 + 0xc);
    if (lVar20 != 0) goto LAB_180028cfc;
    piVar23 = piVar23 + 0x15;
  }
  else {
LAB_180028cfc:
    iVar22 = piVar23[0x14];
    if (iVar22 == 0) {
      piVar23 = piVar23 + 0x15;
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar22 <= (int)uVar8) {
        uVar8 = iVar22 - 1;
      }
      piVar23 = (int *)(lVar20 + (longlong)(int)uVar8 * 4);
    }
  }
  *piVar23 = iVar7;
  uVar6 = param_3[0xe1];
  lVar20 = *plVar27;
  uVar8 = uVar6;
  if (lVar20 == 0) {
    if ((code *)plVar27[2] != (code *)0x0) {
      (*(code *)plVar27[2])((int)plVar27[3]);
      uVar8 = param_3[0xe1];
    }
    lVar20 = *plVar27;
    if (lVar20 != 0) goto LAB_180028d68;
    piVar23 = (int *)((longlong)plVar27 + 0x24);
  }
  else {
LAB_180028d68:
    iVar7 = (int)plVar27[4];
    if (iVar7 == 0) {
      piVar23 = (int *)((longlong)plVar27 + 0x24);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar6) {
        uVar6 = iVar7 - 1;
      }
      piVar23 = (int *)(lVar20 + (longlong)(int)uVar6 * 4);
    }
  }
  iVar7 = *piVar23;
  lVar20 = *(longlong *)(piVar26 + 0xc);
  if (lVar20 == 0) {
    if (*(code **)(piVar26 + 0x10) != (code *)0x0) {
      (**(code **)(piVar26 + 0x10))(piVar26[0x12]);
    }
    lVar20 = *(longlong *)(piVar26 + 0xc);
    if (lVar20 != 0) goto LAB_180028dc4;
    piVar26 = piVar26 + 0x15;
  }
  else {
LAB_180028dc4:
    iVar22 = piVar26[0x14];
    if (iVar22 == 0) {
      piVar26 = piVar26 + 0x15;
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar22 <= (int)uVar8) {
        uVar8 = iVar22 - 1;
      }
      piVar26 = (int *)(lVar20 + (longlong)(int)uVar8 * 4);
    }
  }
  *piVar26 = iVar7;
  uVar6 = param_3[0xe1];
  lVar20 = *plVar29;
  uVar8 = uVar6;
  if (lVar20 == 0) {
    if ((code *)plVar29[2] != (code *)0x0) {
      (*(code *)plVar29[2])((int)plVar29[3]);
      uVar8 = param_3[0xe1];
    }
    lVar20 = *plVar29;
    if (lVar20 != 0) goto LAB_180028e30;
    piVar23 = (int *)((longlong)plVar29 + 0x24);
  }
  else {
LAB_180028e30:
    iVar7 = (int)plVar29[4];
    if (iVar7 == 0) {
      piVar23 = (int *)((longlong)plVar29 + 0x24);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar6) {
        uVar6 = iVar7 - 1;
      }
      piVar23 = (int *)(lVar20 + (longlong)(int)uVar6 * 4);
    }
  }
  iVar7 = *piVar23;
  lVar20 = *(longlong *)(piVar28 + 0xc);
  if (lVar20 == 0) {
    if (*(code **)(piVar28 + 0x10) != (code *)0x0) {
      (**(code **)(piVar28 + 0x10))(piVar28[0x12]);
    }
    lVar20 = *(longlong *)(piVar28 + 0xc);
    if (lVar20 != 0) goto LAB_180028e8c;
    piVar28 = piVar28 + 0x15;
  }
  else {
LAB_180028e8c:
    iVar22 = piVar28[0x14];
    if (iVar22 == 0) {
      piVar28 = piVar28 + 0x15;
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar22 <= (int)uVar8) {
        uVar8 = iVar22 - 1;
      }
      piVar28 = (int *)(lVar20 + (longlong)(int)uVar8 * 4);
    }
  }
  *piVar28 = iVar7;
  uVar6 = param_3[0xe1];
  lVar20 = *plVar31;
  uVar8 = uVar6;
  if (lVar20 == 0) {
    if ((code *)plVar31[2] != (code *)0x0) {
      (*(code *)plVar31[2])((int)plVar31[3]);
      uVar8 = param_3[0xe1];
    }
    lVar20 = *plVar31;
    if (lVar20 != 0) goto LAB_180028ef8;
    piVar23 = (int *)((longlong)plVar31 + 0x24);
  }
  else {
LAB_180028ef8:
    iVar7 = (int)plVar31[4];
    if (iVar7 == 0) {
      piVar23 = (int *)((longlong)plVar31 + 0x24);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar6) {
        uVar6 = iVar7 - 1;
      }
      piVar23 = (int *)(lVar20 + (longlong)(int)uVar6 * 4);
    }
  }
  iVar7 = *piVar23;
  lVar20 = *(longlong *)(piVar30 + 0xc);
  if (lVar20 == 0) {
    if (*(code **)(piVar30 + 0x10) != (code *)0x0) {
      (**(code **)(piVar30 + 0x10))(piVar30[0x12]);
    }
    lVar20 = *(longlong *)(piVar30 + 0xc);
    if (lVar20 != 0) goto LAB_180028f54;
    piVar30 = piVar30 + 0x15;
  }
  else {
LAB_180028f54:
    iVar22 = piVar30[0x14];
    if (iVar22 == 0) {
      piVar30 = piVar30 + 0x15;
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar22 <= (int)uVar8) {
        uVar8 = iVar22 - 1;
      }
      piVar30 = (int *)(lVar20 + (longlong)(int)uVar8 * 4);
    }
  }
  *piVar30 = iVar7;
  uVar6 = param_3[0xe1];
  lVar20 = *plVar33;
  uVar8 = uVar6;
  if (lVar20 == 0) {
    if ((code *)plVar33[2] != (code *)0x0) {
      (*(code *)plVar33[2])((int)plVar33[3]);
      uVar8 = param_3[0xe1];
    }
    lVar20 = *plVar33;
    if (lVar20 != 0) goto LAB_180028fc0;
    puVar17 = (undefined4 *)((longlong)plVar33 + 0x24);
  }
  else {
LAB_180028fc0:
    iVar7 = (int)plVar33[4];
    if (iVar7 == 0) {
      puVar17 = (undefined4 *)((longlong)plVar33 + 0x24);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar6) {
        uVar6 = iVar7 - 1;
      }
      puVar17 = (undefined4 *)(lVar20 + (longlong)(int)uVar6 * 4);
    }
  }
  uVar49 = *puVar17;
  lVar20 = plVar32[6];
  if (lVar20 == 0) {
    if ((code *)plVar32[8] != (code *)0x0) {
      (*(code *)plVar32[8])((int)plVar32[9]);
    }
    lVar20 = plVar32[6];
    if (lVar20 != 0) goto LAB_18002901c;
    puVar17 = (undefined4 *)((longlong)plVar32 + 0x54);
  }
  else {
LAB_18002901c:
    iVar7 = (int)plVar32[10];
    if (iVar7 == 0) {
      puVar17 = (undefined4 *)((longlong)plVar32 + 0x54);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar8) {
        uVar8 = iVar7 - 1;
      }
      puVar17 = (undefined4 *)(lVar20 + (longlong)(int)uVar8 * 4);
    }
  }
  *puVar17 = uVar49;
  uVar6 = param_3[0xe1];
  lVar20 = *plVar35;
  uVar8 = uVar6;
  if (lVar20 == 0) {
    if ((code *)plVar35[2] != (code *)0x0) {
      (*(code *)plVar35[2])((int)plVar35[3]);
      uVar8 = param_3[0xe1];
    }
    lVar20 = *plVar35;
    if (lVar20 != 0) goto LAB_180029088;
    puVar17 = (undefined4 *)((longlong)plVar35 + 0x24);
  }
  else {
LAB_180029088:
    iVar7 = (int)plVar35[4];
    if (iVar7 == 0) {
      puVar17 = (undefined4 *)((longlong)plVar35 + 0x24);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar6) {
        uVar6 = iVar7 - 1;
      }
      puVar17 = (undefined4 *)(lVar20 + (longlong)(int)uVar6 * 4);
    }
  }
  uVar49 = *puVar17;
  lVar20 = plVar34[6];
  if (lVar20 == 0) {
    if ((code *)plVar34[8] != (code *)0x0) {
      (*(code *)plVar34[8])((int)plVar34[9]);
    }
    lVar20 = plVar34[6];
    if (lVar20 != 0) goto LAB_1800290e4;
    puVar17 = (undefined4 *)((longlong)plVar34 + 0x54);
  }
  else {
LAB_1800290e4:
    iVar7 = (int)plVar34[10];
    if (iVar7 == 0) {
      puVar17 = (undefined4 *)((longlong)plVar34 + 0x54);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar8) {
        uVar8 = iVar7 - 1;
      }
      puVar17 = (undefined4 *)(lVar20 + (longlong)(int)uVar8 * 4);
    }
  }
  *puVar17 = uVar49;
  uVar6 = param_3[0xe1];
  lVar20 = *plVar37;
  uVar8 = uVar6;
  if (lVar20 == 0) {
    if ((code *)plVar37[2] != (code *)0x0) {
      (*(code *)plVar37[2])((int)plVar37[3]);
      uVar8 = param_3[0xe1];
    }
    lVar20 = *plVar37;
    if (lVar20 != 0) goto LAB_180029150;
    piVar23 = (int *)((longlong)plVar37 + 0x24);
  }
  else {
LAB_180029150:
    iVar7 = (int)plVar37[4];
    if (iVar7 == 0) {
      piVar23 = (int *)((longlong)plVar37 + 0x24);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar6) {
        uVar6 = iVar7 - 1;
      }
      piVar23 = (int *)(lVar20 + (longlong)(int)uVar6 * 4);
    }
  }
  iVar7 = *piVar23;
  lVar20 = *(longlong *)(piVar36 + 0xc);
  if (lVar20 == 0) {
    if (*(code **)(piVar36 + 0x10) != (code *)0x0) {
      (**(code **)(piVar36 + 0x10))(piVar36[0x12]);
    }
    lVar20 = *(longlong *)(piVar36 + 0xc);
    if (lVar20 != 0) goto LAB_1800291ac;
    piVar36 = piVar36 + 0x15;
  }
  else {
LAB_1800291ac:
    iVar22 = piVar36[0x14];
    if (iVar22 == 0) {
      piVar36 = piVar36 + 0x15;
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar22 <= (int)uVar8) {
        uVar8 = iVar22 - 1;
      }
      piVar36 = (int *)(lVar20 + (longlong)(int)uVar8 * 4);
    }
  }
  *piVar36 = iVar7;
  uVar6 = param_3[0xe1];
  lVar20 = *plVar39;
  uVar8 = uVar6;
  if (lVar20 == 0) {
    if ((code *)plVar39[2] != (code *)0x0) {
      (*(code *)plVar39[2])((int)plVar39[3]);
      uVar8 = param_3[0xe1];
    }
    lVar20 = *plVar39;
    if (lVar20 != 0) goto LAB_180029218;
    piVar23 = (int *)((longlong)plVar39 + 0x24);
  }
  else {
LAB_180029218:
    iVar7 = (int)plVar39[4];
    if (iVar7 == 0) {
      piVar23 = (int *)((longlong)plVar39 + 0x24);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar6) {
        uVar6 = iVar7 - 1;
      }
      piVar23 = (int *)(lVar20 + (longlong)(int)uVar6 * 4);
    }
  }
  iVar7 = *piVar23;
  lVar20 = *(longlong *)(piVar38 + 0xc);
  if (lVar20 == 0) {
    if (*(code **)(piVar38 + 0x10) != (code *)0x0) {
      (**(code **)(piVar38 + 0x10))(piVar38[0x12]);
    }
    lVar20 = *(longlong *)(piVar38 + 0xc);
    if (lVar20 != 0) goto LAB_180029274;
    piVar38 = piVar38 + 0x15;
  }
  else {
LAB_180029274:
    iVar22 = piVar38[0x14];
    if (iVar22 == 0) {
      piVar38 = piVar38 + 0x15;
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar22 <= (int)uVar8) {
        uVar8 = iVar22 - 1;
      }
      piVar38 = (int *)(lVar20 + (longlong)(int)uVar8 * 4);
    }
  }
  *piVar38 = iVar7;
  uVar6 = param_3[0xe1];
  lVar20 = *local_1d0;
  uVar8 = uVar6;
  if (lVar20 == 0) {
    if ((code *)local_1d0[2] != (code *)0x0) {
      (*(code *)local_1d0[2])((int)local_1d0[3]);
      uVar8 = param_3[0xe1];
    }
    lVar20 = *local_1d0;
    if (lVar20 != 0) goto LAB_1800292e0;
    piVar23 = (int *)((longlong)local_1d0 + 0x24);
  }
  else {
LAB_1800292e0:
    iVar7 = (int)local_1d0[4];
    if (iVar7 == 0) {
      piVar23 = (int *)((longlong)local_1d0 + 0x24);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar6) {
        uVar6 = iVar7 - 1;
      }
      piVar23 = (int *)(lVar20 + (longlong)(int)uVar6 * 4);
    }
  }
  iVar7 = *piVar23;
  lVar20 = *(longlong *)(piVar19 + 0xc);
  if (lVar20 == 0) {
    if (*(code **)(piVar19 + 0x10) != (code *)0x0) {
      (**(code **)(piVar19 + 0x10))(piVar19[0x12]);
    }
    lVar20 = *(longlong *)(piVar19 + 0xc);
    if (lVar20 != 0) goto LAB_18002933c;
    piVar19 = piVar19 + 0x15;
  }
  else {
LAB_18002933c:
    iVar22 = piVar19[0x14];
    if (iVar22 == 0) {
      piVar19 = piVar19 + 0x15;
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar22 <= (int)uVar8) {
        uVar8 = iVar22 - 1;
      }
      piVar19 = (int *)(lVar20 + (longlong)(int)uVar8 * 4);
    }
  }
  *piVar19 = iVar7;
  uVar6 = param_3[0xe1];
  lVar20 = *plVar33;
  uVar8 = uVar6;
  if (lVar20 == 0) {
    if ((code *)plVar33[2] != (code *)0x0) {
      (*(code *)plVar33[2])((int)plVar33[3]);
      uVar8 = param_3[0xe1];
    }
    lVar20 = *plVar33;
    if (lVar20 != 0) goto LAB_1800293a8;
    pfVar14 = (float *)((longlong)plVar33 + 0x24);
  }
  else {
LAB_1800293a8:
    iVar7 = (int)plVar33[4];
    if (iVar7 == 0) {
      pfVar14 = (float *)((longlong)plVar33 + 0x24);
    }
    else {
      uVar6 = uVar6 & ((int)uVar6 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar6) {
        uVar6 = iVar7 - 1;
      }
      pfVar14 = (float *)(lVar20 + (longlong)(int)uVar6 * 4);
    }
  }
  lVar20 = *plVar35;
  if (lVar20 == 0) {
    if ((code *)plVar35[2] != (code *)0x0) {
      (*(code *)plVar35[2])((int)plVar35[3]);
    }
    lVar20 = *plVar35;
    if (lVar20 != 0) goto LAB_180029400;
    pfVar15 = (float *)((longlong)plVar35 + 0x24);
  }
  else {
LAB_180029400:
    iVar7 = (int)plVar35[4];
    if (iVar7 == 0) {
      pfVar15 = (float *)((longlong)plVar35 + 0x24);
    }
    else {
      uVar8 = uVar8 & ((int)uVar8 >> 0x1f ^ 0xffffffffU);
      if (iVar7 <= (int)uVar8) {
        uVar8 = iVar7 - 1;
      }
      pfVar15 = (float *)(lVar20 + (longlong)(int)uVar8 * 4);
    }
  }
  if (*local_1a0 < *pfVar14 - *pfVar15) {
    if ((param_3[0x2b] == 0) && (param_3[0xe1] == *param_3 + -1)) {
      uVar49 = FUN_180026888((longlong)local_200);
      piVar23 = local_198;
      puVar17 = (undefined4 *)FUN_180005d08(plVar32 + 0xb,local_198[0xe1]);
      *puVar17 = uVar49;
      uVar49 = FUN_180026888((longlong)local_200);
      puVar17 = (undefined4 *)FUN_180005d08(plVar34 + 0xb,piVar23[0xe1]);
      *puVar17 = uVar49;
    }
    else {
      piVar23 = local_198 + 0xe1;
      lVar20 = plVar32[3];
      puVar17 = (undefined4 *)FUN_180005d08(plVar32 + 0xb,local_198[0xe1]);
      *puVar17 = (int)lVar20;
      lVar20 = plVar34[3];
      puVar17 = (undefined4 *)FUN_180005d08(plVar34 + 0xb,*piVar23);
      *puVar17 = (int)lVar20;
    }
  }
  else {
    uVar49 = *(undefined4 *)((longlong)plVar32 + 0x1c);
    puVar17 = (undefined4 *)FUN_180005d08(plVar32 + 0xb,param_3[0xe1]);
    *puVar17 = uVar49;
    uVar49 = *(undefined4 *)((longlong)plVar34 + 0x1c);
    puVar17 = (undefined4 *)FUN_180005d08(plVar34 + 0xb,param_3[0xe1]);
    *puVar17 = uVar49;
  }
  bVar5 = FUN_180026690((longlong)local_248);
  if (!bVar5) {
    return;
  }
  if (lVar24 < lVar43) {
    if ((lVar18 < lVar43) && (bVar5 = false, lVar24 <= lVar18)) goto LAB_180029574;
  }
  else if ((lVar18 < lVar43) || (lVar24 <= lVar18)) {
    bVar5 = false;
    goto LAB_180029574;
  }
  bVar5 = true;
LAB_180029574:
  if (bVar5) {
    puVar17 = (undefined4 *)FUN_18000f448((longlong)plVar32,param_3[0xe1]);
    *puVar17 = 0;
    puVar17 = (undefined4 *)FUN_18000f448((longlong)plVar34,param_3[0xe1]);
    *puVar17 = 0;
  }
  return;
}

